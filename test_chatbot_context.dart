// import 'package:flutter/foundation.dart';
// import 'lib/src/features/ai_mimi/chat_bot/chatbot_service.dart';

// void main() async {
//   // Test the enhanced chatbot context management
//   print('Testing Enhanced Chatbot Context Management');
//   print('=' * 50);
  
//   final aiService = await AIService.create();
//   final sessionId = DateTime.now().toIso8601String();
  
//   // Simulate a conversation with multiple messages
//   final testMessages = [
//     "Hi, I'm feeling really anxious about my upcoming presentation at work.",
//     "I've been having trouble sleeping because of it.",
//     "What techniques can you suggest to help me manage this anxiety?",
//     "That's helpful. Can you tell me more about breathing exercises?",
//     "I tried the breathing exercise you mentioned earlier, but I'm still feeling nervous.",
//     "What about the presentation tips you gave me before?",
//     "Can you remind me what we discussed about managing anxiety?",
//     "I want to practice the techniques we talked about earlier.",
//   ];
  
//   print('Starting conversation simulation...\n');
  
//   for (int i = 0; i < testMessages.length; i++) {
//     print('Message ${i + 1}: ${testMessages[i]}');
    
//     try {
//       final response = await aiService.getChatResponse(
//         testMessages[i], 
//         sessionId: sessionId
//       );
      
//       print('Response: ${response.substring(0, response.length > 100 ? 100 : response.length)}${response.length > 100 ? '...' : ''}');
      
//       // Get conversation stats
//       final stats = aiService.getConversationStats(sessionId);
//       print('Stats: ${stats['totalMessages']} messages, ~${stats['estimatedTokens']} tokens');
//       print('-' * 30);
      
//       // Add a small delay to avoid rate limiting
//       await Future.delayed(Duration(seconds: 1));
      
//     } catch (e) {
//       print('Error: $e');
//       print('-' * 30);
//     }
//   }
  
//   // Final stats
//   final finalStats = aiService.getConversationStats(sessionId);
//   print('\nFinal Conversation Statistics:');
//   print('Total Messages: ${finalStats['totalMessages']}');
//   print('Estimated Tokens: ${finalStats['estimatedTokens']}');
//   print('Session Duration: ${DateTime.parse(finalStats['newestMessage']).difference(DateTime.parse(finalStats['oldestMessage'])).inMinutes} minutes');
  
//   print('\nTest completed! The chatbot should now maintain context throughout the entire conversation.');
// }
