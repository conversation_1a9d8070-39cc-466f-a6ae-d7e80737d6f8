// test/onboarding_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';

void main() {
  group('Onboarding State Tests', () {
    test('should create initial onboarding state', () {
      const state = OnboardingState();

      expect(state.currentStep, OnboardingStep.welcome);
      expect(state.isAuthenticated, false);
      expect(state.hasCreatedIntention, false);
      expect(state.hasCreatedRoutine, false);
      expect(state.hasSubscribed, false);
      expect(state.isOnboardingComplete, false);
    });

    test('should progress through onboarding steps correctly', () {
      const state = OnboardingState();

      // Initially can proceed from welcome
      expect(state.canProceedToNextStep, true);

      // Cannot proceed from auth without authentication
      final authState = state.copyWith(currentStep: OnboardingStep.auth);
      expect(authState.canProceedToNextStep, false);

      // Can proceed from auth when authenticated
      final authenticatedState = authState.copyWith(isAuthenticated: true);
      expect(authenticatedState.canProceedToNextStep, true);

      // Cannot proceed from intention without creating intention
      final intentionState =
          authenticatedState.copyWith(currentStep: OnboardingStep.intention);
      expect(intentionState.canProceedToNextStep, false);

      // Can proceed from intention when intention is created
      final intentionCreatedState = intentionState.copyWith(
        hasCreatedIntention: true,
        createdIntentionId: 1,
      );
      expect(intentionCreatedState.canProceedToNextStep, true);
    });

    test('should be complete when all steps are finished', () {
      const completeState = OnboardingState(
        currentStep: OnboardingStep.complete,
        isAuthenticated: true,
        hasCreatedIntention: true,
        hasCreatedRoutine: true,
        hasSubscribed: true,
        createdIntentionId: 1,
        createdRoutineId: 1,
      );

      expect(completeState.isOnboardingComplete, true);
    });

    test('should have correct step numbers', () {
      expect(OnboardingStep.welcome.stepNumber, 1);
      expect(OnboardingStep.intentionHeader.stepNumber, 2);
      expect(OnboardingStep.intention.stepNumber, 3);
      expect(OnboardingStep.routineHeader.stepNumber, 4);
      expect(OnboardingStep.routineName.stepNumber, 5);
      expect(OnboardingStep.routineTime.stepNumber, 6);
      expect(OnboardingStep.activitiesHeader.stepNumber, 7);
      expect(OnboardingStep.activitiesSelection.stepNumber, 8);
      expect(OnboardingStep.activitiesConfig.stepNumber, 9);
      expect(OnboardingStep.paywall.stepNumber, 10);
      expect(OnboardingStep.auth.stepNumber, 11);
      expect(OnboardingStep.complete.stepNumber, 12);
      expect(OnboardingStepExtension.totalSteps, 12);
    });

    test('should have correct step titles and descriptions', () {
      expect(OnboardingStep.welcome.title, 'Welcome to Mimi');
      expect(OnboardingStep.intentionHeader.title, 'Step 1 of 3');
      expect(OnboardingStep.intention.title, 'Set Your Intention');
      expect(OnboardingStep.routineHeader.title, 'Step 2 of 3');
      expect(OnboardingStep.routineName.title, 'Create Your Check-in Routine');
      expect(OnboardingStep.routineTime.title, 'Set Your Routine Time');
      expect(OnboardingStep.activitiesHeader.title, 'Step 3 of 3');
      expect(OnboardingStep.activitiesSelection.title,
          'Add Activities to Your Routine');
      expect(
          OnboardingStep.activitiesConfig.title, 'Configure Your Activities');
      expect(OnboardingStep.paywall.title, 'Start Your Journey');
      expect(OnboardingStep.auth.title, 'Create Your Account');
      expect(OnboardingStep.complete.title, 'Welcome Home');

      expect(OnboardingStep.welcome.description,
          'Your mindfulness journey starts here');
      expect(OnboardingStep.intention.description,
          'What would you like to focus on?');
      expect(OnboardingStep.paywall.description, 'Unlock your full potential');
      expect(OnboardingStep.auth.description, 'Join the Mimi community');
      expect(OnboardingStep.complete.description, 'Your journey begins now');
    });
  });
}
