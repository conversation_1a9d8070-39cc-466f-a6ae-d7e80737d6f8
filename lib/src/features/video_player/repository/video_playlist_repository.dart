import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mimi_app/src/features/video_player/model/video_playlist.dart';

class PlaylistRepository {
  final FirebaseFirestore _firestore;
  final String _userId;

  PlaylistRepository(this._firestore, this._userId);

  Future<List<VideoPlaylist>> getUserPlaylists() async {
    final snapshot = await _firestore
        .collection('users')
        .doc(_userId)
        .collection('playlists')
        .get();

    return snapshot.docs
        .map((doc) => VideoPlaylist.fromMap(doc.data()))
        .toList();
  }

  Future<void> createPlaylist(String name, List<String> videoIds) async {
    final playlist = VideoPlaylist(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      userId: _userId,
      videoIds: videoIds,
    );

    await _firestore
        .collection('users')
        .doc(_userId)
        .collection('playlists')
        .doc(playlist.id)
        .set(playlist.toMap());
  }

  Future<void> addVideoToPlaylist(String playlistId, String videoId) async {
    await _firestore
        .collection('users')
        .doc(_userId)
        .collection('playlists')
        .doc(playlistId)
        .update({
      'videoIds': FieldValue.arrayUnion([videoId])
    });
  }
}
