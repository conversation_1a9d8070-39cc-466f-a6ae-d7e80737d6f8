import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:mimi_app/src/features/video_player/youtube_helper.dart';

class VideoRepository {
  final FirebaseFirestore _firestore;
  final String _userId;

  VideoRepository(this._firestore, this._userId);

  Future<List<Video>> getVideosByCategory(String categoryId) async {
    final snapshot = await _firestore
        .collection('videos')
        .where('categoryId', isEqualTo: categoryId)
        .get();

    return snapshot.docs.map((doc) => Video.fromMap(doc.data())).toList();
  }

  Future<void> toggleFavorite(String videoId, bool isFavorite) async {
    await _firestore
        .collection('users')
        .doc(_userId)
        .collection('favorites')
        .doc(videoId)
        .set({'isFavorite': isFavorite});
  }

  Future<List<Video>> getFavoriteVideos() async {
    final favoritesSnapshot = await _firestore
        .collection('users')
        .doc(_userId)
        .collection('favorites')
        .where('isFavorite', isEqualTo: true)
        .get();

    final videoIds = favoritesSnapshot.docs.map((doc) => doc.id).toList();

    final videosSnapshot = await _firestore
        .collection('videos')
        .where(FieldPath.documentId, whereIn: videoIds)
        .get();

    return videosSnapshot.docs.map((doc) => Video.fromMap(doc.data())).toList();
  }

  Future<void> addVideo({
    required String title,
    required String url,
    required String categoryId,
  }) async {
    final isYouTube = YouTubeHelper.isYouTubeUrl(url);
    String videoId;
    String thumbnailUrl = '';

    if (isYouTube) {
      final youtubeId = YouTubeHelper.extractVideoId(url);
      if (youtubeId == null) {
        // Handle the case where YouTube ID extraction fails
        // Use a timestamp as fallback ID
        videoId = DateTime.now().millisecondsSinceEpoch.toString();
      } else {
        videoId = youtubeId;
        thumbnailUrl = YouTubeHelper.getThumbnailUrl(youtubeId);
      }
    } else {
      videoId = DateTime.now().millisecondsSinceEpoch.toString();
    }

    final video = Video(
      id: videoId,
      title: title,
      url: url,
      categoryId: categoryId,
      thumbnailUrl: thumbnailUrl,
      type: isYouTube ? VideoType.youtube : VideoType.network,
      isFavorite: false,
    );

    await _firestore.collection('videos').doc(videoId).set(video.toMap());
  }

  Future<List<Video>> getAllVideos() async {
    final snapshot = await _firestore.collection('videos').get();
    return snapshot.docs.map((doc) => Video.fromMap(doc.data())).toList();
  }
}
