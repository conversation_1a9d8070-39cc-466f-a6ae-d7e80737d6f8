import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class YouTubeHelper {
  static bool isYouTubeUrl(String url) {
    return url.contains('youtube.com') || url.contains('youtu.be');
  }

  static String? extractVideoId(String url) {
    // Try to convert using the YoutubePlayer method
    String? videoId = YoutubePlayer.convertUrlToId(url);

    // If that fails, try manual extraction
    if (videoId == null) {
      try {
        if (url.contains('youtu.be')) {
          // Format: https://youtu.be/videoId
          videoId = url.split('youtu.be/')[1].split('?')[0];
        } else if (url.contains('youtube.com/watch')) {
          // Format: https://www.youtube.com/watch?v=videoId
          final uri = Uri.parse(url);
          videoId = uri.queryParameters['v'];
        } else if (url.contains('youtube.com/embed')) {
          // Format: https://www.youtube.com/embed/videoId
          videoId = url.split('youtube.com/embed/')[1].split('?')[0];
        }
      } catch (e) {
        print('Error extracting YouTube ID: $e');
        return null;
      }
    }

    return videoId;
  }

  static String getThumbnailUrl(String videoId) {
    return 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg';
  }
}
