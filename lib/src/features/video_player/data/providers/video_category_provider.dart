// lib/src/features/video_player/data/providers/video_category_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/video_category.dart';
import '../repositories/video_category_repository.dart';

part 'video_category_provider.g.dart';

@riverpod
Future<List<VideoCategory>> videoCategories(Ref ref) async {
  final repository = ref.watch(videoCategoryRepositoryProvider);
  return repository.getCategories();
}

@riverpod
Future<VideoCategory?> videoCategory(Ref ref, String categoryId) async {
  final repository = ref.watch(videoCategoryRepositoryProvider);
  return repository.getCategoryById(categoryId);
}
