// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_category_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$videoCategoriesHash() => r'6b716afc900cfde33104810170ff82a32f4c80cb';

/// See also [videoCategories].
@ProviderFor(videoCategories)
final videoCategoriesProvider =
    AutoDisposeFutureProvider<List<VideoCategory>>.internal(
  videoCategories,
  name: r'videoCategoriesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoCategoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef VideoCategoriesRef = AutoDisposeFutureProviderRef<List<VideoCategory>>;
String _$videoCategoryHash() => r'55b68380c935c00cac605734a85d88a768b4c890';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [videoCategory].
@ProviderFor(videoCategory)
const videoCategoryProvider = VideoCategoryFamily();

/// See also [videoCategory].
class VideoCategoryFamily extends Family<AsyncValue<VideoCategory?>> {
  /// See also [videoCategory].
  const VideoCategoryFamily();

  /// See also [videoCategory].
  VideoCategoryProvider call(
    String categoryId,
  ) {
    return VideoCategoryProvider(
      categoryId,
    );
  }

  @override
  VideoCategoryProvider getProviderOverride(
    covariant VideoCategoryProvider provider,
  ) {
    return call(
      provider.categoryId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'videoCategoryProvider';
}

/// See also [videoCategory].
class VideoCategoryProvider extends AutoDisposeFutureProvider<VideoCategory?> {
  /// See also [videoCategory].
  VideoCategoryProvider(
    String categoryId,
  ) : this._internal(
          (ref) => videoCategory(
            ref as VideoCategoryRef,
            categoryId,
          ),
          from: videoCategoryProvider,
          name: r'videoCategoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$videoCategoryHash,
          dependencies: VideoCategoryFamily._dependencies,
          allTransitiveDependencies:
              VideoCategoryFamily._allTransitiveDependencies,
          categoryId: categoryId,
        );

  VideoCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<VideoCategory?> Function(VideoCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VideoCategoryProvider._internal(
        (ref) => create(ref as VideoCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<VideoCategory?> createElement() {
    return _VideoCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VideoCategoryProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VideoCategoryRef on AutoDisposeFutureProviderRef<VideoCategory?> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _VideoCategoryProviderElement
    extends AutoDisposeFutureProviderElement<VideoCategory?>
    with VideoCategoryRef {
  _VideoCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as VideoCategoryProvider).categoryId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
