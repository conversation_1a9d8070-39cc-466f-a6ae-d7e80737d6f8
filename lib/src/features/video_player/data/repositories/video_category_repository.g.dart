// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_category_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$videoCategoryRepositoryHash() =>
    r'13286296b49d7dbfa52ca4538ea35698bca847d5';

/// See also [videoCategoryRepository].
@ProviderFor(videoCategoryRepository)
final videoCategoryRepositoryProvider =
    AutoDisposeProvider<VideoCategoryRepository>.internal(
  videoCategoryRepository,
  name: r'videoCategoryRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoCategoryRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef VideoCategoryRepositoryRef
    = AutoDisposeProviderRef<VideoCategoryRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
