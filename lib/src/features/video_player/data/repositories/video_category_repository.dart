// lib/src/features/video_player/data/repositories/video_category_repository.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/video_category.dart';

part 'video_category_repository.g.dart';

@riverpod
VideoCategoryRepository videoCategoryRepository(Ref ref) {
  return VideoCategoryRepository();
}

class VideoCategoryRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection names
  static const String _categoriesCollection = 'video_categories';

  Future<List<VideoCategory>> getCategories() async {
    try {
      final snapshot = await _firestore
          .collection(_categoriesCollection)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => VideoCategory.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error fetching video categories: $e');
      return [];
    }
  }

  Future<VideoCategory?> getCategoryById(String categoryId) async {
    try {
      final doc = await _firestore
          .collection(_categoriesCollection)
          .doc(categoryId)
          .get();

      if (doc.exists && doc.data() != null) {
        return VideoCategory.fromFirestore(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching video category: $e');
      return null;
    }
  }

  Future<void> createCategory(VideoCategory category) async {
    try {
      await _firestore
          .collection(_categoriesCollection)
          .doc(category.id)
          .set(category.toFirestore());
    } catch (e) {
      debugPrint('Error creating video category: $e');
      rethrow;
    }
  }

  Future<void> updateCategory(VideoCategory category) async {
    try {
      await _firestore
          .collection(_categoriesCollection)
          .doc(category.id)
          .update(category.toFirestore());
    } catch (e) {
      debugPrint('Error updating video category: $e');
      rethrow;
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    try {
      await _firestore
          .collection(_categoriesCollection)
          .doc(categoryId)
          .delete();
    } catch (e) {
      debugPrint('Error deleting video category: $e');
      rethrow;
    }
  }

  Future<void> updateVideoCount(String categoryId, int count) async {
    try {
      await _firestore
          .collection(_categoriesCollection)
          .doc(categoryId)
          .update({'videoCount': count});
    } catch (e) {
      debugPrint('Error updating video count: $e');
      rethrow;
    }
  }
}
