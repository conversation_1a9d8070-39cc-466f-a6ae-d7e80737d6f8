// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'video_category.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VideoCategory _$VideoCategoryFromJson(Map<String, dynamic> json) {
  return _VideoCategory.fromJson(json);
}

/// @nodoc
mixin _$VideoCategory {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get iconUrl => throw _privateConstructorUsedError;
  int get videoCount => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this VideoCategory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VideoCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VideoCategoryCopyWith<VideoCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VideoCategoryCopyWith<$Res> {
  factory $VideoCategoryCopyWith(
          VideoCategory value, $Res Function(VideoCategory) then) =
      _$VideoCategoryCopyWithImpl<$Res, VideoCategory>;
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      String? iconUrl,
      int videoCount,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$VideoCategoryCopyWithImpl<$Res, $Val extends VideoCategory>
    implements $VideoCategoryCopyWith<$Res> {
  _$VideoCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VideoCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? iconUrl = freezed,
    Object? videoCount = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoCount: null == videoCount
          ? _value.videoCount
          : videoCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VideoCategoryImplCopyWith<$Res>
    implements $VideoCategoryCopyWith<$Res> {
  factory _$$VideoCategoryImplCopyWith(
          _$VideoCategoryImpl value, $Res Function(_$VideoCategoryImpl) then) =
      __$$VideoCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      String? iconUrl,
      int videoCount,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$VideoCategoryImplCopyWithImpl<$Res>
    extends _$VideoCategoryCopyWithImpl<$Res, _$VideoCategoryImpl>
    implements _$$VideoCategoryImplCopyWith<$Res> {
  __$$VideoCategoryImplCopyWithImpl(
      _$VideoCategoryImpl _value, $Res Function(_$VideoCategoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of VideoCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? iconUrl = freezed,
    Object? videoCount = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$VideoCategoryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoCount: null == videoCount
          ? _value.videoCount
          : videoCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VideoCategoryImpl extends _VideoCategory {
  const _$VideoCategoryImpl(
      {required this.id,
      required this.name,
      this.description,
      this.iconUrl,
      this.videoCount = 0,
      this.createdAt,
      this.updatedAt})
      : super._();

  factory _$VideoCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$VideoCategoryImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? iconUrl;
  @override
  @JsonKey()
  final int videoCount;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'VideoCategory(id: $id, name: $name, description: $description, iconUrl: $iconUrl, videoCount: $videoCount, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VideoCategoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.videoCount, videoCount) ||
                other.videoCount == videoCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, description, iconUrl,
      videoCount, createdAt, updatedAt);

  /// Create a copy of VideoCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VideoCategoryImplCopyWith<_$VideoCategoryImpl> get copyWith =>
      __$$VideoCategoryImplCopyWithImpl<_$VideoCategoryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VideoCategoryImplToJson(
      this,
    );
  }
}

abstract class _VideoCategory extends VideoCategory {
  const factory _VideoCategory(
      {required final String id,
      required final String name,
      final String? description,
      final String? iconUrl,
      final int videoCount,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$VideoCategoryImpl;
  const _VideoCategory._() : super._();

  factory _VideoCategory.fromJson(Map<String, dynamic> json) =
      _$VideoCategoryImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  String? get iconUrl;
  @override
  int get videoCount;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of VideoCategory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VideoCategoryImplCopyWith<_$VideoCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
