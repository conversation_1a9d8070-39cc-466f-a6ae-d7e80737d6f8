// lib/src/features/video_player/data/models/video_category.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'video_category.freezed.dart';
part 'video_category.g.dart';

@freezed
class VideoCategory with _$VideoCategory {
  const factory VideoCategory({
    required String id,
    required String name,
    String? description,
    String? iconUrl,
    @Default(0) int videoCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _VideoCategory;

  const VideoCategory._();

  factory VideoCategory.fromJson(Map<String, dynamic> json) =>
      _$VideoCategoryFromJson(json);

  // Helper method to convert Firestore data
  factory VideoCategory.fromFirestore(Map<String, dynamic> data, String id) {
    return VideoCategory(
      id: id,
      name: data['name'] ?? '',
      description: data['description'],
      iconUrl: data['iconUrl'],
      videoCount: data['videoCount'] ?? 0,
      createdAt: data['createdAt']?.toDate(),
      updatedAt: data['updatedAt']?.toDate(),
    );
  }

  // Helper method to convert to Firestore data
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'videoCount': videoCount,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}
