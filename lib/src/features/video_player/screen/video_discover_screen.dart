import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/video_player/screen/video_list_screen.dart';
import 'package:mimi_app/src/features/video_player/data/providers/video_category_provider.dart';
import 'package:mimi_app/src/features/video_player/data/models/video_category.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:mimi_app/src/features/video_player/providers/video_providers.dart';

class DiscoverVideoScreen extends ConsumerWidget {
  const DiscoverVideoScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoriesAsync = ref.watch(videoCategoriesProvider);

    return GradientScaffold(
      appBar: AppBar(
        title: Text(
          'Videos',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.textPrimary,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search Bar
              // TextField(
              //   decoration: InputDecoration(
              //     hintText: 'Search videos...',
              //     hintStyle: TextStyle(color: AppColors.textSecondary),
              //     prefixIcon:
              //         Icon(Icons.search, color: AppColors.textSecondary),
              //     filled: true,
              //     fillColor: AppColors.surface.withValues(alpha: 0.3),
              //     border: OutlineInputBorder(
              //       borderRadius: BorderRadius.circular(12),
              //       borderSide: BorderSide.none,
              //     ),
              //     contentPadding: const EdgeInsets.symmetric(
              //       horizontal: 16,
              //       vertical: 12,
              //     ),
              //   ),
              //   style: TextStyle(color: AppColors.textPrimary),
              // ),
              const SizedBox(height: 24),

              // Categories Section
              categoriesAsync.when(
                data: (categories) => Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Categories Grid
                        Text(
                          'Categories',
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 2.5,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                          itemCount: categories.length,
                          itemBuilder: (context, index) {
                            final category = categories[index];
                            return GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => VideoListScreen(
                                      category: category.id,
                                      categoryName: category.name,
                                    ),
                                  ),
                                );
                              },
                              child: CategoryCard(
                                title: category.name,
                                description: category.description,
                                videoCount: category.videoCount,
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 32),

                        // Video Sections for each category
                        ...categories.map((category) => VideoSection(
                              category: category,
                            )),
                      ],
                    ),
                  ),
                ),
                loading: () => const Expanded(
                  child: Center(child: CircularProgressIndicator()),
                ),
                error: (error, stack) => Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading categories',
                          style: TextStyle(color: AppColors.textPrimary),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          error.toString(),
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CategoryCard extends StatelessWidget {
  final String title;
  final String? description;
  final int videoCount;

  const CategoryCard({
    super.key,
    required this.title,
    this.description,
    this.videoCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.secondary.withValues(alpha: 0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Icon(
                  Icons.play_circle_outline,
                  color: AppColors.textOnPrimary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: AppColors.textOnPrimary,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '$videoCount videos',
              style: TextStyle(
                color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class VideoSection extends StatelessWidget {
  final VideoCategory category;

  const VideoSection({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final videosAsync = ref.watch(videosProvider(category.id));

        return videosAsync.when(
          data: (videos) {
            if (videos.isEmpty) return const SizedBox.shrink();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      category.name,
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => VideoListScreen(
                              category: category.id,
                              categoryName: category.name,
                            ),
                          ),
                        );
                      },
                      child: Text(
                        'Show All',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: videos.take(10).length,
                    itemBuilder: (context, index) {
                      final video = videos[index];
                      return Padding(
                        padding: EdgeInsets.only(
                          right: index == videos.length - 1 ? 0 : 12,
                        ),
                        child: VideoCard(video: video),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 24),
              ],
            );
          },
          loading: () => const SizedBox(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stack) => const SizedBox.shrink(),
        );
      },
    );
  }
}

class VideoCard extends StatelessWidget {
  final Video video;

  const VideoCard({
    super.key,
    required this.video,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.pushNamed(
          RouteNames.videoPlayer,
          extra: video,
        );
      },
      child: Container(
        width: 200, // Slightly wider to accommodate landscape videos
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Video Thumbnail with 16:9 aspect ratio
            AspectRatio(
              aspectRatio: 16 / 9, // Standard YouTube aspect ratio
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: AppColors.primary.withValues(alpha: 0.1),
                ),
                child: Stack(
                  children: [
                    // Thumbnail image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: video.thumbnailUrl.isNotEmpty
                          ? Image.network(
                              video.thumbnailUrl,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color:
                                      AppColors.primary.withValues(alpha: 0.1),
                                ),
                                child: Icon(
                                  Icons.video_library,
                                  color: AppColors.primary,
                                  size: 40,
                                ),
                              ),
                            )
                          : Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: AppColors.primary.withValues(alpha: 0.1),
                              ),
                              child: Icon(
                                Icons.video_library,
                                color: AppColors.primary,
                                size: 40,
                              ),
                            ),
                    ),
                    // Play button overlay
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.surface.withValues(alpha: 0.7),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.play_arrow,
                          color: AppColors.textOnPrimary,
                          size: 28,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            // Video Title
            Text(
              video.title,
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
