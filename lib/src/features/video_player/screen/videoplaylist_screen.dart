import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/video_player/providers/video_playlist_provider.dart';

class PlaylistScreen extends ConsumerWidget {
  const PlaylistScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final playlistsAsyncValue = ref.watch(playlistsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('My Playlists'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              // Show dialog to create new playlist
              // showDialog(
              //   context: context,
              //   builder: (context) => CreatePlaylistDialog(),
              // );
            },
          ),
        ],
      ),
      body: playlistsAsyncValue.when(
        data: (playlists) => ListView.builder(
          itemCount: playlists.length,
          itemBuilder: (context, index) {
            final playlist = playlists[index];
            return ListTile(
              title: Text(playlist.name),
              subtitle: Text('${playlist.videoIds.length} videos'),
              onTap: () {
                // Navigate to playlist details screen
              },
            );
          },
        ),
        loading: () => Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
      ),
    );
  }
}
