import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/video_player/providers/video_providers.dart';

class VideoListScreen extends ConsumerWidget {
  final String category;
  final String? categoryName;

  const VideoListScreen({
    super.key,
    required this.category,
    this.categoryName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAll = category == 'all';
    final videosAsyncValue = isAll
        ? ref.watch(allVideosProvider)
        : ref.watch(videosProvider(category));

    return Scaffold(
      appBar: AppBar(
        title: Text(categoryName ?? category),
      ),
      body: videosAsyncValue.when(
        data: (videos) => Padding(
          padding: const EdgeInsets.all(16.0),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.4, // Increased height for better visibility
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: videos.length,
            itemBuilder: (context, index) {
              final video = videos[index];
              return GestureDetector(
                onTap: () {
                  context.pushNamed(
                    RouteNames.videoPlayer,
                    extra: video,
                  );
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Video Thumbnail with 16:9 aspect ratio
                    Expanded(
                      child: AspectRatio(
                        aspectRatio: 16 / 9,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: AppColors.surface,
                          ),
                          child: Stack(
                            children: [
                              // Thumbnail image
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: video.thumbnailUrl.isNotEmpty
                                    ? Image.network(
                                        video.thumbnailUrl,
                                        width: double.infinity,
                                        height: double.infinity,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                Container(
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            color: AppColors.surface,
                                          ),
                                          child: Icon(
                                            Icons.video_library,
                                            size: 40,
                                            color: AppColors.textSecondary,
                                          ),
                                        ),
                                        loadingBuilder:
                                            (context, child, loadingProgress) {
                                          if (loadingProgress == null) {
                                            return child;
                                          }
                                          return const Center(
                                            child: CircularProgressIndicator(),
                                          );
                                        },
                                      )
                                    : Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          color: AppColors.surface,
                                        ),
                                        child: Icon(
                                          Icons.video_library,
                                          size: 40,
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                              ),
                              // Play button overlay
                              Center(
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: AppColors.surface
                                        .withValues(alpha: 0.7),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.play_arrow,
                                    color: AppColors.textOnPrimary,
                                    size: 24,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Video Title
                    Text(
                      video.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error Found: $error')),
      ),
    );
  }
}
