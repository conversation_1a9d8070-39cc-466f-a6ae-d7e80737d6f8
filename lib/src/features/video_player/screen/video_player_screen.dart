import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:mimi_app/src/features/video_player/providers/video_playlist_provider.dart';
import 'package:mimi_app/src/features/video_player/providers/video_providers.dart';
import 'package:mimi_app/src/features/video_player/youtube_helper.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class UnifiedVideoPlayerScreen extends ConsumerStatefulWidget {
  final Video video;

  const UnifiedVideoPlayerScreen({
    super.key,
    required this.video,
  });

  @override
  UnifiedVideoPlayerScreenState createState() =>
      UnifiedVideoPlayerScreenState();
}

class UnifiedVideoPlayerScreenState
    extends ConsumerState<UnifiedVideoPlayerScreen> {
  YoutubePlayerController? _youtubeController;
  VideoPlayerController? _videoController; //sdienst
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _showControls = true;
  Timer? _controlsTimer;

  @override
  void initState() {
    super.initState();
    _enableFullScreen();
    _initializePlayer();
    // Auto-hide controls after 3 seconds
    _startControlsTimer();
  }

  Future<void> _enableFullScreen() async {
    try {
      // Allow all orientations for video player
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      // Set system UI overlay style for better visibility
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
      );

      // Hide status bar and navigation bar for immersive experience
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

      // Force a rebuild to apply orientation changes
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('Error enabling full screen: $e');
    }
  }

  Future<void> _restoreSystemUI() async {
    try {
      // Restore portrait orientation
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      // Restore system UI
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

      // Restore system UI overlay style
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );
    } catch (e) {
      print('Error restoring system UI: $e');
    }
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startControlsTimer();
    }
  }

  void _startControlsTimer() {
    // Cancel any existing timer
    _controlsTimer?.cancel();
    // Start new timer to hide controls after 3 seconds
    _controlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  Future<void> _initializePlayer() async {
    try {
      if (widget.video.type == VideoType.youtube) {
        // Handle YouTube videos
        final videoId = YouTubeHelper.extractVideoId(widget.video.url);
        if (videoId != null) {
          _youtubeController = YoutubePlayerController(
            initialVideoId: videoId,
            flags: const YoutubePlayerFlags(
              autoPlay: true,
              mute: false,
            ),
          );
        } else {
          // Failed to extract YouTube ID
          setState(() {
            _hasError = true;
            _errorMessage = 'Invalid YouTube URL. Could not extract video ID.';
          });

          // Try as network video if it's a valid URL
          if (widget.video.url.startsWith('http')) {
            _fallbackToNetworkVideo();
          }
        }
      } else {
        // Handle network videos
        await _initializeNetworkVideo();
      }
    } catch (e) {
      print('Error initializing video player: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'Error loading video: ${e.toString()}';
      });
    }

    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  Future<void> _initializeNetworkVideo() async {
    try {
      _videoController =
          VideoPlayerController.networkUrl(Uri.parse(widget.video.url));
      await _videoController!.initialize();
      await _videoController!.play();
    } catch (e) {
      print('Error initializing network video: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'Error loading video: ${e.toString()}';
      });
    }
  }

  Future<void> _fallbackToNetworkVideo() async {
    try {
      print('Attempting to fall back to network video player');
      _videoController =
          VideoPlayerController.networkUrl(Uri.parse(widget.video.url));
      await _videoController!.initialize();
      await _videoController!.play();
      // If we get here, we've successfully fallen back to network video
      setState(() {
        _hasError = false;
        _errorMessage = '';
      });
    } catch (e) {
      print('Fallback to network video failed: $e');
      // Error already set by initial YouTube failure
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await _restoreSystemUI();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: GestureDetector(
          onTap: _toggleControls,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Video Player Content
              if (!_isInitialized)
                const Center(
                    child: CircularProgressIndicator(color: Colors.white))
              else if (_hasError)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Go Back'),
                      ),
                    ],
                  ),
                )
              else if (widget.video.type == VideoType.youtube &&
                  _youtubeController != null)
                YoutubePlayer(
                  controller: _youtubeController!,
                  showVideoProgressIndicator: true,
                  progressIndicatorColor: Colors.red,
                  progressColors: const ProgressBarColors(
                    playedColor: Colors.red,
                    handleColor: Colors.redAccent,
                  ),
                )
              else if (_videoController != null &&
                  _videoController!.value.isInitialized)
                Center(
                  child: AspectRatio(
                    aspectRatio: _videoController!.value.aspectRatio,
                    child: VideoPlayer(_videoController!),
                  ),
                )
              else
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Unable to load video',
                        style: TextStyle(color: Colors.white),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Go Back'),
                      ),
                    ],
                  ),
                ),

              // Always visible back button
              Positioned(
                top: MediaQuery.of(context).padding.top + 10,
                left: 10,
                child: SafeArea(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back,
                          color: Colors.white, size: 24),
                      onPressed: () async {
                        final navigator = Navigator.of(context);
                        await _restoreSystemUI();
                        if (mounted) {
                          navigator.pop();
                        }
                      },
                    ),
                  ),
                ),
              ),

              // Overlay Controls
              if (_showControls) _buildOverlayControls(),

              // Custom Video Controls for network videos
              if (_showControls &&
                  _videoController != null &&
                  _videoController!.value.isInitialized)
                Positioned(
                  bottom: MediaQuery.of(context).padding.bottom + 20,
                  left: 20,
                  right: 20,
                  child: SafeArea(
                    child:
                        FullScreenVideoControls(controller: _videoController!),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverlayControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Top Controls
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back,
                        color: Colors.white, size: 28),
                    onPressed: () async {
                      final navigator = Navigator.of(context);
                      await _restoreSystemUI();
                      if (mounted) {
                        navigator.pop();
                      }
                    },
                  ),
                  Expanded(
                    child: Text(
                      widget.video.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      widget.video.isFavorite
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: Colors.white,
                      size: 28,
                    ),
                    onPressed: () {
                      ref.read(videoRepositoryProvider).toggleFavorite(
                            widget.video.id,
                            !widget.video.isFavorite,
                          );
                    },
                  ),
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert,
                        color: Colors.white, size: 28),
                    onSelected: (String playlistId) {
                      ref.read(playlistRepositoryProvider).addVideoToPlaylist(
                            playlistId,
                            widget.video.id,
                          );
                    },
                    itemBuilder: (BuildContext context) {
                      final playlistsAsyncValue = ref.watch(playlistsProvider);
                      return playlistsAsyncValue.when(
                        data: (playlists) => playlists
                            .map(
                              (playlist) => PopupMenuItem<String>(
                                value: playlist.id,
                                child: Text('Add to ${playlist.name}'),
                              ),
                            )
                            .toList(),
                        loading: () => [],
                        error: (_, __) => [],
                      );
                    },
                  ),
                ],
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controlsTimer?.cancel();
    _restoreSystemUI();
    _youtubeController?.dispose();
    _videoController?.dispose();
    super.dispose();
  }
}

// Full-screen video controls for network video player
class FullScreenVideoControls extends StatelessWidget {
  final VideoPlayerController controller;

  const FullScreenVideoControls({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 32,
            ),
            onPressed: () {
              if (controller.value.isPlaying) {
                controller.pause();
              } else {
                controller.play();
              }
            },
          ),
          Expanded(
            child: VideoProgressIndicator(
              controller,
              allowScrubbing: true,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              colors: const VideoProgressColors(
                playedColor: Colors.white,
                bufferedColor: Colors.grey,
                backgroundColor: Colors.white24,
              ),
            ),
          ),
          Text(
            _formatDuration(controller.value.position),
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
          const Text(
            ' / ',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
          Text(
            _formatDuration(controller.value.duration),
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }
}
