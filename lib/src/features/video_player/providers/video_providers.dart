import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:mimi_app/src/features/video_player/repository/video_repository.dart';

final videoRepositoryProvider = Provider<VideoRepository>((ref) {
  final firestore = FirebaseFirestore.instance;
  final userId = ref.watch(userNotifierProvider.notifier).currentUserId ?? '';
  return VideoRepository(firestore, userId);
});

final videosProvider =
    FutureProvider.family<List<Video>, String>((ref, category) async {
  final repository = ref.watch(videoRepositoryProvider);
  return repository.getVideosByCategory(category);
});

final favoriteVideosProvider = FutureProvider<List<Video>>((ref) async {
  final repository = ref.watch(videoRepositoryProvider);
  return repository.getFavoriteVideos();
});

final allVideosProvider = FutureProvider<List<Video>>((ref) async {
  final repository = ref.watch(videoRepositoryProvider);
  return repository.getAllVideos();
});
