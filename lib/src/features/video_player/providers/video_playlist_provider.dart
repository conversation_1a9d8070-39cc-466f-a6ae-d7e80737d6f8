import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:mimi_app/src/features/video_player/model/video_playlist.dart';
import 'package:mimi_app/src/features/video_player/repository/video_playlist_repository.dart';

final playlistRepositoryProvider = Provider<PlaylistRepository>((ref) {
  final firestore = FirebaseFirestore.instance;
  final userId = ref.watch(userNotifierProvider.notifier).currentUserId ?? '';
  return PlaylistRepository(firestore, userId);
});

final playlistsProvider = FutureProvider<List<VideoPlaylist>>((ref) async {
  final repository = ref.watch(playlistRepositoryProvider);
  return repository.getUserPlaylists();
});
