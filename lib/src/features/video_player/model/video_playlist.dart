class VideoPlaylist {
  final String id;
  final String name;
  final String userId;
  final List<String> videoIds;

  VideoPlaylist({
    required this.id,
    required this.name,
    required this.userId,
    required this.videoIds,
  });

  factory VideoPlaylist.fromMap(Map<String, dynamic> map) {
    return VideoPlaylist(
      id: map['id'],
      name: map['name'],
      userId: map['userId'],
      videoIds: List<String>.from(map['videoIds']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'userId': userId,
      'videoIds': videoIds,
    };
  }
}


/*!SECTION

import 'package:mimi_app/src/features/video_player/youtube_helper.dart';

class Video {
  final String id;
  final String title;
  final String url;
  final String category;
  final String thumbnailUrl;
  final VideoType type;
  bool isFavorite;

  Video({
    required this.id,
    required this.title,
    required this.url,
    required this.category,
    required this.thumbnailUrl,
    required this.type,
    this.isFavorite = false,
  });

  factory Video.fromMap(Map<String, dynamic> map) {
    final url = map['url'] as String;
    final videoType =
        YouTubeHelper.isYouTubeUrl(url) ? VideoType.youtube : VideoType.network;

    return Video(
      id: map['id'],
      title: map['title'],
      url: url,
      category: map['category'],
      thumbnailUrl: map['thumbnailUrl'],
      type: videoType,
      isFavorite: map['isFavorite'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'category': category,
      'thumbnailUrl': thumbnailUrl,
      'type': type.toString(),
      'isFavorite': isFavorite,
    };
  }
}

enum VideoType { youtube, network }



class VideoPlaylist {
  final String id;
  final String name;
  final String userId;
  final List<String> videoIds;

  VideoPlaylist({
    required this.id,
    required this.name,
    required this.userId,
    required this.videoIds,
  });

  factory VideoPlaylist.fromMap(Map<String, dynamic> map) {
    return VideoPlaylist(
      id: map['id'],
      name: map['name'],
      userId: map['userId'],
      videoIds: List<String>.from(map['videoIds']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'userId': userId,
      'videoIds': videoIds,
    };
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:mimi_app/src/features/video_player/model/video_playlist.dart';
import 'package:mimi_app/src/features/video_player/repository/video_playlist_repository.dart';

final playlistRepositoryProvider = Provider<PlaylistRepository>((ref) {
  final firestore = FirebaseFirestore.instance;
  final userId = ref.watch(userNotifierProvider.notifier).currentUserId ?? '';
  return PlaylistRepository(firestore, userId);
});

final playlistsProvider = FutureProvider<List<VideoPlaylist>>((ref) async {
  final repository = ref.watch(playlistRepositoryProvider);
  return repository.getUserPlaylists();
});


import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:mimi_app/src/features/video_player/repository/video_repository.dart';

final videoRepositoryProvider = Provider<VideoRepository>((ref) {
  final firestore = FirebaseFirestore.instance;
  final userId = ref.watch(userNotifierProvider.notifier).currentUserId ?? '';
  return VideoRepository(firestore, userId);
});

final videosProvider =
    FutureProvider.family<List<Video>, String>((ref, category) async {
  final repository = ref.watch(videoRepositoryProvider);
  return repository.getVideosByCategory(category);
});

final favoriteVideosProvider = FutureProvider<List<Video>>((ref) async {
  final repository = ref.watch(videoRepositoryProvider);
  return repository.getFavoriteVideos();
});


import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mimi_app/src/features/video_player/model/video_playlist.dart';

class PlaylistRepository {
  final FirebaseFirestore _firestore;
  final String _userId;

  PlaylistRepository(this._firestore, this._userId);

  Future<List<VideoPlaylist>> getUserPlaylists() async {
    final snapshot = await _firestore
        .collection('users')
        .doc(_userId)
        .collection('playlists')
        .get();

    return snapshot.docs
        .map((doc) => VideoPlaylist.fromMap(doc.data()))
        .toList();
  }

  Future<void> createPlaylist(String name, List<String> videoIds) async {
    final playlist = VideoPlaylist(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      userId: _userId,
      videoIds: videoIds,
    );

    await _firestore
        .collection('users')
        .doc(_userId)
        .collection('playlists')
        .doc(playlist.id)
        .set(playlist.toMap());
  }

  Future<void> addVideoToPlaylist(String playlistId, String videoId) async {
    await _firestore
        .collection('users')
        .doc(_userId)
        .collection('playlists')
        .doc(playlistId)
        .update({
      'videoIds': FieldValue.arrayUnion([videoId])
    });
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:mimi_app/src/features/video_player/youtube_helper.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class VideoRepository {
  final FirebaseFirestore _firestore;
  final String _userId;

  VideoRepository(this._firestore, this._userId);

  Future<List<Video>> getVideosByCategory(String category) async {
    final snapshot = await _firestore
        .collection('videos')
        .where('category', isEqualTo: category)
        .get();

    return snapshot.docs.map((doc) => Video.fromMap(doc.data())).toList();
  }

  Future<void> toggleFavorite(String videoId, bool isFavorite) async {
    await _firestore
        .collection('users')
        .doc(_userId)
        .collection('favorites')
        .doc(videoId)
        .set({'isFavorite': isFavorite});
  }

  Future<List<Video>> getFavoriteVideos() async {
    final favoritesSnapshot = await _firestore
        .collection('users')
        .doc(_userId)
        .collection('favorites')
        .where('isFavorite', isEqualTo: true)
        .get();

    final videoIds = favoritesSnapshot.docs.map((doc) => doc.id).toList();

    final videosSnapshot = await _firestore
        .collection('videos')
        .where(FieldPath.documentId, whereIn: videoIds)
        .get();

    return videosSnapshot.docs.map((doc) => Video.fromMap(doc.data())).toList();
  }

  Future<void> addVideo({
    required String title,
    required String url,
    required String category,
  }) async {
    final isYouTube = YouTubeHelper.isYouTubeUrl(url);
    String videoId;
    String thumbnailUrl;

    if (isYouTube) {
      final youtubeId = YouTubeHelper.extractVideoId(url);
      if (youtubeId == null) {
        throw Exception('Invalid YouTube URL');
      }
      videoId = youtubeId;
      thumbnailUrl = YouTubeHelper.getThumbnailUrl(youtubeId);
    } else {
      videoId = DateTime.now().millisecondsSinceEpoch.toString();
      thumbnailUrl =
          ''; // You might want to generate or provide a thumbnail for network videos
    }

    final video = Video(
      id: videoId,
      title: title,
      url: url,
      category: category,
      thumbnailUrl: thumbnailUrl,
      type: isYouTube ? VideoType.youtube : VideoType.network,
      isFavorite: false,
    );

    await _firestore.collection('videos').doc(videoId).set(video.toMap());
  }
}


*/