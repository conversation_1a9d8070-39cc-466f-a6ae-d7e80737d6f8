import 'package:mimi_app/src/features/video_player/youtube_helper.dart';

class Video {
  final String id;
  final String title;
  final String url;
  final String categoryId;
  final String thumbnailUrl;
  final VideoType type;
  bool isFavorite;

  Video({
    required this.id,
    required this.title,
    required this.url,
    required this.categoryId,
    required this.thumbnailUrl,
    required this.type,
    this.isFavorite = false,
  });

  factory Video.fromMap(Map<String, dynamic> map) {
    final url = map['url'] as String;
    VideoType videoType = VideoType.network;
    String thumbnailUrl = map['thumbnailUrl'] ?? '';
    String id = map['id'] ?? DateTime.now().millisecondsSinceEpoch.toString();

    if (YouTubeHelper.isYouTubeUrl(url)) {
      videoType = VideoType.youtube;
      final youtubeId = YouTubeHelper.extractVideoId(url);
      if (youtubeId != null) {
        // Only update the ID and thumbnailUrl if we successfully extract the YouTube ID
        id = youtubeId;
        thumbnailUrl = YouTubeHelper.getThumbnailUrl(youtubeId);
      }
    }

    return Video(
      id: id,
      title: map['title'] ?? '',
      url: url,
      categoryId: map['categoryId'] ??
          map['category'] ??
          '', // Support both old and new format
      thumbnailUrl: thumbnailUrl,
      type: videoType,
      isFavorite: map['isFavorite'] ?? false,
    );
  }
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'categoryId': categoryId,
      'thumbnailUrl': thumbnailUrl,
      'type':
          type == VideoType.youtube ? 'VideoType.youtube' : 'VideoType.network',
      'isFavorite': isFavorite,
    };
  }
}

enum VideoType { youtube, network }
