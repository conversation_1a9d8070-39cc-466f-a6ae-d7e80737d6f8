// lib/src/features/user/repositories/user_repository.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user.dart';
import '../../ai_mimi/conversational/conversation_testing_util.dart';

part 'user_repository.g.dart';

@Riverpod(keepAlive: true)
class UserRepository extends _$UserRepository {
  late final FirebaseFirestore _firestore;
  static const String _collection = 'users';

  @override
  FutureOr<void> build() {
    _firestore = FirebaseFirestore.instance;
  }

  Stream<User?> watchUser(String uid) {
    return _firestore.collection(_collection).doc(uid).snapshots().map((doc) {
      if (!doc.exists) {
        return null;
      }
      try {
        final user = User.fromJson(doc.data()!);

        return user;
      } catch (e) {
        return null;
      }
    }).handleError((error) {
      return null;
    });
  }

  Future<User?> getUser(String uid) async {
    try {
      final doc = await _firestore.collection(_collection).doc(uid).get();
      if (!doc.exists) return null;
      return User.fromJson(doc.data()!);
    } catch (e) {
      return null;
    }
  }

  Future<void> createOrUpdateUser(User user) async {
    try {
      // For new users, we want to set initial conversation data
      final isNewUser =
          !(await _firestore.collection(_collection).doc(user.auth.uid).get())
              .exists;

      final baseUserData = user.toFirestore();

      // If it's a new user, add initial conversation data
      final userData = isNewUser
          ? {
              ...baseUserData,
              'total_conversation_minutes': 0.0,
              'current_month_usage': 0.0,
              'purchased_minutes': ConversationTestingUtils
                  .initialFreeMinutes, // Initial free minutes (corrected from 120.0)
              'is_premium': false,
              'last_usage_reset': FieldValue.serverTimestamp(),
              'last_usage_update': FieldValue.serverTimestamp(),
              'lastUpdated': FieldValue.serverTimestamp(),
            }
          : {
              ...baseUserData,
              'lastUpdated': FieldValue.serverTimestamp(),
            };

      await _firestore.collection(_collection).doc(user.auth.uid).set(
            userData,
            SetOptions(merge: true),
          );
    } catch (e) {
      throw Exception('Failed to create/update user: $e');
    }
  }

  // Future<void> createOrUpdateUser(User user) async {
  //   try {
  //     final userData = user.toFirestore(); // Use the new toFirestore method

  //     await _firestore.collection(_collection).doc(user.auth.uid).set(
  //       {
  //         ...userData,
  //         'lastUpdated': FieldValue.serverTimestamp(),
  //       },
  //       SetOptions(merge: true),
  //     );
  //   } catch (e) {
  //     throw Exception('Failed to create/update user: $e');
  //   }
  // }

  Future<void> updateUser(String uid, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(_collection).doc(uid).update({
        ...data,
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  Future<void> deleteUser(String uid) async {
    try {
      await _firestore.collection(_collection).doc(uid).delete();
    } catch (e) {
      throw Exception('Failed to delete user: $e');
    }
  }
}
