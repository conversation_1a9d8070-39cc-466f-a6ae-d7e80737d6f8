// lib/features/user/data/repositories/settings_repository.dart
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'settings_repository.g.dart';

@riverpod
class SettingsRepository extends _$SettingsRepository {
  late final SharedPreferences _prefs;

  static const String _themeKey = 'theme_mode';
  static const String _languageKey = 'language_code';
  static const String _pushNotificationsKey = 'push_notifications';
  static const String _emailNotificationsKey = 'email_notifications';
  static const String _headerImageThemeKey = 'header_image_theme';

  @override
  FutureOr<void> build() async {
    _prefs = await SharedPreferences.getInstance();
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    await _prefs.setString(_themeKey, mode.name);
  }

  Future<void> setLanguage(String languageCode) async {
    await _prefs.setString(_language<PERSON>ey, languageCode);
  }

  Future<void> setPushNotifications(bool enabled) async {
    await _prefs.setBool(_pushNotificationsKey, enabled);
  }

  Future<void> setEmailNotifications(bool enabled) async {
    await _prefs.setBool(_emailNotificationsKey, enabled);
  }

  Future<void> setHeaderImageTheme(String themeId) async {
    await _prefs.setString(_headerImageThemeKey, themeId);
  }

  ThemeMode getThemeMode() {
    final name = _prefs.getString(_themeKey) ?? ThemeMode.system.name;
    return ThemeMode.values.firstWhere((e) => e.name == name);
  }

  String getLanguage() {
    return _prefs.getString(_languageKey) ?? 'en';
  }

  bool getPushNotifications() {
    return _prefs.getBool(_pushNotificationsKey) ?? true;
  }

  bool getEmailNotifications() {
    return _prefs.getBool(_emailNotificationsKey) ?? true;
  }

  String getHeaderImageTheme() {
    return _prefs.getString(_headerImageThemeKey) ?? 'mountain';
  }
}
