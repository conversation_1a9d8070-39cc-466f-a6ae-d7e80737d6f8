// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      auth: AuthUser.fromJson(json['auth'] as Map<String, dynamic>),
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      intention: json['intention'] as String?,
      languageCode: json['languageCode'] as String? ?? 'en',
      themeMode: $enumDecodeNullable(_$ThemeModeEnumMap, json['themeMode']) ??
          ThemeMode.system,
      notificationPreferences:
          (json['notificationPreferences'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, e as bool),
              ) ??
              const {'pushNotifications': true, 'emailNotifications': true},
      socialLinks: (json['socialLinks'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
      lastUpdated: const TimestampConverter().fromJson(json['lastUpdated']),
      totalConversationMinutes:
          (json['total_conversation_minutes'] as num?)?.toDouble() ?? 0.0,
      currentMonthUsage:
          (json['current_month_usage'] as num?)?.toDouble() ?? 0.0,
      purchasedMinutes: (json['purchased_minutes'] as num?)?.toDouble() ?? 0.0,
      isPremium: json['is_premium'] as bool? ?? false,
      lastUsageReset:
          const TimestampConverter().fromJson(json['last_usage_reset']),
      lastUsageUpdate:
          const TimestampConverter().fromJson(json['last_usage_update']),
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'auth': instance.auth,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'intention': instance.intention,
      'languageCode': instance.languageCode,
      'themeMode': _$ThemeModeEnumMap[instance.themeMode]!,
      'notificationPreferences': instance.notificationPreferences,
      'socialLinks': instance.socialLinks,
      'lastUpdated': const TimestampConverter().toJson(instance.lastUpdated),
      'total_conversation_minutes': instance.totalConversationMinutes,
      'current_month_usage': instance.currentMonthUsage,
      'purchased_minutes': instance.purchasedMinutes,
      'is_premium': instance.isPremium,
      'last_usage_reset':
          const TimestampConverter().toJson(instance.lastUsageReset),
      'last_usage_update':
          const TimestampConverter().toJson(instance.lastUsageUpdate),
    };

const _$ThemeModeEnumMap = {
  ThemeMode.system: 'system',
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
};
