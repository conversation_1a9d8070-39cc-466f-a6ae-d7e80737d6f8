// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  AuthUser get auth => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String? get intention => throw _privateConstructorUsedError;
  String get languageCode => throw _privateConstructorUsedError;
  ThemeMode get themeMode => throw _privateConstructorUsedError;
  Map<String, bool> get notificationPreferences =>
      throw _privateConstructorUsedError;
  Map<String, String> get socialLinks => throw _privateConstructorUsedError;
  @TimestampConverter()
  DateTime? get lastUpdated =>
      throw _privateConstructorUsedError; // Add conversation-related fields
  @JsonKey(name: 'total_conversation_minutes')
  double get totalConversationMinutes => throw _privateConstructorUsedError;
  @JsonKey(name: 'current_month_usage')
  double get currentMonthUsage => throw _privateConstructorUsedError;
  @JsonKey(name: 'purchased_minutes')
  double get purchasedMinutes => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_premium')
  bool get isPremium => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_usage_reset')
  @TimestampConverter()
  DateTime? get lastUsageReset => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_usage_update')
  @TimestampConverter()
  DateTime? get lastUsageUpdate => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {AuthUser auth,
      String firstName,
      String lastName,
      String? intention,
      String languageCode,
      ThemeMode themeMode,
      Map<String, bool> notificationPreferences,
      Map<String, String> socialLinks,
      @TimestampConverter() DateTime? lastUpdated,
      @JsonKey(name: 'total_conversation_minutes')
      double totalConversationMinutes,
      @JsonKey(name: 'current_month_usage') double currentMonthUsage,
      @JsonKey(name: 'purchased_minutes') double purchasedMinutes,
      @JsonKey(name: 'is_premium') bool isPremium,
      @JsonKey(name: 'last_usage_reset')
      @TimestampConverter()
      DateTime? lastUsageReset,
      @JsonKey(name: 'last_usage_update')
      @TimestampConverter()
      DateTime? lastUsageUpdate});

  $AuthUserCopyWith<$Res> get auth;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? auth = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? intention = freezed,
    Object? languageCode = null,
    Object? themeMode = null,
    Object? notificationPreferences = null,
    Object? socialLinks = null,
    Object? lastUpdated = freezed,
    Object? totalConversationMinutes = null,
    Object? currentMonthUsage = null,
    Object? purchasedMinutes = null,
    Object? isPremium = null,
    Object? lastUsageReset = freezed,
    Object? lastUsageUpdate = freezed,
  }) {
    return _then(_value.copyWith(
      auth: null == auth
          ? _value.auth
          : auth // ignore: cast_nullable_to_non_nullable
              as AuthUser,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      intention: freezed == intention
          ? _value.intention
          : intention // ignore: cast_nullable_to_non_nullable
              as String?,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as ThemeMode,
      notificationPreferences: null == notificationPreferences
          ? _value.notificationPreferences
          : notificationPreferences // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      socialLinks: null == socialLinks
          ? _value.socialLinks
          : socialLinks // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalConversationMinutes: null == totalConversationMinutes
          ? _value.totalConversationMinutes
          : totalConversationMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      currentMonthUsage: null == currentMonthUsage
          ? _value.currentMonthUsage
          : currentMonthUsage // ignore: cast_nullable_to_non_nullable
              as double,
      purchasedMinutes: null == purchasedMinutes
          ? _value.purchasedMinutes
          : purchasedMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      isPremium: null == isPremium
          ? _value.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUsageReset: freezed == lastUsageReset
          ? _value.lastUsageReset
          : lastUsageReset // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastUsageUpdate: freezed == lastUsageUpdate
          ? _value.lastUsageUpdate
          : lastUsageUpdate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AuthUserCopyWith<$Res> get auth {
    return $AuthUserCopyWith<$Res>(_value.auth, (value) {
      return _then(_value.copyWith(auth: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AuthUser auth,
      String firstName,
      String lastName,
      String? intention,
      String languageCode,
      ThemeMode themeMode,
      Map<String, bool> notificationPreferences,
      Map<String, String> socialLinks,
      @TimestampConverter() DateTime? lastUpdated,
      @JsonKey(name: 'total_conversation_minutes')
      double totalConversationMinutes,
      @JsonKey(name: 'current_month_usage') double currentMonthUsage,
      @JsonKey(name: 'purchased_minutes') double purchasedMinutes,
      @JsonKey(name: 'is_premium') bool isPremium,
      @JsonKey(name: 'last_usage_reset')
      @TimestampConverter()
      DateTime? lastUsageReset,
      @JsonKey(name: 'last_usage_update')
      @TimestampConverter()
      DateTime? lastUsageUpdate});

  @override
  $AuthUserCopyWith<$Res> get auth;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? auth = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? intention = freezed,
    Object? languageCode = null,
    Object? themeMode = null,
    Object? notificationPreferences = null,
    Object? socialLinks = null,
    Object? lastUpdated = freezed,
    Object? totalConversationMinutes = null,
    Object? currentMonthUsage = null,
    Object? purchasedMinutes = null,
    Object? isPremium = null,
    Object? lastUsageReset = freezed,
    Object? lastUsageUpdate = freezed,
  }) {
    return _then(_$UserImpl(
      auth: null == auth
          ? _value.auth
          : auth // ignore: cast_nullable_to_non_nullable
              as AuthUser,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      intention: freezed == intention
          ? _value.intention
          : intention // ignore: cast_nullable_to_non_nullable
              as String?,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as ThemeMode,
      notificationPreferences: null == notificationPreferences
          ? _value._notificationPreferences
          : notificationPreferences // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      socialLinks: null == socialLinks
          ? _value._socialLinks
          : socialLinks // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalConversationMinutes: null == totalConversationMinutes
          ? _value.totalConversationMinutes
          : totalConversationMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      currentMonthUsage: null == currentMonthUsage
          ? _value.currentMonthUsage
          : currentMonthUsage // ignore: cast_nullable_to_non_nullable
              as double,
      purchasedMinutes: null == purchasedMinutes
          ? _value.purchasedMinutes
          : purchasedMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      isPremium: null == isPremium
          ? _value.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUsageReset: freezed == lastUsageReset
          ? _value.lastUsageReset
          : lastUsageReset // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastUsageUpdate: freezed == lastUsageUpdate
          ? _value.lastUsageUpdate
          : lastUsageUpdate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl extends _User {
  const _$UserImpl(
      {required this.auth,
      required this.firstName,
      required this.lastName,
      this.intention,
      this.languageCode = 'en',
      this.themeMode = ThemeMode.system,
      final Map<String, bool> notificationPreferences = const {
        'pushNotifications': true,
        'emailNotifications': true
      },
      final Map<String, String> socialLinks = const {},
      @TimestampConverter() this.lastUpdated,
      @JsonKey(name: 'total_conversation_minutes')
      this.totalConversationMinutes = 0.0,
      @JsonKey(name: 'current_month_usage') this.currentMonthUsage = 0.0,
      @JsonKey(name: 'purchased_minutes') this.purchasedMinutes = 0.0,
      @JsonKey(name: 'is_premium') this.isPremium = false,
      @JsonKey(name: 'last_usage_reset')
      @TimestampConverter()
      this.lastUsageReset,
      @JsonKey(name: 'last_usage_update')
      @TimestampConverter()
      this.lastUsageUpdate})
      : _notificationPreferences = notificationPreferences,
        _socialLinks = socialLinks,
        super._();

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final AuthUser auth;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String? intention;
  @override
  @JsonKey()
  final String languageCode;
  @override
  @JsonKey()
  final ThemeMode themeMode;
  final Map<String, bool> _notificationPreferences;
  @override
  @JsonKey()
  Map<String, bool> get notificationPreferences {
    if (_notificationPreferences is EqualUnmodifiableMapView)
      return _notificationPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_notificationPreferences);
  }

  final Map<String, String> _socialLinks;
  @override
  @JsonKey()
  Map<String, String> get socialLinks {
    if (_socialLinks is EqualUnmodifiableMapView) return _socialLinks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_socialLinks);
  }

  @override
  @TimestampConverter()
  final DateTime? lastUpdated;
// Add conversation-related fields
  @override
  @JsonKey(name: 'total_conversation_minutes')
  final double totalConversationMinutes;
  @override
  @JsonKey(name: 'current_month_usage')
  final double currentMonthUsage;
  @override
  @JsonKey(name: 'purchased_minutes')
  final double purchasedMinutes;
  @override
  @JsonKey(name: 'is_premium')
  final bool isPremium;
  @override
  @JsonKey(name: 'last_usage_reset')
  @TimestampConverter()
  final DateTime? lastUsageReset;
  @override
  @JsonKey(name: 'last_usage_update')
  @TimestampConverter()
  final DateTime? lastUsageUpdate;

  @override
  String toString() {
    return 'User(auth: $auth, firstName: $firstName, lastName: $lastName, intention: $intention, languageCode: $languageCode, themeMode: $themeMode, notificationPreferences: $notificationPreferences, socialLinks: $socialLinks, lastUpdated: $lastUpdated, totalConversationMinutes: $totalConversationMinutes, currentMonthUsage: $currentMonthUsage, purchasedMinutes: $purchasedMinutes, isPremium: $isPremium, lastUsageReset: $lastUsageReset, lastUsageUpdate: $lastUsageUpdate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.auth, auth) || other.auth == auth) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.intention, intention) ||
                other.intention == intention) &&
            (identical(other.languageCode, languageCode) ||
                other.languageCode == languageCode) &&
            (identical(other.themeMode, themeMode) ||
                other.themeMode == themeMode) &&
            const DeepCollectionEquality().equals(
                other._notificationPreferences, _notificationPreferences) &&
            const DeepCollectionEquality()
                .equals(other._socialLinks, _socialLinks) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(
                    other.totalConversationMinutes, totalConversationMinutes) ||
                other.totalConversationMinutes == totalConversationMinutes) &&
            (identical(other.currentMonthUsage, currentMonthUsage) ||
                other.currentMonthUsage == currentMonthUsage) &&
            (identical(other.purchasedMinutes, purchasedMinutes) ||
                other.purchasedMinutes == purchasedMinutes) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.lastUsageReset, lastUsageReset) ||
                other.lastUsageReset == lastUsageReset) &&
            (identical(other.lastUsageUpdate, lastUsageUpdate) ||
                other.lastUsageUpdate == lastUsageUpdate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      auth,
      firstName,
      lastName,
      intention,
      languageCode,
      themeMode,
      const DeepCollectionEquality().hash(_notificationPreferences),
      const DeepCollectionEquality().hash(_socialLinks),
      lastUpdated,
      totalConversationMinutes,
      currentMonthUsage,
      purchasedMinutes,
      isPremium,
      lastUsageReset,
      lastUsageUpdate);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User extends User {
  const factory _User(
      {required final AuthUser auth,
      required final String firstName,
      required final String lastName,
      final String? intention,
      final String languageCode,
      final ThemeMode themeMode,
      final Map<String, bool> notificationPreferences,
      final Map<String, String> socialLinks,
      @TimestampConverter() final DateTime? lastUpdated,
      @JsonKey(name: 'total_conversation_minutes')
      final double totalConversationMinutes,
      @JsonKey(name: 'current_month_usage') final double currentMonthUsage,
      @JsonKey(name: 'purchased_minutes') final double purchasedMinutes,
      @JsonKey(name: 'is_premium') final bool isPremium,
      @JsonKey(name: 'last_usage_reset')
      @TimestampConverter()
      final DateTime? lastUsageReset,
      @JsonKey(name: 'last_usage_update')
      @TimestampConverter()
      final DateTime? lastUsageUpdate}) = _$UserImpl;
  const _User._() : super._();

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  AuthUser get auth;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String? get intention;
  @override
  String get languageCode;
  @override
  ThemeMode get themeMode;
  @override
  Map<String, bool> get notificationPreferences;
  @override
  Map<String, String> get socialLinks;
  @override
  @TimestampConverter()
  DateTime? get lastUpdated; // Add conversation-related fields
  @override
  @JsonKey(name: 'total_conversation_minutes')
  double get totalConversationMinutes;
  @override
  @JsonKey(name: 'current_month_usage')
  double get currentMonthUsage;
  @override
  @JsonKey(name: 'purchased_minutes')
  double get purchasedMinutes;
  @override
  @JsonKey(name: 'is_premium')
  bool get isPremium;
  @override
  @JsonKey(name: 'last_usage_reset')
  @TimestampConverter()
  DateTime? get lastUsageReset;
  @override
  @JsonKey(name: 'last_usage_update')
  @TimestampConverter()
  DateTime? get lastUsageUpdate;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
