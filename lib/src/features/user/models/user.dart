// lib/features/user/data/models/user.dart
// lib/src/features/user/models/user.dart
// lib/src/features/user/models/user.dart
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../auth/model/auth_user.dart';

part 'user.freezed.dart';
part 'user.g.dart';

// lib/src/features/user/models/user.dart

class TimestampConverter implements JsonConverter<DateTime?, dynamic> {
  const TimestampConverter();

  @override
  DateTime? fromJson(dynamic timestamp) {
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    } else if (timestamp is DateTime) {
      return timestamp;
    }
    return null;
  }

  @override
  dynamic toJson(DateTime? date) => date;
}

@freezed
class User with _$User {
  const factory User({
    required AuthUser auth,
    required String firstName,
    required String lastName,
    String? intention,
    @Default('en') String languageCode,
    @Default(ThemeMode.system) ThemeMode themeMode,
    @Default({
      'pushNotifications': true,
      'emailNotifications': true,
    })
    Map<String, bool> notificationPreferences,
    @Default({}) Map<String, String> socialLinks,
    @TimestampConverter() DateTime? lastUpdated,
    // Add conversation-related fields
    @JsonKey(name: 'total_conversation_minutes')
    @Default(0.0)
    double totalConversationMinutes,
    @JsonKey(name: 'current_month_usage')
    @Default(0.0)
    double currentMonthUsage,
    @JsonKey(name: 'purchased_minutes') @Default(0.0) double purchasedMinutes,
    @JsonKey(name: 'is_premium') @Default(false) bool isPremium,
    @JsonKey(name: 'last_usage_reset')
    @TimestampConverter()
    DateTime? lastUsageReset,
    @JsonKey(name: 'last_usage_update')
    @TimestampConverter()
    DateTime? lastUsageUpdate,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  const User._(); // Required for custom methods

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    // Convert auth object to a simple map with required fields
    json['auth'] = {
      'uid': auth.uid,
      'email': auth.email,
      'displayName': auth.displayName,
      'photoUrl': auth.photoUrl,
      'isEmailVerified': auth.isEmailVerified,
      'provider': auth.provider.name,
    };

    // Ensure conversation fields are included
    json['total_conversation_minutes'] = totalConversationMinutes;
    json['current_month_usage'] = currentMonthUsage;
    json['purchased_minutes'] = purchasedMinutes;
    json['is_premium'] = isPremium;
    json['last_usage_reset'] = lastUsageReset;
    json['last_usage_update'] = lastUsageUpdate;

    return json;
  }

  // Helper method to get remaining minutes
  double get remainingMinutes {
    double minutes = purchasedMinutes;
    if (isPremium) {
      const premiumMonthlyMinutes = 60.0;
      minutes += premiumMonthlyMinutes - currentMonthUsage;
    }
    return minutes.clamp(0.0, double.infinity);
  }
}
