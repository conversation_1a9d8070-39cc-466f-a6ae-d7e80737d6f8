// lib/features/user/domain/providers/settings_provider.dart
import 'package:mimi_app/src/features/user/repositories/settings_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter/material.dart';

import 'user_provider.dart';

part 'settings_provider.g.dart';

@riverpod
class SettingsNotifier extends _$SettingsNotifier {
  @override
  FutureOr<void> build() async {
    // Initialize settings from repository
    await ref.read(settingsRepositoryProvider.future);
  }

  Future<void> toggleTheme() async {
    final settings = ref.read(settingsRepositoryProvider.notifier);
    final currentMode = settings.getThemeMode();
    final newMode =
        currentMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;

    await settings.setThemeMode(newMode);
    ref.invalidateSelf();
  }

  // Future<void> updateLanguage(String languageCode) async {
  //   final settings = ref.read(settingsRepositoryProvider.notifier);
  //   await settings.setLanguage(languageCode);

  //   // Also update in user profile if logged in
  //   await ref.read(userNotifierProvider.notifier).updateLanguage(languageCode);

  //   ref.invalidateSelf();
  // }

  Future<void> togglePushNotifications(bool enabled) async {
    final settings = ref.read(settingsRepositoryProvider.notifier);
    await settings.setPushNotifications(enabled);

    // Also update in user profile if logged in
    await ref.read(userNotifierProvider.notifier).updateNotificationPreferences(
          key: 'pushNotifications',
          value: enabled,
        );

    ref.invalidateSelf();
  }

  Future<void> toggleEmailNotifications(bool enabled) async {
    final settings = ref.read(settingsRepositoryProvider.notifier);
    await settings.setEmailNotifications(enabled);

    // Also update in user profile if logged in
    await ref.read(userNotifierProvider.notifier).updateNotificationPreferences(
          key: 'emailNotifications',
          value: enabled,
        );

    ref.invalidateSelf();
  }

  ThemeMode getCurrentThemeMode() {
    return ref.read(settingsRepositoryProvider.notifier).getThemeMode();
  }

  String getCurrentLanguage() {
    return ref.read(settingsRepositoryProvider.notifier).getLanguage();
  }

  bool getPushNotificationsEnabled() {
    return ref.read(settingsRepositoryProvider.notifier).getPushNotifications();
  }

  bool getEmailNotificationsEnabled() {
    return ref
        .read(settingsRepositoryProvider.notifier)
        .getEmailNotifications();
  }
}
