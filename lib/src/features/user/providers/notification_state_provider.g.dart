// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_state_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationStateHash() => r'682f9540024b6afe93801885d402e18c252ba313';

/// See also [NotificationState].
@ProviderFor(NotificationState)
final notificationStateProvider =
    AutoDisposeNotifierProvider<NotificationState, bool>.internal(
  NotificationState.new,
  name: r'notificationStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NotificationState = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
