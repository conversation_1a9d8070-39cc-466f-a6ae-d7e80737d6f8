import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mimi_app/src/features/journal/providers/providers.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'intention_provider.g.dart';

@riverpod
class IntentionController extends _$IntentionController {
  FirebaseFirestore get _firestore => FirebaseFirestore.instance;
  SharedPreferences? _prefs;

  @override
  Future<String?> build() async {
    _prefs = await SharedPreferences.getInstance();
    // Get current user
    final user = ref.watch(userNotifierProvider);
    // First try to get from Firebase
    final intention = user.value?.intention;
    if (intention != null) {
      // Sync to local storage
      await _prefs?.setString('user_intention', intention);
      return intention;
    }
    // Fallback to local storage
    return _prefs?.getString('user_intention');
  }

  Future<void> setIntention(String intention) async {
    state = const AsyncLoading();
    try {
      // Update Firebase
      final user = ref.read(userNotifierProvider).value;
      if (user != null) {
        await _firestore
            .collection('users')
            .doc(user.auth.uid)
            .update({'intention': intention});
      }
      // Update local storage
      _prefs ??= await SharedPreferences.getInstance();
      await _prefs?.setString('user_intention', intention);
      state = AsyncData(intention);
    } catch (e, st) {
      state = AsyncError(e, st);
    }
  }

  Future<void> clearIntention() async {
    state = const AsyncLoading();
    try {
      // First delete all routines
      await ref.read(journalNotifierProvider.notifier).deleteAllRoutines();

      // Update Firebase
      final user = ref.read(userNotifierProvider).value;
      if (user != null) {
        await _firestore
            .collection('users')
            .doc(user.auth.uid)
            .update({'intention': null});
      }
      // Clear from local storage
      _prefs ??= await SharedPreferences.getInstance();
      await _prefs?.remove('user_intention');
      state = const AsyncData(null);
    } catch (e, st) {
      state = AsyncError(e, st);
    }
  }
}
