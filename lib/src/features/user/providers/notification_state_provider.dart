import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mimi_app/src/core/services/notification_service.dart';

part 'notification_state_provider.g.dart';

@riverpod
class NotificationState extends _$NotificationState {
  final _notificationService = NotificationService();

  @override
  bool build() {
    // Initialize with current permission status
    _checkInitialPermission();
    return false;
  }

  Future<void> _checkInitialPermission() async {
    final isGranted = await _notificationService.checkPermissionStatus();
    state = isGranted;
  }

  Future<bool> toggleNotifications(bool value) async {
    if (!value) {
      state = false;
      return true;
    }

    // First check if permissions are already granted
    final isGranted = await _notificationService.checkPermissionStatus();
    if (isGranted) {
      state = true;
      return true;
    }

    // If not granted, try to request permissions
    final granted = await _notificationService.requestPermissions();

    // Check the status again after requesting permissions
    final finalStatus = await _notificationService.checkPermissionStatus();
    state = finalStatus;

    if (finalStatus) {
      return true;
    }

    // If still not granted, check if it's permanently denied
    final status = await Permission.notification.status;
    if (status.isPermanentlyDenied) {
      state = false;
      return false;
    }

    state = false;
    return false;
  }
}
