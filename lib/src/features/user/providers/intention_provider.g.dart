// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'intention_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$intentionControllerHash() =>
    r'7cf2fcc0d3136876b6b6ead2108a43af427e0c7b';

/// See also [IntentionController].
@ProviderFor(IntentionController)
final intentionControllerProvider =
    AutoDisposeAsyncNotifierProvider<IntentionController, String?>.internal(
  IntentionController.new,
  name: r'intentionControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$intentionControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IntentionController = AutoDisposeAsyncNotifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
