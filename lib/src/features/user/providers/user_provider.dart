import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../auth/providers/auth_state_provider.dart';
import '../../auth/providers/auth_controller.dart';
import '../models/user.dart';
import '../repositories/user_repository.dart';

part 'user_provider.g.dart';

@Riverpod(keepAlive: true)
class UserNotifier extends _$UserNotifier {
  @override
  Stream<User?> build() {
    return ref.watch(authStateNotifierProvider).when(
          initial: () => Stream.value(null),
          loading: () => Stream.value(null),
          authenticated: (authUser) {
            return ref
                .watch(userRepositoryProvider.notifier)
                .watchUser(authUser.uid);
          },
          unauthenticated: () => Stream.value(null),
          error: (_) => Stream.value(null),
        );
  }

  String? get currentUserId => state.valueOrNull?.auth.uid;

  Future<void> updateProfile({
    String? firstName,
    String? lastName,
  }) async {
    final user = state.valueOrNull;
    if (user == null) throw Exception('No authenticated user found');

    final updates = <String, dynamic>{
      if (firstName != null) 'firstName': firstName,
      if (lastName != null) 'lastName': lastName,
    };

    await ref
        .read(userRepositoryProvider.notifier)
        .updateUser(user.auth.uid, updates);
  }

  Future<void> deleteAccount() async {
    final user = state.valueOrNull;
    if (user == null) throw Exception('No authenticated user found');

    try {
      // First delete the user profile from Firestore
      await ref.read(userRepositoryProvider.notifier).deleteUser(user.auth.uid);

      // Then delete the Firebase Auth account and sign out
      await ref.read(authControllerProvider.notifier).signOut();
    } catch (e) {
      throw Exception('Failed to delete account: $e');
    }
  }

  Future<void> updateEmail({
    required String newEmail,
    required String password,
  }) async {
    final user = state.valueOrNull;
    if (user == null) throw Exception('No authenticated user found');

    final authController = ref.read(authControllerProvider.notifier);

    try {
      // First reauthenticate
      await authController.reauthenticateWithPassword(password);

      // Then update email in Firebase Auth
      await authController.updateEmail(newEmail);

      // Finally update in Firestore
      await ref.read(userRepositoryProvider.notifier).updateUser(
        user.auth.uid,
        {'auth.email': newEmail},
      );
    } catch (e) {
      throw Exception('Failed to update email: $e');
    }
  }

  Future<void> updateNotificationPreferences({
    required String key,
    required bool value,
  }) async {
    final user = state.valueOrNull;
    if (user == null) throw Exception('No authenticated user found');

    final updatedPreferences =
        Map<String, bool>.from(user.notificationPreferences)..[key] = value;

    await ref.read(userRepositoryProvider.notifier).updateUser(
      user.auth.uid,
      {'notificationPreferences': updatedPreferences},
    );
  }

  Future<void> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final user = state.valueOrNull;
    if (user == null) throw Exception('No authenticated user found');

    try {
      // First reauthenticate user
      await ref
          .read(authControllerProvider.notifier)
          .reauthenticateWithPassword(
            currentPassword,
          );

      // Then update password in Firebase Auth
      await ref.read(authControllerProvider.notifier).updatePassword(
            newPassword,
          );
    } catch (e) {
      throw Exception('Failed to update password: $e');
    }
  }
}
