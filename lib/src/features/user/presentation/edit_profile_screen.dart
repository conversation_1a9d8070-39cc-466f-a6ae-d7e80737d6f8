// lib/features/user/presentation/screens/edit_profile_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/core/widgets/loading_overlay.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final user = ref.read(userNotifierProvider).value;
    _firstNameController = TextEditingController(text: user?.firstName);
    _lastNameController = TextEditingController(text: user?.lastName);
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isLoading,
      loadingText: AppStrings.saving,
      child: Scaffold(
        appBar: AppBar(
          title: Text(AppStrings.editProfile),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _handleSave,
              child: Text(AppStrings.save),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: ListView(
            padding: AppSizing.screenEdgeInsets,
            children: [
              TextFormField(
                controller: _firstNameController,
                decoration: InputDecoration(
                  labelText: AppStrings.firstName,
                  prefixIcon: const Icon(Icons.person_outline),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppStrings.firstNameRequired;
                  }
                  return null;
                },
              ),
              SizedBox(height: AppSizing.spaceL),
              TextFormField(
                controller: _lastNameController,
                decoration: InputDecoration(
                  labelText: AppStrings.lastName,
                  prefixIcon: const Icon(Icons.person_outline),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppStrings.lastNameRequired;
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      await ref.read(userNotifierProvider.notifier).updateProfile(
            firstName: _firstNameController.text,
            lastName: _lastNameController.text,
          );

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: AppStrings.profileUpdated,
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: AppStrings.errorOccurred,
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
