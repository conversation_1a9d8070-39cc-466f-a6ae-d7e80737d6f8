// lib/features/user/presentation/screens/profile_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/features/admin/admin_provider.dart';

import 'package:mimi_app/src/features/user/presentation/widgets/logout_section.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/profile_header.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/profile_menu_section.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/social_section.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/support_section.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/subscription_banner.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      //showMiniPlayer: false,
      appBar: AppBar(
        title: Text(AppStrings.profile),
        automaticallyImplyLeading: false,
      ),
      body: ListView(
        padding: AppSizing.screenEdgeInsets,
        children: const [
          ProfileHeader(),
          SizedBox(height: AppSizing.spaceM),
          SubscriptionBanner(),
          SizedBox(height: AppSizing.spaceL),
          AccountSection(),
          // SizedBox(height: AppSizing.spaceM),
          PreferencesSection(),
          // SizedBox(height: AppSizing.spaceM),
          SupportSection(),
          // SizedBox(height: AppSizing.spaceM),
          SocialSection(),
          // SizedBox(height: AppSizing.spaceL),
          AdminSection(),
          // SizedBox(height: AppSizing.spaceL),
          LogoutSection(),
          SizedBox(
              height:
                  20), // Bottom spacing to avoid overlap with navigation bar
        ],
      ),
    );
  }
}

class AdminSection extends ConsumerWidget {
  const AdminSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAdminAsync = ref.watch(isAdminProvider);

    return isAdminAsync.when(
      data: (isAdmin) {
        if (!isAdmin) return const SizedBox.shrink();

        return Card(
          child: ListTile(
            leading: const Icon(Icons.admin_panel_settings),
            title: const Text('Admin Panel'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.push('/profile/admin'),
          ),
        );
      },
      error: (_, __) => const SizedBox.shrink(),
      loading: () => const SizedBox.shrink(),
    );
  }
}
