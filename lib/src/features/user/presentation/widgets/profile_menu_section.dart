// lib/features/user/presentation/widgets/profile_menu_section.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/features/user/providers/notification_state_provider.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';

import 'package:mimi_app/src/features/user/presentation/widgets/menu_tile.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/switch_menu_tile.dart';
import 'package:mimi_app/src/features/purchases/utils/purchase_utils.dart';

class AccountSection extends StatelessWidget {
  const AccountSection({super.key});

  void _showMinutesSettings(BuildContext context) {
    PurchaseUtils.showMinutesSettingsBottomSheet(context);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Padding(
        //   padding: EdgeInsets.all(AppSizing.spaceM),
        //   child: Text(
        //     AppStrings.accountSettings,
        //     style: Theme.of(context).textTheme.titleMedium,
        //   ),
        // ),
        MenuTile(
          icon: Icons.person_outline,
          title: AppStrings.editProfile,
          onTap: () => context.pushNamed(RouteNames.profileEdit),
        ),
        MenuTile(
          icon: Icons.email_outlined,
          title: AppStrings.updateEmail,
          onTap: () => context.pushNamed(RouteNames.profileEmail),
        ),
        MenuTile(
          icon: Icons.lock_outline,
          title: AppStrings.changePassword,
          onTap: () => context.pushNamed(RouteNames.profilePassword),
        ),
        MenuTile(
          icon: Icons.access_time,
          title: 'Conversation Minutes',
          onTap: () => _showMinutesSettings(context),
        ),
      ],
    );
  }
}

class PreferencesSection extends ConsumerWidget {
  const PreferencesSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsEnabled = ref.watch(notificationStateProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Padding(
        //   padding: EdgeInsets.all(AppSizing.spaceM),
        //   child: Text(
        //     AppStrings.preferences,
        //     style: Theme.of(context).textTheme.titleMedium,
        //   ),
        // ),
        SwitchMenuTile(
          icon: Icons.notifications_outlined,
          title: AppStrings.notifications,
          value: notificationsEnabled,
          onChanged: (value) async {
            final success = await ref
                .read(notificationStateProvider.notifier)
                .toggleNotifications(value);
            if (!success && value) {
              // Show toast if permission was denied
              if (context.mounted) {
                showWarningToast(
                  context,
                  title: 'Notifications',
                  description:
                      'Please enable notifications in your device settings.',
                );
              }
            }
          },
        ),
      ],
    );
  }
}
