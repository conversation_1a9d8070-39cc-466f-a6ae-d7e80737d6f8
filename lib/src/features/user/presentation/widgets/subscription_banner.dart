// lib/src/features/user/presentation/widgets/subscription_banner.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/features/purchases/providers/subscription_status_provider.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';

class SubscriptionBanner extends ConsumerWidget {
  const SubscriptionBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    try {
      final subscriptionStatus = ref.watch(subscriptionStatusNotifierProvider);

      return subscriptionStatus.when(
        data: (isPremium) => _buildBanner(context, isPremium),
        loading: () => _buildLoadingBanner(context),
        error: (error, stackTrace) {
          // For errors, show the upgrade banner as fallback
          return _buildBanner(context, false);
        },
      );
    } catch (e) {
      // Ultimate fallback - always show upgrade banner
      return _buildBanner(context, false);
    }
  }

  Widget _buildBanner(BuildContext context, bool isPremium) {
    final theme = Theme.of(context);

    return Consumer(
        builder: (context, ref, child) => Container(
              margin: const EdgeInsets.symmetric(horizontal: AppSizing.spaceM),
              child: Card(
                elevation: 2,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: isPremium
                        ? LinearGradient(
                            colors: [
                              Colors.amber.withValues(alpha: 0.1),
                              Colors.orange.withValues(alpha: 0.1),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : LinearGradient(
                            colors: [
                              theme.colorScheme.primary.withValues(alpha: 0.1),
                              theme.colorScheme.secondary
                                  .withValues(alpha: 0.1),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppSizing.spaceM),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(AppSizing.spaceS),
                              decoration: BoxDecoration(
                                color: isPremium
                                    ? Colors.amber.withValues(alpha: 0.2)
                                    : theme.colorScheme.primary
                                        .withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                isPremium ? Icons.star : Icons.star_outline,
                                color: isPremium
                                    ? Colors.amber[700]
                                    : theme.colorScheme.primary,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: AppSizing.spaceM),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    isPremium
                                        ? 'Premium Active'
                                        : 'Upgrade to Premium',
                                    style:
                                        theme.textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: isPremium
                                          ? Colors.amber[700]
                                          : theme.colorScheme.primary,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    isPremium
                                        ? 'Enjoy unlimited access to all features'
                                        : 'Unlock meditation, breathwork, and more',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurface
                                          .withValues(alpha: 0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (isPremium)
                              TextButton(
                                onPressed: () =>
                                    _showSubscriptionManagement(context),
                                style: TextButton.styleFrom(
                                  foregroundColor: Colors.amber[700],
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppSizing.spaceM,
                                    vertical: AppSizing.spaceS,
                                  ),
                                ),
                                child: const Text('Manage'),
                              ),
                          ],
                        ),
                        if (!isPremium) ...[
                          const SizedBox(height: AppSizing.spaceM),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () =>
                                  _showRevenueCatPaywall(context, ref),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(
                                  vertical: AppSizing.spaceM,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text('Subscribe'),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

  Widget _buildLoadingBanner(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSizing.spaceM),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(AppSizing.spaceM),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSizing.spaceS),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
              const SizedBox(width: AppSizing.spaceM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loading subscription status...',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Please wait',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSubscriptionManagement(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizing.spaceL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: AppSizing.spaceL),
            Container(
              padding: const EdgeInsets.all(AppSizing.spaceM),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.star,
                size: 48,
                color: Colors.amber,
              ),
            ),
            const SizedBox(height: AppSizing.spaceM),
            Text(
              'Premium Subscription',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: AppSizing.spaceS),
            Text(
              'You have access to all premium features including unlimited meditation tracks, advanced breathwork patterns, and personalised insights.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withValues(alpha: 0.7),
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizing.spaceL),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(vertical: AppSizing.spaceM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Close'),
              ),
            ),
            const SizedBox(height: AppSizing.spaceS),
          ],
        ),
      ),
    );
  }

  Future<void> _showRevenueCatPaywall(
      BuildContext context, WidgetRef ref) async {
    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);

      // Show RevenueCat's built-in paywall
      final success = await revenueCatService.showPaywallIfNeeded(context);

      // After paywall is dismissed, refresh subscription status
      ref.read(subscriptionStatusNotifierProvider.notifier).refresh();

      if (context.mounted && success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Subscription activated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error showing paywall: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
