// lib/src/features/user/presentation/widgets/logout_section.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/config/app_config.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/utils/snackbar_utils.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/core/widgets/loading_overlay.dart';
import 'package:mimi_app/src/features/auth/providers/auth_controller.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/features/auth/providers/auth_state_provider.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/confirmation_dialog.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';

class LogoutSection extends ConsumerStatefulWidget {
  const LogoutSection({super.key});

  @override
  ConsumerState<LogoutSection> createState() => _LogoutSectionState();
}

class _LogoutSectionState extends ConsumerState<LogoutSection> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes
    ref.listen<AsyncValue<void>>(
      authControllerProvider,
      (_, state) {
        state.whenOrNull(
          error: (error, _) {
            if (mounted) {
              showFailureToast(context,
                  title: 'Error', description: error.toString());
              setState(() => _isLoading = false);
            }
          },
        );
      },
    );

    // Listen to auth state for navigation
    ref.listen(authStateNotifierProvider, (_, state) {
      state.whenOrNull(
        unauthenticated: () {
          if (mounted) {
            context.goNamed(RouteNames.login);
          }
        },
      );
    });

    return LoadingOverlay(
      isLoading: _isLoading,
      loadingText: AppStrings.processingRequest,
      child: Column(
        children: [
          if (AppConfig.enableDeleteAccount)
            ListTile(
              leading:
                  const Icon(Icons.delete_forever_outlined, color: Colors.red),
              title: Text(
                AppStrings.deleteAccount,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              onTap: _isLoading ? null : () => _handleDeleteAccount(context),
            ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: Text(AppStrings.logout),
            onTap: _isLoading ? null : () => _handleLogout(context),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: AppStrings.logout,
        message: AppStrings.logoutConfirmation,
        confirmText: AppStrings.logout,
        cancelText: AppStrings.cancel,
      ),
    );

    if (confirmed == true && mounted) {
      setState(() => _isLoading = true);
      try {
        await ref.read(authControllerProvider.notifier).signOut();
        // Navigation will be handled by the auth state listener
      } catch (e) {
        if (mounted) {
          setState(() => _isLoading = false);
          showFailureToast(context,
              title: 'Error', description: 'Failed to logout: $e');
        }
      }
    }
  }

  Future<void> _handleDeleteAccount(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: AppStrings.deleteAccount,
        message: AppStrings.deleteAccountConfirmation,
        confirmText: AppStrings.delete,
        cancelText: AppStrings.cancel,
        isDestructive: true,
      ),
    );

    if (confirmed == true && mounted) {
      setState(() => _isLoading = true);
      try {
        await ref.read(userNotifierProvider.notifier).deleteAccount();
        if (mounted) {
          showSuccessToast(context,
              title: 'Success', description: AppStrings.accountDeleted);
          context.goNamed(RouteNames.login);
        }
      } catch (e) {
        if (mounted) {
          showFailureToast(context, title: 'Error', description: e.toString());
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }
}
