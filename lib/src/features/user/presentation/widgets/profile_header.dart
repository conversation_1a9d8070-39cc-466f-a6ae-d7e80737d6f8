// lib/features/user/presentation/widgets/profile_header.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';

import 'package:mimi_app/src/features/user/models/user.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:mimi_app/src/features/purchases/providers/subscription_status_provider.dart';

class ProfileHeader extends ConsumerWidget {
  const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userStream = ref.watch(userNotifierProvider);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: userStream.when(
        data: (user) =>
            user != null ? _buildHeader(context, user) : const SizedBox(),
        loading: () => const Padding(
          padding: EdgeInsets.all(20),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        error: (error, stack) => Center(
          child: Text(
            'Error: ${error.toString()}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, User user) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSizing.spaceL),
        child: Column(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundImage: user.auth.photoUrl != null
                  ? NetworkImage(user.auth.photoUrl!)
                  : null,
              child: user.auth.photoUrl == null
                  ? Text(
                      '${user.firstName[0]}${user.lastName[0]}',
                      style: theme.textTheme.headlineMedium,
                    )
                  : null,
            ),
            SizedBox(height: AppSizing.spaceM),
            Text(
              '${user.firstName} ${user.lastName}',
              style: theme.textTheme.titleLarge,
            ),
            Text(
              user.auth.email,
              style: theme.textTheme.bodyMedium,
            ),
            SizedBox(height: AppSizing.spaceS),
            // _PremiumStatusWidget(),
          ],
        ),
      ),
    );
  }
}

// class _PremiumStatusWidget extends ConsumerWidget {
//   const _PremiumStatusWidget();

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final subscriptionStatusAsync =
//         ref.watch(subscriptionStatusNotifierProvider);

//     return subscriptionStatusAsync.when(
//       data: (isPremium) => Container(
//         padding: const EdgeInsets.symmetric(
//           horizontal: 12,
//           vertical: 6,
//         ),
//         decoration: BoxDecoration(
//           color: isPremium
//               ? Colors.green.withValues(alpha: 0.1)
//               : Colors.grey.withValues(alpha: 0.1),
//           borderRadius: BorderRadius.circular(20),
//           border: Border.all(
//             color: isPremium ? Colors.green : Colors.grey,
//             width: 1,
//           ),
//         ),
//         child: Row(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Icon(
//               isPremium ? Icons.star : Icons.star_border,
//               size: 16,
//               color: isPremium ? Colors.green : Colors.grey,
//             ),
//             // const SizedBox(width: 4),
//             // Text(
//             //   isPremium ? 'Premium User' : 'Free User',
//             //   style: Theme.of(context).textTheme.bodySmall?.copyWith(
//             //         color: isPremium ? Colors.green : Colors.grey,
//             //         fontWeight: FontWeight.w600,
//             //       ),
//             // ),
//           ],
//         ),
//       ),
//       loading: () => const SizedBox(
//         height: 20,
//         width: 20,
//         child: CircularProgressIndicator(strokeWidth: 2),
//       ),
//       error: (error, _) => Container(
//         padding: const EdgeInsets.symmetric(
//           horizontal: 12,
//           vertical: 6,
//         ),
//         decoration: BoxDecoration(
//           color: Colors.grey.withValues(alpha: 0.1),
//           borderRadius: BorderRadius.circular(20),
//           border: Border.all(
//             color: Colors.grey,
//             width: 1,
//           ),
//         ),
//         child: Row(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             const Icon(
//               Icons.error_outline,
//               size: 16,
//               color: Colors.grey,
//             ),
//             const SizedBox(width: 4),
//             Text(
//               'Status Unknown',
//               style: Theme.of(context).textTheme.bodySmall?.copyWith(
//                     color: Colors.grey,
//                     fontWeight: FontWeight.w600,
//                   ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
