// lib/features/user/presentation/widgets/social_section.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/menu_tile.dart';

import '../../../../core/utils/share_utils.dart';

// lib/features/user/presentation/widgets/social_section.dart

import '../../../../core/config/url_config.dart';
import '../../../../core/utils/url_launcher_utils.dart';

import '../../../../core/utils/snackbar_utils.dart';

class SocialSection extends StatelessWidget {
  const SocialSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Padding(
        //   padding: EdgeInsets.all(AppSizing.spaceM),
        //   child: Text(
        //     AppStrings.socialMedia,
        //     style: Theme.of(context).textTheme.titleMedium,
        //   ),
        // ),
        // _buildSocialTile(
        //   context: context,
        //   icon: Icons.facebook,
        //   title: AppStrings.facebook,
        //   platform: 'facebook',
        // ),
        // _buildSocialTile(
        //   context: context,
        //   icon: Icons.play_circle_outline,
        //   title: AppStrings.youtube,
        //   platform: 'youtube',
        // ),
        // _buildSocialTile(
        //   context: context,
        //   icon: Icons.facebook,
        //   title: AppStrings.twitter,
        //   platform: 'twitter',
        // ),
        _buildSocialTile(
          context: context,
          icon: Icons.photo_camera_outlined,
          title: AppStrings.instagram,
          platform: 'instagram',
        ),
        MenuTile(
          icon: Icons.share_outlined,
          title: AppStrings.shareApp,
          onTap: () => _handleShare(context),
        ),
        // MenuTile(
        //   icon: Icons.star_outline,
        //   title: AppStrings.rateApp,
        //   onTap: () => _handleRate(context),
        // ),
      ],
    );
  }

  Widget _buildSocialTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String platform,
  }) {
    return MenuTile(
      icon: icon,
      title: title,
      onTap: () => _handleSocialMediaTap(context, platform),
    );
  }

  Future<void> _handleSocialMediaTap(
    BuildContext context,
    String platform,
  ) async {
    final url = UrlConfig.socialUrls[platform];
    if (url != null) {
      await UrlLauncherUtils.launchURL(context, url);
    } else {
      showFailureToast(context,
          title: 'Error', description: AppStrings.socialMediaError);
    }
  }

  Future<void> _handleShare(BuildContext context) async {
    try {
      await ShareUtils.shareApp(context);
    } catch (e) {
      showFailureToast(context,
          title: 'Error', description: AppStrings.shareError);
    }
  }

  Future<void> _handleRate(BuildContext context) async {
    try {
      final url = Theme.of(context).platform == TargetPlatform.iOS
          ? UrlConfig.storeUrls['ios']
          : UrlConfig.storeUrls['android'];

      if (url != null) {
        await UrlLauncherUtils.launchURL(context, url);
      } else {
        showFailureToast(context,
            title: 'Error', description: AppStrings.urlError);
      }
    } catch (e) {
      showFailureToast(context,
          title: 'Error', description: AppStrings.urlError);
    }
  }
}
