// lib/features/user/presentation/widgets/support_section.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/menu_tile.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/config/url_config.dart';
import '../../../../core/utils/url_launcher_utils.dart';

class SupportSection extends StatelessWidget {
  const SupportSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Padding(
        //   padding: EdgeInsets.all(AppSizing.spaceM),
        //   child: Text(
        //     AppStrings.support,
        //     style: Theme.of(context).textTheme.titleMedium,
        //   ),
        // ),
        MenuTile(
          icon: Icons.help_outline,
          title: AppStrings.customerSupport,
          onTap: () => _handleSupport(context),
        ),
        MenuTile(
          icon: Icons.privacy_tip_outlined,
          title: AppStrings.privacyPolicy,
          onTap: () => _handlePrivacyPolicy(context),
        ),
        MenuTile(
          icon: Icons.description_outlined,
          title: AppStrings.termsOfService,
          onTap: () => _handleTerms(context),
        ),
        MenuTile(
          icon: Icons.gavel_outlined,
          title: AppStrings.eula,
          onTap: () => _handleEula(context),
        ),
        // MenuTile(
        //   icon: Icons.info_outline,
        //   title: AppStrings.about,
        //   onTap: () => context.pushNamed(RouteNames.about),
        // ),
      ],
    );
  }

  Future<void> _handleSupport(BuildContext context) async {
    try {
      await UrlLauncherUtils.launchEmail(
        context,
        email: UrlConfig.supportEmail,
        subject: AppStrings.supportEmailSubject,
        body: AppStrings.supportEmailBody,
      );
    } catch (e) {
      showFailureToast(context,
          title: 'Error', description: AppStrings.emailError);
    }
  }

  Future<void> _handlePrivacyPolicy(BuildContext context) async {
    await UrlLauncherUtils.launchURL(
      context,
      UrlConfig.privacyPolicyUrl,
      mode: LaunchMode.inAppWebView,
    );
  }

  Future<void> _handleTerms(BuildContext context) async {
    await UrlLauncherUtils.launchURL(
      context,
      UrlConfig.termsUrl,
      mode: LaunchMode.inAppWebView,
    );
  }

  Future<void> _handleEula(BuildContext context) async {
    await UrlLauncherUtils.launchURL(
      context,
      UrlConfig.eulaUrl,
      mode: LaunchMode.inAppWebView,
    );
  }
}
