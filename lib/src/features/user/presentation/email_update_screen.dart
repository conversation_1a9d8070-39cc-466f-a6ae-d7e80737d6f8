// lib/features/user/presentation/screens/change_email_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/snackbar_utils.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/core/utils/validation_utils.dart';
import 'package:mimi_app/src/core/widgets/loading_overlay.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';

class ChangeEmailScreen extends ConsumerStatefulWidget {
  const ChangeEmailScreen({super.key});

  @override
  ConsumerState<ChangeEmailScreen> createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends ConsumerState<ChangeEmailScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _newEmailController;
  late final TextEditingController _confirmEmailController;
  late final TextEditingController _passwordController;
  bool _isLoading = false;
  bool _showPassword = false;

  @override
  void initState() {
    super.initState();
    _newEmailController = TextEditingController();
    _confirmEmailController = TextEditingController();
    _passwordController = TextEditingController();
  }

  @override
  void dispose() {
    _newEmailController.dispose();
    _confirmEmailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return LoadingOverlay(
      isLoading: _isLoading,
      loadingText: AppStrings.processingRequest,
      child: Scaffold(
        appBar: AppBar(
          title: Text(AppStrings.changeEmail),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _handleSubmit,
              child: Text(AppStrings.save),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: ListView(
            padding: AppSizing.screenEdgeInsets,
            children: [
              Text(
                AppStrings.changeEmailDescription,
                style: theme.textTheme.bodyMedium,
              ),
              SizedBox(height: AppSizing.spaceL),
              TextFormField(
                controller: _newEmailController,
                decoration: InputDecoration(
                  labelText: AppStrings.newEmail,
                  prefixIcon: const Icon(Icons.email_outlined),
                ),
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                validator: ValidationUtils.validateEmail,
                enabled: !_isLoading,
              ),
              SizedBox(height: AppSizing.spaceM),
              TextFormField(
                controller: _confirmEmailController,
                decoration: InputDecoration(
                  labelText: AppStrings.confirmEmail,
                  prefixIcon: const Icon(Icons.email_outlined),
                ),
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                validator: (value) => ValidationUtils.validateConfirmEmail(
                  value,
                  _newEmailController.text,
                ),
                enabled: !_isLoading,
              ),
              SizedBox(height: AppSizing.spaceM),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: AppStrings.currentPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showPassword ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() => _showPassword = !_showPassword);
                    },
                  ),
                ),
                obscureText: !_showPassword,
                validator: ValidationUtils.validatePassword,
                enabled: !_isLoading,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      await ref.read(userNotifierProvider.notifier).updateEmail(
            newEmail: _newEmailController.text,
            password: _passwordController.text,
          );

      if (mounted) {
        showSuccessToast(context,
            title: 'Success', description: AppStrings.emailChangeSuccess);
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(context, title: 'Error', description: e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
