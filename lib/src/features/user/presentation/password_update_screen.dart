// lib/features/user/presentation/screens/change_password_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/core/utils/validation_utils.dart';
import 'package:mimi_app/src/core/widgets/loading_overlay.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';

class ChangePasswordScreen extends ConsumerStatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  ConsumerState<ChangePasswordScreen> createState() =>
      _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends ConsumerState<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _currentPasswordController;
  late final TextEditingController _newPasswordController;
  late final TextEditingController _confirmPasswordController;
  bool _isLoading = false;
  bool _showCurrentPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  @override
  void initState() {
    super.initState();
    _currentPasswordController = TextEditingController();
    _newPasswordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isLoading,
      loadingText: AppStrings.processingRequest,
      child: Scaffold(
        appBar: AppBar(
          title: Text(AppStrings.changePassword),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _handleSubmit,
              child: Text(AppStrings.save),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: ListView(
            padding: AppSizing.screenEdgeInsets,
            children: [
              TextFormField(
                controller: _currentPasswordController,
                decoration: InputDecoration(
                  labelText: AppStrings.currentPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showCurrentPassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(
                          () => _showCurrentPassword = !_showCurrentPassword);
                    },
                  ),
                ),
                obscureText: !_showCurrentPassword,
                validator: ValidationUtils.validatePassword,
                enabled: !_isLoading,
              ),
              SizedBox(height: AppSizing.spaceL),
              TextFormField(
                controller: _newPasswordController,
                decoration: InputDecoration(
                  labelText: AppStrings.newPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showNewPassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() => _showNewPassword = !_showNewPassword);
                    },
                  ),
                ),
                obscureText: !_showNewPassword,
                validator: ValidationUtils.validateNewPassword,
                enabled: !_isLoading,
              ),
              SizedBox(height: AppSizing.spaceM),
              TextFormField(
                controller: _confirmPasswordController,
                decoration: InputDecoration(
                  labelText: AppStrings.confirmPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showConfirmPassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(
                          () => _showConfirmPassword = !_showConfirmPassword);
                    },
                  ),
                ),
                obscureText: !_showConfirmPassword,
                validator: (value) => ValidationUtils.validateConfirmPassword(
                  value,
                  _newPasswordController.text,
                ),
                enabled: !_isLoading,
              ),
              SizedBox(height: AppSizing.spaceL),
              Text(
                AppStrings.passwordRequirements,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      await ref.read(userNotifierProvider.notifier).updatePassword(
            currentPassword: _currentPasswordController.text,
            newPassword: _newPasswordController.text,
          );

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: AppStrings.passwordChangeSuccess,
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: e.toString(),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
