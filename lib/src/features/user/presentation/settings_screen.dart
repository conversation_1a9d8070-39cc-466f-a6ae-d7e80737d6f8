// // lib/features/user/presentation/screens/settings_screen.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
// //import 'package:mimi_app/src/core/theme/providers/theme_provider.dart';
// import 'package:mimi_app/src/core/theme/constants/app_strings.dart';

// //import 'package:mimi_app/src/features/user/providers/settings_provider.dart';

// class SettingsScreen extends ConsumerWidget {
//   const SettingsScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Scaffold(
//       appBar: AppBar(title: Text(AppStrings.settings)),
//       body: ListView(
//         children: [
//           _buildSection(
//             context,
//             title: AppStrings.notifications,
//             children: [
//               // SwitchMenuTile(
//               //   icon: Icons.notifications_outlined,
//               //   title: AppStrings.pushNotifications,
//               //   value: pushNotifications,
//               //   onChanged: (value) =>
//               //       settingsNotifier.togglePushNotifications(value),
//               // ),
//               // SwitchMenuTile(
//               //   icon: Icons.email_outlined,
//               //   title: AppStrings.emailNotifications,
//               //   value: emailNotifications,
//               //   onChanged: (value) =>
//               //       settingsNotifier.toggleEmailNotifications(value),
//               // ),
//             ],
//           ),
//           // if (AppConfig.enableThemeSwitching)
//           //   _buildSection(
//           //     context,
//           //     title: AppStrings.appearance,
//           //     children: [
//           //       SwitchMenuTile(
//           //         icon: Icons.dark_mode_outlined,
//           //         title: AppStrings.darkMode,
//           //         value: isDarkMode ? true : false,
//           //         onChanged: (_) => settingsNotifier.toggleTheme(),
//           //       ),
//           //     ],
//           //   ),
//         ],
//       ),
//     );
//   }

//   Widget _buildSection(
//     BuildContext context, {
//     required String title,
//     required List<Widget> children,
//   }) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: EdgeInsets.all(AppSizing.spaceM),
//           child: Text(
//             title,
//             style: Theme.of(context).textTheme.titleMedium,
//           ),
//         ),
//         Card(
//           margin: EdgeInsets.symmetric(horizontal: AppSizing.spaceM),
//           child: Column(children: children),
//         ),
//         SizedBox(height: AppSizing.spaceL),
//       ],
//     );
//   }
// }
