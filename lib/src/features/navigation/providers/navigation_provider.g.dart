// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$navigationItemsHash() => r'9516032a2bb93f010c940075990ebf5cdb2c4a56';

/// See also [navigationItems].
@ProviderFor(navigationItems)
final navigationItemsProvider = AutoDisposeProvider<List<NavItem>>.internal(
  navigationItems,
  name: r'navigationItemsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$navigationItemsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NavigationItemsRef = AutoDisposeProviderRef<List<NavItem>>;
String _$selectedNavIndexHash() => r'511f12562df9618d762fb65792477ac82c6c8e48';

/// See also [SelectedNavIndex].
@ProviderFor(SelectedNavIndex)
final selectedNavIndexProvider =
    AutoDisposeNotifierProvider<SelectedNavIndex, int>.internal(
  SelectedNavIndex.new,
  name: r'selectedNavIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedNavIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedNavIndex = AutoDisposeNotifier<int>;
String _$currentRouteHash() => r'ecd4b29e51dfca874a260ac30f438c1ccaa4bcdd';

/// See also [CurrentRoute].
@ProviderFor(CurrentRoute)
final currentRouteProvider =
    AutoDisposeNotifierProvider<CurrentRoute, String>.internal(
  CurrentRoute.new,
  name: r'currentRouteProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentRouteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentRoute = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
