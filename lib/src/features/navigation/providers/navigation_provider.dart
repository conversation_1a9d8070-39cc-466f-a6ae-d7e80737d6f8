// lib/features/navigation/presentation/providers/navigation_provider.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/features/navigation/models/nav_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'navigation_provider.g.dart';

@riverpod
class SelectedNavIndex extends _$SelectedNavIndex {
  @override
  int build() {
    // Get the current route and return the corresponding index
    return _getIndexFromPath(RouteNames.home); // Default to home
  }

  void setIndex(int index) => state = index;

  // Helper method to get index from path
  int _getIndexFromPath(String path) {
    final items = ref.read(navigationItemsProvider);
    final index = items.indexWhere((item) => item.path == path);
    return index != -1 ? index : 0;
  }
}

@riverpod
class CurrentRoute extends _$CurrentRoute {
  @override
  String build() => '/home'; // default route

  void setRoute(String route) => state = route;
}

@riverpod
List<NavItem> navigationItems(Ref ref) {
  return [
    NavItem(
      label: AppStrings.home,
      path: RouteNames.home,
      iconPath: 'assets/icons/home_outline.svg',
      selectedIconPath: 'assets/icons/home_fill.svg',
    ),
    NavItem(
      label: 'Discover',
      path: '/discover',
      iconPath: 'assets/icons/compass_outline.svg',
      selectedIconPath: 'assets/icons/compass_fill.svg',
    ),
    NavItem(
      label: 'Insights',
      path: '/insights',
      iconPath: 'assets/icons/chart_outline.svg',
      selectedIconPath: 'assets/icons/chart_fill.svg',
    ),
    NavItem(
      label: AppStrings.profile,
      path: RouteNames.profile,
      iconPath: 'assets/icons/user_outline.svg',
      selectedIconPath: 'assets/icons/user_fill.svg',
    ),
  ];
}
