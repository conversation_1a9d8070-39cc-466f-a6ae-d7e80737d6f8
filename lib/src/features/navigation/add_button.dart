import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';

class AddButton extends StatefulWidget {
  const AddButton({super.key});

  @override
  State<AddButton> createState() => _AddButtonState();
}

class _AddButtonState extends State<AddButton>
    with SingleTickerProviderStateMixin {
  bool _expanded = false;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleMenu() {
    setState(() {
      _expanded = !_expanded;
      if (_expanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  void _closeMenu() {
    setState(() {
      _expanded = false;
      _controller.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final gradient = LinearGradient(
      colors: [
        colorScheme.primary,
        colorScheme.secondary,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
    final menuWidth = 260.0;
    final menuHeight = 320.0;
    final fabSize = 65.0;

    return Stack(
      alignment: Alignment.bottomCenter,
      clipBehavior: Clip.none,
      fit: StackFit.passthrough,
      children: [
        // Full-screen overlay
        AnimatedPositioned(
          duration: const Duration(milliseconds: 200),
          top: _expanded ? -MediaQuery.of(context).size.height : 0,
          left: _expanded ? -MediaQuery.of(context).size.width : 0,
          right: _expanded ? -MediaQuery.of(context).size.width : 0,
          bottom: _expanded ? -MediaQuery.of(context).size.height : 0,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: _expanded ? 1 : 0,
            child: IgnorePointer(
              ignoring: !_expanded,
              child: GestureDetector(
                onTap: _closeMenu,
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.black.withValues(alpha: 0.3),
                  width: MediaQuery.of(context).size.width * 3,
                  height: MediaQuery.of(context).size.height * 3,
                ),
              ),
            ),
          ),
        ),
        // Animated menu
        AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          bottom: 45,
          // bottom: _expanded ? fabSize / 2 - 8 : fabSize / 2 - 8 - 20,
          left: (MediaQuery.of(context).size.width - menuWidth) / 2,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: _expanded ? 1 : 0,
            child: IgnorePointer(
              ignoring: !_expanded,
              child: CustomPaint(
                painter: _MenuWithNotchPainter(
                  colorGradient: gradient,
                  shadow: BoxShadow(
                    color: Colors.black.withValues(alpha: 0.18),
                    blurRadius: 18,
                    offset: const Offset(0, 8),
                  ),
                ),
                child: SizedBox(
                  width: menuWidth,
                  height: menuHeight,
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: 28,
                      left: 12,
                      right: 12,
                      bottom: fabSize / 2 + 8,
                    ),
                    child: _AddMenu(
                      onClose: _closeMenu,
                      gradient: gradient,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        // FAB
        Positioned(
          bottom: 32,
          child: IgnorePointer(
            ignoring: MediaQuery.of(context).viewInsets.bottom > 0,
            child: SizedBox(
              width: fabSize,
              height: fabSize,
              child: Material(
                color: Colors.transparent,
                shape: const CircleBorder(),
                child: InkWell(
                  onTap: _toggleMenu,
                  customBorder: const CircleBorder(),
                  splashColor: Colors.white.withValues(alpha: 0.24),
                  highlightColor: Colors.white.withValues(alpha: 0.10),
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: gradient,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.26),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Center(
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          _expanded ? Icons.close : Icons.add,
                          key: ValueKey(_expanded),
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _MenuWithNotchPainter extends CustomPainter {
  final Gradient colorGradient;
  final BoxShadow shadow;
  _MenuWithNotchPainter({required this.colorGradient, required this.shadow});

  @override
  void paint(Canvas canvas, Size size) {
    final r = 28.0; // Border radius
    final notchWidth = 65.0; // match FAB size
    final notchDepth = 18.0; // shallow notch

    final path = Path();
    // Top left
    path.moveTo(0, r);
    path.quadraticBezierTo(0, 0, r, 0);
    path.lineTo(size.width - r, 0);
    path.quadraticBezierTo(size.width, 0, size.width, r);
    path.lineTo(size.width, size.height - notchDepth - r);
    path.quadraticBezierTo(size.width, size.height - notchDepth, size.width - r,
        size.height - notchDepth);
    // Right to notch start
    path.lineTo((size.width + notchWidth) / 2, size.height - notchDepth);
    // Notch (convex, U shape)
    path.quadraticBezierTo(
      size.width / 2, size.height + notchDepth, // control point below the bar
      (size.width - notchWidth) / 2, size.height - notchDepth,
    );
    // Left to bottom left
    path.lineTo(r, size.height - notchDepth);
    path.quadraticBezierTo(
        0, size.height - notchDepth, 0, size.height - notchDepth - r);
    path.close();

    // Draw shadow
    if (shadow.color.a > 0) {
      final shadowPaint = Paint()
        ..color = shadow.color
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadow.blurRadius)
        ..style = PaintingStyle.fill;
      canvas.save();
      canvas.translate(shadow.offset.dx, shadow.offset.dy);
      canvas.drawPath(path, shadowPaint);
      canvas.restore();
    }

    // Draw gradient
    final paint = Paint()
      ..shader = colorGradient
          .createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _AddMenu extends StatelessWidget {
  final VoidCallback onClose;
  final Gradient gradient;
  const _AddMenu({required this.onClose, required this.gradient});

  @override
  Widget build(BuildContext context) {
    final menuItems = [
      _AddMenuItem(
        label: 'Talk to Mimi',
        iconPath: 'assets/icons/mic_fill.svg',
        onTap: () {
          onClose();
          context.pushNamed(RouteNames.conversation);
        },
      ),
      _AddMenuItem(
        label: 'Chat with Mimi',
        iconPath: 'assets/icons/comment_fill.svg',
        onTap: () {
          onClose();
          context.goNamed(RouteNames.chat);
        },
      ),
      _AddMenuItem(
        label: 'Focus',
        iconPath: 'assets/icons/clock_fill.svg',
        onTap: () {
          onClose();
          context.pushNamed(RouteNames.timer);
        },
      ),
      _AddMenuItem(
        label: 'Breathwork',
        iconPath: 'assets/icons/play_fill.svg',
        onTap: () {
          onClose();
          context.pushNamed(RouteNames.breathwork);
        },
      ),
      _AddMenuItem(
        label: 'Quotes',
        iconPath: 'assets/icons/quote_fill.svg',
        onTap: () {
          onClose();
          context.pushNamed(RouteNames.quote);
        },
      ),
    ];

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...menuItems,
      ],
    );
  }
}

class _AddMenuItem extends StatelessWidget {
  final String label;
  final String iconPath;
  final VoidCallback onTap;
  const _AddMenuItem(
      {required this.label, required this.iconPath, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 6),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
              ),
              SvgPicture.asset(
                iconPath,
                width: 28,
                height: 28,
                colorFilter:
                    const ColorFilter.mode(Colors.white, BlendMode.srcIn),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
