// lib/features/navigation/presentation/widgets/bottom_nav_bar.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/features/navigation/models/nav_item.dart';
import 'package:mimi_app/src/features/navigation/providers/navigation_provider.dart';

// lib/features/navigation/presentation/widgets/bottom_nav_bar.dart

class BottomNavBar extends ConsumerWidget {
  const BottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(selectedNavIndexProvider);
    final navItems = ref.watch(navigationItemsProvider);

    return MediaQuery.of(context).viewInsets.bottom > 0
        ? const SizedBox.shrink()
        : Container(
            // Add a container to ensure proper hit testing
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(0),
                topRight: Radius.circular(0),
              ),
              child: BottomAppBar(
                height: 65,
                shape: const CircularNotchedRectangle(),
                notchMargin: 16,
                clipBehavior: Clip.none,
                color: Theme.of(context).scaffoldBackgroundColor,
                elevation:
                    0, // Remove elevation since we handle it with container
                padding: EdgeInsets.zero,
                child: Container(
                  // Additional container to ensure hit testing works properly
                  color: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Row(
                      children: [
                        _buildNavBarItem(
                            context, ref, navItems[0], 0, selectedIndex),
                        _buildNavBarItem(
                            context, ref, navItems[1], 1, selectedIndex),
                        const SizedBox(width: 60), // Space for FAB
                        _buildNavBarItem(
                            context, ref, navItems[2], 2, selectedIndex),
                        _buildNavBarItem(
                            context, ref, navItems[3], 3, selectedIndex),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
  }

  Widget _buildNavBarItem(BuildContext context, WidgetRef ref, NavItem item,
      int index, int selectedIndex) {
    final isSelected = index == selectedIndex;
    final colorScheme = Theme.of(context).colorScheme;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            ref.read(selectedNavIndexProvider.notifier).setIndex(index);
            context.go(item.path);
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2),
            child: SafeArea(
              top: false,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Badge(
                    isLabelVisible: item.badgeCount != null,
                    label: Text('${item.badgeCount}'),
                    child: SvgPicture.asset(
                      isSelected ? item.selectedIconPath : item.iconPath,
                      width: 27,
                      height: 27,
                      colorFilter: ColorFilter.mode(
                        isSelected
                            ? colorScheme.primary
                            : colorScheme.onSurfaceVariant,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.label,
                    style: TextStyle(
                      color: isSelected
                          ? colorScheme.primary
                          : colorScheme.onSurfaceVariant,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
