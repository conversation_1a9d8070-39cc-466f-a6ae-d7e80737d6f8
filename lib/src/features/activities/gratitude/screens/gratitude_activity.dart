import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
import 'package:mimi_app/src/features/activities/shared/mixins/routine_config_mixin.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/providers/activity_data_provider.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';

class GratitudeActivity extends BaseActivity {
  const GratitudeActivity({
    super.key,
    required super.activity,
    super.onComplete,
    super.standalone = false,
    required super.routineId,
  });

  @override
  ConsumerState<GratitudeActivity> createState() => _GratitudeActivityState();
}

class _GratitudeActivityState extends BaseActivityState<GratitudeActivity>
    with RoutineConfigMixin<GratitudeActivity> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  late final ActivityDataNotifier _dataNotifier;
  bool _hasAutoFocused = false;

  GratitudeConfig get _config => currentConfig.maybeWhen(
        gratitude: (prompts, numberOfEntries) => GratitudeConfig(
          prompts: prompts,
          numberOfEntries: numberOfEntries,
        ),
        orElse: () => throw Exception('Invalid config type'),
      );

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      _config.numberOfEntries,
      (index) => TextEditingController(),
    );
    _focusNodes = List.generate(
      _config.numberOfEntries,
      (index) => FocusNode(),
    );
    _dataNotifier = ref.read(
      activityDataNotifierProvider(widget.activity.id, DateTime.now()).notifier,
    );
    _loadExistingEntries();
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  Future<void> _loadExistingEntries() async {
    final data = await _dataNotifier.build(widget.activity.id, DateTime.now());
    if (data != null) {
      final entries = data.data['entries'] as List<dynamic>? ?? [];
      for (var i = 0; i < _controllers.length && i < entries.length; i++) {
        _controllers[i].text = entries[i] as String? ?? '';
      }
    }

    // Schedule auto-focus after all initialization is complete
    _scheduleAutoFocus();
  }

  void _scheduleAutoFocus() {
    if (_hasAutoFocused) return;

    // Use a longer delay to ensure all initialization (including config loading) is complete
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted && _focusNodes.isNotEmpty && !_hasAutoFocused) {
        _hasAutoFocused = true;
        // Find the first empty field or focus the first field if all are empty
        int focusIndex = 0;
        for (int i = 0; i < _controllers.length; i++) {
          if (_controllers[i].text.isEmpty) {
            focusIndex = i;
            break;
          }
        }
        _focusNodes[focusIndex].requestFocus();
      }
    });
  }

  Future<void> _saveEntries() async {
    final entries = _controllers.map((controller) => controller.text).toList();

    await _dataNotifier.saveActivityData({
      'entries': entries,
      'timestamp': DateTime.now().toIso8601String(),
    });
    markComplete();
  }

  // Override onNextStep to save data when moving to next step in checkin flow
  @override
  void onNextStep() {
    if (_canSaveEntries()) {
      // Save entries asynchronously but don't wait for completion
      // to avoid blocking the UI transition
      _saveEntries().catchError((error) {
        // Handle any save errors silently to avoid disrupting the flow
        debugPrint('Error saving gratitude entries: $error');
      });
    }
    super.onNextStep();
  }

  @override
  IconData getActivityIcon() => Icons.favorite;

  @override
  Widget buildActivityContent(BuildContext context) {
    return currentConfig.maybeWhen(
      gratitude: (prompts, numberOfEntries) => SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.only(
            bottom: 20,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with close and info buttons

              // Title
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12),
                child: Text("What are you grateful\nfor today?",
                    style: Theme.of(context).textTheme.headlineMedium),
              ),

              // Gratitude entries
              Container(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height * 0.4,
                ),
                padding: EdgeInsets.only(top: 20, bottom: 40),
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.black.withAlpha(40),
                        Colors.transparent,
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24))),
                child: Column(
                  children: [
                    for (int i = 0; i < numberOfEntries; i++)
                      Padding(
                        padding: const EdgeInsets.only(left: 24, bottom: 20),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              "${i + 1}.",
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(right: 18.0),
                                child: CustomTextField(
                                  controller: _controllers[i],
                                  focusNode: _focusNodes[i],
                                  hintText: "I'm grateful for...",
                                  style: CustomTextFieldStyle.withBottomBorder,
                                  autofocus: false,
                                  enableInteractiveSelection: true,
                                  onChanged: (_) {
                                    // Remove setState to prevent unnecessary rebuilds
                                    // that could interfere with focus
                                  },
                                  onTap: () {
                                    // Ensure focus is maintained when tapping
                                    if (!_focusNodes[i].hasFocus) {
                                      _focusNodes[i].requestFocus();
                                    }
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      orElse: () => throw Exception('Invalid config type'),
    );
  }

  bool _canSaveEntries() {
    return _controllers.any((controller) => controller.text.trim().isNotEmpty);
  }

  @override
  int getTotalSteps() => 1; // Single step activity

  @override
  int getCurrentStep() => 0;

  @override
  bool moveToStep(int step) => step == 0;
}
