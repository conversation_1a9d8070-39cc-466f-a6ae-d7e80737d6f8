import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_settings_screen.dart';

class GratitudeSettingsScreen extends BaseSettingsScreen {
  const GratitudeSettingsScreen({
    super.key,
    required super.activity,
    required super.routineId,
    super.navArgs,
    super.onConfigChanged,
  });

  @override
  ConsumerState<GratitudeSettingsScreen> createState() =>
      _GratitudeSettingsScreenState();
}

class _GratitudeSettingsScreenState
    extends BaseSettingsScreenState<GratitudeSettingsScreen> {
  late int _numberOfEntries;
  late List<String> _prompts;

  @override
  void initState() {
    super.initState();
    _initializeFromConfig();
  }

  void _initializeFromConfig() {
    final gratitudeConfig = config.maybeWhen(
      gratitude: (prompts, numberOfEntries) => GratitudeConfig(
        prompts: prompts,
        numberOfEntries: numberOfEntries,
      ),
      orElse: () => const GratitudeConfig(),
    );

    _numberOfEntries = gratitudeConfig.numberOfEntries;
    _prompts = List.from(gratitudeConfig.prompts);
  }

  void _updateConfig() {
    final newConfig = ActivityConfig.gratitude(
      prompts: _prompts,
      numberOfEntries: _numberOfEntries,
    );
    updateConfig(newConfig);
  }

  @override
  Widget buildConfigSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Gratitude Settings',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 24),

          // Number of entries setting
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Number of Gratitude Entries',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'How many things would you like to be grateful for?',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      for (int i = 1; i <= 3; i++)
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ChoiceChip(
                            label: Text('$i'),
                            selected: _numberOfEntries == i,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _numberOfEntries = i;
                                });
                                _updateConfig();
                              }
                            },
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Info card
          Card(
            color: Theme.of(context).colorScheme.primaryContainer,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Gratitude practice helps improve mental well-being and positive thinking. Take a moment to reflect on the good things in your life.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onPrimaryContainer,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
