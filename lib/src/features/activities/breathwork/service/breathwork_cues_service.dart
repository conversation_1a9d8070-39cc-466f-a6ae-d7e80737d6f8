import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';

class BreathworkCuesService {
  final AudioPlayer _cuePlayer = AudioPlayer();
  bool _isMuted = false;

  bool get isMuted => _isMuted;

  Future<void> playPhase(String phase) async {
    if (_isMuted) return;

    try {
      await _cuePlayer.stop();
      final audioFile = 'assets/audio/breathwork/$phase.mp3';
      await _cuePlayer.play(AssetSource(audioFile));
    } catch (e) {
      debugPrint('Error playing breathwork cue: $e');
    }
  }

  void setMuted(bool muted) {
    _isMuted = muted;
    if (muted) {
      _cuePlayer.stop();
    }
  }

  void dispose() {
    _cuePlayer.dispose();
  }
}
