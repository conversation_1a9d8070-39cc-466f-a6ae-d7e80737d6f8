import 'dart:async';
import 'dart:math' as math;
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import '../models/breathe_pattern.dart';
import '../providers/breathwork_provider.dart';

class BreathingVisualization extends ConsumerStatefulWidget {
  final BreathePattern pattern;
  final int cycles;
  final VoidCallback onComplete;
  final bool showTitle;

  const BreathingVisualization({
    super.key,
    required this.pattern,
    required this.cycles,
    required this.onComplete,
    this.showTitle = true,
  });

  @override
  ConsumerState<BreathingVisualization> createState() =>
      _BreathingVisualizationState();
}

class _BreathingVisualizationState extends ConsumerState<BreathingVisualization>
    with TickerProviderStateMixin {
  late AnimationController _cycleController;
  late AnimationController _visualController;
  Timer? _timer;
  int _currentCycle = 0;
  String _phase = 'ready';
  bool _isPaused = false;
  double _circleSize = 1.0;
  double _progress = 0.0;
  late final Duration _cycleDuration;
  AudioPlayerController? _backgroundMusicController;
  AudioPlayer? _cuePlayer;

  @override
  void initState() {
    super.initState();
    _setupAnimation();

    _cuePlayer = AudioPlayer();
  }

  void _setupAnimation() {
    final totalDuration = widget.pattern.inhaleSeconds +
        widget.pattern.holdSeconds +
        widget.pattern.exhaleSeconds +
        widget.pattern.holdAfterExhaleSeconds;

    _cycleDuration = Duration(milliseconds: totalDuration * 1000);

    _cycleController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: totalDuration * 1000 * widget.cycles),
    );

    _visualController = AnimationController(
      vsync: this,
      duration: _cycleDuration,
    );

    _cycleController.addListener(() {
      setState(() {
        _currentCycle = (_cycleController.value * widget.cycles).ceil();
        _currentCycle = _currentCycle.clamp(1, widget.cycles);
      });
    });

    _visualController.addListener(() {
      setState(() {
        _progress = _visualController.value;
      });
    });
  }

  @override
  void didUpdateWidget(BreathingVisualization oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Clean up and reinitialize background music if pattern changes
    if (oldWidget.pattern != widget.pattern) {
      _cleanupBackgroundMusic();
      _setupBackgroundMusic();
    }
  }

  Future<void> _cleanupBackgroundMusic() async {
    if (_backgroundMusicController != null) {
      await _backgroundMusicController?.stopAndClear();
      _backgroundMusicController = null;
    }
  }

  Future<void> _setupBackgroundMusic() async {
    // First cleanup any existing music
    await _cleanupBackgroundMusic();

    final state = ref.read(breathworkStateProvider);
    if (state.backgroundMusicId != null) {
      try {
        final tracks =
            await ref.read(categoryTracksProvider('breathwork').future);
        final selectedTrack = tracks.firstWhere(
          (t) => t.id == state.backgroundMusicId,
          orElse: () => throw Exception('Track not found'),
        );

        _backgroundMusicController =
            ref.read(audioPlayerControllerProvider.notifier);
        await _backgroundMusicController?.playTracks([selectedTrack]);
        await _backgroundMusicController?.setVolume(state.musicVolume);
      } catch (e) {
        debugPrint('Error setting up background music: $e');
      }
    }
  }

  void _startBreathwork() async {
    await _setupBackgroundMusic();
    setState(() {
      _phase = 'breathe in';
      _currentCycle = 1;
      _isPaused = false;
    });

    _cycleController.forward();
    _visualController.repeat();
    _startTimer();
  }

  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
      if (_isPaused) {
        _cycleController.stop();
        _visualController.stop();
        _timer?.cancel();
        _backgroundMusicController?.pause();
      } else {
        _cycleController.forward();
        _visualController.repeat();
        _startTimer();
        _backgroundMusicController?.play();
      }
    });
  }

  void _startTimer() {
    _timer?.cancel();
    final cycleMs = _cycleDuration.inMilliseconds;

    final totalDuration = widget.pattern.inhaleSeconds +
        widget.pattern.holdSeconds +
        widget.pattern.exhaleSeconds +
        widget.pattern.holdAfterExhaleSeconds;

    final inhaleEnd = (widget.pattern.inhaleSeconds / totalDuration) * cycleMs;
    final holdEnd =
        inhaleEnd + (widget.pattern.holdSeconds / totalDuration) * cycleMs;
    final exhaleEnd =
        holdEnd + (widget.pattern.exhaleSeconds / totalDuration) * cycleMs;

    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isPaused) {
        setState(() {
          final cycleTime =
              (_visualController.value * cycleMs).toInt() % cycleMs;

          // Play audio cues at phase transitions
          _playAudioCues(cycleTime, inhaleEnd, holdEnd, exhaleEnd);

          // Update visual state
          _updatePhaseAndSize(cycleTime, inhaleEnd, holdEnd, exhaleEnd);

          // Check for completion
          if (_currentCycle == widget.cycles && cycleTime >= cycleMs - 100) {
            _completeBreathwork();
          }
        });
      }
    });
  }

  void _playAudioCues(
    int cycleTime,
    double inhaleEnd,
    double holdEnd,
    double exhaleEnd,
  ) {
    if (cycleTime < 100) {
      _playCue('breathein');
    } else if (cycleTime >= inhaleEnd &&
        cycleTime < inhaleEnd + 100 &&
        widget.pattern.holdSeconds > 0) {
      _playCue('hold');
    } else if (cycleTime >= holdEnd && cycleTime < holdEnd + 100) {
      _playCue('breatheout');
    } else if (cycleTime >= exhaleEnd &&
        cycleTime < exhaleEnd + 100 &&
        widget.pattern.holdAfterExhaleSeconds > 0) {
      _playCue('hold');
    }
  }

  void _playCue(String cue) async {
    final state = ref.read(breathworkStateProvider);
    if (state.cuesMuted) return;

    try {
      await _cuePlayer?.stop();
      await _cuePlayer?.play(AssetSource('assets/audio/breathwork/$cue.mp3'));
    } catch (e) {
      debugPrint('Error playing cue $cue: $e');
    }
  }

  void _updatePhaseAndSize(
    int cycleTime,
    double inhaleEnd,
    double holdEnd,
    double exhaleEnd,
  ) {
    final totalDuration = widget.pattern.inhaleSeconds +
        widget.pattern.holdSeconds +
        widget.pattern.exhaleSeconds +
        widget.pattern.holdAfterExhaleSeconds;

    if (cycleTime < inhaleEnd) {
      _phase = 'breathe in';
      _circleSize = _mapToRange(
        cycleTime.toDouble(),
        0,
        inhaleEnd,
        1.0,
        1.2,
      );
    } else if (widget.pattern.holdSeconds > 0 && cycleTime < holdEnd) {
      _phase = 'hold';
      _circleSize = 1.2;
    } else if (cycleTime < exhaleEnd) {
      _phase = 'breathe out';
      final exhaleProgress = cycleTime - holdEnd;
      final exhaleDuration = (widget.pattern.exhaleSeconds / totalDuration) *
          _cycleDuration.inMilliseconds;
      _circleSize = _mapToRange(
        exhaleProgress,
        0,
        exhaleDuration,
        1.2,
        1.0,
      );
    } else if (widget.pattern.holdAfterExhaleSeconds > 0) {
      _phase = 'hold';
      _circleSize = 1.0;
    } else {
      _phase = 'breathe in';
      _circleSize = 1.0;
    }
  }

  void _completeBreathwork() {
    _timer?.cancel();
    _cycleController.stop();
    _visualController.stop();
    _phase = 'complete';
    _backgroundMusicController?.stopAndClear();
    widget.onComplete();
  }

  double _mapToRange(
    double value,
    double inMin,
    double inMax,
    double outMin,
    double outMax,
  ) {
    return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(breathworkStateProvider);
    final backgroundsAsync = ref.watch(breathworkBackgroundsProvider);

    return backgroundsAsync.when(
      data: (backgrounds) {
        final selectedBackground = backgrounds.firstWhere(
          (bg) => bg.id == state.backgroundImageId,
          orElse: () => backgrounds.first,
        );

        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 60),
                  if (widget.showTitle) ...[
                    Text(
                      widget.pattern.name,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: AppTypography.headlineSmall,
                        fontWeight: AppTypography.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.pattern.description,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: AppTypography.bodySmall,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                  ],
                  Text(
                    'Cycle $_currentCycle of ${widget.cycles}',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: AppTypography.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Flexible(
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Circle container with background image
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 50),
                          width: 280 * _circleSize,
                          height: 280 * _circleSize,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                              image: selectedBackground.assetPath
                                      .startsWith('http')
                                  ? NetworkImage(selectedBackground.assetPath)
                                      as ImageProvider
                                  : AssetImage(selectedBackground.assetPath),
                              fit: BoxFit.cover,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 10,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                        ),
                        // Progress indicator
                        CustomPaint(
                          size: const Size(280, 280),
                          painter: BreathworkProgressPainter(
                            progress: _progress,
                            pattern: widget.pattern,
                            totalSeconds: _cycleDuration.inSeconds,
                          ),
                        ),
                        // Phase text
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            _phase,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: AppTypography.headlineLarge,
                              fontWeight: AppTypography.light,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  // Controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_phase == 'ready')
                        IconButton(
                          icon: Icon(
                            Icons.play_circle_outline,
                            size: 64,
                            color: Theme.of(context).primaryColor,
                          ),
                          onPressed: _startBreathwork,
                        )
                      else if (_phase != 'complete')
                        IconButton(
                          icon: Icon(
                            _isPaused
                                ? Icons.play_circle_outline
                                : Icons.pause_circle_outline,
                            size: 64,
                            color: Theme.of(context).primaryColor,
                          ),
                          onPressed: _togglePause,
                        ),
                    ],
                  ),
                  if (state.backgroundMusicId != null) ...[
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.volume_down, size: 20),
                        SizedBox(
                          width: 200,
                          child: Slider(
                            value: state.musicVolume,
                            onChanged: (value) {
                              ref
                                  .read(breathworkStateProvider.notifier)
                                  .updateMusicVolume(value);
                              _backgroundMusicController?.setVolume(value);
                            },
                          ),
                        ),
                        const Icon(Icons.volume_up, size: 20),
                      ],
                    ),
                  ],
                  const SizedBox(height: 16), // Add bottom padding
                ],
              ),
            ),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const Center(child: Text('Error loading background')),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _cycleController.dispose();
    _visualController.dispose();
    _cuePlayer?.dispose();

    _cleanupBackgroundMusic();
    super.dispose();
  }
}

class BreathworkProgressPainter extends CustomPainter {
  final double progress;
  final BreathePattern pattern;
  final int totalSeconds;

  BreathworkProgressPainter({
    required this.progress,
    required this.pattern,
    required this.totalSeconds,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    final trackPaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    canvas.drawCircle(center, radius, trackPaint);

    final totalDuration = pattern.inhaleSeconds +
        pattern.holdSeconds +
        pattern.exhaleSeconds +
        pattern.holdAfterExhaleSeconds;

    final phases = <({String name, double percent})>[];
    var currentPercent = 0.0;

    phases.add((name: 'inhale', percent: currentPercent));
    currentPercent += pattern.inhaleSeconds / totalDuration;

    if (pattern.holdSeconds > 0) {
      phases.add((name: 'hold', percent: currentPercent));
    }
    currentPercent += pattern.holdSeconds / totalDuration;

    phases.add((name: 'exhale', percent: currentPercent));
    currentPercent += pattern.exhaleSeconds / totalDuration;

    if (pattern.holdAfterExhaleSeconds > 0) {
      phases.add((name: 'holdAfterExhale', percent: currentPercent));
    }

    final markerPaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.fill;

    void drawMarker(double percentage) {
      final angle = percentage * 2 * math.pi - math.pi / 2;
      final markerCenter = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      canvas.drawCircle(markerCenter, 4, markerPaint);
    }

    for (final phase in phases) {
      drawMarker(phase.percent);
    }

    final currentAngle = progress * 2 * math.pi - math.pi / 2;
    final dotCenter = Offset(
      center.dx + radius * math.cos(currentAngle),
      center.dy + radius * math.sin(currentAngle),
    );

    canvas.drawCircle(
      dotCenter,
      8,
      Paint()
        ..color = Colors.white.withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4),
    );

    canvas.drawCircle(
      dotCenter,
      6,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill,
    );
  }

  @override
  bool shouldRepaint(covariant BreathworkProgressPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
