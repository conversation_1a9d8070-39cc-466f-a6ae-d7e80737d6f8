// lib/src/features/breathwork/data/breathwork_patterns.dart
import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';

final defaultBreathworkPatterns = [
  BreathePattern(
    id: 'box-breathing',
    name: 'Box Breathing',
    description: 'Perfect for focus and stress reduction',
    category: 'Focus',
    inhaleSeconds: 4,
    holdSeconds: 4,
    exhaleSeconds: 4,
    holdAfterExhaleSeconds: 4,
    cycles: 5,
  ),
  BreathePattern(
    id: '4-7-8',
    name: 'Relaxation Breath (4-7-8)',
    description: 'Helps with sleep and deep relaxation',
    category: 'Sleep',
    inhaleSeconds: 4,
    holdSeconds: 7,
    exhaleSeconds: 8,
    holdAfterExhaleSeconds: 0,
    cycles: 4,
  ),
  BreathePattern(
    id: 'energizing',
    name: 'Energizing Breath',
    description: 'Increases energy and alertness',
    category: 'Energy',
    inhaleSeconds: 6,
    holdSeconds: 0,
    exhaleSeconds: 2,
    holdAfterExhaleSeconds: 0,
    cycles: 8,
  ),
];
