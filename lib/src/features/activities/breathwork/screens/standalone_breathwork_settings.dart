import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import '../providers/breathwork_provider.dart';

class StandaloneBreathworkSettings extends ConsumerStatefulWidget {
  const StandaloneBreathworkSettings({super.key});

  @override
  ConsumerState<StandaloneBreathworkSettings> createState() =>
      _StandaloneBreathworkSettingsState();
}

class _StandaloneBreathworkSettingsState
    extends ConsumerState<StandaloneBreathworkSettings> {
  AudioPlayerController? _previewController;
  String? _playingTrackId;
  int _selectedTabIndex = 0;

  @override
  void dispose() {
    _stopPreview();
    super.dispose();
  }

  Future<void> _stopPreview() async {
    if (_previewController != null) {
      await _previewController?.stopAndClear();
      _previewController = null;
      if (mounted) {
        setState(() {
          _playingTrackId = null;
        });
      }
    }
  }

  Future<void> _togglePreview(AudioTrack track) async {
    if (_playingTrackId == track.id) {
      await _stopPreview();
    } else {
      await _stopPreview();
      _previewController = ref.read(audioPlayerControllerProvider.notifier);
      await _previewController?.playTracks([track]);
      setState(() {
        _playingTrackId = track.id;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        await _stopPreview();
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Breathwork Settings'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              _stopPreview();
              context.pop();
            },
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Tabs with animated selection indicator
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Row(
                children: [
                  _buildTab('Pattern', 0),
                  _buildTab('Background', 1),
                  _buildTab('Music', 2),
                ],
              ),
            ),
            const SizedBox(height: 48),
            // Tab Content
            Flexible(
              child: IndexedStack(
                index: _selectedTabIndex,
                children: [
                  _buildPatternTab(),
                  _buildBackgroundTab(),
                  _buildMusicTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTabIndex = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(30),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPatternTab() {
    final state = ref.watch(breathworkStateProvider);
    final patterns = ref.watch(breathworkPatternsProvider);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Pattern Categories
            SizedBox(
              height: 40,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: _buildCategoryChips(patterns),
              ),
            ),
            const SizedBox(height: 16),

            // Pattern Cards
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: patterns.length,
              itemBuilder: (context, index) {
                final pattern = patterns[index];
                return BreathPatternCard(
                  pattern: pattern,
                  isSelected: pattern.id == state.selectedPatternId,
                  onTap: () {
                    ref
                        .read(breathworkStateProvider.notifier)
                        .updatePattern(pattern.id);
                  },
                );
              },
            ),

            const SizedBox(height: 24),

            // Cycles Adjustment
            ListTile(
              title: const Text('Number of Cycles'),
              trailing: SizedBox(
                width: 100,
                child: TextField(
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  controller:
                      TextEditingController(text: state.cycles.toString()),
                  onChanged: (value) {
                    final newCycles = int.tryParse(value);
                    if (newCycles != null) {
                      ref
                          .read(breathworkStateProvider.notifier)
                          .updateCycles(newCycles);
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundTab() {
    final backgroundsAsync = ref.watch(breathworkBackgroundsProvider);
    final state = ref.watch(breathworkStateProvider);

    return backgroundsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
      data: (backgrounds) => SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 0.6,
                ),
                itemCount: backgrounds.length,
                itemBuilder: (context, index) {
                  final bg = backgrounds[index];
                  return GestureDetector(
                    onTap: () {
                      ref
                          .read(breathworkStateProvider.notifier)
                          .updateBackgroundImage(bg.id);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: bg.id == state.backgroundImageId
                              ? Theme.of(context).primaryColor
                              : Colors.transparent,
                          width: 3,
                        ),
                        image: DecorationImage(
                          image: bg.assetPath.startsWith('http')
                              ? NetworkImage(bg.assetPath) as ImageProvider
                              : AssetImage(bg.assetPath),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMusicTab() {
    final state = ref.watch(breathworkStateProvider);
    final breathworkTracksAsync =
        ref.watch(categoryTracksProvider('breathwork'));

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Audio Cues Toggle
            SwitchListTile(
              title: const Text('Audio Cues'),
              subtitle: const Text('Voice guidance for breathing phases'),
              value: !state.cuesMuted,
              onChanged: (value) {
                ref.read(breathworkStateProvider.notifier).toggleCuesMuted();
              },
            ),

            const Divider(),
            const SizedBox(height: 16),

            // Background Music Selection
            breathworkTracksAsync.when(
              data: (tracks) => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Background Music',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // No music option
                  ListTile(
                    title: const Text('No Background Music'),
                    trailing: const Icon(Icons.music_off),
                    leading: Radio<String?>(
                      value: null,
                      groupValue: state.backgroundMusicId,
                      onChanged: (value) async {
                        await _stopPreview();
                        ref
                            .read(breathworkStateProvider.notifier)
                            .updateBackgroundMusic(value);
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Available tracks
                  ...tracks.map(
                    (track) => ListTile(
                      title: Text(track.title),
                      leading: Radio<String?>(
                        value: track.id,
                        groupValue: state.backgroundMusicId,
                        onChanged: (value) async {
                          await _stopPreview();
                          ref
                              .read(breathworkStateProvider.notifier)
                              .updateBackgroundMusic(value);
                        },
                      ),
                      trailing: IconButton(
                        icon: Icon(
                          _playingTrackId == track.id
                              ? Icons.stop_circle_outlined
                              : Icons.play_circle_outline,
                          color: Theme.of(context).primaryColor,
                        ),
                        onPressed: () => _togglePreview(track),
                      ),
                    ),
                  ),
                ],
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) =>
                  Text('Error loading background music tracks: $error'),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCategoryChips(List<BreathePattern> patterns) {
    final categories = patterns.map((p) => p.category).toSet().toList();
    return categories.map((category) {
      return Padding(
        padding: const EdgeInsets.only(right: 8),
        child: FilterChip(
          label: Text(category),
          selected: true,
          onSelected: (selected) {
            // TODO: Implement category filtering if needed
          },
        ),
      );
    }).toList();
  }
}

class BreathPatternCard extends StatelessWidget {
  final BreathePattern pattern;
  final bool isSelected;
  final VoidCallback onTap;

  const BreathPatternCard({
    super.key,
    required this.pattern,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: isSelected
                ? Border.all(color: Theme.of(context).primaryColor, width: 2)
                : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                pattern.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(pattern.description),
              const SizedBox(height: 8),
              Text(
                '${pattern.inhaleSeconds}-${pattern.holdSeconds}-'
                '${pattern.exhaleSeconds}-${pattern.holdAfterExhaleSeconds}',
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
