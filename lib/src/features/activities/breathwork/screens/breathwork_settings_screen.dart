import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_settings_screen.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import 'package:mimi_app/src/features/activities/breathwork/providers/breathwork_provider.dart';

// lib/src/features/activities/breathwork/screens/breathwork_settings_screen.dart

class BreathworkSettingsScreen extends BaseSettingsScreen {
  const BreathworkSettingsScreen({
    required super.activity,
    super.key,
    required super.routineId,
    super.onConfigChanged,
  });

  @override
  ConsumerState<BreathworkSettingsScreen> createState() =>
      _BreathworkSettingsScreenState();
}

class _BreathworkSettingsScreenState
    extends BaseSettingsScreenState<BreathworkSettingsScreen> {
  String? _previewingTrackId;
  int _selectedTabIndex = 0;

  @override
  void dispose() {
    _stopPreview();
    super.dispose();
  }

  Future<void> _togglePreview(String trackId) async {
    if (_previewingTrackId == trackId) {
      _stopPreview();
    } else {
      await _stopPreview();
      _startPreview(trackId);
    }
  }

  Future<void> _startPreview(String trackId) async {
    final tracks = await ref.read(categoryTracksProvider('breathwork').future);
    final track = tracks.firstWhere((t) => t.id == trackId);
    await ref.read(audioPlayerControllerProvider.notifier).playTracks([track]);
    setState(() {
      _previewingTrackId = trackId;
    });
  }

  Future<void> _stopPreview() async {
    if (_previewingTrackId != null) {
      await ref.read(audioPlayerControllerProvider.notifier).stopAndClear();
      setState(() {
        _previewingTrackId = null;
      });
    }
  }

  @override
  Widget buildConfigSection() {
    return config.maybeWhen(
      breathwork: (
        String selectedPatternId,
        int cycles,
        List<BreathePattern> availablePatterns,
        String? backgroundMusicId,
        bool cuesMuted,
      ) =>
          Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Activity Title
            Text(
              'Breathwork Settings',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 24),
            // Tabs with animated selection indicator
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Row(
                children: [
                  _buildTab('Pattern', 0),
                  _buildTab('Background', 1),
                  _buildTab('Music', 2),
                ],
              ),
            ),
            const SizedBox(height: 24),
            // Tab Content
            IndexedStack(
              index: _selectedTabIndex,
              children: [
                _buildPatternTab(
                  selectedPatternId,
                  cycles,
                  availablePatterns,
                  backgroundMusicId,
                  cuesMuted,
                ),
                _buildBackgroundTab(
                  selectedPatternId,
                  cycles,
                  availablePatterns,
                  backgroundMusicId,
                  cuesMuted,
                ),
                _buildMusicTab(
                  selectedPatternId,
                  cycles,
                  availablePatterns,
                  backgroundMusicId,
                  cuesMuted,
                ),
              ],
            ),
          ],
        ),
      ),
      orElse: () => const SizedBox.shrink(),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTabIndex = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(30),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPatternTab(
    String selectedPatternId,
    int cycles,
    List<BreathePattern> availablePatterns,
    String? backgroundMusicId,
    bool cuesMuted,
  ) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Pattern Categories
            // SizedBox(
            //   height: 40,
            //   child: ListView(
            //     scrollDirection: Axis.horizontal,
            //     children: _buildCategoryChips(availablePatterns),
            //   ),
            // ),
            // const SizedBox(height: 16),

            // Pattern Cards
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: availablePatterns.length,
              itemBuilder: (context, index) {
                final pattern = availablePatterns[index];
                return BreathPatternCard(
                  pattern: pattern,
                  isSelected: pattern.id == selectedPatternId,
                  onTap: () {
                    updateConfig(ActivityConfig.breathwork(
                      selectedPatternId: pattern.id,
                      cycles: cycles,
                      availablePatterns: availablePatterns,
                      backgroundMusicId: backgroundMusicId,
                      cuesMuted: cuesMuted,
                    ));
                  },
                );
              },
            ),

            const SizedBox(height: 24),

            // Cycles Adjustment
            ListTile(
              title: const Text('Number of Cycles'),
              trailing: SizedBox(
                width: 100,
                child: TextField(
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  controller: TextEditingController(text: cycles.toString()),
                  onChanged: (value) {
                    final newCycles = int.tryParse(value);
                    if (newCycles != null) {
                      updateConfig(ActivityConfig.breathwork(
                        selectedPatternId: selectedPatternId,
                        cycles: newCycles,
                        availablePatterns: availablePatterns,
                        backgroundMusicId: backgroundMusicId,
                        cuesMuted: cuesMuted,
                      ));
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundTab(
    String selectedPatternId,
    int cycles,
    List<BreathePattern> availablePatterns,
    String? backgroundMusicId,
    bool cuesMuted,
  ) {
    final backgroundsAsync = ref.watch(breathworkBackgroundsProvider);
    final state = ref.watch(breathworkStateProvider);

    return backgroundsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
      data: (backgrounds) {
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: .6,
                  ),
                  itemCount: backgrounds.length,
                  itemBuilder: (context, index) {
                    final bg = backgrounds[index];
                    return GestureDetector(
                      onTap: () {
                        ref
                            .read(breathworkStateProvider.notifier)
                            .updateBackgroundImage(bg.id);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: bg.id == state.backgroundImageId
                                ? Theme.of(context).primaryColor
                                : Colors.transparent,
                            width: 3,
                          ),
                          image: DecorationImage(
                            image: bg.assetPath.startsWith('http')
                                ? NetworkImage(bg.assetPath) as ImageProvider
                                : AssetImage(bg.assetPath),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMusicTab(
    String selectedPatternId,
    int cycles,
    List<BreathePattern> availablePatterns,
    String? backgroundMusicId,
    bool cuesMuted,
  ) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Audio Cues Toggle
            SwitchListTile(
              title: const Text('Audio Cues'),
              subtitle: const Text('Voice guidance for breathing phases'),
              value: !cuesMuted,
              onChanged: (value) {
                updateConfig(ActivityConfig.breathwork(
                  selectedPatternId: selectedPatternId,
                  cycles: cycles,
                  availablePatterns: availablePatterns,
                  backgroundMusicId: backgroundMusicId,
                  cuesMuted: !value,
                ));
              },
            ),

            const Divider(),
            const SizedBox(height: 16),

            // Background Music Selection
            Consumer(
              builder: (context, ref, child) {
                final breathworkTracksAsync =
                    ref.watch(categoryTracksProvider('breathwork'));

                return breathworkTracksAsync.when(
                  data: (tracks) => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Background Music',
                        style: TextStyle(
                          fontSize: AppTypography.bodyMedium,
                          fontWeight: AppTypography.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // No music option
                      ListTile(
                        title: const Text('No Background Music'),
                        leading: const Icon(Icons.music_off),
                        trailing: Radio<String?>(
                          value: null,
                          groupValue: backgroundMusicId,
                          onChanged: (value) {
                            updateConfig(ActivityConfig.breathwork(
                              selectedPatternId: selectedPatternId,
                              cycles: cycles,
                              availablePatterns: availablePatterns,
                              backgroundMusicId: value,
                              cuesMuted: cuesMuted,
                            ));
                          },
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Available tracks
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: tracks.length,
                        itemBuilder: (context, index) {
                          final track = tracks[index];
                          return ListTile(
                            title: Text(track.title),
                            leading: Radio<String?>(
                              value: track.id,
                              groupValue: backgroundMusicId,
                              onChanged: (value) {
                                updateConfig(ActivityConfig.breathwork(
                                  selectedPatternId: selectedPatternId,
                                  cycles: cycles,
                                  availablePatterns: availablePatterns,
                                  backgroundMusicId: value,
                                  cuesMuted: cuesMuted,
                                ));
                              },
                            ),
                            trailing: IconButton(
                              icon: Icon(
                                _previewingTrackId == track.id
                                    ? Icons.stop_circle_outlined
                                    : Icons.play_circle_outline,
                                color: Theme.of(context).primaryColor,
                              ),
                              onPressed: () => _togglePreview(track.id),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) =>
                      Text('Error loading background music tracks: $error'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCategoryChips(List<BreathePattern> patterns) {
    final categories = patterns.map((p) => p.category).toSet().toList();
    return categories.map((category) {
      return Padding(
        padding: const EdgeInsets.only(right: 8),
        child: FilterChip(
          label: Text(category),
          selected: true,
          onSelected: (selected) {
            // TODO: Implement category filtering if needed
          },
        ),
      );
    }).toList();
  }
}

class BreathPatternCard extends StatelessWidget {
  final BreathePattern pattern;
  final bool isSelected;
  final VoidCallback onTap;

  const BreathPatternCard({
    super.key,
    required this.pattern,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: isSelected
                ? Border.all(color: Theme.of(context).primaryColor, width: 2)
                : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                pattern.name,
                style: TextStyle(
                  fontSize: AppTypography.bodySmall,
                  fontWeight: AppTypography.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(pattern.description),
              const SizedBox(height: 8),
              Text(
                '${pattern.inhaleSeconds}-${pattern.holdSeconds}-'
                '${pattern.exhaleSeconds}-${pattern.holdAfterExhaleSeconds}',
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
