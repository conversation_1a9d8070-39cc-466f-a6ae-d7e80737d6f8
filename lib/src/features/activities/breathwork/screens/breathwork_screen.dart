// lib/src/features/activities/breathwork/screens/breathwork_screen.dart
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';
import 'package:mimi_app/src/features/activities/breathwork/widgets/breathwork_visualization.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import '../providers/breathwork_provider.dart';

class BreathworkScreen extends ConsumerWidget {
  const BreathworkScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(breathworkStateProvider);
    final patterns = ref.watch(breathworkPatternsProvider);
    final backgroundsAsync = ref.watch(breathworkBackgroundsProvider);

    final pattern = patterns.firstWhere(
      (p) => p.id == state.selectedPatternId,
      orElse: () => patterns.first,
    );

    return backgroundsAsync.when(
      loading: () => const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        body: Center(child: Text('Error loading backgrounds: $error')),
      ),
      data: (backgrounds) {
        final selectedBackground = backgrounds.firstWhere(
          (bg) => bg.id == state.backgroundImageId,
          orElse: () => backgrounds.first,
        );

        return PopScope(
          canPop: true,
          onPopInvoked: (didPop) async {
            // Cleanup audio when popping
            if (didPop) {
              final controller =
                  ref.read(audioPlayerControllerProvider.notifier);
              await controller.stopAndClear();
            }
          },
          child: Scaffold(
            body: Stack(
              fit: StackFit.expand,
              children: [
                // Blurred background image
                selectedBackground.assetPath.startsWith('http')
                    ? Image.network(
                        selectedBackground.assetPath,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      )
                    : Image.asset(
                        selectedBackground.assetPath,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      ),
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    color: Colors.white.withOpacity(0.1),
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
                // Content
                Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: AppBar(
                    title: const Text('Breathwork'),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: IconButton(
                      onPressed: () => context.pop(),
                      icon: const Icon(Icons.arrow_back),
                    ),
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.settings),
                        onPressed: () => context.push('/breathwork/settings'),
                      ),
                    ],
                  ),
                  body: SafeArea(
                    child: BreathingVisualization(
                      key: ValueKey('${pattern.id}_${state.cycles}'),
                      pattern: pattern,
                      cycles: state.cycles,
                      onComplete: () {
                        // if (context.mounted) {
                        //   context.pop();
                        // }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
