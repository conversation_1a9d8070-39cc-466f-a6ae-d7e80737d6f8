import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart';
import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';
import 'package:mimi_app/src/features/activities/breathwork/widgets/breathwork_visualization.dart';
import 'package:mimi_app/src/features/activities/shared/mixins/routine_config_mixin.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';

class BreathworkActivity extends BaseActivity {
  const BreathworkActivity({
    super.key,
    required super.activity,
    super.onComplete,
    super.standalone = false,
    required super.routineId,
  });

  @override
  ConsumerState<BreathworkActivity> createState() => _BreathworkActivityState();
}

class _BreathworkActivityState extends BaseActivityState<BreathworkActivity>
    with RoutineConfigMixin<BreathworkActivity> {
  @override
  Widget buildActivityContent(BuildContext context) {
    return currentConfig.maybeWhen(
      breathwork: (
        String selectedPatternId,
        int cycles,
        List<BreathePattern> availablePatterns,
        String? backgroundMusicId,
        bool cuesMuted,
      ) {
        final pattern = availablePatterns.firstWhere(
          (p) => p.id == selectedPatternId,
          orElse: () => defaultBreathworkPatterns.first,
        );

        return Material(
          color: Colors.transparent,
          child: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: BreathingVisualization(
                    pattern: pattern,
                    cycles: cycles,
                    onComplete: markComplete,
                    showTitle: true,
                  ),
                ),
              ],
            ),
          ),
        );
      },
      orElse: () => throw Exception('Invalid config type'),
    );
  }

  @override
  int getTotalSteps() => 1; // Single step activity

  @override
  int getCurrentStep() => 0;

  @override
  bool moveToStep(int step) => step == 0;

  @override
  IconData getActivityIcon() => Icons.air;
}
