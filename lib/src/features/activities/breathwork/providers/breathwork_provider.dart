// lib/src/features/activities/breathwork/providers/breathwork_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';
import 'package:mimi_app/src/features/timer/sound_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/breathwork_patterns.dart';
import '../../../admin/providers/firebase_background_provider.dart';

part 'breathwork_provider.g.dart';

// lib/src/features/activities/breathwork/providers/breathwork_provider.dart
// lib/src/features/activities/breathwork/providers/breathwork_provider.dart

// Add this provider reference
@riverpod
class BreathworkState extends _$BreathworkState {
  static const selectedPatternKey = 'selected_breath_pattern';
  static const cyclesKey = 'breath_cycles';
  static const backgroundMusicKey = 'breath_background_music';
  static const backgroundImageKey = 'breath_background_image';
  static const musicVolumeKey = 'breath_music_volume';
  static const cuesMutedKey = 'breath_cues_muted';

  @override
  ({
    String selectedPatternId,
    int cycles,
    String? backgroundMusicId,
    String? backgroundImageId,
    double musicVolume,
    bool cuesMuted,
  }) build() {
    final prefs = ref.watch(sharedPreferencesProvider);

    final savedPatternId = prefs.getString(selectedPatternKey);
    final savedCycles = prefs.getInt(cyclesKey);
    final savedMusicId = prefs.getString(backgroundMusicKey);
    final savedImageId = prefs.getString(backgroundImageKey);
    final savedMusicVolume = prefs.getDouble(musicVolumeKey);
    final savedCuesMuted = prefs.getBool(cuesMutedKey) ?? false;

    return (
      selectedPatternId: savedPatternId ?? defaultBreathworkPatterns.first.id,
      cycles: savedCycles ?? 5,
      backgroundMusicId: savedMusicId,
      backgroundImageId: savedImageId,
      musicVolume: savedMusicVolume ?? 1.0,
      cuesMuted: savedCuesMuted,
    );
  }

  void updatePattern(String patternId) {
    final prefs = ref.read(sharedPreferencesProvider);
    state = (
      selectedPatternId: patternId,
      cycles: state.cycles,
      backgroundMusicId: state.backgroundMusicId,
      backgroundImageId: state.backgroundImageId,
      musicVolume: state.musicVolume,
      cuesMuted: state.cuesMuted,
    );
    prefs.setString(selectedPatternKey, patternId);
  }

  void updateBackgroundMusic(String? musicId) {
    final prefs = ref.read(sharedPreferencesProvider);
    state = (
      selectedPatternId: state.selectedPatternId,
      cycles: state.cycles,
      backgroundMusicId: musicId,
      backgroundImageId: state.backgroundImageId,
      musicVolume: state.musicVolume,
      cuesMuted: state.cuesMuted,
    );

    if (musicId != null) {
      prefs.setString(backgroundMusicKey, musicId);
    } else {
      prefs.remove(backgroundMusicKey);
    }
  }

  void updateMusicVolume(double volume) {
    final prefs = ref.read(sharedPreferencesProvider);
    final clampedVolume = volume.clamp(0.0, 1.0);

    state = (
      selectedPatternId: state.selectedPatternId,
      cycles: state.cycles,
      backgroundMusicId: state.backgroundMusicId,
      backgroundImageId: state.backgroundImageId,
      musicVolume: clampedVolume,
      cuesMuted: state.cuesMuted,
    );

    prefs.setDouble(musicVolumeKey, clampedVolume);
  }

  void updateBackgroundImage(String? imageId) {
    final prefs = ref.read(sharedPreferencesProvider);
    state = (
      selectedPatternId: state.selectedPatternId,
      cycles: state.cycles,
      backgroundMusicId: state.backgroundMusicId,
      backgroundImageId: imageId,
      musicVolume: state.musicVolume,
      cuesMuted: state.cuesMuted,
    );

    if (imageId != null) {
      prefs.setString(backgroundImageKey, imageId);
    } else {
      prefs.remove(backgroundImageKey);
    }
  }

  void toggleCuesMuted() {
    final prefs = ref.read(sharedPreferencesProvider);
    state = (
      selectedPatternId: state.selectedPatternId,
      cycles: state.cycles,
      backgroundMusicId: state.backgroundMusicId,
      backgroundImageId: state.backgroundImageId,
      musicVolume: state.musicVolume,
      cuesMuted: !state.cuesMuted,
    );

    prefs.setBool(cuesMutedKey, state.cuesMuted);
  }

  void updateCycles(int cycles) {
    final prefs = ref.read(sharedPreferencesProvider);
    state = (
      selectedPatternId: state.selectedPatternId,
      cycles: cycles,
      backgroundMusicId: state.backgroundMusicId,
      backgroundImageId: state.backgroundImageId,
      musicVolume: state.musicVolume,
      cuesMuted: state.cuesMuted,
    );

    prefs.setInt(cyclesKey, cycles);
  }

  void reset() {
    final prefs = ref.read(sharedPreferencesProvider);
    state = (
      selectedPatternId: defaultBreathworkPatterns.first.id,
      cycles: 5,
      backgroundMusicId: null,
      backgroundImageId: null,
      musicVolume: 1.0,
      cuesMuted: false,
    );

    prefs.remove(selectedPatternKey);
    prefs.remove(cyclesKey);
    prefs.remove(backgroundMusicKey);
    prefs.remove(backgroundImageKey);
    prefs.remove(musicVolumeKey);
    prefs.remove(cuesMutedKey);
  }
}

//Provider for breathwork patterns
@riverpod
List<BreathePattern> breathworkPatterns(Ref ref) {
  return defaultBreathworkPatterns;
}

// // Provider for selected pattern

// You can create similar helper providers for other frequently accessed state values

// Provider for background images
@riverpod
Future<List<BreathworkBackground>> breathworkBackgrounds(Ref ref) async {
  try {
    // Try to get Firebase background images first
    final firebaseImages =
        await ref.watch(firebaseBackgroundImagesProvider.future);

    if (firebaseImages.isNotEmpty) {
      // Convert Firebase images to BreathworkBackground format
      return firebaseImages.map((image) {
        return BreathworkBackground(
          id: image.id,
          name: image.name,
          assetPath: image.imageUrl, // Use imageUrl for Firebase images
        );
      }).toList();
    }
  } catch (e) {
    // If Firebase fails, fall back to local assets
  }

  // Fallback to local assets
  return const [
    BreathworkBackground(
      id: 'ocean',
      name: 'Ocean Waves',
      assetPath: 'assets/images/breathwork/ocean.jpeg',
    ),
    BreathworkBackground(
      id: 'forest',
      name: 'Forest',
      assetPath: 'assets/images/breathwork/forest.jpeg',
    ),
    BreathworkBackground(
      id: 'mountain',
      name: 'Mountain View',
      assetPath: 'assets/images/breathwork/mountain.jpeg',
    ),
    BreathworkBackground(
      id: 'space',
      name: 'Space',
      assetPath: 'assets/images/breathwork/space.jpeg',
    ),
  ];
}

// Background image model
class BreathworkBackground {
  final String id;
  final String name;
  final String assetPath;

  const BreathworkBackground({
    required this.id,
    required this.name,
    required this.assetPath,
  });
}
