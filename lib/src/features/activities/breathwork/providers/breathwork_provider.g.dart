// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'breathwork_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$breathworkPatternsHash() =>
    r'43a96af6dba3ceedbb7a5b0dd0ad78eb753a3ee7';

/// See also [breathworkPatterns].
@ProviderFor(breathworkPatterns)
final breathworkPatternsProvider =
    AutoDisposeProvider<List<BreathePattern>>.internal(
  breathworkPatterns,
  name: r'breathworkPatternsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$breathworkPatternsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BreathworkPatternsRef = AutoDisposeProviderRef<List<BreathePattern>>;
String _$breathworkBackgroundsHash() =>
    r'e02d0f73e0d07555498f2337ae67c72f72f1d21f';

/// See also [breathworkBackgrounds].
@ProviderFor(breathworkBackgrounds)
final breathworkBackgroundsProvider =
    AutoDisposeFutureProvider<List<BreathworkBackground>>.internal(
  breathworkBackgrounds,
  name: r'breathworkBackgroundsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$breathworkBackgroundsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BreathworkBackgroundsRef
    = AutoDisposeFutureProviderRef<List<BreathworkBackground>>;
String _$breathworkStateHash() => r'ea0df544223ee4fa5c2e2d2aa25a6fd2d2d42de6';

/// See also [BreathworkState].
@ProviderFor(BreathworkState)
final breathworkStateProvider = AutoDisposeNotifierProvider<
    BreathworkState,
    ({
      String selectedPatternId,
      int cycles,
      String? backgroundMusicId,
      String? backgroundImageId,
      double musicVolume,
      bool cuesMuted
    })>.internal(
  BreathworkState.new,
  name: r'breathworkStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$breathworkStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BreathworkState = AutoDisposeNotifier<
    ({
      String selectedPatternId,
      int cycles,
      String? backgroundMusicId,
      String? backgroundImageId,
      double musicVolume,
      bool cuesMuted
    })>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
