// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'breathwork_cues_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$breathworkCuesHash() => r'7a0e7e41c9e506e0a01eff35c6be9b3852da17d5';

/// See also [BreathworkCues].
@ProviderFor(BreathworkCues)
final breathworkCuesProvider =
    AutoDisposeNotifierProvider<BreathworkCues, BreathworkCuesService>.internal(
  BreathworkCues.new,
  name: r'breathworkCuesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$breathworkCuesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BreathworkCues = AutoDisposeNotifier<BreathworkCuesService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
