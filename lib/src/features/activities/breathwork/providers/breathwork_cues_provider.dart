import 'package:mimi_app/src/features/activities/breathwork/service/breathwork_cues_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'breathwork_cues_provider.g.dart';

@riverpod
class BreathworkCues extends _$BreathworkCues {
  @override
  BreathworkCuesService build() {
    final service = BreathworkCuesService();
    ref.onDispose(() {
      service.dispose();
    });
    return service;
  }
}
