// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'breathe_pattern.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BreathePatternImpl _$$BreathePatternImplFromJson(Map<String, dynamic> json) =>
    _$BreathePatternImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      inhaleSeconds: (json['inhaleSeconds'] as num).toInt(),
      holdSeconds: (json['holdSeconds'] as num).toInt(),
      exhaleSeconds: (json['exhaleSeconds'] as num).toInt(),
      holdAfterExhaleSeconds: (json['holdAfterExhaleSeconds'] as num).toInt(),
      cycles: (json['cycles'] as num?)?.toInt() ?? 5,
      selectedBackgroundMusicId: json['selectedBackgroundMusicId'] as String?,
    );

Map<String, dynamic> _$$BreathePatternImplToJson(
        _$BreathePatternImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': instance.category,
      'inhaleSeconds': instance.inhaleSeconds,
      'holdSeconds': instance.holdSeconds,
      'exhaleSeconds': instance.exhaleSeconds,
      'holdAfterExhaleSeconds': instance.holdAfterExhaleSeconds,
      'cycles': instance.cycles,
      'selectedBackgroundMusicId': instance.selectedBackgroundMusicId,
    };
