// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'breathe_pattern.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BreathePattern _$BreathePatternFromJson(Map<String, dynamic> json) {
  return _BreathePattern.fromJson(json);
}

/// @nodoc
mixin _$BreathePattern {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  int get inhaleSeconds => throw _privateConstructorUsedError;
  int get holdSeconds => throw _privateConstructorUsedError;
  int get exhaleSeconds => throw _privateConstructorUsedError;
  int get holdAfterExhaleSeconds => throw _privateConstructorUsedError;
  int get cycles => throw _privateConstructorUsedError;
  String? get selectedBackgroundMusicId => throw _privateConstructorUsedError;

  /// Serializes this BreathePattern to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BreathePattern
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BreathePatternCopyWith<BreathePattern> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BreathePatternCopyWith<$Res> {
  factory $BreathePatternCopyWith(
          BreathePattern value, $Res Function(BreathePattern) then) =
      _$BreathePatternCopyWithImpl<$Res, BreathePattern>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      String category,
      int inhaleSeconds,
      int holdSeconds,
      int exhaleSeconds,
      int holdAfterExhaleSeconds,
      int cycles,
      String? selectedBackgroundMusicId});
}

/// @nodoc
class _$BreathePatternCopyWithImpl<$Res, $Val extends BreathePattern>
    implements $BreathePatternCopyWith<$Res> {
  _$BreathePatternCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BreathePattern
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? category = null,
    Object? inhaleSeconds = null,
    Object? holdSeconds = null,
    Object? exhaleSeconds = null,
    Object? holdAfterExhaleSeconds = null,
    Object? cycles = null,
    Object? selectedBackgroundMusicId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      inhaleSeconds: null == inhaleSeconds
          ? _value.inhaleSeconds
          : inhaleSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      holdSeconds: null == holdSeconds
          ? _value.holdSeconds
          : holdSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      exhaleSeconds: null == exhaleSeconds
          ? _value.exhaleSeconds
          : exhaleSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      holdAfterExhaleSeconds: null == holdAfterExhaleSeconds
          ? _value.holdAfterExhaleSeconds
          : holdAfterExhaleSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      cycles: null == cycles
          ? _value.cycles
          : cycles // ignore: cast_nullable_to_non_nullable
              as int,
      selectedBackgroundMusicId: freezed == selectedBackgroundMusicId
          ? _value.selectedBackgroundMusicId
          : selectedBackgroundMusicId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BreathePatternImplCopyWith<$Res>
    implements $BreathePatternCopyWith<$Res> {
  factory _$$BreathePatternImplCopyWith(_$BreathePatternImpl value,
          $Res Function(_$BreathePatternImpl) then) =
      __$$BreathePatternImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      String category,
      int inhaleSeconds,
      int holdSeconds,
      int exhaleSeconds,
      int holdAfterExhaleSeconds,
      int cycles,
      String? selectedBackgroundMusicId});
}

/// @nodoc
class __$$BreathePatternImplCopyWithImpl<$Res>
    extends _$BreathePatternCopyWithImpl<$Res, _$BreathePatternImpl>
    implements _$$BreathePatternImplCopyWith<$Res> {
  __$$BreathePatternImplCopyWithImpl(
      _$BreathePatternImpl _value, $Res Function(_$BreathePatternImpl) _then)
      : super(_value, _then);

  /// Create a copy of BreathePattern
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? category = null,
    Object? inhaleSeconds = null,
    Object? holdSeconds = null,
    Object? exhaleSeconds = null,
    Object? holdAfterExhaleSeconds = null,
    Object? cycles = null,
    Object? selectedBackgroundMusicId = freezed,
  }) {
    return _then(_$BreathePatternImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      inhaleSeconds: null == inhaleSeconds
          ? _value.inhaleSeconds
          : inhaleSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      holdSeconds: null == holdSeconds
          ? _value.holdSeconds
          : holdSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      exhaleSeconds: null == exhaleSeconds
          ? _value.exhaleSeconds
          : exhaleSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      holdAfterExhaleSeconds: null == holdAfterExhaleSeconds
          ? _value.holdAfterExhaleSeconds
          : holdAfterExhaleSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      cycles: null == cycles
          ? _value.cycles
          : cycles // ignore: cast_nullable_to_non_nullable
              as int,
      selectedBackgroundMusicId: freezed == selectedBackgroundMusicId
          ? _value.selectedBackgroundMusicId
          : selectedBackgroundMusicId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BreathePatternImpl implements _BreathePattern {
  const _$BreathePatternImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.category,
      required this.inhaleSeconds,
      required this.holdSeconds,
      required this.exhaleSeconds,
      required this.holdAfterExhaleSeconds,
      this.cycles = 5,
      this.selectedBackgroundMusicId});

  factory _$BreathePatternImpl.fromJson(Map<String, dynamic> json) =>
      _$$BreathePatternImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final String category;
  @override
  final int inhaleSeconds;
  @override
  final int holdSeconds;
  @override
  final int exhaleSeconds;
  @override
  final int holdAfterExhaleSeconds;
  @override
  @JsonKey()
  final int cycles;
  @override
  final String? selectedBackgroundMusicId;

  @override
  String toString() {
    return 'BreathePattern(id: $id, name: $name, description: $description, category: $category, inhaleSeconds: $inhaleSeconds, holdSeconds: $holdSeconds, exhaleSeconds: $exhaleSeconds, holdAfterExhaleSeconds: $holdAfterExhaleSeconds, cycles: $cycles, selectedBackgroundMusicId: $selectedBackgroundMusicId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BreathePatternImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.inhaleSeconds, inhaleSeconds) ||
                other.inhaleSeconds == inhaleSeconds) &&
            (identical(other.holdSeconds, holdSeconds) ||
                other.holdSeconds == holdSeconds) &&
            (identical(other.exhaleSeconds, exhaleSeconds) ||
                other.exhaleSeconds == exhaleSeconds) &&
            (identical(other.holdAfterExhaleSeconds, holdAfterExhaleSeconds) ||
                other.holdAfterExhaleSeconds == holdAfterExhaleSeconds) &&
            (identical(other.cycles, cycles) || other.cycles == cycles) &&
            (identical(other.selectedBackgroundMusicId,
                    selectedBackgroundMusicId) ||
                other.selectedBackgroundMusicId == selectedBackgroundMusicId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      category,
      inhaleSeconds,
      holdSeconds,
      exhaleSeconds,
      holdAfterExhaleSeconds,
      cycles,
      selectedBackgroundMusicId);

  /// Create a copy of BreathePattern
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BreathePatternImplCopyWith<_$BreathePatternImpl> get copyWith =>
      __$$BreathePatternImplCopyWithImpl<_$BreathePatternImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BreathePatternImplToJson(
      this,
    );
  }
}

abstract class _BreathePattern implements BreathePattern {
  const factory _BreathePattern(
      {required final String id,
      required final String name,
      required final String description,
      required final String category,
      required final int inhaleSeconds,
      required final int holdSeconds,
      required final int exhaleSeconds,
      required final int holdAfterExhaleSeconds,
      final int cycles,
      final String? selectedBackgroundMusicId}) = _$BreathePatternImpl;

  factory _BreathePattern.fromJson(Map<String, dynamic> json) =
      _$BreathePatternImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  String get category;
  @override
  int get inhaleSeconds;
  @override
  int get holdSeconds;
  @override
  int get exhaleSeconds;
  @override
  int get holdAfterExhaleSeconds;
  @override
  int get cycles;
  @override
  String? get selectedBackgroundMusicId;

  /// Create a copy of BreathePattern
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BreathePatternImplCopyWith<_$BreathePatternImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
