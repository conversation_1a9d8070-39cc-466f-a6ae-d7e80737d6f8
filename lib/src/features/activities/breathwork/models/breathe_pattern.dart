// lib/src/features/breathwork/models/breathe_pattern.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'breathe_pattern.freezed.dart';
part 'breathe_pattern.g.dart';

@freezed
class BreathePattern with _$BreathePattern {
  const factory BreathePattern({
    required String id,
    required String name,
    required String description,
    required String category,
    required int inhaleSeconds,
    required int holdSeconds,
    required int exhaleSeconds,
    required int holdAfterExhaleSeconds,
    @Default(5) int cycles,
    String? selectedBackgroundMusicId, // Add this field
  }) = _BreathePattern;

  factory BreathePattern.fromJson(Map<String, dynamic> json) =>
      _$BreathePatternFromJson(json);
}
