// lib/src/features/activities/mood_tracking/screens/mood_tracking_activity.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
// import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
import 'package:mimi_app/src/features/activities/shared/mixins/routine_config_mixin.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/providers/activity_data_provider.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';

class MoodTrackingActivity extends BaseActivity {
  const MoodTrackingActivity({
    super.key,
    required super.activity,
    super.onComplete,
    super.standalone = false,
    required super.routineId,
  });

  @override
  ConsumerState<MoodTrackingActivity> createState() =>
      _MoodTrackingActivityState();
}

class _MoodTrackingActivityState extends BaseActivityState<MoodTrackingActivity>
    with RoutineConfigMixin<MoodTrackingActivity> {
  MoodOption? _selectedMood;
  final TextEditingController _noteController = TextEditingController();
  late final ActivityDataNotifier _dataNotifier;
  int _currentStep = 0; // 0 = mood selection, 1 = note input

  MoodTrackingConfig get _config => currentConfig.maybeWhen(
        moodTracking: (moods, includeNote) => MoodTrackingConfig(
          moods: moods,
          includeNote: includeNote,
        ),
        orElse: () => throw Exception('Invalid config type'),
      );

  @override
  void initState() {
    super.initState();
    _dataNotifier = ref.read(
      activityDataNotifierProvider(widget.activity.id, DateTime.now()).notifier,
    );
    _loadExistingMood();
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingMood() async {
    final data = await _dataNotifier.build(widget.activity.id, DateTime.now());
    if (data != null) {
      setState(() {
        _selectedMood = data.data['mood'];
        _noteController.text = data.data['note'] as String? ?? '';
      });
    }
  }

  Future<void> _saveMood() async {
    if (_selectedMood != null) {
      // Save only the necessary string data
      final moodData = {
        'mood_label': _selectedMood!.label,
        'mood_emoji': _selectedMood!.emoji,
        if (_config.includeNote) 'note': _noteController.text,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await _dataNotifier.saveActivityData(moodData);
      markComplete();
    }
  }

  @override
  IconData getActivityIcon() => Icons.mood;

  @override
  int getTotalSteps() => 2;

  @override
  int getCurrentStep() => _currentStep;

  @override
  bool moveToStep(int step) {
    print(
        'MoodTracking moveToStep called: step=$step, currentStep=$_currentStep, totalSteps=${getTotalSteps()}');

    if (step >= 0 && step < getTotalSteps()) {
      setState(() {
        _currentStep = step;
      });
      print('MoodTracking moveToStep successful: moved to step $step');
      return true;
    }

    print('MoodTracking moveToStep failed: step $step is out of range');
    return false;
  }

  @override
  bool canMoveNext() {
    if (_currentStep == 0) {
      return _selectedMood != null;
    }
    return true;
  }

  @override
  Widget buildActivityContent(BuildContext context) {
    return currentConfig.maybeWhen(
      moodTracking: (_, includeNote) => Container(
        color: Colors.transparent,
        child: SafeArea(
          child: _currentStep == 0
              ? Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: _buildMoodSelectionStep(),
                )
              : _buildNoteStep(),
        ),
      ),
      orElse: () => throw Exception('Invalid config type'),
    );
  }

  Widget _buildMoodSelectionStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "How are you feeling Today?",
          style: TextStyle(
            fontSize: AppTypography.headlineMedium,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: 32),
        Expanded(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 1,
            ),
            itemCount: getMoodOptionsWithSvg(context).length,
            itemBuilder: (context, index) {
              final mood = getMoodOptionsWithSvg(context)[index];
              return MoodCardWithSvg(
                mood: mood,
                isSelected: _selectedMood == mood,
                heroTag: 'mood_${mood.label}',
                onTap: () {
                  setState(() => _selectedMood = mood);
                  // Auto-save when mood is selected
                  _autoSaveAndMoveNext();
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNoteStep() {
    final question = _getQuestionForMood(_selectedMood?.label ?? '');

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Mood image with hero animation
          if (_selectedMood?.svgAsset != null)
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 20.0,
                ),
                child: Hero(
                  tag: 'mood_${_selectedMood!.label}',
                  child: SvgPicture.asset(
                    _selectedMood!.svgAsset!,
                    width: 80,
                    height: 80,
                    placeholderBuilder: (BuildContext context) => Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.2),
                      ),
                      child: Icon(
                        Icons.mood,
                        size: 60,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.5),
                      ),
                    ),
                    errorBuilder: (BuildContext context, Object error,
                        StackTrace? stackTrace) {
                      return Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context)
                              .colorScheme
                              .outline
                              .withValues(alpha: 0.2),
                        ),
                        child: Text(
                          _selectedMood!.emoji,
                          style: const TextStyle(fontSize: 60),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

          // Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12),
            child: Text(
              question,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ),

          // Text field container with gratitude-style design
          CustomTextField(
            controller: _noteController,
            hintText: 'Share your thoughts...',
            style: CustomTextFieldStyle.withGradient,
            autofocus: true,
            enableInteractiveSelection: true,
            maxLines: 8,
            onTap: () {
              // Ensure focus is maintained when tapping
              if (!_noteController.selection.isValid) {
                _noteController.selection = TextSelection.fromPosition(
                  TextPosition(offset: _noteController.text.length),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _autoSaveAndMoveNext() async {
    // Auto-save the mood selection and move to next step
    if (_selectedMood != null) {
      await _dataNotifier.saveActivityData({
        'mood_label': _selectedMood!.label,
        'mood_emoji': _selectedMood!.emoji,
        'step': 'mood_selected',
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  String _getQuestionForMood(String moodLabel) {
    switch (moodLabel.toLowerCase()) {
      case 'happy':
        return 'What made you happy today?';
      case 'grateful':
        return 'What are you grateful for today?';
      case 'excited':
        return 'What are you excited about?';
      case 'love':
        return 'What or who brought love into your day?';
      case 'sad':
        return 'What\'s making you feel sad today?';
      case 'angry':
        return 'What triggered your anger today?';
      case 'anxious':
        return 'What\'s causing you to feel anxious?';
      case 'tired':
        return 'What\'s been draining your energy today?';
      case 'confused':
        return 'What\'s making you feel confused today?';
      default:
        return 'Tell us more about how you\'re feeling today?';
    }
  }

  @override
  void onNextStep() {
    // Save note when moving to next step
    if (_currentStep == 1 && _selectedMood != null) {
      _saveMood();
    }
  }
}

class MoodOption {
  final String emoji;
  final String label;
  final Color color;
  final String? svgAsset; // New field for SVG asset path

  const MoodOption({
    required this.emoji,
    required this.label,
    required this.color,
    this.svgAsset,
  });
}

// Note: These mood options now need to be created within a widget context
// to access theme colors. Consider moving to a method that takes BuildContext.
List<MoodOption> getMoodOptions(BuildContext context) => [
      MoodOption(
          emoji: '😊',
          label: 'Grateful',
          color: Theme.of(context).colorScheme.primary),
      MoodOption(
          emoji: '😃',
          label: 'Happy',
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.9)),
      MoodOption(
          emoji: '😋',
          label: 'Neutral',
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.7)),
      MoodOption(
          emoji: '😖',
          label: 'Tired',
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5)),
      MoodOption(
          emoji: '😢',
          label: 'Sad',
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)),
      MoodOption(
          emoji: '😠',
          label: 'Angry',
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)),
    ];

// New mood options with SVG assets using proper theme colors
List<MoodOption> getMoodOptionsWithSvg(BuildContext context) => [
      MoodOption(
        emoji: '😊',
        label: 'Happy',
        color: Theme.of(context).colorScheme.secondary, // Yellow for happiness
        svgAsset: 'assets/images/mood/happy.svg',
      ),
      MoodOption(
        emoji: '🙏',
        label: 'Grateful',
        color: Colors.green, // Green for gratitude
        svgAsset: 'assets/images/mood/grateful.svg',
      ),
      MoodOption(
        emoji: '🎉',
        label: 'Excited',
        color: Theme.of(context)
            .colorScheme
            .primary, // Primary purple for excitement
        svgAsset: 'assets/images/mood/excited.svg',
      ),
      MoodOption(
        emoji: '❤️',
        label: 'Love',
        color: Colors.pink, // Pink for love
        svgAsset: 'assets/images/mood/love.svg',
      ),
      MoodOption(
        emoji: '😢',
        label: 'Sad',
        color: Colors.blue, // Blue for sadness
        svgAsset: 'assets/images/mood/sad.svg',
      ),
      MoodOption(
        emoji: '😠',
        label: 'Angry',
        color: Theme.of(context).colorScheme.error, // Red for anger
        svgAsset: 'assets/images/mood/angry.svg',
      ),
      MoodOption(
        emoji: '😰',
        label: 'Anxious',
        color: Colors.orange, // Orange for anxiety
        svgAsset: 'assets/images/mood/anxious.svg',
      ),
      MoodOption(
        emoji: '😴',
        label: 'Tired',
        color: Theme.of(context)
            .colorScheme
            .onSurface
            .withValues(alpha: 0.7), // Gray for tiredness
        svgAsset: 'assets/images/mood/tired.svg',
      ),
      MoodOption(
        emoji: '😕',
        label: 'Confused',
        color: Theme.of(context)
            .colorScheme
            .secondary, // Secondary purple for confusion
        svgAsset: 'assets/images/mood/confused.svg',
      ),
    ];

class MoodCard extends StatelessWidget {
  final MoodOption mood;
  final bool isSelected;
  final VoidCallback onTap;

  const MoodCard({
    super.key,
    required this.mood,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: isSelected ? Border.all(color: mood.color, width: 6) : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              mood.emoji,
              style: const TextStyle(fontSize: 32),
            ),
            const SizedBox(height: 8),
            Text(
              mood.label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MoodCardWithSvg extends StatelessWidget {
  final MoodOption mood;
  final bool isSelected;
  final VoidCallback onTap;
  final String? heroTag;

  const MoodCardWithSvg({
    super.key,
    required this.mood,
    required this.isSelected,
    required this.onTap,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            //   color: Theme.of(context).colorScheme.surface,
            //   borderRadius: BorderRadius.circular(20),
            border: isSelected ? Border.all(color: mood.color, width: 2) : null
            // boxShadow: isSelected
            //     ? [
            //         BoxShadow(
            //           color: mood.color.withValues(alpha: 0.3),
            //           blurRadius: 8,
            //           spreadRadius: 2,
            //         ),
            //       ]
            //     : null,
            ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (mood.svgAsset != null)
              heroTag != null
                  ? Hero(
                      tag: heroTag!,
                      child: SvgPicture.asset(
                        mood.svgAsset!,
                        width: 80,
                        height: 80,
                        placeholderBuilder: (BuildContext context) => Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Theme.of(context)
                                .colorScheme
                                .outline
                                .withValues(alpha: 0.2),
                          ),
                          child: Icon(
                            Icons.mood,
                            size: 40,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withValues(alpha: 0.5),
                          ),
                        ),
                        errorBuilder: (BuildContext context, Object error,
                            StackTrace? stackTrace) {
                          print(
                              'Error loading SVG: ${mood.svgAsset}, Error: $error');
                          return Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Theme.of(context)
                                  .colorScheme
                                  .outline
                                  .withValues(alpha: 0.2),
                            ),
                            child: Text(
                              mood.emoji,
                              style: const TextStyle(fontSize: 40),
                            ),
                          );
                        },
                      ),
                    )
                  : SvgPicture.asset(
                      mood.svgAsset!,
                      width: 80,
                      height: 80,
                      placeholderBuilder: (BuildContext context) => Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context)
                              .colorScheme
                              .outline
                              .withValues(alpha: 0.2),
                        ),
                        child: Icon(
                          Icons.mood,
                          size: 40,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.5),
                        ),
                      ),
                      errorBuilder: (BuildContext context, Object error,
                          StackTrace? stackTrace) {
                        print(
                            'Error loading SVG: ${mood.svgAsset}, Error: $error');
                        return Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Theme.of(context)
                                .colorScheme
                                .outline
                                .withValues(alpha: 0.2),
                          ),
                          child: Text(
                            mood.emoji,
                            style: const TextStyle(fontSize: 40),
                          ),
                        );
                      },
                    )
            else
              Text(
                mood.emoji,
                style: const TextStyle(fontSize: 48),
              ),
            const SizedBox(height: 8),
            Text(
              mood.label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected
                    ? mood.color
                    : Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
