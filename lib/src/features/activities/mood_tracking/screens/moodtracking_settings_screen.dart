import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_settings_screen.dart';

class MoodTrackingSettingsScreen extends BaseSettingsScreen {
  const MoodTrackingSettingsScreen({
    required super.activity,
    super.key,
    required super.routineId,
    super.onConfigChanged,
  });

  @override
  ConsumerState<MoodTrackingSettingsScreen> createState() =>
      _MoodTrackingSettingsScreenState();
}

class _MoodTrackingSettingsScreenState
    extends BaseSettingsScreenState<MoodTrackingSettingsScreen> {
  @override
  Widget buildConfigSection() {
    return config.maybeWhen(
      moodTracking: (moods, includeNote) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Mood Tracking Settings',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Include Note Field'),
            subtitle: const Text('Allow adding notes with mood entries'),
            value: includeNote,
            onChanged: (value) {
              updateConfig(ActivityConfig.moodTracking(
                moods: moods,
                includeNote: value,
              ));
            },
          ),
          const SizedBox(height: 16),
          const Text('Available Moods'),
          const SizedBox(height: 8),
          ReorderableListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: moods.asMap().entries.map((entry) {
              return ListTile(
                key: ValueKey(entry.key),
                leading: const Icon(Icons.drag_handle),
                title: Text(entry.value),
                trailing: IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () {
                    final newMoods = List<String>.from(moods)
                      ..removeAt(entry.key);
                    updateConfig(ActivityConfig.moodTracking(
                      moods: newMoods,
                      includeNote: includeNote,
                    ));
                  },
                ),
              );
            }).toList(),
            onReorder: (oldIndex, newIndex) {
              if (newIndex > oldIndex) newIndex -= 1;
              final newMoods = List<String>.from(moods);
              final mood = newMoods.removeAt(oldIndex);
              newMoods.insert(newIndex, mood);
              updateConfig(ActivityConfig.moodTracking(
                moods: newMoods,
                includeNote: includeNote,
              ));
            },
          ),
          TextButton.icon(
            onPressed: _showAddMoodDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Mood'),
          ),
        ],
      ),
      orElse: () => const SizedBox.shrink(),
    );
  }

  Future<void> _showAddMoodDialog() async {
    final controller = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Mood'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Mood (e.g., "😊 Happy")',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                Navigator.pop(context, controller.text);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );

    if (result != null) {
      config.maybeWhen(
        moodTracking: (moods, includeNote) => updateConfig(
          ActivityConfig.moodTracking(
            moods: [...moods, result],
            includeNote: includeNote,
          ),
        ),
        orElse: () => {},
      );
    }
  }
}
