// lib/src/features/activities/mood_tracking/widgets/mood_selector.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';

class MoodSelector extends StatelessWidget {
  final List<String> moods;
  final String? selectedMood;
  final ValueChanged<String?> onMoodSelected;

  const MoodSelector({
    super.key,
    required this.moods,
    required this.selectedMood,
    required this.onMoodSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 280,
          height: 280,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _getMoodColor(context, selectedMood)?.withValues(alpha: 0.3),
            boxShadow: [
              if (selectedMood != null)
                BoxShadow(
                  color: _getMoodColor(context, selectedMood)!
                      .withValues(alpha: 0.1),
                  blurRadius: 15,
                  spreadRadius: 5,
                ),
            ],
          ),
          child: Center(
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _getMoodColor(context, selectedMood)
                    ?.withValues(alpha: 0.3),
                boxShadow: [
                  if (selectedMood != null)
                    BoxShadow(
                      color: _getMoodColor(context, selectedMood)!
                          .withValues(alpha: 0.3),
                      blurRadius: 15,
                      spreadRadius: 5,
                    ),
                ],
              ),
              child: Center(
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getMoodColor(context, selectedMood),
                    boxShadow: [
                      if (selectedMood != null)
                        BoxShadow(
                          color: _getMoodColor(context, selectedMood)!,
                          blurRadius: 15,
                          spreadRadius: 5,
                        ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      _getEmoji(selectedMood),
                      style: TextStyle(fontSize: AppTypography.displayLarge),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 60),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: moods.map((mood) {
            return GestureDetector(
              onTap: () => onMoodSelected(mood == selectedMood ? null : mood),
              child: Column(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _getMoodColor(context, mood),
                      border: Border.all(
                        color: mood == selectedMood
                            ? Theme.of(context).colorScheme.onPrimary
                            : Colors.transparent,
                        width: 3,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        _getEmoji(mood),
                        style: TextStyle(fontSize: AppTypography.headlineSmall),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getMoodLabel(mood),
                    style: TextStyle(
                      fontSize: AppTypography.labelMedium,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Color? _getMoodColor(BuildContext context, String? mood) {
    if (mood == null) return null;
    if (mood.contains('Happy')) return Theme.of(context).colorScheme.primary;
    if (mood.contains('Good')) {
      return Theme.of(context).colorScheme.primary.withValues(alpha: 0.8);
    }
    if (mood.contains('Neutral')) {
      return Theme.of(context).colorScheme.primary.withValues(alpha: 0.6);
    }
    if (mood.contains('Sad')) {
      return Theme.of(context).colorScheme.primary.withValues(alpha: 0.4);
    }
    if (mood.contains('Angry')) {
      return Theme.of(context).colorScheme.primary.withValues(alpha: 0.2);
    }
    return Theme.of(context).colorScheme.outline;
  }

  String _getEmoji(String? mood) {
    if (mood == null) return '😊';
    return mood.split(' ').first;
  }

  String _getMoodLabel(String mood) {
    return mood.split(' ').last;
  }
}
