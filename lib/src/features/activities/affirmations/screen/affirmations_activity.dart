// lib/src/features/activities/affirmations/screens/affirmations_activity.dart
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/shared/mixins/routine_config_mixin.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';
import 'package:mimi_app/src/features/activities/shared/widgets/common_audio_player.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';

class AffirmationsActivity extends BaseActivity {
  const AffirmationsActivity({
    super.key,
    required super.activity,
    super.onComplete,
    super.standalone = false,
    required super.routineId,
  });

  @override
  ConsumerState<AffirmationsActivity> createState() =>
      _AffirmationsActivityState();
}

class _AffirmationsActivityState extends BaseActivityState<AffirmationsActivity>
    with RoutineConfigMixin<AffirmationsActivity> {
  AudioPlayer? _audioPlayer;

  @override
  Widget buildActivityContent(BuildContext context) {
    return currentConfig.maybeWhen(
      affirmations: (audioTracks, selectedTrack, autoPlay) {
        if (audioTracks.isEmpty) {
          return const Center(
            child: Text(
              'No affirmation tracks selected. Please configure tracks in settings.',
            ),
          );
        }

        final tracks = audioTracks
            .map((trackMap) => AudioTrack(
                  id: trackMap['id'],
                  title: trackMap['title'],
                  audioUrl: trackMap['audioUrl'],
                  artworkUrl: trackMap['artworkUrl'],
                  categoryId: trackMap['categoryId'],
                  isSelected: trackMap['isSelected'] ?? false,
                ))
            .toList();

        final selectedTrackId = selectedTrack?['id'];

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: CommonAudioPlayer(
              tracks: tracks,
              onComplete: markComplete,
              selectedTrackId: selectedTrackId,
              playerType: AudioPlayerType.affirmation,
              onPlayerCreated: (player) {
                _audioPlayer = player;
              },
            ),
          ),
        );
      },
      orElse: () => const Center(
        child: Text('Invalid affirmations configuration'),
      ),
    );
  }

  @override
  void onActivityInterrupted() {
    _audioPlayer?.dispose();
    _audioPlayer = null;
  }

  @override
  void onNextStep() {
    onActivityInterrupted();
  }

  @override
  void onPreviousStep() {
    onActivityInterrupted();
  }

  @override
  int getTotalSteps() => 1; // Single step activity

  @override
  int getCurrentStep() => 0;

  @override
  bool moveToStep(int step) => step == 0;
  @override
  IconData getActivityIcon() => Icons.record_voice_over;
}
