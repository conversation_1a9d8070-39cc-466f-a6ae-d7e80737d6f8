// lib/src/features/activities/affirmations/screens/affirmations_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/activities/affirmations/widgets/affirmations_player.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';

class AffirmationsScreen extends ConsumerWidget {
  final List<AudioTrack> tracks;
  final String? selectedTrackId;
  final bool autoPlay;
  final VoidCallback? onComplete;

  const AffirmationsScreen({
    super.key,
    required this.tracks,
    this.selectedTrackId,
    this.autoPlay = true,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Daily Affirmations'),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Selected Affirmations',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: AffirmationsPlayer(
                  tracks: tracks,
                  selectedTrackId: selectedTrackId,
                  autoPlay: autoPlay,
                  onComplete: () {
                    onComplete?.call();
                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
