// lib/src/features/activities/affirmations/screens/affirmations_settings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_settings_screen.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/track_duration_provider.dart';

// class AffirmationsSettingsScreen extends BaseSettingsScreen {
//   const AffirmationsSettingsScreen({
//     required super.activity,
//     super.key,
//   });

//   @override
//   ConsumerState<AffirmationsSettingsScreen> createState() =>
//       _AffirmationsSettingsScreenState();
// }

// class _AffirmationsSettingsScreenState
//     extends BaseSettingsScreenState<AffirmationsSettingsScreen> {
//   @override
//   Widget buildConfigSection() {
//     return config.maybeWhen(
//       affirmations: (audioAssets, selectedAudioAsset, autoPlay) => Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SwitchListTile(
//             title: const Text('Auto-play'),
//             value: autoPlay,
//             onChanged: (value) {
//               setState(() {
//                 config = ActivityConfig.affirmations(
//                   audioAssets: audioAssets,
//                   selectedAudioAsset: selectedAudioAsset,
//                   autoPlay: value,
//                 );
//               });
//             },
//           ),
//           const SizedBox(height: 24),
//           _buildTrackSelector(),
//         ],
//       ),
//       orElse: () => const SizedBox.shrink(),
//     );
//   }

//   Widget _buildTrackSelector() {
//     final categoryTracksAsync =
//         ref.watch(categoryTracksProvider('affirmations'));

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Select Affirmation Tracks',
//           style: Theme.of(context).textTheme.titleMedium,
//         ),
//         const SizedBox(height: 16),
//         categoryTracksAsync.when(
//           data: (tracks) => ListView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: tracks.length,
//             itemBuilder: (context, index) {
//               final track = tracks[index];
//               return config.maybeWhen(
//                 affirmations: (audioAssets, selectedAudioAsset, autoPlay) {
//                   final isSelected = audioAssets.contains(track.audioUrl);
//                   final isDefault = selectedAudioAsset == track.audioUrl;

//                   return ListTile(
//                     title: Text(track.title),
//                     // subtitle: Text(track.duration?.toString() ?? ''),
//                     trailing: Row(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         Checkbox(
//                           value: isSelected,
//                           onChanged: (selected) {
//                             if (selected == null) return;
//                             setState(() {
//                               final newAssets = List<String>.from(audioAssets);
//                               if (selected) {
//                                 newAssets.add(track.audioUrl);
//                               } else {
//                                 newAssets.remove(track.audioUrl);
//                               }
//                               config = ActivityConfig.affirmations(
//                                 audioAssets: newAssets,
//                                 selectedAudioAsset: isDefault && !selected
//                                     ? null
//                                     : selectedAudioAsset,
//                                 autoPlay: autoPlay,
//                               );
//                             });
//                           },
//                         ),
//                         if (isSelected)
//                           IconButton(
//                             icon: Icon(
//                               isDefault ? Icons.star : Icons.star_border,
//                             ),
//                             onPressed: () {
//                               setState(() {
//                                 config = ActivityConfig.affirmations(
//                                   audioAssets: audioAssets,
//                                   selectedAudioAsset:
//                                       isDefault ? null : track.audioUrl,
//                                   autoPlay: autoPlay,
//                                 );
//                               });
//                             },
//                           ),
//                       ],
//                     ),
//                   );
//                 },
//                 orElse: () => const SizedBox.shrink(),
//               );
//             },
//           ),
//           loading: () => const Center(child: CircularProgressIndicator()),
//           error: (error, _) => Center(child: Text('Error: $error')),
//         ),
//       ],
//     );
//   }
// }

class AffirmationsSettingsScreen extends BaseSettingsScreen {
  const AffirmationsSettingsScreen({
    required super.activity,
    super.key,
    required super.routineId,
    super.onConfigChanged,
  });

  @override
  ConsumerState<AffirmationsSettingsScreen> createState() =>
      _AffirmationsSettingsScreenState();
}

class _AffirmationsSettingsScreenState
    extends BaseSettingsScreenState<AffirmationsSettingsScreen> {
  @override
  Widget buildConfigSection() {
    return config.maybeWhen(
      affirmations: (audioTracks, selectedTrack, autoPlay) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Activity Title
            Text(
              'Affirmations Settings',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 24),
            Text(
              'Tracks',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            _buildTracksWithCheckboxes(audioTracks),
          ],
        ),
      ),
      orElse: () => const SizedBox.shrink(),
    );
  }

  Widget _buildTracksWithCheckboxes(List<Map<String, dynamic>> selectedTracks) {
    final categoryTracksAsync =
        ref.watch(categoryTracksProvider('affirmations'));

    return categoryTracksAsync.when(
      data: (tracks) {
        if (tracks.isEmpty) {
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text('No tracks available'),
          );
        }

        final selectedUrls = selectedTracks.map((t) => t['audioUrl']).toSet();

        return ListView.separated(
          primary: false,
          shrinkWrap: true,
          itemCount: tracks.length,
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final track = tracks[index];
            final isSelected = selectedUrls.contains(track.audioUrl);

            return Card(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: isSelected
                      ? Border.all(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : null,
                ),
                child: _buildTrackListTile(track, isSelected),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildTrackListTile(AudioTrack track, bool isSelected) {
    final durationAsync = ref.watch(trackDurationProvider(track));

    return ListTile(
      leading: Container(
        height: 60,
        width: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          image: track.artworkUrl != null
              ? DecorationImage(
                  image: AssetImage(track.artworkUrl!),
                  fit: BoxFit.cover,
                )
              : null,
          color: Colors.grey.shade300,
        ),
        child: track.artworkUrl == null
            ? const Icon(
                Icons.music_note,
                color: Colors.grey,
              )
            : null,
      ),
      title: Text(track.title),
      subtitle: durationAsync.when(
        data: (duration) => Text(_formatDuration(duration)),
        loading: () => const Text('Loading...'),
        error: (_, __) => const Text('--:--'),
      ),
      trailing: Checkbox(
        value: isSelected,
        onChanged: (bool? value) {
          if (value == true) {
            _addTrack(track);
          } else {
            _removeTrackByUrl(track.audioUrl);
          }
        },
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  void _addTrack(AudioTrack track) {
    final currentTracks = List<Map<String, dynamic>>.from(
      config.maybeWhen(
        affirmations: (tracks, selected, autoPlay) => tracks,
        orElse: () => <Map<String, dynamic>>[],
      ),
    );

    if (!currentTracks.any((t) => t['audioUrl'] == track.audioUrl)) {
      currentTracks.add({
        'id': track.id,
        'title': track.title,
        'audioUrl': track.audioUrl,
        'artworkUrl': track.artworkUrl,
        'categoryId': track.categoryId,
        'isSelected': true,
      });

      updateConfig(ActivityConfig.affirmations(
        audioTracks: currentTracks,
        selectedTrack: currentTracks.first,
        autoPlay: config.maybeWhen(
          affirmations: (_, __, autoPlay) => autoPlay,
          orElse: () => true,
        ),
      ));
    }
  }

  void _removeTrackByUrl(String audioUrl) {
    final currentTracks = config.maybeWhen(
      affirmations: (tracks, selected, autoPlay) =>
          List<Map<String, dynamic>>.from(tracks),
      orElse: () => <Map<String, dynamic>>[],
    );

    currentTracks.removeWhere((t) => t['audioUrl'] == audioUrl);

    updateConfig(ActivityConfig.affirmations(
      audioTracks: currentTracks,
      selectedTrack: currentTracks.isNotEmpty ? currentTracks.first : null,
      autoPlay: config.maybeWhen(
        affirmations: (_, __, autoPlay) => autoPlay,
        orElse: () => true,
      ),
    ));
  }
}
// class AffirmationsSettingsScreen extends BaseSettingsScreen {
//   const AffirmationsSettingsScreen({
//     required super.activity,
//     super.key,
//     required super.routineId,
//   });

//   @override
//   ConsumerState<AffirmationsSettingsScreen> createState() =>
//       _AffirmationsSettingsScreenState();
// }

// class _AffirmationsSettingsScreenState
//     extends BaseSettingsScreenState<AffirmationsSettingsScreen> {
//   @override
//   Widget buildConfigSection() {
//     return config.maybeWhen(
//       affirmations: (audioAssets, defaultDuration, selectedAudioAsset) =>
//           Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const SizedBox(height: 24),
//           _buildTrackSelector(audioAssets),
//         ],
//       ),
//       orElse: () => const SizedBox.shrink(),
//     );
//   }

//   Widget _buildTrackSelector(List<String> selectedAssets) {
//     final categoryTracksAsync =
//         ref.watch(categoryTracksProvider('affirmations'));

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Select Affirmation Tracks',
//           style: Theme.of(context).textTheme.titleMedium,
//         ),
//         const SizedBox(height: 16),
//         categoryTracksAsync.when(
//           data: (tracks) => ListView.separated(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: tracks.length,
//             separatorBuilder: (_, __) => const SizedBox(height: 8),
//             itemBuilder: (context, index) {
//               final track = tracks[index];
//               final isSelected = selectedAssets.contains(track.audioUrl);

//               return TrackListItem(
//                 track: track.copyWith(isSelected: isSelected),
//                 onSelect: () {
//                   setState(() {
//                     final newAssets = List<String>.from(
//                       config.maybeWhen(
//                         affirmations: (assets, duration, selected) => assets,
//                         orElse: () => [],
//                       ),
//                     );

//                     if (isSelected) {
//                       newAssets.remove(track.audioUrl);
//                     } else {
//                       newAssets.add(track.audioUrl);
//                     }

//                     config = ActivityConfig.affirmations(
//                       audioAssets: newAssets,
//                       // defaultDuration: config.maybeWhen(
//                       //   affirmations: (_, duration, __) => duration,
//                       //   orElse: () => 10,
//                       // ),
//                       selectedAudioAsset: track.audioUrl,
//                     );
//                   });
//                 },
//               );
//             },
//           ),
//           loading: () => const Center(child: CircularProgressIndicator()),
//           error: (error, _) => Center(child: Text('Error: $error')),
//         ),
//       ],
//     );
//   }
// }
