// lib/src/features/activities/affirmations/widgets/affirmations_player.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';

class AffirmationsPlayer extends ConsumerStatefulWidget {
  final List<AudioTrack> tracks;
  final String? selectedTrackId;
  final bool autoPlay;
  final Function() onComplete;
  final bool showTitle;
  final Function(AudioPlayer)? onPlayerCreated;

  const AffirmationsPlayer({
    super.key,
    required this.tracks,
    required this.onComplete,
    this.selectedTrackId,
    this.autoPlay = true,
    this.onPlayerCreated,
    this.showTitle = true,
  });

  @override
  ConsumerState<AffirmationsPlayer> createState() => _AffirmationsPlayerState();
}

class _AffirmationsPlayerState extends ConsumerState<AffirmationsPlayer> {
  late final AudioPlayer _player;
  bool _isPlaying = false;
  int _playCount = 0;
  Duration _currentDuration = Duration.zero;
  Duration _totalDuration = Duration.zero;
  AudioTrack? _currentTrack;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    widget.onPlayerCreated?.call(_player);
    _setupPlayer();
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  Future<void> _setupPlayer() async {
    // Set up event listeners
    _player.onDurationChanged.listen((duration) {
      setState(() => _totalDuration = duration);
    });

    _player.onPositionChanged.listen((position) {
      setState(() => _currentDuration = position);
    });

    _player.onPlayerComplete.listen((_) {
      _playCount++;
      if (_playCount >= 3) {
        widget.onComplete();
      } else if (widget.autoPlay) {
        _player.seek(Duration.zero);
        _player.resume();
      }
    });

    _player.onPlayerStateChanged.listen((state) {
      setState(() => _isPlaying = state == PlayerState.playing);
    });

    // Initialize with selected track if available
    if (widget.selectedTrackId != null) {
      _currentTrack = widget.tracks.firstWhere(
        (track) => track.audioUrl == widget.selectedTrackId,
        orElse: () => widget.tracks.first,
      );
    } else if (widget.tracks.isNotEmpty) {
      _currentTrack = widget.tracks.first;
    }

    if (_currentTrack != null) {
      await _player.setSourceUrl(_currentTrack!.audioUrl);
      if (widget.autoPlay) {
        await _player.resume();
      }
    }
  }

  void _togglePlayPause() async {
    if (_isPlaying) {
      await _player.pause();
    } else {
      await _player.resume();
    }
  }

  Future<void> _changeTrack(AudioTrack track) async {
    setState(() => _currentTrack = track);
    await _player.stop();
    await _player.setSourceUrl(track.audioUrl);
    _playCount = 0;
    if (widget.autoPlay) {
      await _player.resume();
    }
  }

  // @override
  // void dispose() {
  //   _player.dispose();
  //   super.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.showTitle) ...[
          Text(
            'Daily Affirmations',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 24),
        ],

        // Player Card
        Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  _currentTrack?.title ?? 'No track selected',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Times Played: $_playCount/3',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Progress Slider
        if (_currentTrack != null) ...[
          Slider(
            value: _currentDuration.inSeconds.toDouble(),
            max: _totalDuration.inSeconds.toDouble(),
            onChanged: (value) {
              _player.seek(Duration(seconds: value.toInt()));
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(_formatDuration(_currentDuration)),
                Text(_formatDuration(_totalDuration)),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Playback Controls
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: Icon(_isPlaying ? Icons.pause_circle : Icons.play_circle),
              iconSize: 64,
              onPressed: _togglePlayPause,
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Track List
        if (widget.tracks.length > 1)
          Expanded(
            child: ListView.builder(
              itemCount: widget.tracks.length,
              itemBuilder: (context, index) {
                final track = widget.tracks[index];
                final isSelected = _currentTrack?.audioUrl == track.audioUrl;

                return ListTile(
                  title: Text(track.title),
                  trailing: isSelected
                      ? const Icon(Icons.equalizer, color: Colors.blue)
                      : null,
                  selected: isSelected,
                  onTap: () => _changeTrack(track),
                );
              },
            ),
          ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
