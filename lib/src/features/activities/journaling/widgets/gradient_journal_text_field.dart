// lib/src/features/activities/journaling/widgets/gradient_journal_text_field.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';

class GradientJournalTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final bool expands;
  final int? maxLines;
  final Function(String)? onChanged;
  final FocusNode? focusNode;

  const GradientJournalTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.expands = true,
    this.maxLines,
    this.onChanged,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      controller: controller,
      focusNode: focusNode,
      hintText: hintText,
      style: CustomTextFieldStyle.withGradient,
      maxLines: maxLines,
      expands: expands,
      onChanged: onChanged,
      textAlignVertical: TextAlignVertical.top,
    );
  }
}
