// lib/src/features/activities/journaling/widgets/journal_form.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/journaling/widgets/gradient_journal_text_field.dart';

class JournalForm extends ConsumerStatefulWidget {
  final String type;
  final List<String> prompts;
  final Function(Map<String, String> responses) onComplete;

  const JournalForm({
    super.key,
    required this.type,
    required this.prompts,
    required this.onComplete,
  });

  @override
  ConsumerState<JournalForm> createState() => _JournalFormState();
}

class _JournalFormState extends ConsumerState<JournalForm> {
  late final List<TextEditingController> _controllers;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Add null check and minimum length check
    if (widget.prompts.isEmpty) {
      throw Exception('JournalForm requires at least one prompt');
    }
    _controllers = List.generate(
      widget.prompts.length,
      (_) => TextEditingController(),
    );
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _submit() {
    final responses = <String, String>{};
    for (var i = 0; i < widget.prompts.length; i++) {
      responses['prompt_$i'] = _controllers[i].text;
    }
    widget.onComplete(responses);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Question Area
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              Text(
                widget.prompts[_currentIndex],
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 200,
                child: GradientJournalTextField(
                  controller: _controllers[_currentIndex],
                  hintText: 'Enter your answer...',
                  expands: false,
                  maxLines: null,
                  onChanged: (_) => setState(() {}),
                ),
              ),
            ],
          ),
        ),

        // Navigation
        SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentIndex > 0)
                  TextButton.icon(
                    onPressed: () {
                      setState(() => _currentIndex--);
                    },
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Previous'),
                  )
                else
                  const SizedBox.shrink(),
                if (_currentIndex < widget.prompts.length - 1)
                  FilledButton.icon(
                    onPressed: _canMoveNext()
                        ? () {
                            setState(() => _currentIndex++);
                          }
                        : null,
                    label: const Text('Next'),
                    icon: const Icon(Icons.arrow_forward),
                  )
                else
                  FilledButton.icon(
                    onPressed: _canComplete() ? _submit : null,
                    label: const Text('Complete'),
                    icon: const Icon(Icons.check),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool _canMoveNext() {
    return _controllers[_currentIndex].text.trim().isNotEmpty;
  }

  bool _canComplete() {
    return _controllers.every((c) => c.text.trim().isNotEmpty);
  }
}
