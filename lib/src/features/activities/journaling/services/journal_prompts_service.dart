// lib/src/features/activities/journaling/services/journal_prompts_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'journal_prompts_service.g.dart';

@riverpod
JournalPromptsService journalPromptsService(JournalPromptsServiceRef ref) {
  return JournalPromptsService();
}

class JournalPromptsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'journal_prompts';

  // Get all prompts
  Future<List<String>> getAllPrompts() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: false)
          .get();

      final prompts = querySnapshot.docs
          .map((doc) => doc.data()['text'] as String)
          .toList();

      // If no prompts found, return fallback prompts
      if (prompts.isEmpty) {
        return _getFallbackPrompts();
      }

      return prompts;
    } catch (e) {
      // If Firebase fails, return fallback prompts
      return _getFallbackPrompts();
    }
  }

  // Stream of all prompts for real-time updates
  Stream<List<String>> watchAllPrompts() {
    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) {
      if (snapshot.docs.isEmpty) {
        return _getFallbackPrompts();
      }
      return snapshot.docs.map((doc) => doc.data()['text'] as String).toList();
    }).handleError((error) {
      // Return fallback prompts on error
      return _getFallbackPrompts();
    });
  }

  // Get all prompts with metadata (for admin purposes)
  Future<List<Map<String, dynamic>>> getAllPromptsWithMetadata() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: false)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'text': data['text'] as String,
          'createdAt': data['createdAt'],
        };
      }).toList();
    } catch (e) {
      return [];
    }
  }

  // Add a new prompt
  Future<void> addPrompt(String text) async {
    await _firestore.collection(_collection).add({
      'text': text,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  // Delete a prompt
  Future<void> deletePrompt(String promptId) async {
    await _firestore.collection(_collection).doc(promptId).delete();
  }

  // Update a prompt
  Future<void> updatePrompt(String promptId, String text) async {
    await _firestore.collection(_collection).doc(promptId).update({
      'text': text,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  // Fallback prompts when Firebase is unavailable
  List<String> _getFallbackPrompts() {
    return [
      'What are you grateful for today?',
      'What are your main goals for today?',
      'How did you sleep last night?',
      'What is your intention for today?',
      'What can you do today to take care of yourself?',
      'How is your energy level right now?',
      'What has been your biggest accomplishment so far today?',
      'What challenges have you faced?',
      'What do you need right now to feel more balanced?',
      'What are your priorities for the rest of the day?',
      'What were three good things that happened today?',
      'What challenged you today and what did you learn?',
      'How did you take care of yourself today?',
      'What are you looking forward to tomorrow?',
      'Is there anything you need to let go of before sleep?',
    ];
  }

  // Check if Firebase prompts are available
  Future<bool> hasFirebasePrompts() async {
    try {
      final querySnapshot =
          await _firestore.collection(_collection).limit(1).get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Seed initial prompts to Firebase (for first-time setup)
  Future<void> seedInitialPrompts() async {
    try {
      // Check if prompts already exist
      final existingPrompts =
          await _firestore.collection(_collection).limit(1).get();

      if (existingPrompts.docs.isNotEmpty) {
        return; // Prompts already exist
      }

      // Add fallback prompts to Firebase
      final batch = _firestore.batch();

      final initialPrompts = _getFallbackPrompts();

      for (final prompt in initialPrompts) {
        final docRef = _firestore.collection(_collection).doc();
        batch.set(docRef, {
          'text': prompt,
          'createdAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
    } catch (e) {
      // Silently fail if seeding fails
    }
  }
}
