// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journal_prompts_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$journalPromptsServiceHash() =>
    r'e3dd0f9b699080708a6ed93c2d6f7d76d5492118';

/// See also [journalPromptsService].
@ProviderFor(journalPromptsService)
final journalPromptsServiceProvider =
    AutoDisposeProvider<JournalPromptsService>.internal(
  journalPromptsService,
  name: r'journalPromptsServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalPromptsServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalPromptsServiceRef
    = AutoDisposeProviderRef<JournalPromptsService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
