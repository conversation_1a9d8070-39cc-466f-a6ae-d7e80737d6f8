// // lib/src/features/journal/widgets/activities/journaling_activity.dart
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
// import 'package:mimi_app/src/features/activities/shared/providers/activity_data_provider.dart';

// import '../shared/screens/base_activity.dart';
// import 'package:mimi_app/src/features/journal/service/journal_question_service.dart';

// class JournalingActivity extends BaseActivity {
//   const JournalingActivity({
//     super.key,
//     required super.activity,
//     super.onComplete,
//     super.standalone = false,
//   });

//   @override
//   ConsumerState<JournalingActivity> createState() => _JournalingActivityState();
// }

// class _JournalingActivityState extends BaseActivityState<JournalingActivity> {
//   late final List<TextEditingController> _controllers;
//   late final List<String> _prompts;
//   int _currentPromptIndex = 0;
//   late final ActivityDataNotifier _dataNotifier;
//   String? _selectedMood;

//   JournalingConfig get _config => widget.activity.config.maybeWhen(
//         journaling: (prompts, includeDate, includeMood) => JournalingConfig(
//           prompts: prompts,
//           includeDate: includeDate,
//           includeMood: includeMood,
//         ),
//         orElse: () => throw Exception('Invalid config type'),
//       );

//   @override
//   void initState() {
//     super.initState();
//     _prompts = _config.prompts.isEmpty
//         ? JournalQuestionsService.getQuestionsByType('morning')
//         : _config.prompts;
//     _controllers = List.generate(
//       _prompts.length,
//       (index) => TextEditingController(),
//     );
//     _dataNotifier = ref.read(
//       activityDataNotifierProvider(widget.activity.id, DateTime.now()).notifier,
//     );
//     _loadExistingEntry();
//   }

//   Future<void> _loadExistingEntry() async {
//     final data = await _dataNotifier.build(widget.activity.id, DateTime.now());
//     if (data != null) {
//       setState(() {
//         final responses = data.data['responses'] as Map<String, dynamic>;
//         for (var i = 0; i < _prompts.length; i++) {
//           _controllers[i].text = responses['prompt_$i'] as String? ?? '';
//         }
//         if (_config.includeMood) {
//           _selectedMood = data.data['mood'] as String?;
//         }
//       });
//     }
//   }

//   Future<void> _saveEntry() async {
//     final responses = <String, String>{};
//     for (var i = 0; i < _prompts.length; i++) {
//       responses['prompt_$i'] = _controllers[i].text;
//     }

//     await _dataNotifier.saveActivityData({
//       'responses': responses,
//       if (_config.includeMood && _selectedMood != null) 'mood': _selectedMood,
//       if (_config.includeDate) 'date': DateTime.now().toIso8601String(),
//     });
//     markComplete();
//   }

//   @override
//   void dispose() {
//     for (var controller in _controllers) {
//       controller.dispose();
//     }
//     super.dispose();
//   }

//   void _nextPrompt() {
//     if (_currentPromptIndex < _prompts.length - 1) {
//       setState(() => _currentPromptIndex++);
//     } else {
//       _saveEntry();
//     }
//   }

//   void _previousPrompt() {
//     if (_currentPromptIndex > 0) {
//       setState(() => _currentPromptIndex--);
//     }
//   }

//   bool _canMoveNext() {
//     return _controllers[_currentPromptIndex].text.isNotEmpty;
//   }

//   // bool _canMoveNext() {
//   //   if (_currentPromptIndex == 0 &&
//   //       _config.includeMood &&
//   //       _selectedMood == null) {
//   //     return false;
//   //   }
//   //   return _controllers[_currentPromptIndex].text.isNotEmpty;
//   // }

//   @override
//   IconData getActivityIcon() => Icons.edit_note;

//   @override
//   Widget buildActivityContent(BuildContext context) {
//     return widget.activity.config.when(
//       journaling: (prompts, includeDate, _) {
//         final theme = Theme.of(context);

//         return Column(
//           children: [
//             // Progress Indicator
//             Padding(
//               padding: const EdgeInsets.all(16),
//               child: Row(
//                 children: List.generate(
//                   _prompts.length,
//                   (index) => Expanded(
//                     child: Container(
//                       height: 4,
//                       margin: const EdgeInsets.symmetric(horizontal: 2),
//                       decoration: BoxDecoration(
//                         color: index <= _currentPromptIndex
//                             ? theme.primaryColor
//                             : theme.disabledColor.withOpacity(0.2),
//                         borderRadius: BorderRadius.circular(2),
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),

//             // Question and Answer Area
//             Expanded(
//               child: Padding(
//                 padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       '${_currentPromptIndex + 1}. ${_prompts[_currentPromptIndex]}',
//                       style: theme.textTheme.titleLarge?.copyWith(
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                     const SizedBox(height: 16),
//                     Expanded(
//                       child: Container(
//                         decoration: BoxDecoration(
//                           border: Border.all(color: theme.dividerColor),
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                         child: TextField(
//                           controller: _controllers[_currentPromptIndex],
//                           decoration: const InputDecoration(
//                             hintText: 'Enter your answer here...',
//                             contentPadding: EdgeInsets.all(16),
//                             border: InputBorder.none,
//                           ),
//                           maxLines: null,
//                           expands: true,
//                           textAlignVertical: TextAlignVertical.top,
//                           onChanged: (_) => setState(() {}),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),

//             // Navigation Buttons
//             Container(
//               padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 border: Border(
//                   top: BorderSide(
//                     color: theme.dividerColor.withOpacity(0.5),
//                   ),
//                 ),
//               ),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   if (_currentPromptIndex > 0)
//                     SizedBox(
//                       width: 120,
//                       child: TextButton.icon(
//                         onPressed: _previousPrompt,
//                         icon: const Icon(Icons.chevron_left),
//                         label: const Text('Previous'),
//                       ),
//                     )
//                   else
//                     const SizedBox(width: 120),
//                   Text(
//                     '${_currentPromptIndex + 1} of ${_prompts.length}',
//                     style: theme.textTheme.bodyLarge,
//                   ),
//                   SizedBox(
//                     width: 120,
//                     child: _currentPromptIndex < _prompts.length - 1
//                         ? TextButton.icon(
//                             onPressed: _canMoveNext() ? _nextPrompt : null,
//                             label: const Text('Next'),
//                             icon: const Icon(Icons.chevron_right),
//                           )
//                         : TextButton.icon(
//                             onPressed: _canMoveNext() ? _nextPrompt : null,
//                             label: const Text('Finish'),
//                             icon: const Icon(Icons.check),
//                           ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         );
//       },
//       meditation: (_, __, ___) => throw Exception('Invalid config type'),
//       breathwork: (_, __, ___) => throw Exception('Invalid config type'),
//       affirmations: (_, __, ___) => throw Exception('Invalid config type'),
//       moodTracking: (_, __) => throw Exception('Invalid config type'),
//     );
//   }
// }
