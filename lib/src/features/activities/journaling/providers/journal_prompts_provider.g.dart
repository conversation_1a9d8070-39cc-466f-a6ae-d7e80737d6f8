// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journal_prompts_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allJournalPromptsHash() => r'1880d386f11a2bff190bbfc7c57def16710c0850';

/// See also [allJournalPrompts].
@ProviderFor(allJournalPrompts)
final allJournalPromptsProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
  allJournalPrompts,
  name: r'allJournalPromptsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$allJournalPromptsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllJournalPromptsRef = AutoDisposeFutureProviderRef<List<String>>;
String _$journalPromptsStreamHash() =>
    r'041891dc2d6aa38fe0a4718a4efcc8a5c7eab226';

/// See also [journalPromptsStream].
@ProviderFor(journalPromptsStream)
final journalPromptsStreamProvider =
    AutoDisposeStreamProvider<List<String>>.internal(
  journalPromptsStream,
  name: r'journalPromptsStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalPromptsStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalPromptsStreamRef = AutoDisposeStreamProviderRef<List<String>>;
String _$allJournalPromptsWithMetadataHash() =>
    r'1c654faf221bf9f01fbc287cfae52bba73f19a8c';

/// See also [allJournalPromptsWithMetadata].
@ProviderFor(allJournalPromptsWithMetadata)
final allJournalPromptsWithMetadataProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
  allJournalPromptsWithMetadata,
  name: r'allJournalPromptsWithMetadataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$allJournalPromptsWithMetadataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllJournalPromptsWithMetadataRef
    = AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$hasFirebasePromptsHash() =>
    r'644c2bf1eb7565e869764a18f7c960f604353c68';

/// See also [hasFirebasePrompts].
@ProviderFor(hasFirebasePrompts)
final hasFirebasePromptsProvider = AutoDisposeFutureProvider<bool>.internal(
  hasFirebasePrompts,
  name: r'hasFirebasePromptsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasFirebasePromptsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HasFirebasePromptsRef = AutoDisposeFutureProviderRef<bool>;
String _$journalPromptsSeederHash() =>
    r'f034c68dd66fdc8d931582fc3c34f46af6895983';

/// See also [JournalPromptsSeeder].
@ProviderFor(JournalPromptsSeeder)
final journalPromptsSeederProvider =
    AutoDisposeAsyncNotifierProvider<JournalPromptsSeeder, bool>.internal(
  JournalPromptsSeeder.new,
  name: r'journalPromptsSeederProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalPromptsSeederHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JournalPromptsSeeder = AutoDisposeAsyncNotifier<bool>;
String _$journalPromptsManagerHash() =>
    r'45afd743bea3391322efecc3ec7b2ed06bad6806';

/// See also [JournalPromptsManager].
@ProviderFor(JournalPromptsManager)
final journalPromptsManagerProvider =
    AutoDisposeAsyncNotifierProvider<JournalPromptsManager, void>.internal(
  JournalPromptsManager.new,
  name: r'journalPromptsManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalPromptsManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JournalPromptsManager = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
