// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journal_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$journalingDataNotifierHash() =>
    r'fb0f8a58583caeeadd368c521371da2757165adb';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$JournalingDataNotifier
    extends BuildlessAutoDisposeAsyncNotifier<ActivityData?> {
  late final int activityId;
  late final DateTime date;

  FutureOr<ActivityData?> build(
    int activityId,
    DateTime date,
  );
}

/// See also [JournalingDataNotifier].
@ProviderFor(JournalingDataNotifier)
const journalingDataNotifierProvider = JournalingDataNotifierFamily();

/// See also [JournalingDataNotifier].
class JournalingDataNotifierFamily extends Family<AsyncValue<ActivityData?>> {
  /// See also [JournalingDataNotifier].
  const JournalingDataNotifierFamily();

  /// See also [JournalingDataNotifier].
  JournalingDataNotifierProvider call(
    int activityId,
    DateTime date,
  ) {
    return JournalingDataNotifierProvider(
      activityId,
      date,
    );
  }

  @override
  JournalingDataNotifierProvider getProviderOverride(
    covariant JournalingDataNotifierProvider provider,
  ) {
    return call(
      provider.activityId,
      provider.date,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'journalingDataNotifierProvider';
}

/// See also [JournalingDataNotifier].
class JournalingDataNotifierProvider
    extends AutoDisposeAsyncNotifierProviderImpl<JournalingDataNotifier,
        ActivityData?> {
  /// See also [JournalingDataNotifier].
  JournalingDataNotifierProvider(
    int activityId,
    DateTime date,
  ) : this._internal(
          () => JournalingDataNotifier()
            ..activityId = activityId
            ..date = date,
          from: journalingDataNotifierProvider,
          name: r'journalingDataNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$journalingDataNotifierHash,
          dependencies: JournalingDataNotifierFamily._dependencies,
          allTransitiveDependencies:
              JournalingDataNotifierFamily._allTransitiveDependencies,
          activityId: activityId,
          date: date,
        );

  JournalingDataNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.activityId,
    required this.date,
  }) : super.internal();

  final int activityId;
  final DateTime date;

  @override
  FutureOr<ActivityData?> runNotifierBuild(
    covariant JournalingDataNotifier notifier,
  ) {
    return notifier.build(
      activityId,
      date,
    );
  }

  @override
  Override overrideWith(JournalingDataNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: JournalingDataNotifierProvider._internal(
        () => create()
          ..activityId = activityId
          ..date = date,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        activityId: activityId,
        date: date,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<JournalingDataNotifier, ActivityData?>
      createElement() {
    return _JournalingDataNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is JournalingDataNotifierProvider &&
        other.activityId == activityId &&
        other.date == date;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, activityId.hashCode);
    hash = _SystemHash.combine(hash, date.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin JournalingDataNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<ActivityData?> {
  /// The parameter `activityId` of this provider.
  int get activityId;

  /// The parameter `date` of this provider.
  DateTime get date;
}

class _JournalingDataNotifierProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<JournalingDataNotifier,
        ActivityData?> with JournalingDataNotifierRef {
  _JournalingDataNotifierProviderElement(super.provider);

  @override
  int get activityId => (origin as JournalingDataNotifierProvider).activityId;
  @override
  DateTime get date => (origin as JournalingDataNotifierProvider).date;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
