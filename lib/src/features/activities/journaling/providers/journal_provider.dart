// lib/src/features/activities/journaling/providers/journaling_provider.dart
import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_data.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'journal_provider.g.dart';

@riverpod
class JournalingDataNotifier extends _$JournalingDataNotifier {
  @override
  Future<ActivityData?> build(int activityId, DateTime date) async {
    final db = ref.watch(journalDatabaseProvider);
    final records = await db.getActivityRecordsForDate(activityId, date);
    return records.isNotEmpty ? records.first : null;
  }

  Future<void> saveEntry({
    required Map<String, String> responses,
    String? mood,
    bool includeDate = true,
  }) async {
    final data = {
      'responses': responses,
      if (mood != null) 'mood': mood,
      if (includeDate) 'date': DateTime.now().toIso8601String(),
    };

    final db = ref.read(journalDatabaseProvider);
    await db.saveActivityRecord(
      ActivityRecordsCompanion.insert(
        activityId: (activityId),
        date: (date),
        data: (json.encode(data)),
        status: ('completed'),
        completedAt: Value(DateTime.now()),
      ),
    );
    ref.invalidateSelf();
  }
}
