// lib/src/features/activities/journaling/providers/journal_prompts_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../services/journal_prompts_service.dart';

part 'journal_prompts_provider.g.dart';

// Provider for getting all prompts (async)
@riverpod
Future<List<String>> allJournalPrompts(AllJournalPromptsRef ref) async {
  final service = ref.watch(journalPromptsServiceProvider);
  return service.getAllPrompts();
}

// Provider for watching all prompts (stream)
@riverpod
Stream<List<String>> journalPromptsStream(JournalPromptsStreamRef ref) {
  final service = ref.watch(journalPromptsServiceProvider);
  return service.watchAllPrompts();
}

// Provider for all prompts with metadata (for admin purposes)
@riverpod
Future<List<Map<String, dynamic>>> allJournalPromptsWithMetadata(
    AllJournalPromptsWithMetadataRef ref) async {
  final service = ref.watch(journalPromptsServiceProvider);
  return service.getAllPromptsWithMetadata();
}

// Provider to check if Firebase prompts are available
@riverpod
Future<bool> hasFirebasePrompts(HasFirebasePromptsRef ref) async {
  final service = ref.watch(journalPromptsServiceProvider);
  return service.hasFirebasePrompts();
}

// Provider for seeding initial prompts
@riverpod
class JournalPromptsSeeder extends _$JournalPromptsSeeder {
  @override
  Future<bool> build() async {
    // Auto-seed prompts when provider is first accessed
    final service = ref.watch(journalPromptsServiceProvider);
    await service.seedInitialPrompts();
    return true;
  }

  Future<void> seedPrompts() async {
    final service = ref.watch(journalPromptsServiceProvider);
    await service.seedInitialPrompts();
    ref.invalidateSelf();
  }
}

// Provider for managing prompts (CRUD operations)
@riverpod
class JournalPromptsManager extends _$JournalPromptsManager {
  @override
  FutureOr<void> build() {
    // Initialize the manager
  }

  Future<void> addPrompt(String text) async {
    state = const AsyncValue.loading();
    try {
      final service = ref.read(journalPromptsServiceProvider);
      await service.addPrompt(text);

      // Invalidate related providers to refresh data
      ref.invalidate(allJournalPromptsProvider);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deletePrompt(String promptId) async {
    state = const AsyncValue.loading();
    try {
      final service = ref.read(journalPromptsServiceProvider);
      await service.deletePrompt(promptId);

      // Invalidate related providers to refresh data
      ref.invalidate(allJournalPromptsProvider);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updatePrompt(String promptId, String text) async {
    state = const AsyncValue.loading();
    try {
      final service = ref.read(journalPromptsServiceProvider);
      await service.updatePrompt(promptId, text);

      // Invalidate related providers to refresh data
      ref.invalidate(allJournalPromptsProvider);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
