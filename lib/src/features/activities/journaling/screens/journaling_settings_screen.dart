// lib/src/features/activities/journaling/screens/journaling_settings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/features/activities/journaling/providers/journal_prompts_provider.dart';

import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';

import 'package:mimi_app/src/features/activities/shared/screens/base_settings_screen.dart';

class JournalingSettingsScreen extends BaseSettingsScreen {
  const JournalingSettingsScreen({
    required super.activity,
    super.key,
    required super.routineId,
    super.onConfigChanged,
  });

  @override
  ConsumerState<JournalingSettingsScreen> createState() =>
      _JournalingSettingsScreenState();
}

// lib/src/features/activities/journaling/screens/journaling_settings_screen.dart
class _JournalingSettingsScreenState
    extends BaseSettingsScreenState<JournalingSettingsScreen> {
  List<String> get activePrompts => config.maybeWhen(
        journaling: (
          prompts,
          _,
        ) =>
            prompts,
        orElse: () => [],
      );

  @override
  void initState() {
    super.initState();
    // Seed initial prompts when screen loads
    ref.read(journalPromptsSeederProvider);
  }

  @override
  Widget build(BuildContext context) {
    // When used in wizard context, don't show the scaffold wrapper
    if (widget.onConfigChanged != null) {
      // In wizard context - return config section with FAB in a Stack
      return SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  buildConfigSection(),
                  const SizedBox(height: 80), // Space for FAB
                ],
              ),
            ),
            Positioned(
              bottom: 16,
              right: 16,
              child: FloatingActionButton(
                onPressed: _showAddPromptDialog,
                tooltip: 'Add Custom Prompt',
                child: const Icon(Icons.add),
              ),
            ),
          ],
        ),
      );
    }

    String saveButtonText = 'Save';
    if (widget.navArgs != null) {
      if (widget.navArgs!.currentActivityIndex <
          widget.navArgs!.selectedActivities.length - 1) {
        saveButtonText = 'Save & Continue';
      } else {
        saveButtonText = 'Save & Finish';
      }
    }

    // When used standalone (not in wizard), show full scaffold with FAB
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.activity.name} Settings'),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        actions: [
          if (isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: CircularProgressIndicator(),
              ),
            )
          else
            TextButton(
              onPressed: saveSettings,
              child: Text(saveButtonText),
            ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Activity Name',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              buildConfigSection(),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddPromptDialog,
        tooltip: 'Add Custom Prompt',
        child: const Icon(Icons.add),
      ),
    );
  }

  @override
  Widget buildConfigSection() {
    return config.maybeWhen(
      journaling: (prompts, includeDate) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Activity Title
            Text(
              'Journal Settings',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 24),

            // Prompts Section
            _buildSectionHeader('Prompts'),
            Consumer(
              builder: (context, ref, child) {
                final promptsAsync = ref.watch(allJournalPromptsProvider);

                return promptsAsync.when(
                  data: (allPrompts) {
                    if (allPrompts.isEmpty) {
                      return const Padding(
                        padding: EdgeInsets.symmetric(vertical: 16),
                        child: Text('No prompts available'),
                      );
                    }

                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: allPrompts.length,
                      itemBuilder: (context, index) {
                        final prompt = allPrompts[index];
                        final isSelected = activePrompts.contains(prompt);

                        return Card(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: isSelected
                                  ? Border.all(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      width: 2,
                                    )
                                  : null,
                            ),
                            child: CheckboxListTile(
                              title: Text(prompt),
                              value: isSelected,
                              onChanged: (bool? value) {
                                if (value == true) {
                                  _addPrompt(prompt);
                                } else {
                                  _removePromptByText(prompt);
                                }
                              },
                            ),
                          ),
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Text('Error loading prompts: $error'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      orElse: () => const SizedBox.shrink(),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  void _addPrompt(String prompt) {
    config.maybeWhen(
      journaling: (
        prompts,
        includeDate,
      ) {
        if (!prompts.contains(prompt)) {
          updateConfig(ActivityConfig.journaling(
            prompts: [...prompts, prompt],
            includeDate: true, // Always set to true
          ));
          // Notify parent of config change if callback is provided
          if (widget.onConfigChanged != null) {
            widget.onConfigChanged!(ActivityConfig.journaling(
              prompts: [...prompts, prompt],
              includeDate: true, // Always set to true
            ));
          }
        }
      },
      orElse: () {},
    );
  }

  void _removePromptByText(String prompt) {
    config.maybeWhen(
      journaling: (
        prompts,
        includeDate,
      ) {
        final newPrompts = List<String>.from(prompts)..remove(prompt);
        updateConfig(ActivityConfig.journaling(
          prompts: newPrompts,
          includeDate: true, // Always set to true
        ));
        // Notify parent of config change if callback is provided
        if (widget.onConfigChanged != null) {
          widget.onConfigChanged!(ActivityConfig.journaling(
            prompts: newPrompts,
            includeDate: true, // Always set to true
          ));
        }
      },
      orElse: () {},
    );
  }

  Future<void> _showAddPromptDialog() async {
    final controller = TextEditingController();

    final prompt = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Custom Prompt'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter your prompt',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                Navigator.pop(context, controller.text);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );

    if (prompt != null && prompt.isNotEmpty) {
      _addPrompt(prompt);
    }
  }
}
