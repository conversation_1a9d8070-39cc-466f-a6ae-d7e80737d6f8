// lib/src/features/activities/journaling/screens/journal_activity.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/activities/shared/providers/activity_data_provider.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';
import 'package:mimi_app/src/features/activities/shared/mixins/routine_config_mixin.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/features/activities/journaling/widgets/gradient_journal_text_field.dart';
import 'dart:convert';

class JournalActivityScreen extends BaseActivity {
  const JournalActivityScreen({
    super.key,
    required Activity activity,
    required int routineId,
    VoidCallback? onComplete,
    bool standalone = false,
  }) : super(
          activity: activity,
          routineId: routineId,
          onComplete: onComplete,
          standalone: standalone,
        );

  @override
  _JournalActivityScreenState createState() => _JournalActivityScreenState();
}

class _JournalActivityScreenState
    extends BaseActivityState<JournalActivityScreen>
    with RoutineConfigMixin<JournalActivityScreen> {
  List<String> _prompts = [];
  final List<TextEditingController> _controllers = [];
  final List<FocusNode> _focusNodes = [];
  int _currentQuestionIndex = 0;
  bool _hasBeenInitialized = false;

  JournalingConfig get _config => currentConfig.maybeWhen(
        journaling: (prompts, includeDate) => JournalingConfig(
          prompts: prompts,
          includeDate: includeDate,
        ),
        orElse: () => throw Exception('Invalid config type'),
      );

  @override
  Widget build(BuildContext context) {
    // If still loading config, show loading indicator
    if (isLoadingConfig) {
      return super.build(context);
    }

    // Once config is loaded, initialize prompts if not done yet
    if (_prompts.isEmpty && !isLoadingConfig) {
      _initializePrompts();
    }

    // Call the mixin's build method after initialization
    return super.build(context);
  }

  void _initializePrompts() {
    print('=== JOURNALING INITIALIZATION START ===');
    _prompts = _config.prompts;
    _controllers.clear();
    _focusNodes.clear();
    _controllers.addAll(
      List.generate(_prompts.length, (_) => TextEditingController()),
    );
    _focusNodes.addAll(
      List.generate(_prompts.length, (_) => FocusNode()),
    );

    // Ensure we start at the first prompt
    _currentQuestionIndex = 0;
    _hasBeenInitialized =
        false; // Explicitly set to false during initialization

    print('Initialized journaling activity with ${_prompts.length} prompts');
    print('Starting at question index: $_currentQuestionIndex');
    print('Has been initialized: $_hasBeenInitialized');
    for (int i = 0; i < _prompts.length; i++) {
      print('Prompt $i: ${_prompts[i]}');
    }

    // Auto-focus the first text field after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_focusNodes.isNotEmpty) {
        _focusNodes[0].requestFocus();
      }
    });

    // Load any existing responses for this activity
    _loadExistingResponses();
    print('=== JOURNALING INITIALIZATION END ===');
  }

  Future<void> _loadExistingResponses() async {
    try {
      final db = ref.read(journalDatabaseProvider);
      final records = await db.getActivityRecordsForDate(
          widget.activity.id, DateTime.now());

      if (records.isNotEmpty) {
        final data = records.first.data;
        if (data.containsKey('responses')) {
          final responses = data['responses'];
          if (responses is Map<String, dynamic>) {
            for (int i = 0; i < _prompts.length; i++) {
              final key = 'prompt_$i';
              if (responses.containsKey(key)) {
                _controllers[i].text = responses[key].toString();
                print(
                    'Loaded existing response for prompt $i: ${_controllers[i].text}');
              }
            }
          }
        }
      }
    } catch (e) {
      print('Error loading existing responses: $e');
    }
  }

  @override
  int getTotalSteps() => _prompts.length;

  @override
  int getCurrentStep() => _currentQuestionIndex;

  @override
  bool moveToStep(int step) {
    print('Moving to step $step of ${_prompts.length} total prompts');
    print('Current question index: $_currentQuestionIndex');
    print('Has been initialized: $_hasBeenInitialized');

    // Check if we're trying to move beyond the available prompts
    if (step >= _prompts.length) {
      print('Reached end of prompts, submitting');
      submit();
      return false; // This tells CheckinFlow to move to next activity
    }

    // For valid step indices, move to that step
    if (step >= 0 && step < _prompts.length) {
      print('Setting current question index to $step');

      // Only save the current response if we're actually moving to a different step
      // and this isn't the initial setup (step 0 when not initialized)
      bool isInitialSetup = !_hasBeenInitialized && step == 0;
      if (!isInitialSetup &&
          _hasBeenInitialized &&
          step != _currentQuestionIndex &&
          _currentQuestionIndex >= 0) {
        print('Saving current response before moving');
        _saveCurrentResponse();
      }

      setState(() {
        _currentQuestionIndex = step;
        if (!isInitialSetup) {
          _hasBeenInitialized =
              true; // Mark as initialized after first actual move
        }
      });

      // Auto-focus the text field for the new step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (step < _focusNodes.length) {
          _focusNodes[step].requestFocus();
        }
      });

      print('Successfully moved to step $step');
      print('Is initial setup: $isInitialSetup');

      // Always return true for valid steps - the checkin flow should not auto-advance
      // based on this return value for journal activities
      return true;
    }

    return false;
  }

  // Helper method to save the current response without completing the activity
  Future<void> _saveCurrentResponse() async {
    // Get the text directly from the controller
    final String text = _controllers[_currentQuestionIndex].text;
    final String trimmedText = text.trim();

    // For debugging - don't skip saving even if empty
    final currentPromptKey = 'prompt_$_currentQuestionIndex';
    print(
        'Saving current response for $currentPromptKey: "$text" (trimmed: "$trimmedText")');

    // Get existing responses if any
    final db = ref.read(journalDatabaseProvider);
    final records =
        await db.getActivityRecordsForDate(widget.activity.id, DateTime.now());

    Map<String, String> responses = {};

    // Find and merge ALL existing responses from previous saves
    // Look through all records and merge their responses
    for (final record in records) {
      try {
        final data = record.data;
        if (data.containsKey('responses')) {
          final existingResponses = data['responses'];
          if (existingResponses is Map<String, dynamic>) {
            print(
                'Found existing responses in record ${record.id}: ${existingResponses.keys.toList()}');
            // Merge into our responses map
            existingResponses.forEach((key, value) {
              responses[key] = value.toString();
            });
          }
        }
      } catch (e) {
        print('Error processing record: $e');
      }
    }

    // Verify the text value before saving
    print('Text being saved: "$text"');

    // Add or update the current response with the correct key and value
    responses[currentPromptKey] = text;

    print('Saving response data: $responses');
    print('Response keys: ${responses.keys.toList()}');

    try {
      // Save back to the database
      final saveData = {
        'responses': responses,
        'date': DateTime.now().toIso8601String(),
      };

      print('Full data being saved: $saveData');

      await ref
          .read(activityDataNotifierProvider(widget.activity.id, DateTime.now())
              .notifier)
          .saveActivityData(saveData);
      print('Successfully saved response for $currentPromptKey: "$text"');
      print('All saved responses: ${responses.keys.toList()}');
    } catch (e) {
      print('Error saving response: $e');
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  bool moveToQuestion(int index) {
    if (index >= 0 && index < _prompts.length) {
      // Only save current response if we're actually moving to a different question
      // and this isn't the initial setup
      if (_hasBeenInitialized &&
          index != _currentQuestionIndex &&
          _currentQuestionIndex >= 0) {
        _saveCurrentResponse();
      }
      setState(() {
        _currentQuestionIndex = index;
        _hasBeenInitialized = true;
      });
      return true;
    }
    return false;
  }

  @override
  IconData getActivityIcon() => Icons.edit;

  @override
  Widget buildActivityContent(BuildContext context) {
    if (isLoadingConfig) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_prompts.isEmpty && widget.standalone) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'No journaling prompts configured for this activity. Please add prompts in settings.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
      );
    } else if (_prompts.isEmpty && !widget.standalone) {
      // If not standalone and no prompts, show a message but don't auto-complete
      // Auto-completion was causing the journaling activity to skip the first prompt
      print('WARNING: Journaling activity has no prompts configured');
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'No journaling prompts configured for this activity.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
      );
    }

    // Ensure _currentQuestionIndex is valid
    final safeCurrentQuestionIndex =
        _currentQuestionIndex.clamp(0, _prompts.length - 1);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              _prompts[safeCurrentQuestionIndex],
              //  style: Theme.of(context).textTheme.titleLarge,
              style: Theme.of(context).textTheme.headlineMedium,
              textAlign: TextAlign.start,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.5,
            child: GradientJournalTextField(
              controller: _controllers[safeCurrentQuestionIndex],
              focusNode: _focusNodes.isNotEmpty
                  ? _focusNodes[safeCurrentQuestionIndex]
                  : null,
              hintText: 'Add a Reflection',
            ),
          ),
          if (widget.standalone) // Only show save button in standalone mode
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: () {
                  _saveCurrentResponse().then((_) {
                    // Optionally, provide feedback to the user that it's saved
                    if (mounted) {
                      showSuccessToast(
                        context,
                        title: 'Success',
                        description: 'Journal entry saved!',
                      );
                    }
                    if (widget.onComplete != null) {
                      widget.onComplete!();
                    }
                  });
                },
                child: const Text('Save Entry'),
              ),
            ),
        ],
      ),
    );
  }

  void submit() async {
    // First ensure current response is saved
    await _saveCurrentResponse();

    // Create a Map with all responses, including ones with empty values
    Map<String, String> allResponses = {};

    // Debug - print prompt count
    print('Total prompts: ${_prompts.length}');
    for (int i = 0; i < _prompts.length; i++) {
      print('Prompt $i: ${_prompts[i]}');
    }

    // First gather any existing responses from the database
    final db = ref.read(journalDatabaseProvider);
    final records =
        await db.getActivityRecordsForDate(widget.activity.id, DateTime.now());

    // Merge responses from ALL existing records
    for (var record in records) {
      try {
        final data = record.data;
        if (data.containsKey('responses')) {
          final existingResponses = data['responses'];
          if (existingResponses is Map<String, dynamic>) {
            existingResponses.forEach((key, value) {
              allResponses[key] = value.toString();
            });
            print(
                'Loaded existing responses from record ${record.id}: ${allResponses.keys.toList()}');
          }
        }
      } catch (e) {
        print('Error loading responses from record: $e');
      }
    }

    // Now explicitly set responses for all prompts - even if not visible in UI
    for (var i = 0; i < _prompts.length; i++) {
      final promptKey = 'prompt_$i';
      // Get text from controller, but verify the controller exists
      String text = "";
      if (i < _controllers.length) {
        text = _controllers[i].text;
      }

      // Check if we got a valid response from the UI
      if (text.isNotEmpty) {
        // If we have text, always use it
        allResponses[promptKey] = text;
        print('Setting final response from controller: $promptKey = "$text"');
      } else if (!allResponses.containsKey(promptKey)) {
        // If no text and no existing response, set empty
        allResponses[promptKey] = "";
        print('Setting empty response for missing prompt: $promptKey');
      } else {
        // Keep existing response if we have one
        print(
            'Keeping existing response for $promptKey = "${allResponses[promptKey]}"');
      }
    }

    print('==== JOURNALING ACTIVITY SUBMIT DEBUG ====');
    print('Activity ID: ${widget.activity.id}');
    print('Prompts: $_prompts');
    print('All responses: $allResponses');
    print('All response keys: ${allResponses.keys.toList()}');

    // Check for any non-empty responses (just for logging)
    final nonEmptyResponses = Map<String, String>.from(allResponses);
    nonEmptyResponses.removeWhere((key, value) => value.trim().isEmpty);
    print('Non-empty responses: $nonEmptyResponses');

    // Create the full data object that will be saved
    final fullData = {
      'responses': allResponses,
      'date': DateTime.now().toIso8601String(),
    };

    print('Final data to be saved: $fullData');
    print('JSON encoded: ${json.encode(fullData)}');
    print('=========================================');

    // Save the final aggregated responses
    try {
      await ref
          .read(activityDataNotifierProvider(widget.activity.id, DateTime.now())
              .notifier)
          .saveActivityData(fullData);
      print('Successfully saved all responses: ${allResponses.keys.toList()}');
    } catch (e) {
      print('Error saving all responses: $e');
    }

    if (widget.standalone) {
      // markComplete(); // Commented out as it's undefined and its role is unclear now
      widget.onComplete?.call();
    } else {
      // In CheckinFlow, completion is handled by moveToStep returning false
    }
  }
}
