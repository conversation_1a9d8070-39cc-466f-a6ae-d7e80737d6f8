import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/journaling/providers/journal_provider.dart';
import 'package:mimi_app/src/features/activities/journaling/widgets/journal_form.dart';
import 'package:mimi_app/src/features/activities/journaling/providers/journal_prompts_provider.dart';

// lib/src/features/activities/journaling/screens/journaling_screen.dart
class JournalingScreen extends ConsumerWidget {
  final String type;
  final int journalId;

  const JournalingScreen({
    super.key,
    required this.type,
    required this.journalId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final promptsAsync = ref.watch(allJournalPromptsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('${type.toUpperCase()} Journal'),
      ),
      body: promptsAsync.when(
        data: (prompts) => JournalForm(
          type: type,
          prompts: prompts,
          onComplete: (responses) async {
            await ref
                .read(journalingDataNotifierProvider(journalId, DateTime.now())
                    .notifier)
                .saveEntry(responses: responses);
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading prompts: $error'),
        ),
      ),
    );
  }
}
