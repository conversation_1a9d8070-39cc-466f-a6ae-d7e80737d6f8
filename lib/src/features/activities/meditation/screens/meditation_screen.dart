// lib/src/features/activities/meditation/screens/meditation_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/activities/meditation/widgets/meditation_player.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';

class MeditationScreen extends ConsumerWidget {
  final List<AudioTrack> tracks;
  final int defaultDuration;

  const MeditationScreen({
    super.key,
    required this.tracks,
    this.defaultDuration = 10, // Default 10 minutes
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Meditation'),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: MeditationPlayer(
            tracks: tracks,
            //defaultDuration: defaultDuration,
            onComplete: () {
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
        ),
      ),
    );
  }
}
