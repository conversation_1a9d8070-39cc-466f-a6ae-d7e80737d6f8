// lib/src/features/activities/meditation/screens/meditation_activity.dart
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:mimi_app/src/features/activities/shared/mixins/routine_config_mixin.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';
import 'package:mimi_app/src/features/activities/shared/widgets/common_audio_player.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
// import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';

class MeditationActivity extends BaseActivity {
  const MeditationActivity({
    super.key,
    required super.activity,
    super.onComplete,
    super.standalone = false,
    required super.routineId,
  });

  @override
  ConsumerState<MeditationActivity> createState() => _MeditationActivityState();
}

class _MeditationActivityState extends BaseActivityState<MeditationActivity>
    with RoutineConfigMixin<MeditationActivity> {
  AudioPlayer? _audioPlayer;

  String _normalizeAssetPath(String asset) {
    // Remove 'assets/' prefix if present
    if (asset.startsWith('assets/')) {
      asset = asset.substring(7);
    }

    // Ensure audio/meditation/ prefix is present
    if (!asset.startsWith('audio/meditation/')) {
      asset = 'audio/meditation/$asset';
    }

    debugPrint('Normalized asset path: $asset');
    return asset;
  }

  @override
  Widget buildActivityContent(BuildContext context) {
    return currentConfig.maybeWhen(
      meditation: (audioTracks, defaultDuration, selectedTrack) {
        if (audioTracks.isEmpty) {
          return const Center(
            child: Text(
              'No meditation tracks selected. Please configure tracks in settings.',
            ),
          );
        }

        final tracks = audioTracks
            .map((trackMap) => AudioTrack(
                  id: trackMap['id'],
                  title: trackMap['title'],
                  audioUrl: trackMap['audioUrl'],
                  artworkUrl: trackMap['artworkUrl'],
                  categoryId: trackMap['categoryId'],
                  isSelected: trackMap['isSelected'],
                ))
            .toList();

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: CommonAudioPlayer(
              tracks: tracks,
              onComplete: markComplete,
              selectedTrackId: selectedTrack?['id'],
              playerType: AudioPlayerType.meditation,
              onPlayerCreated: (player) {
                _audioPlayer = player;
              },
            ),
          ),
        );
      },
      orElse: () => const Center(
        child: Text('Invalid meditation configuration'),
      ),
    );
  }

  @override
  void onActivityInterrupted() {
    _audioPlayer?.dispose();
    _audioPlayer = null;
  }

  @override
  void onNextStep() {
    onActivityInterrupted();
  }

  @override
  void onPreviousStep() {
    onActivityInterrupted();
  }

  @override
  int getTotalSteps() => 1; // Single step activity

  @override
  int getCurrentStep() => 0;

  @override
  bool moveToStep(int step) => step == 0;
  @override
  IconData getActivityIcon() => Icons.self_improvement;
}
