// lib/src/features/activities/meditation/screens/meditation_settings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';

import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_settings_screen.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/track_duration_provider.dart';

// class MeditationSettingsScreen extends BaseSettingsScreen {
//   const MeditationSettingsScreen({
//     required super.activity,
//     super.key,
//   });

//   @override
//   ConsumerState<MeditationSettingsScreen> createState() =>
//       _MeditationSettingsScreenState();
// }

// class _MeditationSettingsScreenState
//     extends BaseSettingsScreenState<MeditationSettingsScreen> {
//   @override
//   Widget buildConfigSection() {
//     return config.maybeWhen(
//       meditation: (audioAssets, defaultDuration, selectedAudioAsset) => Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // _buildDurationSetting(),
//           const SizedBox(height: 24),
//           _buildTrackSelector(),
//         ],
//       ),
//       orElse: () => const SizedBox.shrink(),
//     );
//   }

//   // Widget _buildDurationSetting() {
//   //   return ListTile(
//   //     title: const Text('Default Duration (minutes)'),
//   //     trailing: SizedBox(
//   //       width: 100,
//   //       child: TextField(
//   //         keyboardType: TextInputType.number,
//   //         textAlign: TextAlign.center,
//   //         controller: TextEditingController(
//   //           text: config.maybeWhen(
//   //             meditation: (_, defaultDuration, __) =>
//   //                 defaultDuration.toString(),
//   //             orElse: () => '5',
//   //           ),
//   //         ),
//   //         onChanged: (value) {
//   //           final duration = int.tryParse(value);
//   //           if (duration != null) {
//   //             setState(() {
//   //               config = config.maybeWhen(
//   //                 meditation: (audioAssets, _, selectedAsset) =>
//   //                     ActivityConfig.meditation(
//   //                   audioAssets: audioAssets,
//   //                   defaultDuration: duration,
//   //                   selectedAudioAsset: selectedAsset,
//   //                 ),
//   //                 orElse: () => config,
//   //               );
//   //             });
//   //           }
//   //         },
//   //       ),
//   //     ),
//   //   );
//   // }

//   Widget _buildTrackSelector() {
//     final categoryTracksAsync = ref.watch(categoryTracksProvider('meditation'));

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Select Meditation Tracks',
//           style: Theme.of(context).textTheme.titleMedium,
//         ),
//         const SizedBox(height: 16),
//         categoryTracksAsync.when(
//           data: (tracks) => ListView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: tracks.length,
//             itemBuilder: (context, index) {
//               final track = tracks[index];
//               final isSelected = config.maybeWhen(
//                 meditation: (audioAssets, _, __) =>
//                     audioAssets.contains(track.audioUrl),
//                 orElse: () => false,
//               );

//               return CheckboxListTile(
//                 title: Text(track.title),
//                 //  subtitle: Text(track.duration?.toString() ?? ''),
//                 value: isSelected,
//                 onChanged: (selected) {
//                   if (selected == null) return;
//                   setState(() {
//                     config = config.maybeWhen(
//                       meditation: (audioAssets, duration, selectedAsset) {
//                         final newAssets = List<String>.from(audioAssets);
//                         if (selected) {
//                           newAssets.add(track.audioUrl);
//                         } else {
//                           newAssets.remove(track.audioUrl);
//                         }
//                         return ActivityConfig.meditation(
//                           audioAssets: newAssets,
//                           defaultDuration: duration,
//                           selectedAudioAsset: selectedAsset,
//                         );
//                       },
//                       orElse: () => config,
//                     );
//                   });
//                 },
//               );
//             },
//           ),
//           loading: () => const Center(child: CircularProgressIndicator()),
//           error: (error, _) => Center(child: Text('Error: $error')),
//         ),
//       ],
//     );
//   }
// }

// class MeditationSettingsScreen extends BaseSettingsScreen {
//   const MeditationSettingsScreen({
//     required super.activity,
//     super.key,
//     required super.routineId,
//   });

//   @override
//   ConsumerState<MeditationSettingsScreen> createState() =>
//       _MeditationSettingsScreenState();
// }

// class _MeditationSettingsScreenState
//     extends BaseSettingsScreenState<MeditationSettingsScreen> {
//   @override
//   Widget buildConfigSection() {
//     return config.maybeWhen(
//       meditation: (audioAssets, defaultDuration, selectedAudioAsset) => Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const SizedBox(height: 24),
//           _buildTrackSelector(audioAssets),
//         ],
//       ),
//       orElse: () => const SizedBox.shrink(),
//     );
//   }

//   Widget _buildTrackSelector(List<String> selectedAssets) {
//     final categoryTracksAsync = ref.watch(categoryTracksProvider('meditation'));

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Select Meditation Tracks',
//           style: Theme.of(context).textTheme.titleMedium,
//         ),
//         const SizedBox(height: 16),
//         categoryTracksAsync.when(
//           data: (tracks) => ListView.separated(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: tracks.length,
//             separatorBuilder: (_, __) => const SizedBox(height: 8),
//             itemBuilder: (context, index) {
//               final track = tracks[index];
//               final isSelected = selectedAssets.contains(track.audioUrl);

//               return TrackListItem(
//                 track: track.copyWith(isSelected: isSelected),
//                 onSelect: () {
//                   setState(() {
//                     final newAssets = List<String>.from(
//                       config.maybeWhen(
//                         meditation: (assets, duration, selected) => assets,
//                         orElse: () => [],
//                       ),
//                     );

//                     if (isSelected) {
//                       newAssets.remove(track.audioUrl);
//                     } else {
//                       newAssets.add(track.audioUrl);
//                     }

//                     config = ActivityConfig.meditation(
//                       audioAssets: newAssets,
//                       defaultDuration: config.maybeWhen(
//                         meditation: (_, duration, __) => duration,
//                         orElse: () => 10,
//                       ),
//                       selectedAudioAsset: track.audioUrl,
//                     );
//                   });
//                 },
//               );
//             },
//           ),
//           loading: () => const Center(child: CircularProgressIndicator()),
//           error: (error, _) => Center(child: Text('Error: $error')),
//         ),
//       ],
//     );
//   }
// }

class MeditationSettingsScreen extends BaseSettingsScreen {
  const MeditationSettingsScreen({
    required super.activity,
    super.key,
    required super.routineId,
    super.onConfigChanged,
  });

  @override
  ConsumerState<MeditationSettingsScreen> createState() =>
      _MeditationSettingsScreenState();
}

class _MeditationSettingsScreenState
    extends BaseSettingsScreenState<MeditationSettingsScreen> {
  @override
  Widget buildConfigSection() {
    return config.maybeWhen(
      meditation: (audioTracks, defaultDuration, selectedTrack) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Activity Title
            Text(
              'Meditation Settings',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 24),
            Text(
              'Tracks',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            _buildTracksWithCheckboxes(audioTracks),
          ],
        ),
      ),
      orElse: () => const SizedBox.shrink(),
    );
  }

  Widget _buildTracksWithCheckboxes(List<Map<String, dynamic>> selectedTracks) {
    final categoryTracksAsync = ref.watch(categoryTracksProvider('meditation'));

    return categoryTracksAsync.when(
      data: (tracks) {
        if (tracks.isEmpty) {
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text('No tracks available'),
          );
        }

        final selectedUrls = selectedTracks.map((t) => t['audioUrl']).toSet();

        return ListView.separated(
          primary: false,
          shrinkWrap: true,
          itemCount: tracks.length,
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final track = tracks[index];
            final isSelected = selectedUrls.contains(track.audioUrl);

            return Card(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: isSelected
                      ? Border.all(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : null,
                ),
                child: _buildTrackListTile(track, isSelected),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildTrackListTile(AudioTrack track, bool isSelected) {
    final durationAsync = ref.watch(trackDurationProvider(track));

    return ListTile(
      leading: Container(
        height: 60,
        width: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          image: track.artworkUrl != null
              ? DecorationImage(
                  image: AssetImage(track.artworkUrl!),
                  fit: BoxFit.cover,
                )
              : null,
          color: Colors.grey.shade300,
        ),
        child: track.artworkUrl == null
            ? const Icon(
                Icons.music_note,
                color: Colors.grey,
              )
            : null,
      ),
      title: Text(track.title),
      subtitle: durationAsync.when(
        data: (duration) => Text(_formatDuration(duration)),
        loading: () => const Text('Loading...'),
        error: (_, __) => const Text('--:--'),
      ),
      trailing: Checkbox(
        value: isSelected,
        onChanged: (bool? value) {
          if (value == true) {
            _addTrack(track);
          } else {
            _removeTrackByUrl(track.audioUrl);
          }
        },
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  void _addTrack(AudioTrack track) {
    final currentTracks = List<Map<String, dynamic>>.from(
      config.maybeWhen(
        meditation: (tracks, duration, selected) => tracks,
        orElse: () => <Map<String, dynamic>>[],
      ),
    );

    if (!currentTracks.any((t) => t['audioUrl'] == track.audioUrl)) {
      currentTracks.add({
        'id': track.id,
        'title': track.title,
        'audioUrl': track.audioUrl,
        'artworkUrl': track.artworkUrl,
        'categoryId': track.categoryId,
        'isSelected': true,
      });

      updateConfig(ActivityConfig.meditation(
        audioTracks: currentTracks,
        defaultDuration: config.maybeWhen(
          meditation: (_, duration, __) => duration,
          orElse: () => 10,
        ),
        selectedTrack: config.maybeWhen(
          meditation: (_, __, selected) => selected,
          orElse: () => null,
        ),
      ));
    }
  }

  void _removeTrackByUrl(String audioUrl) {
    final currentTracks = List<Map<String, dynamic>>.from(
      config.maybeWhen(
        meditation: (tracks, duration, selected) => tracks,
        orElse: () => <Map<String, dynamic>>[],
      ),
    );

    currentTracks.removeWhere((t) => t['audioUrl'] == audioUrl);

    updateConfig(ActivityConfig.meditation(
      audioTracks: currentTracks,
      defaultDuration: config.maybeWhen(
        meditation: (_, duration, __) => duration,
        orElse: () => 10,
      ),
      selectedTrack: currentTracks.isNotEmpty ? currentTracks.first : null,
    ));
  }
}
// class _MeditationSettingsScreenState
//     extends BaseSettingsScreenState<MeditationSettingsScreen> {
//   @override
//   Widget buildConfigSection() {
//     return config.maybeWhen(
//       meditation: (audioAssets, defaultDuration, selectedAudioAsset) => Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const SizedBox(height: 24),
//           // Selected Tracks Section
//           Text(
//             'Selected Tracks',
//             style: Theme.of(context).textTheme.titleMedium,
//           ),
//           const SizedBox(height: 8),
//           if (audioAssets.isEmpty)
//             const Padding(
//               padding: EdgeInsets.symmetric(vertical: 8.0),
//               child: Text('No tracks selected'),
//             )
//           else
//             _buildSelectedTracks(audioAssets),
//           const SizedBox(height: 24),
//           // Available Tracks Section
//           Text(
//             'Available Tracks',
//             style: Theme.of(context).textTheme.titleMedium,
//           ),
//           const SizedBox(height: 8),
//           _buildAvailableTracks(audioAssets),
//         ],
//       ),
//       orElse: () => const SizedBox.shrink(),
//     );
//   }

//   Widget _buildSelectedTracks(List<String> selectedAssets) {
//     return ListView.separated(
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       itemCount: selectedAssets.length,
//       separatorBuilder: (_, __) => const SizedBox(height: 8),
//       itemBuilder: (context, index) {
//         final asset = selectedAssets[index];
//         final filename = asset.split('/').last;
//         final title = filename
//             .replaceAll(RegExp(r'[_-]'), ' ')
//             .replaceAll('.mp3', '')
//             .replaceAll('.wav', '');

//         return ListTile(
//           title: Text(title),
//           trailing: IconButton(
//             icon: const Icon(Icons.remove_circle_outline),
//             onPressed: () => _removeTrack(asset),
//           ),
//           tileColor: Theme.of(context).colorScheme.surfaceVariant,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(8),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildAvailableTracks(List<String> selectedAssets) {
//     final categoryTracksAsync = ref.watch(categoryTracksProvider('meditation'));

//     return categoryTracksAsync.when(
//       data: (tracks) {
//         final availableTracks = tracks
//             .where((track) => !selectedAssets.contains(track.audioUrl))
//             .toList();

//         return ListView.separated(
//           shrinkWrap: true,
//           physics: const NeverScrollableScrollPhysics(),
//           itemCount: availableTracks.length,
//           separatorBuilder: (_, __) => const SizedBox(height: 8),
//           itemBuilder: (context, index) {
//             final track = availableTracks[index];
//             return TrackListItem(
//               track: track,
//               onSelect: () => _addTrack(track.audioUrl),
//             );
//           },
//         );
//       },
//       loading: () => const Center(child: CircularProgressIndicator()),
//       error: (error, _) => Center(child: Text('Error: $error')),
//     );
//   }

//   void _addTrack(String audioUrl) {
//     setState(() {
//       final newAssets = List<String>.from(
//         config.maybeWhen(
//           meditation: (assets, duration, selected) => assets,
//           orElse: () => [],
//         ),
//       );
//       if (!newAssets.contains(audioUrl)) {
//         newAssets.add(audioUrl);
//         config = ActivityConfig.meditation(
//           audioAssets: newAssets,
//           defaultDuration: config.maybeWhen(
//             meditation: (_, duration, __) => duration,
//             orElse: () => 10,
//           ),
//           selectedAudioAsset: audioUrl,
//         );
//       }
//     });
//   }

//   void _removeTrack(String audioUrl) {
//     setState(() {
//       final newAssets = List<String>.from(
//         config.maybeWhen(
//           meditation: (assets, duration, selected) => assets,
//           orElse: () => [],
//         ),
//       );
//       newAssets.remove(audioUrl);
//       config = ActivityConfig.meditation(
//         audioAssets: newAssets,
//         defaultDuration: config.maybeWhen(
//           meditation: (_, duration, __) => duration,
//           orElse: () => 10,
//         ),
//         selectedAudioAsset: newAssets.isNotEmpty ? newAssets.first : null,
//       );
//     });
//   }
// }

class TrackUnListItem extends ConsumerWidget {
  final AudioTrack track;
  final VoidCallback onSelect;
  final bool showDownloadButton;

  const TrackUnListItem({
    super.key,
    required this.track,
    required this.onSelect,
    this.showDownloadButton = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final durationAsync = ref.watch(trackDurationProvider(track));

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSizing.radiusS),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onSelect,
        borderRadius: BorderRadius.circular(AppSizing.radiusS),
        child: Padding(
          padding: EdgeInsets.all(AppSizing.spaceM),
          child: Row(
            children: [
              if (track.artworkUrl != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppSizing.radiusXS),
                  child: Image.asset(
                    track.artworkUrl!,
                    width: AppSizing.trackArtworkSize,
                    height: AppSizing.trackArtworkSize,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => Container(
                      width: AppSizing.trackArtworkSize,
                      height: AppSizing.trackArtworkSize,
                      color: Theme.of(context).colorScheme.surface,
                      child: Icon(
                        Icons.music_note,
                        color: Theme.of(context).colorScheme.secondary,
                        size: AppSizing.iconM,
                      ),
                    ),
                  ),
                ),
              SizedBox(width: AppSizing.spaceM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      track.title,
                      style: Theme.of(context).textTheme.titleMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: AppSizing.spaceXS),
                    // Duration display
                    durationAsync.when(
                      data: (duration) => Text(
                        _formatDuration(duration),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                      ),
                      loading: () => SizedBox(
                        width: 35,
                        height: 2,
                        child: LinearProgressIndicator(
                          backgroundColor: AppColors.border,
                        ),
                      ),
                      error: (_, __) => Text(
                        '--:--',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.error,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
              Checkbox(
                value: track.isSelected,
                onChanged: (_) => onSelect(),
                activeColor: Theme.of(context).colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
