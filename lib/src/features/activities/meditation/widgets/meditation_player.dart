import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/presentation/screens/audio_player_screen.dart';

class MeditationPlayer extends ConsumerStatefulWidget {
  final List<AudioTrack> tracks;
  final VoidCallback? onComplete;
  final bool showQueue;
  final bool showSpeed;

  const MeditationPlayer({
    super.key,
    required this.tracks,
    this.onComplete,
    this.showQueue = false,
    this.showSpeed = true,
  });

  @override
  ConsumerState<MeditationPlayer> createState() => _MeditationPlayerState();
}

class _MeditationPlayerState extends ConsumerState<MeditationPlayer> {
  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      // Set all tracks as selected
      final selectedTracks =
          widget.tracks.map((t) => t.copyWith(isSelected: true)).toList();

      // Pre-fetch durations
      ref
          .read(audioPlayerControllerProvider.notifier)
          .preFetchDurations(selectedTracks);

      // Start playing tracks
      await ref
          .read(audioPlayerControllerProvider.notifier)
          .playTracks(selectedTracks);
    } catch (e) {
      debugPrint('Error initializing meditation player: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomAudioPlayerScreen(
      showQueue: widget.showQueue,
      showSpeed: widget.showSpeed,
      onComplete: widget.onComplete,
    );
  }

  @override
  void dispose() {
    ref.read(audioPlayerControllerProvider.notifier).stopAndClear();
    super.dispose();
  }
}
