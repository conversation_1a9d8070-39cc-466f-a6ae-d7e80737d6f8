import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/track_duration_provider.dart';

class TrackActivityListItem extends ConsumerWidget {
  final AudioTrack track;
  final VoidCallback onSelect;
  final bool showDownloadButton;
  final bool showRemoveButton; // Added this parameter

  const TrackActivityListItem({
    super.key,
    required this.track,
    required this.onSelect,
    this.showDownloadButton = false,
    this.showRemoveButton = false, // Added with default value
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final durationAsync = ref.watch(trackDurationProvider(track));

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? AppColors.surfaceLight
            : AppColors.surfaceDark,
        borderRadius: BorderRadius.circular(AppSizing.radiusS),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.light
              ? AppColors.greyLight300
              : AppColors.greyDark300,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onSelect,
        borderRadius: BorderRadius.circular(AppSizing.radiusS),
        child: Padding(
          padding: EdgeInsets.all(AppSizing.spaceM),
          child: Row(
            children: [
              if (track.artworkUrl != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppSizing.radiusXS),
                  child: Image.asset(
                    track.artworkUrl!,
                    width: AppSizing.trackArtworkSize,
                    height: AppSizing.trackArtworkSize,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => Container(
                      width: AppSizing.trackArtworkSize,
                      height: AppSizing.trackArtworkSize,
                      color: Theme.of(context).brightness == Brightness.light
                          ? AppColors.greyLight300
                          : AppColors.greyDark300,
                      child: Icon(
                        Icons.music_note,
                        color: Theme.of(context).colorScheme.secondary,
                        size: AppSizing.iconM,
                      ),
                    ),
                  ),
                ),
              SizedBox(width: AppSizing.spaceM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      track.title,
                      style: Theme.of(context).textTheme.titleMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: AppSizing.spaceXS),
                    // Duration display
                    durationAsync.when(
                      data: (duration) => Text(
                        _formatDuration(duration),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).brightness ==
                                      Brightness.light
                                  ? AppColors.greyLight300
                                  : AppColors.greyDark300,
                            ),
                      ),
                      loading: () => SizedBox(
                        width: 35,
                        height: 2,
                        child: LinearProgressIndicator(
                          backgroundColor: Theme.of(context)
                              .colorScheme
                              .secondary
                              .withOpacity(0.1),
                        ),
                      ),
                      error: (_, __) => Text(
                        '--:--',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.error,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
              if (showRemoveButton)
                IconButton(
                  icon: Icon(
                    Icons.remove_circle_outline,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  onPressed: onSelect,
                )
              else
                Checkbox(
                  value: track.isSelected,
                  onChanged: (_) => onSelect(),
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
