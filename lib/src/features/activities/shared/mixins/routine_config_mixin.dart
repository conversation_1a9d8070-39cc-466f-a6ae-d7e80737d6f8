// lib/src/features/activities/shared/mixins/routine_config_mixin.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';

mixin RoutineConfigMixin<T extends BaseActivity> on BaseActivityState<T> {
  ActivityConfig? routineConfig;
  bool isLoadingConfig = true;

  @override
  void initState() {
    super.initState();
    _loadRoutineConfig();
  }

  Future<void> _loadRoutineConfig() async {
    try {
      debugPrint('=== LOADING ACTIVITY CONFIG ===');
      debugPrint('Routine ID: ${widget.routineId}');
      debugPrint('Activity ID: ${widget.activity.id}');
      debugPrint('Activity Type: ${widget.activity.type}');
      debugPrint('Activity Name: ${widget.activity.name}');

      // First try to load routine-specific configuration
      final db = ref.read(journalDatabaseProvider);
      final routineSpecificConfig = await db.getRoutineActivityConfig(
        widget.routineId,
        widget.activity.id,
      );

      ActivityConfig config;
      if (routineSpecificConfig != null) {
        debugPrint('Using routine-specific config from database');
        config = routineSpecificConfig;
      } else {
        debugPrint(
            'No routine-specific config found, using activity default config');
        config = widget.activity.config;
      }

      debugPrint('Config type: ${config.runtimeType}');
      config.maybeWhen(
        journaling: (prompts, includeDate) {
          debugPrint('Journaling config - prompts: ${prompts.length}');
          for (int i = 0; i < prompts.length; i++) {
            debugPrint('  Prompt $i: ${prompts[i]}');
          }
        },
        meditation: (audioTracks, defaultDuration, selectedTrack) {
          debugPrint(
              'Meditation config - tracks: ${audioTracks.length}, duration: $defaultDuration');
        },
        orElse: () {
          debugPrint('Other config type loaded');
        },
      );

      if (mounted) {
        setState(() {
          routineConfig = config;
          isLoadingConfig = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading activity config: $e');
      if (mounted) {
        setState(() {
          routineConfig = widget.activity.config;
          isLoadingConfig = false;
        });
      }
    }
  }

  ActivityConfig get currentConfig => routineConfig ?? widget.activity.config;

  @override
  Widget build(BuildContext context) {
    if (isLoadingConfig) {
      return const Center(child: CircularProgressIndicator());
    }

    // Debug print for current configuration

    return super.build(context);
  }
}
