// ignore_for_file: unused_field

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';

enum AudioPlayerType { meditation, affirmation }

class CommonAudioPlayer extends ConsumerStatefulWidget {
  final List<AudioTrack> tracks;
  final String? selectedTrackId;
  final bool autoPlay;
  final VoidCallback onComplete;
  final bool showTitle;
  final AudioPlayerType playerType;
  final Function(AudioPlayer)? onPlayerCreated;
  final bool showQueue; // New parameter

  const CommonAudioPlayer({
    super.key,
    required this.tracks,
    required this.onComplete,
    this.selectedTrackId,
    this.autoPlay = true,
    this.showTitle = true,
    required this.playerType,
    this.onPlayerCreated,
    this.showQueue = true, // Default to true
  });

  @override
  ConsumerState<CommonAudioPlayer> createState() => _CommonAudioPlayerState();
}

class _CommonAudioPlayerState extends ConsumerState<CommonAudioPlayer> {
  late final AudioPlayer _player;
  bool _isPlaying = false;
  Duration _currentDuration = Duration.zero;
  Duration _totalDuration = Duration.zero;
  AudioTrack? _currentTrack;
  int _currentTrackIndex = 0;
  double _currentSpeed = 1.0;

  StreamSubscription? _durationSubscription;
  StreamSubscription? _positionSubscription;
  StreamSubscription? _playerCompleteSubscription;
  StreamSubscription? _playerStateSubscription;

  bool get _hasNext => _currentTrackIndex < widget.tracks.length - 1;
  bool get _hasPrevious => _currentTrackIndex > 0;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    widget.onPlayerCreated?.call(_player);
    _setupPlayer();
  }

  Future<void> _setupPlayer() async {
    _durationSubscription = _player.onDurationChanged.listen((duration) {
      if (mounted) {
        setState(() => _totalDuration = duration);
      }
    });

    _positionSubscription = _player.onPositionChanged.listen((position) {
      if (mounted) {
        setState(() => _currentDuration = position);
      }
    });

    _playerCompleteSubscription = _player.onPlayerComplete.listen((_) {
      if (mounted) {
        widget.onComplete();
      }
    });

    _playerStateSubscription = _player.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() => _isPlaying = state == PlayerState.playing);
      }
    });

    if (widget.selectedTrackId != null) {
      _currentTrack = widget.tracks.firstWhere(
        (track) => track.audioUrl == widget.selectedTrackId,
        orElse: () => widget.tracks.first,
      );
    } else if (widget.tracks.isNotEmpty) {
      _currentTrack = widget.tracks.first;
    }

    if (_currentTrack != null) {
      await _playAsset(_currentTrack!.audioUrl);
    }
  }

  Future<void> _setPlaybackSpeed(double speed) async {
    try {
      await _player.setPlaybackRate(speed);
      setState(() {
        _currentSpeed = speed;
      });
    } catch (e) {
      debugPrint('Error setting playback speed: $e');
      if (mounted) {
        showFailureToast(
          context,
          title: 'Playback Error',
          description: 'Error changing playback speed: $e',
        );
      }
    }
  }

  Future<void> _playNext() async {
    if (_hasNext) {
      _currentTrackIndex++;
      _currentTrack = widget.tracks[_currentTrackIndex];
      await _playAsset(_currentTrack!.audioUrl);
    }
  }

  Future<void> _playPrevious() async {
    if (_hasPrevious) {
      _currentTrackIndex--;
      _currentTrack = widget.tracks[_currentTrackIndex];
      await _playAsset(_currentTrack!.audioUrl);
    }
  }

  Future<void> _playTrackAtIndex(int index) async {
    if (index >= 0 && index < widget.tracks.length) {
      _currentTrackIndex = index;
      _currentTrack = widget.tracks[_currentTrackIndex];
      await _playAsset(_currentTrack!.audioUrl);
    }
  }

  Future<void> _playAsset(String audioPath) async {
    try {
      debugPrint('Playing audio: $audioPath');

      // Check if it's a Firebase URL or local asset
      if (audioPath.startsWith('http://') || audioPath.startsWith('https://')) {
        // Firebase URL - use UrlSource
        await _player.play(UrlSource(audioPath));
      } else {
        // Local asset - use AssetSource
        try {
          await _player.play(AssetSource(audioPath));
        } catch (e) {
          debugPrint('First attempt failed, trying with assets/ prefix');
          // If that fails, try with the assets/ prefix
          await _player.play(AssetSource('assets/$audioPath'));
        }
      }

      if (!widget.autoPlay) {
        await _player.pause();
      }
    } catch (e) {
      debugPrint('Error playing audio: $e');
      // Show error to user
      if (mounted) {
        showFailureToast(
          context,
          title: 'Playback Error',
          description: 'Error playing audio: $e',
        );
      }
    }
  }

  void _togglePlayPause() async {
    if (_isPlaying) {
      await _player.pause();
    } else {
      await _player.resume();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showTitle) ...[
          Text(
            widget.playerType == AudioPlayerType.meditation
                ? 'Meditation Session'
                : 'Daily Affirmations',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 24),
        ],

        // Track Artwork
        if (_currentTrack != null) ...[
          Center(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.width * 0.8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSizing.radiusL),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppSizing.radiusS),
                child: _currentTrack?.artworkUrl != null
                    ? Image.asset(
                        _currentTrack!.artworkUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (_, __, ___) =>
                            _buildPlaceholder(context),
                      )
                    : _buildPlaceholder(context),
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],

        const SizedBox(height: 24),

        // Progress Slider
        if (_currentTrack != null) ...[
          Slider(
            value: _currentDuration.inSeconds.toDouble(),
            max: _totalDuration.inSeconds.toDouble(),
            onChanged: (value) {
              _player.seek(Duration(seconds: value.toInt()));
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(_formatDuration(_currentDuration)),
                Text(_formatDuration(_totalDuration)),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Playback Controls
        _PlaybackControls(
          isPlaying: _isPlaying,
          onTogglePlay: _togglePlayPause,
          currentSpeed: _currentSpeed,
          onSpeedChanged: _setPlaybackSpeed,
          hasNext: _hasNext,
          hasPrevious: _hasPrevious,
          onNext: _playNext,
          onPrevious: _playPrevious,
          showQueue: widget.showQueue,
          onQueuePressed: () => _showQueueBottomSheet(context),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: Icon(
        Icons.music_note,
        size: AppSizing.iconXL * 2,
        color: Theme.of(context).colorScheme.secondary.withOpacity(0.5),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void _showQueueBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _QueueBottomSheet(
        tracks: widget.tracks,
        currentTrackIndex: _currentTrackIndex,
        onTrackSelected: (index) {
          _playTrackAtIndex(index);
          Navigator.pop(context);
        },
      ),
    );
  }
}

class _PlaybackControls extends StatelessWidget {
  final bool isPlaying;
  final VoidCallback onTogglePlay;
  final double currentSpeed;
  final Function(double) onSpeedChanged;
  final bool hasNext;
  final bool hasPrevious;
  final VoidCallback onNext;
  final VoidCallback onPrevious;
  final bool showQueue;
  final VoidCallback onQueuePressed;

  const _PlaybackControls({
    required this.isPlaying,
    required this.onTogglePlay,
    required this.currentSpeed,
    required this.onSpeedChanged,
    required this.hasNext,
    required this.hasPrevious,
    required this.onNext,
    required this.onPrevious,
    required this.showQueue,
    required this.onQueuePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showQueue)
          IconButton(
            icon: Icon(
              Icons.queue_music,
              size: AppSizing.iconL,
              color: Theme.of(context).colorScheme.primary,
            ),
            onPressed: onQueuePressed,
          ),
        IconButton(
          icon: Icon(
            Icons.skip_previous,
            size: AppSizing.iconL,
            color: hasPrevious
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.primary.withOpacity(0.3),
          ),
          onPressed: hasPrevious ? onPrevious : null,
        ),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            iconSize: AppSizing.iconL,
            icon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              transitionBuilder: (child, animation) {
                return RotationTransition(
                  turns: animation,
                  child: ScaleTransition(
                    scale: animation,
                    child: child,
                  ),
                );
              },
              child: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                key: ValueKey<bool>(isPlaying),
                color: Colors.white,
              ),
            ),
            onPressed: onTogglePlay,
          ),
        ),
        IconButton(
          icon: Icon(
            Icons.skip_next,
            size: AppSizing.iconL,
            color: hasNext
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.primary.withOpacity(0.3),
          ),
          onPressed: hasNext ? onNext : null,
        ),
        IconButton(
          icon: Icon(
            Icons.speed,
            size: AppSizing.iconL,
            color: Theme.of(context).colorScheme.primary,
          ),
          onPressed: () => _showSpeedBottomSheet(context),
        ),
      ],
    );
  }

  void _showSpeedBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _SpeedBottomSheet(
        currentSpeed: currentSpeed,
        onSpeedChanged: onSpeedChanged,
      ),
    );
  }
}

class _SpeedBottomSheet extends StatelessWidget {
  final List<double> speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];
  final double currentSpeed;
  final Function(double) onSpeedChanged;

  _SpeedBottomSheet({
    required this.currentSpeed,
    required this.onSpeedChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: AppSizing.spaceS),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSizing.spaceM,
                  vertical: AppSizing.spaceS,
                ),
                child: Row(
                  children: [
                    Text(
                      'Playback Speed',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 300,
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const BouncingScrollPhysics(),
                  itemCount: speeds.length,
                  itemBuilder: (context, index) {
                    final speed = speeds[index];
                    final isSelected = speed == currentSpeed;

                    return ListTile(
                      title: Text(
                        '${speed}x',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : null,
                              fontWeight: isSelected ? FontWeight.bold : null,
                            ),
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                      onTap: () {
                        onSpeedChanged(speed);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _QueueBottomSheet extends StatelessWidget {
  final List<AudioTrack> tracks;
  final int currentTrackIndex;
  final Function(int) onTrackSelected;

  const _QueueBottomSheet({
    required this.tracks,
    required this.currentTrackIndex,
    required this.onTrackSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.all(AppSizing.spaceM),
            child: Row(
              children: [
                Text(
                  'Queue',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: tracks.length,
              itemBuilder: (context, index) {
                final track = tracks[index];
                final isCurrentTrack = index == currentTrackIndex;

                return ListTile(
                  leading: Container(
                    width: AppSizing.iconXL,
                    height: AppSizing.iconXL,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppSizing.radiusS),
                    ),
                    child: track.artworkUrl != null
                        ? Image.asset(
                            track.artworkUrl!,
                            fit: BoxFit.cover,
                          )
                        : Icon(
                            Icons.music_note,
                            color: Theme.of(context).colorScheme.secondary,
                          ),
                  ),
                  title: Text(
                    track.title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: isCurrentTrack
                              ? Theme.of(context).colorScheme.primary
                              : null,
                          fontWeight: isCurrentTrack
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                  ),
                  trailing: isCurrentTrack
                      ? Icon(
                          Icons.equalizer,
                          color: Theme.of(context).colorScheme.primary,
                        )
                      : null,
                  onTap: () => onTrackSelected(index),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
