import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';

/// A widget that displays informative messages for activity configuration
/// with a user-friendly design that matches the app's theme
class ActivityInfoWidget extends StatelessWidget {
  final List<String> messages;
  final String activityType;
  final EdgeInsetsGeometry? padding;

  const ActivityInfoWidget({
    super.key,
    required this.messages,
    required this.activityType,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (messages.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: padding ?? const EdgeInsets.all(16.0),
      margin: const EdgeInsets.only(bottom: 16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          width: 1.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                _getActivityIcon(activityType),
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Configuration Needed',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...messages.map((message) => Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 6,
                      height: 6,
                      margin: const EdgeInsets.only(top: 8, right: 8),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        message,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String activityType) {
    final lowerType = activityType.toLowerCase();
    switch (lowerType) {
      case 'journaling':
      case 'journal':
        return Icons.edit_note_rounded;
      case 'meditation':
        return Icons.self_improvement_rounded;
      case 'affirmations':
        return Icons.record_voice_over_rounded;
      case 'breathwork':
        return Icons.air_rounded;
      case 'mood tracking':
      case 'mood_tracking':
        return Icons.mood_rounded;
      case 'gratitude':
        return Icons.favorite_rounded;
      default:
        return Icons.settings_rounded;
    }
  }
}

/// A compact activity info widget for inline display
class InlineActivityInfoWidget extends StatelessWidget {
  final String message;
  final String activityType;
  final EdgeInsetsGeometry? padding;

  const InlineActivityInfoWidget({
    super.key,
    required this.message,
    required this.activityType,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ??
          const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      margin: const EdgeInsets.only(top: 8.0),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6.0),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
          width: 1.0,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getActivityIcon(activityType),
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: 6),
          Flexible(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String activityType) {
    final lowerType = activityType.toLowerCase();
    switch (lowerType) {
      case 'journaling':
      case 'journal':
        return Icons.edit_note_rounded;
      case 'meditation':
        return Icons.self_improvement_rounded;
      case 'affirmations':
        return Icons.record_voice_over_rounded;
      case 'breathwork':
        return Icons.air_rounded;
      case 'mood tracking':
      case 'mood_tracking':
        return Icons.mood_rounded;
      case 'gratitude':
        return Icons.favorite_rounded;
      default:
        return Icons.settings_rounded;
    }
  }
}
