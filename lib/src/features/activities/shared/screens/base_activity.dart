// lib/src/features/journal/widgets/activities/base_activity.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';

abstract class BaseActivity extends ConsumerStatefulWidget {
  final Activity activity;
  final int routineId;
  final VoidCallback? onComplete;
  final bool standalone;

  const BaseActivity({
    super.key,
    required this.activity,
    required this.routineId,
    this.onComplete,
    this.standalone = false,
  });
}

abstract class BaseActivityState<T extends BaseActivity>
    extends ConsumerState<T> {
  bool _isComplete = false;
  bool get isComplete => _isComplete;

  // New method to handle cleanup when activity is interrupted
  void onActivityInterrupted() {
    // Override in subclasses that need cleanup
  }

  // New method to handle cleanup when moving to next step
  void onNextStep() {
    // Override in subclasses that need cleanup
  }

  // New method to handle cleanup when moving to previous step
  void onPreviousStep() {
    // Override in subclasses that need cleanup
  }

  void markComplete() {
    if (!_isComplete) {
      setState(() => _isComplete = true);
      widget.onComplete?.call();
    }
  }

  @override
  void dispose() {
    onActivityInterrupted();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: buildActivityContent(context),
    );
  }

  // Abstract methods that need to be implemented by children
  IconData getActivityIcon();
  Widget buildActivityContent(BuildContext context);
  int getTotalSteps();
  int getCurrentStep();
  bool moveToStep(int step);
  bool canMoveNext() => true; // Default implementation
  bool canComplete() => true; // Default implementation
}

// abstract class BaseActivity extends ConsumerStatefulWidget {
//   final Activity activity;
//   final int routineId;
//   final VoidCallback? onComplete;
//   final bool standalone;

//   const BaseActivity({
//     super.key,
//     required this.activity,
//     required this.routineId,
//     this.onComplete,
//     this.standalone = false,
//   });
// }

// abstract class BaseActivityState<T extends BaseActivity>
//     extends ConsumerState<T> {
//   bool _isComplete = false;
//   bool get isComplete => _isComplete;

//   void markComplete() {
//     if (!_isComplete) {
//       setState(() => _isComplete = true);
//       widget.onComplete?.call();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Material(
//       color: Colors.transparent,
//       child: SafeArea(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.stretch,
//           children: [
//             Padding(
//               padding: const EdgeInsets.all(16),
//               child: Row(
//                 children: [
//                   Icon(getActivityIcon()),
//                   const SizedBox(width: 8),
//                   Text(
//                     widget.activity.name,
//                     style: Theme.of(context).textTheme.titleLarge,
//                   ),
//                   const Spacer(),
//                   if (_isComplete)
//                     Icon(
//                       Icons.check_circle,
//                       color: Theme.of(context).colorScheme.primary,
//                     ),
//                 ],
//               ),
//             ),
//             const Divider(height: 1),
//             Expanded(
//               child: buildActivityContent(context),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   IconData getActivityIcon();
//   Widget buildActivityContent(BuildContext context);
// }
