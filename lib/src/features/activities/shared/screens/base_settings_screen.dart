// lib/src/features/activities/shared/screens/base_settings_screen.dart
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_details.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart'; // Corrected import path

// Temporarily defining ActivitySettingsNavArgs here due to file creation issues.
class ActivitySettingsNavArgs {
  final int intentionId;
  final int routineId;
  final List<String> selectedActivities; // List of activity types/names
  final int currentActivityIndex;

  ActivitySettingsNavArgs({
    required this.intentionId,
    required this.routineId,
    required this.selectedActivities,
    required this.currentActivityIndex,
  });

  ActivitySettingsNavArgs copyWith({
    int? intentionId,
    int? routineId,
    List<String>? selectedActivities,
    int? currentActivityIndex,
  }) {
    return ActivitySettingsNavArgs(
      intentionId: intentionId ?? this.intentionId,
      routineId: routineId ?? this.routineId,
      selectedActivities: selectedActivities ?? this.selectedActivities,
      currentActivityIndex: currentActivityIndex ?? this.currentActivityIndex,
    );
  }

  // Basic equality for now, can be expanded if needed.
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ActivitySettingsNavArgs &&
          runtimeType == other.runtimeType &&
          intentionId == other.intentionId &&
          routineId == other.routineId &&
          listEquals(
              selectedActivities,
              other
                  .selectedActivities) && // Requires listEquals from foundation.dart or custom implementation
          currentActivityIndex == other.currentActivityIndex;

  @override
  int get hashCode =>
      intentionId.hashCode ^
      routineId.hashCode ^
      selectedActivities.hashCode ^
      currentActivityIndex.hashCode;

  // Helper for list equality (requires import 'package:flutter/foundation.dart'; for listEquals)
  // For simplicity, direct comparison if not using foundation.dart for basic types.
  bool listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) {
        return false;
      }
    }
    return true;
  }
}

// TODO: Define this function properly, likely in a provider/repository.
// This function is CRITICAL for fetching/creating the correct Activity object
// with the right ID for the next step in the sequence.
Future<Activity> getActivityForRoutine(
    String activityType, int routineId, WidgetRef ref) async {
  final db = ref.read(
      journalDatabaseProvider); // Ensure this is the correct provider name from the imported file
  final activityDetails = getActivityDetailsByType(activityType);

  if (activityDetails == null) {
    // This case should ideally not happen if selectedActivities are validated
    print('Error: Could not find details for activity type: $activityType');
    // Return a fallback or throw an error
    // For now, returning a dummy activity to prevent crashing, but this needs robust handling.
    return Activity(
      id: -1, // Indicate an error or invalid activity
      type: activityType,
      name: 'Unknown Activity',
      config: ActivityConfig.journaling(), // A generic default
    );
  }

  final activityDbId = activityDetails.activityDbId;
  ActivityConfig? config =
      await db.getRoutineActivityConfig(routineId, activityDbId);

  if (config == null) {
    print(
        'No config found in DB for $activityType (ID: $activityDbId) in routine $routineId. Using default.');
    config = activityDetails.defaultConfigFactory();
  } else {
    print(
        'Loaded config from DB for $activityType (ID: $activityDbId) in routine $routineId.');
  }

  return Activity(
    id: activityDbId, // Use the globally unique activityDbId as the Activity's ID
    type: activityDetails.type,
    name: activityDetails.displayName,
    config: config,
  );
}

abstract class BaseSettingsScreen extends ConsumerStatefulWidget {
  final Activity activity;
  final int routineId;
  final ActivitySettingsNavArgs? navArgs; // Made optional for now
  final void Function(ActivityConfig)? onConfigChanged; // Add callback

  const BaseSettingsScreen({
    required this.activity,
    required this.routineId,
    this.navArgs,
    this.onConfigChanged,
    super.key,
  });
}

abstract class BaseSettingsScreenState<T extends BaseSettingsScreen>
    extends ConsumerState<T> {
  late TextEditingController nameController;
  late ActivityConfig config = widget.activity.config;
  bool isLoading = false;

  // Helper method to update config and notify parent
  void updateConfig(ActivityConfig newConfig) {
    setState(() {
      config = newConfig;
    });
    // Notify parent widget if callback is provided
    // Use addPostFrameCallback to avoid calling setState during build
    if (widget.onConfigChanged != null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onConfigChanged!(newConfig);
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    nameController = TextEditingController(text: widget.activity.name);
    _loadRoutineConfig();
  }

  Future<void> _loadRoutineConfig() async {
    debugPrint('=== LOADING ROUTINE CONFIG ===');
    debugPrint('Activity ID: ${widget.activity.id}');
    debugPrint('Activity Type: ${widget.activity.type}');
    debugPrint('Routine ID: ${widget.routineId}');

    final db = ref.read(journalDatabaseProvider);
    ActivityConfig? routineConfig;

    // Try to load routine-specific configuration
    // For both existing activities (ID > 0) and temporary activities (ID -1)
    if (widget.activity.id != -1) {
      // For existing activities, load from routine_activity_configs table
      routineConfig = await db.getRoutineActivityConfig(
        widget.routineId,
        widget.activity.id,
      );
      debugPrint(
          'Loaded routine config for existing activity: ${routineConfig != null ? 'Found' : 'Not found'}');
    } else {
      // For temporary activities (ID -1), try to find existing routine config
      // by looking for activities of the same type in this routine
      try {
        final routine = await db.getRoutineById(widget.routineId);
        if (routine != null &&
            routine.activities.isNotEmpty &&
            routine.activities != '[]') {
          debugPrint(
              'Raw routine activities for config lookup: ${routine.activities}');

          // Parse the activities field - it could be either activity IDs (new format) or activity names (old format)
          final activitiesData = jsonDecode(routine.activities) as List;

          if (activitiesData.isNotEmpty && activitiesData.first is int) {
            // New format: activity IDs
            final activityIds = activitiesData.cast<int>();

            // Look for an activity of the same type
            for (final activityId in activityIds) {
              final existingActivity = await db.getActivityById(activityId);
              if (existingActivity != null &&
                  _normalizeActivityType(existingActivity.type) ==
                      _normalizeActivityType(widget.activity.type)) {
                routineConfig = await db.getRoutineActivityConfig(
                    widget.routineId, activityId);
                if (routineConfig != null) {
                  debugPrint(
                      'Found routine config for temporary activity via existing activity ID $activityId');
                  break;
                }
              }
            }
          } else {
            // Old format: activity names - no routine-specific configs to load
            debugPrint(
                'Old format routine detected, no routine-specific configs available');
          }
        }
      } catch (e) {
        debugPrint('Error searching for existing routine config: $e');
      }
    }

    // Use routine config if found, otherwise use activity's default config
    final newConfig = routineConfig ?? widget.activity.config;
    debugPrint('Final config type: ${newConfig.runtimeType}');

    setState(() {
      config = newConfig;
    });

    // Notify parent widget if callback is provided
    if (widget.onConfigChanged != null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onConfigChanged!(newConfig);
        }
      });
    }
  }

  String _normalizeActivityType(String type) {
    // Normalize activity types for comparison
    switch (type.toLowerCase()) {
      case 'journal':
      case 'journaling':
        return 'journaling';
      case 'mood tracking':
      case 'mood_tracking':
      case 'moodtracking':
        return 'moodtracking';
      default:
        return type.toLowerCase();
    }
  }

  Future<void> saveSettings() async {
    setState(() => isLoading = true);
    try {
      // For temporary activities (ID -1), don't save to database
      // The wizard will handle the saving
      if (widget.activity.id != -1) {
        final db = ref.read(journalDatabaseProvider);

        // Update the activity's configuration directly
        await db.updateActivity(
          id: widget.activity.id,
          name: widget.activity.name,
          type: widget.activity.type,
          config: config,
        );

        debugPrint('Updated activity ${widget.activity.id} with new config');
      } else {
        debugPrint('Skipping database save for temporary activity (ID -1)');
      }

      if (mounted) {
        // Settings saved successfully - no toast needed

        if (widget.navArgs != null) {
          final navArgs = widget.navArgs!;
          final nextActivityIndex = navArgs.currentActivityIndex + 1;

          if (nextActivityIndex < navArgs.selectedActivities.length) {
            final nextActivityType =
                navArgs.selectedActivities[nextActivityIndex];
            final activityDetails =
                getActivityDetailsByType(nextActivityType); // Using new helper

            if (activityDetails != null) {
              // CRITICAL: Fetch the correct Activity object for the next screen
              final nextActivityObject = await getActivityForRoutine(
                  nextActivityType, widget.routineId, ref);

              final newNavArgs =
                  navArgs.copyWith(currentActivityIndex: nextActivityIndex);

              // Ensure the route name used here matches your GoRouter setup for these existing screens
              // And that the target screen can accept 'activity', 'routineId', and 'navArgs' via 'extra'
              context.pushReplacementNamed(
                activityDetails.settingsRouteName,
                // pathParameters might be needed if your routes are like /:intentionId/:routineId/activityType
                // For now, assuming parameters are passed via 'extra' or handled by the target route.
                extra: {
                  'activity': nextActivityObject,
                  'routineId': widget.routineId,
                  'navArgs': newNavArgs,
                },
              );
            } else {
              print(
                  'Error: Could not find details for next activity: $nextActivityType');
              // Fallback navigation if next activity details are not found
              context.go('/'); // Or to intentions list, etc.
            }
          } else {
            // Last activity in the sequence
            print(
                'All activities configured for routineId: ${widget.routineId}');
            // TODO: Replace with actual route name for intentions list or routine detail
            context.go('/'); // Example: Navigate to home or intentions list
          }
        } else {
          // Original behavior: not part of a sequence, just pop
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        // Error saving settings - silently handle
        debugPrint('Error saving settings: $e');
      }
    } finally {
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  Widget buildConfigSection();
  @override
  Widget build(BuildContext context) {
    String saveButtonText = 'Save';
    if (widget.navArgs != null) {
      if (widget.navArgs!.currentActivityIndex <
          widget.navArgs!.selectedActivities.length - 1) {
        saveButtonText = 'Save & Continue';
      } else {
        saveButtonText = 'Save & Finish';
      }
    }

    // When used in wizard context, don't show the scaffold wrapper
    if (widget.onConfigChanged != null) {
      // In wizard context - just return the config section without scaffold
      return SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              buildConfigSection(),
            ],
          ),
        ),
      );
    }

    // When used standalone (not in wizard), show full scaffold
    return Scaffold(
      appBar: AppBar(
        title: Text(
            '${widget.activity.name} Settings'), // Use activity name for title
        leading: IconButton(
            onPressed: () {
              // Simply pop. Changes are only saved via the action button.
              context.pop();
            },
            icon: const Icon(Icons.arrow_back)),
        actions: [
          if (isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: CircularProgressIndicator(),
              ),
            )
          else
            TextButton(
              onPressed: saveSettings,
              child: Text(saveButtonText),
            ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Activity Name',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              buildConfigSection(),
            ],
          ),
        ),
      ),
    );
  }
}
