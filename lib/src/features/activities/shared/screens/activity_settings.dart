import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/affirmations/screen/affirmations_settings_screen.dart';
import 'package:mimi_app/src/features/activities/breathwork/screens/breathwork_settings_screen.dart';
import 'package:mimi_app/src/features/activities/journaling/screens/journaling_settings_screen.dart';
import 'package:mimi_app/src/features/activities/meditation/screens/meditation_settings_screen.dart';
import 'package:mimi_app/src/features/activities/mood_tracking/screens/moodtracking_settings_screen.dart';
import 'package:mimi_app/src/features/activities/gratitude/screens/gratitude_settings_screen.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';

class ActivitySettingsScreen extends ConsumerWidget {
  final int routineId;
  final Activity activity;

  const ActivitySettingsScreen({
    required this.activity,
    required this.routineId,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return activity.config.when(
      journaling: (
        _,
        __,
      ) =>
          JournalingSettingsScreen(
        activity: activity,
        routineId: routineId,
      ),
      meditation: (_, __, ___) => MeditationSettingsScreen(
        activity: activity,
        routineId: routineId,
      ),
      affirmations: (_, __, ___) => AffirmationsSettingsScreen(
        activity: activity,
        routineId: routineId,
      ),
      breathwork: (_, __, ___, ____, _____) => BreathworkSettingsScreen(
        activity: activity,
        routineId: routineId,
      ),
      moodTracking: (_, __) => MoodTrackingSettingsScreen(
        activity: activity,
        routineId: routineId,
      ),
      gratitude: (_, __) => GratitudeSettingsScreen(
        activity: activity,
        routineId: routineId,
      ),
    );
  }
}
