import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/confirmation_dialog.dart';

import 'package:mimi_app/src/features/activities/affirmations/screen/affirmations_settings_screen.dart';
import 'package:mimi_app/src/features/activities/breathwork/screens/breathwork_settings_screen.dart';
import 'package:mimi_app/src/features/activities/journaling/screens/journaling_settings_screen.dart';
import 'package:mimi_app/src/features/activities/meditation/screens/meditation_settings_screen.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/activities/shared/widgets/activity_info_widget.dart';

class ActivityWizardScreen extends ConsumerStatefulWidget {
  final int intentionId;
  final int routineId;
  final List<String> selectedActivities;
  final int currentIndex;
  final List<Activity>? activities;

  const ActivityWizardScreen({
    super.key,
    required this.intentionId,
    required this.routineId,
    required this.selectedActivities,
    this.currentIndex = 0,
    this.activities,
  });

  @override
  ConsumerState<ActivityWizardScreen> createState() =>
      _ActivityWizardScreenState();
}

class _ActivityWizardScreenState extends ConsumerState<ActivityWizardScreen> {
  late final List<GlobalKey<FormState>> _formKeys;
  List<Activity> _activities = []; // Initialize as empty list instead of late
  bool _isLoading = false;
  List<String> _validationErrors =
      []; // Track validation errors for current activity

  @override
  void initState() {
    super.initState();
    _formKeys = List.generate(
      widget.selectedActivities.length,
      (_) => GlobalKey<FormState>(),
    );
    _initializeActivities();
  }

  void _initializeActivities() {
    // Use provided activities or load existing activities for this routine
    if (widget.activities != null && widget.activities!.isNotEmpty) {
      try {
        _activities = widget.activities!.map<Activity>((dynamic activity) {
          if (activity is Map<String, dynamic>) {
            return Activity.fromJson(activity);
          }
          return activity as Activity;
        }).toList();

        debugPrint('=== WIZARD INITIALIZATION ===');
        debugPrint('Using ${_activities.length} provided activities:');
        for (int i = 0; i < _activities.length; i++) {
          final activity = _activities[i];
          debugPrint(
              'Activity $i: ID=${activity.id}, Type=${activity.type}, Name=${activity.name}');
        }
        debugPrint('Selected activities: ${widget.selectedActivities}');

        // Ensure activities are ordered according to selectedActivities
        _reorderActivitiesAccordingToSelection();

        // No need for setState here since this is called from initState
      } catch (e) {
        debugPrint('Error initializing activities: $e');
        // Initialize with default activities immediately to prevent empty list
        _initializeDefaultActivities();
        // Then try to load from database asynchronously
        _loadExistingActivitiesOrDefaults();
      }
    } else {
      debugPrint('No activities provided, initializing with defaults first');
      // Initialize with default activities immediately to prevent empty list
      _initializeDefaultActivities();
      // Then try to load existing activities asynchronously
      _loadExistingActivitiesOrDefaults();
    }

    // Initialize validation errors for current activity
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateValidationErrors();
    });
  }

  void _updateValidationErrors() {
    if (_activities.isNotEmpty && widget.currentIndex < _activities.length) {
      final currentActivity = _activities[widget.currentIndex];
      final currentActivityType =
          widget.selectedActivities[widget.currentIndex];

      // Only validate activities that need configuration
      if (!_shouldSkipConfiguration(currentActivityType)) {
        if (mounted) {
          setState(() {
            _validationErrors = currentActivity.config.getValidationErrors();
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _validationErrors = [];
          });
        }
      }
    }
  }

  void _reorderActivitiesAccordingToSelection() {
    // Create a mapping from activity type to activity for quick lookup
    final activityMap = <String, Activity>{};
    for (final activity in _activities) {
      // Map both the exact type and common variations
      activityMap[activity.type.toLowerCase()] = activity;

      // Add common mappings
      switch (activity.type.toLowerCase()) {
        case 'journaling':
          activityMap['journal'] = activity;
          break;
        case 'moodtracking':
          activityMap['mood tracking'] = activity;
          break;
      }
    }

    // Reorder activities according to selectedActivities order
    final reorderedActivities = <Activity>[];
    for (final selectedName in widget.selectedActivities) {
      final activity = activityMap[selectedName.toLowerCase()];
      if (activity != null) {
        reorderedActivities.add(activity);
        debugPrint(
            'Mapped "$selectedName" to activity ID ${activity.id} (${activity.type})');
      } else {
        debugPrint('Warning: Could not find activity for "$selectedName"');
        // Create a default activity if not found
        final defaultActivity = _createDefaultActivityForName(selectedName);
        if (defaultActivity != null) {
          reorderedActivities.add(defaultActivity);
          debugPrint('Created default activity for "$selectedName"');
        }
      }
    }

    if (reorderedActivities.isNotEmpty) {
      _activities = reorderedActivities;
      debugPrint('Reordered activities to match selection order');
    }
  }

  Activity? _createDefaultActivityForName(String activityName) {
    switch (activityName.toLowerCase()) {
      case 'breathwork':
        return Activity(
          id: -1,
          name: 'Breathwork',
          type: 'breathwork',
          config: ActivityConfig.breathwork(
            selectedPatternId: defaultBreathworkPatterns.first.id,
            cycles: 3,
            availablePatterns: defaultBreathworkPatterns,
          ),
        );
      case 'meditation':
        return Activity(
          id: -1,
          name: 'Meditation',
          type: 'meditation',
          config: const ActivityConfig.meditation(),
        );
      case 'journal':
      case 'journaling':
        return Activity(
          id: -1,
          name: 'Journal',
          type: 'journaling',
          config: const ActivityConfig.journaling(),
        );
      case 'affirmations':
        return Activity(
          id: -1,
          name: 'Affirmations',
          type: 'affirmations',
          config: const ActivityConfig.affirmations(),
        );
      case 'mood tracking':
      case 'mood_tracking':
        return Activity(
          id: -1,
          name: 'Mood Tracking',
          type: 'moodTracking',
          config: const ActivityConfig.moodTracking(),
        );
      case 'gratitude':
        return Activity(
          id: -1,
          name: 'Gratitude',
          type: 'gratitude',
          config: const ActivityConfig.gratitude(),
        );
      default:
        return null;
    }
  }

  ActivityConfig _getDefaultConfigForType(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'breathwork':
        return ActivityConfig.breathwork(
          selectedPatternId: defaultBreathworkPatterns.first.id,
          cycles: 3,
          availablePatterns: defaultBreathworkPatterns,
        );
      case 'meditation':
        return const ActivityConfig.meditation();
      case 'journal':
      case 'journaling':
        return const ActivityConfig.journaling();
      case 'affirmations':
        return const ActivityConfig.affirmations();
      case 'moodtracking':
      case 'mood_tracking':
        return const ActivityConfig.moodTracking();
      case 'gratitude':
        return const ActivityConfig.gratitude();
      default:
        return const ActivityConfig.journaling(); // fallback
    }
  }

  /// Create or find activity ID for a given activity name
  Future<int?> _createOrFindActivityForName(
      String activityName, JournalDatabase db) async {
    try {
      // Normalize activity name and determine type
      final normalizedName = activityName.toLowerCase().trim();
      String activityType;
      String displayName;

      // Map activity names to types and display names
      switch (normalizedName) {
        case 'breathwork':
          activityType = 'breathwork';
          displayName = 'Breathwork';
          break;
        case 'meditation':
          activityType = 'meditation';
          displayName = 'Meditation';
          break;
        case 'journal':
        case 'journaling':
          activityType = 'journaling';
          displayName = 'Journal';
          break;
        case 'affirmations':
          activityType = 'affirmations';
          displayName = 'Affirmations';
          break;
        case 'mood tracking':
        case 'mood_tracking':
        case 'moodtracking':
          activityType = 'moodTracking';
          displayName = 'Mood Tracking';
          break;
        case 'gratitude':
          activityType = 'gratitude';
          displayName = 'Gratitude';
          break;
        default:
          debugPrint('⚠️ Unknown activity name: "$activityName"');
          return null;
      }

      // Check if activity already exists (get the first one if multiple exist)
      final existingActivities = await (db.select(db.checkInActivities)
            ..where((a) => a.type.equals(activityType))
            ..limit(1))
          .get();

      if (existingActivities.isNotEmpty) {
        return existingActivities.first.id;
      }

      // Create new activity with default configuration
      final defaultConfig = _getDefaultConfigForType(activityType);

      final activityId = await db.into(db.checkInActivities).insert(
            CheckInActivitiesCompanion.insert(
              type: activityType,
              name: displayName,
              config: jsonEncode(defaultConfig.toJson()),
              lastUpdated: DateTime.now(),
            ),
          );

      debugPrint(
          '✅ Created new activity: ID $activityId, type "$activityType", name "$displayName"');
      return activityId;
    } catch (e) {
      debugPrint('❌ Error creating/finding activity for "$activityName": $e');
      return null;
    }
  }

  Future<void> _loadExistingActivitiesOrDefaults() async {
    try {
      final db = ref.read(journalDatabaseProvider);
      final routine = await db.getRoutineById(widget.routineId);

      if (routine != null &&
          routine.activities.isNotEmpty &&
          routine.activities != '[]') {
        debugPrint('Raw routine activities: ${routine.activities}');

        // Parse the activities field - it could be either activity IDs (new format) or activity names (old format)
        final activitiesData = jsonDecode(routine.activities) as List;
        final loadedActivities = <Activity>[];

        if (activitiesData.isNotEmpty) {
          // Check if the first element is an integer (new format) or string (old format)
          if (activitiesData.first is int) {
            // New format: activity IDs - load with routine-specific configurations
            debugPrint(
                'Loading activities by IDs (new format) with routine-specific configs');
            final activityIds = activitiesData.cast<int>();

            for (final activityId in activityIds) {
              final baseActivity = await db.getActivityById(activityId);
              if (baseActivity != null) {
                // Load routine-specific configuration for this activity
                final routineConfig = await db.getRoutineActivityConfig(
                  widget.routineId,
                  activityId,
                );

                // Use routine-specific config if available, otherwise use base activity config
                final finalConfig = routineConfig ?? baseActivity.config;

                final activityWithConfig =
                    baseActivity.copyWith(config: finalConfig);
                loadedActivities.add(activityWithConfig);

                debugPrint(
                    'Loaded activity ID $activityId: ${baseActivity.name} (${baseActivity.type}) with ${routineConfig != null ? 'routine-specific' : 'default'} config');
              }
            }
          } else {
            // Old format: activity names - migrate to new format and load existing configs
            debugPrint(
                'Converting from old format (activity names) to new format with migration');
            final activityNames = activitiesData.cast<String>();

            for (final activityName in activityNames) {
              // Create or find existing activity for this name
              final activityId =
                  await _createOrFindActivityForName(activityName, db);
              if (activityId != null) {
                // Load the base activity
                final baseActivity = await db.getActivityById(activityId);
                if (baseActivity != null) {
                  // Try to load routine-specific configuration
                  final routineConfig = await db.getRoutineActivityConfig(
                    widget.routineId,
                    activityId,
                  );

                  // Use routine-specific config if available, otherwise use base activity config
                  final finalConfig = routineConfig ?? baseActivity.config;

                  final activityWithConfig =
                      baseActivity.copyWith(config: finalConfig);
                  loadedActivities.add(activityWithConfig);

                  debugPrint(
                      'Migrated activity "$activityName" to ID $activityId with ${routineConfig != null ? 'routine-specific' : 'default'} config');
                }
              } else {
                // Fallback: create default activity if migration fails
                final activity = _createDefaultActivityForName(activityName);
                if (activity != null) {
                  loadedActivities.add(activity);
                  debugPrint(
                      'Created fallback activity for "$activityName": ${activity.name} (${activity.type})');
                }
              }
            }

            // Update the routine to use the new format (activity IDs) if we successfully migrated
            if (loadedActivities.isNotEmpty) {
              final activityIds = loadedActivities
                  .map((a) => a.id)
                  .where((id) => id != -1)
                  .toList();
              if (activityIds.isNotEmpty) {
                try {
                  await db.updateRoutineActivities(
                      widget.routineId, activityIds);
                  debugPrint(
                      'Migrated routine ${widget.routineId} to new format with IDs: $activityIds');
                } catch (e) {
                  debugPrint(
                      'Failed to update routine with new activity IDs: $e');
                }
              }
            }
          }
        }

        if (loadedActivities.isNotEmpty) {
          if (mounted) {
            setState(() {
              _activities = loadedActivities;
            });
            // Update validation errors after loading activities
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _updateValidationErrors();
            });
          } else {
            _activities = loadedActivities;
          }
          debugPrint(
              'Loaded ${loadedActivities.length} existing activities for routine ${widget.routineId}');
          return;
        }
      }
    } catch (e) {
      debugPrint('Error loading existing activities: $e');
    }

    // Fallback to default activities if no existing activities found
    if (mounted) {
      setState(() {
        _initializeDefaultActivities();
      });
      // Update validation errors after initializing default activities
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateValidationErrors();
      });
    } else {
      _initializeDefaultActivities();
    }
  }

  void _initializeDefaultActivities() {
    _activities = widget.selectedActivities.map((activityName) {
      switch (activityName.toLowerCase()) {
        case 'breathwork':
          return Activity(
            id: -1,
            name: 'Breathwork',
            type: 'breathwork',
            config: ActivityConfig.breathwork(
              selectedPatternId: defaultBreathworkPatterns.first.id,
              cycles: 3,
              availablePatterns: defaultBreathworkPatterns,
            ),
          );
        case 'meditation':
          return Activity(
            id: -1,
            name: 'Meditation',
            type: 'meditation',
            config: const ActivityConfig.meditation(),
          );
        case 'journal':
        case 'journaling':
          return Activity(
            id: -1,
            name: 'Journal',
            type: 'journaling',
            config: const ActivityConfig.journaling(),
          );
        case 'affirmations':
          return Activity(
            id: -1,
            name: 'Affirmations',
            type: 'affirmations',
            config: const ActivityConfig.affirmations(),
          );
        case 'mood tracking':
        case 'mood_tracking': // Add this case to handle both formats
          return Activity(
            id: -1,
            name: 'Mood Tracking',
            type: 'mood_tracking',
            config: const ActivityConfig.moodTracking(),
          );
        case 'gratitude':
          return Activity(
            id: -1,
            name: 'Gratitude',
            type: 'gratitude',
            config: const ActivityConfig.gratitude(),
          );
        default:
          throw UnsupportedError('Unsupported activity type: $activityName');
      }
    }).toList();
  }

  Future<void> _goToNext() async {
    final currentActivity = widget.selectedActivities[widget.currentIndex];

    // Skip configuration for mood tracking and gratitude - use defaults
    if (_shouldSkipConfiguration(currentActivity)) {
      await _skipToNextActivity();
      return;
    }

    // Validate current activity configuration
    final activity = _activities[widget.currentIndex];
    final validationErrors = activity.config.getValidationErrors();

    if (validationErrors.isNotEmpty) {
      // Update validation errors and show them to user
      setState(() {
        _validationErrors = validationErrors;
      });
      return; // Don't proceed if validation fails
    }

    // Clear validation errors if validation passes
    setState(() {
      _validationErrors = [];
    });

    // Save current activity configuration
    if (_formKeys[widget.currentIndex].currentState?.validate() ?? false) {
      _formKeys[widget.currentIndex].currentState?.save();

      if (!mounted) return;
      setState(() {
        _isLoading = true;
      });

      try {
        if (widget.currentIndex < widget.selectedActivities.length - 1) {
          // Find next activity index that needs configuration
          int nextIndex =
              _findNextConfigurableActivity(widget.currentIndex + 1);

          // Convert activities to JSON for navigation
          final activitiesJson = _activities.map((a) => a.toJson()).toList();

          if (nextIndex < widget.selectedActivities.length) {
            if (mounted) {
              context.pushReplacementNamed(
                RouteNames.activityWizard,
                pathParameters: {
                  'intentionId': widget.intentionId.toString(),
                  'routineId': widget.routineId.toString(),
                },
                extra: {
                  'selectedActivities': widget.selectedActivities,
                  'currentIndex': nextIndex,
                  'activities': activitiesJson,
                },
              );
            }
          } else {
            // All activities configured, save and return
            await _saveAndFinish();
          }
        } else {
          // All activities configured, save and return
          await _saveAndFinish();
        }
      } catch (e) {
        if (mounted) {
          // Failed to save activity configuration - silently handle
          debugPrint('Failed to save activity configuration: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  bool _shouldSkipConfiguration(String activityName) {
    final lowerName = activityName.toLowerCase();
    return lowerName == 'mood tracking' ||
        lowerName == 'mood_tracking' ||
        lowerName == 'gratitude';
  }

  /// Returns the total number of activities that require configuration
  /// (excludes gratitude and mood tracking activities)
  int _getConfigurableActivitiesCount() {
    return widget.selectedActivities
        .where((activity) => !_shouldSkipConfiguration(activity))
        .length;
  }

  /// Returns the current step number among configurable activities only
  /// (excludes gratitude and mood tracking from the count)
  int _getCurrentConfigurableStep() {
    int configurableStep = 0;
    for (int i = 0; i < widget.currentIndex; i++) {
      if (!_shouldSkipConfiguration(widget.selectedActivities[i])) {
        configurableStep++;
      }
    }
    // Add 1 if the current activity is configurable
    if (!_shouldSkipConfiguration(
        widget.selectedActivities[widget.currentIndex])) {
      configurableStep++;
    }
    return configurableStep;
  }

  int _findNextConfigurableActivity(int startIndex) {
    for (int i = startIndex; i < widget.selectedActivities.length; i++) {
      if (!_shouldSkipConfiguration(widget.selectedActivities[i])) {
        return i;
      }
    }
    return widget.selectedActivities
        .length; // Return length if no configurable activities found
  }

  Future<void> _skipToNextActivity() async {
    setState(() => _isLoading = true);

    try {
      // Current activity uses default configuration (already set in _createDefaultActivities)

      if (widget.currentIndex < widget.selectedActivities.length - 1) {
        // Find next activity that needs configuration
        int nextIndex = _findNextConfigurableActivity(widget.currentIndex + 1);

        if (nextIndex < widget.selectedActivities.length) {
          // Navigate to next activity that needs configuration
          final activitiesJson = _activities.map((a) => a.toJson()).toList();

          if (mounted) {
            context.pushReplacementNamed(
              RouteNames.activityWizard,
              pathParameters: {
                'intentionId': widget.intentionId.toString(),
                'routineId': widget.routineId.toString(),
              },
              extra: {
                'selectedActivities': widget.selectedActivities,
                'currentIndex': nextIndex,
                'activities': activitiesJson,
              },
            );
          }
        } else {
          // All activities processed, save and return
          await _saveAndFinish();
        }
      } else {
        // Last activity, save and return
        await _saveAndFinish();
      }
    } catch (e) {
      if (mounted) {
        debugPrint('Failed to skip activity configuration: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveAndFinish() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });

    try {
      final db = ref.read(journalDatabaseProvider);
      final List<int> activityIds = [];

      // Save each activity configuration
      for (int i = 0; i < _activities.length; i++) {
        final activity = _activities[i];
        try {
          int activityId = activity.id;

          if (activityId == -1) {
            // Create a new base activity with default configuration
            activityId = await db.createActivity(
              type: activity.type,
              name: activity.name, // Use the base name without routine suffix
              config:
                  jsonEncode(_getDefaultConfigForType(activity.type).toJson()),
            );
            debugPrint(
                'Created new base activity ID $activityId for type ${activity.type}');

            // Update the activity in our list with the new ID
            _activities[i] = activity.copyWith(id: activityId);
          }

          // Save routine-specific configuration to routine_activity_configs table
          await db.saveRoutineActivityConfig(
            widget.routineId,
            activityId,
            activity.config,
          );
          debugPrint(
              'Saved routine-specific config for activity $activityId in routine ${widget.routineId}');

          activityIds.add(activityId);
        } catch (e) {
          debugPrint('Error saving activity ${activity.id}: $e');
          if (mounted) {
            // Failed to save activity - silently handle
            debugPrint(
                'Failed to save activity: ${activity.name}. Please try again.');
          }
          rethrow;
        }
      }

      // Update the routine to store activity IDs instead of names
      await db.updateRoutineActivities(widget.routineId, activityIds);
      debugPrint(
          'Updated routine ${widget.routineId} with activity IDs: $activityIds');

      // Invalidate relevant providers to ensure fresh data is loaded
      ref.invalidate(journalDatabaseProvider);

      // Add a small delay to ensure database operations are fully committed
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        // All activities configured successfully - no toast needed

        // Navigate back to the intentions list without parameters
        context.goNamed(
          RouteNames.intentionsList,
          queryParameters: {
            'intentionId': widget.intentionId.toString(),
            'routineId': widget.routineId.toString(),
          },
        );
      }
    } catch (e) {
      if (mounted) {
        // Failed to save activities - silently handle
        debugPrint('Failed to save activities: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reset form state when activity changes (only if activities are initialized)
    if (widget.currentIndex < _formKeys.length) {
      _formKeys[widget.currentIndex] = GlobalKey<FormState>();
    }
  }

  Future<bool> _onWillPop() async {
    // Only delete the routine if it's truly incomplete (no activities configured)
    // This prevents deletion of existing routines that are being edited
    final routinesAsyncValue =
        ref.read(routinesForIntentionProvider(widget.intentionId));
    if (routinesAsyncValue is AsyncData<List<CheckInRoutine>>) {
      final routines = routinesAsyncValue.value;
      final routine = routines.firstWhere((r) => r.id == widget.routineId,
          orElse: () => throw StateError('Routine not found'));

      if (_isRoutineIncomplete(routine)) {
        try {
          final notifier = ref.read(routineCrudNotifierProvider.notifier);
          await notifier.deleteRoutine(widget.routineId);
          debugPrint(
              'Deleted incomplete routine ${widget.routineId} from wizard');
        } catch (e) {
          debugPrint('Error deleting incomplete routine from wizard: $e');
        }
      }
    }
    return true;
  }

  bool _isRoutineIncomplete(CheckInRoutine routine) {
    // Consider a routine incomplete if it has no activities configured
    try {
      final activities = jsonDecode(routine.activities) as List;
      return activities.isEmpty;
    } catch (e) {
      // If we can't parse activities, assume it's incomplete
      return true;
    }
  }

  Future<void> _showExitConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmationDialog(
        title: 'End Setup?',
        message:
            'Are you sure you want to end the activity configuration? Your progress will be lost.',
        confirmText: 'End Setup',
        cancelText: 'Continue',
        isDestructive: true,
      ),
    );

    if (confirmed == true && mounted) {
      // Only delete if this is truly an incomplete creation flow
      final routinesAsyncValue =
          ref.read(routinesForIntentionProvider(widget.intentionId));
      if (routinesAsyncValue is AsyncData<List<CheckInRoutine>>) {
        final routines = routinesAsyncValue.value;
        try {
          final routine = routines.firstWhere((r) => r.id == widget.routineId);

          if (_isRoutineIncomplete(routine)) {
            // Delete the incomplete routine
            try {
              final routineNotifier =
                  ref.read(routineCrudNotifierProvider.notifier);
              await routineNotifier.deleteRoutine(widget.routineId);
              debugPrint('Deleted incomplete routine ${widget.routineId}');
            } catch (e) {
              debugPrint('Error deleting incomplete routine: $e');
            }

            // Also delete the intention if it was created in this session
            // Only delete intention if the routine was truly incomplete
            try {
              final intentionNotifier =
                  ref.read(intentionCrudNotifierProvider.notifier);
              await intentionNotifier.deleteIntention(widget.intentionId);
              debugPrint('Deleted incomplete intention ${widget.intentionId}');
            } catch (e) {
              debugPrint('Error deleting incomplete intention: $e');
            }
          }
        } catch (e) {
          debugPrint('Routine not found: $e');
        }
      }

      if (mounted) {
        context.go('/intentions');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Guard against empty activities list during initialization
    if (_activities.isEmpty || widget.currentIndex >= _activities.length) {
      return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text('Loading...'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final currentActivity = widget.selectedActivities[widget.currentIndex];
    final isLast = widget.currentIndex == widget.selectedActivities.length - 1;
    final activity = _activities[widget.currentIndex];

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await _onWillPop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: _showExitConfirmation,
          ),
          actions: [
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: CircularProgressIndicator(),
                ),
              )
            else
              TextButton(
                onPressed: _goToNext,
                style: _validationErrors.isNotEmpty
                    ? TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.error,
                      )
                    : null,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_validationErrors.isNotEmpty) ...[
                      Icon(
                        Icons.error_outline,
                        size: 16,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(isLast ? 'Finish' : 'Next'),
                  ],
                ),
              ),
          ],
        ),
        body: Form(
          key: _formKeys[widget.currentIndex],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: _getConfigurableActivitiesCount() > 0
                    ? _getCurrentConfigurableStep() /
                        _getConfigurableActivitiesCount()
                    : 0.0,
                minHeight: 4,
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Step ${_getCurrentConfigurableStep()} of ${_getConfigurableActivitiesCount()}',
                  style: Theme.of(context).textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
              ),

              // Current activity settings form
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Show informative messages if any configuration is needed
                      if (_validationErrors.isNotEmpty)
                        ActivityInfoWidget(
                          messages: _validationErrors,
                          activityType:
                              widget.selectedActivities[widget.currentIndex],
                          padding: const EdgeInsets.all(16.0),
                        ),
                      _buildActivitySpecificForm(
                          currentActivity.toLowerCase(), activity),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivitySpecificForm(String activityType, Activity activity) {
    // Check if this activity should be skipped (mood tracking and gratitude)
    if (_shouldSkipConfiguration(activityType)) {
      return Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              activityType.toLowerCase() == 'gratitude'
                  ? Icons.favorite_rounded
                  : Icons.track_changes_rounded,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              '${activity.name} is ready!',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              activityType.toLowerCase() == 'gratitude'
                  ? 'Your gratitude activity is configured with default settings. You\'ll be able to write 3 gratitude entries during your routine.'
                  : 'Your mood tracking activity is configured with default settings. You\'ll be able to track your mood and add notes during your routine.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Callback to update the activity config in the wizard
    void onConfigChanged(ActivityConfig newConfig) {
      if (mounted) {
        setState(() {
          _activities[widget.currentIndex] =
              activity.copyWith(config: newConfig);
          // Update validation errors when config changes
          _validationErrors = newConfig.getValidationErrors();
        });
      }
    }

    switch (activityType) {
      case 'breathwork':
        return BreathworkSettingsScreen(
          activity: activity,
          routineId: widget.routineId,
          onConfigChanged: onConfigChanged,
        );
      case 'meditation':
        return MeditationSettingsScreen(
          activity: activity,
          routineId: widget.routineId,
          onConfigChanged: onConfigChanged,
        );
      case 'journal':
      case 'journaling':
        return JournalingSettingsScreen(
          activity: activity,
          routineId: widget.routineId,
          onConfigChanged: onConfigChanged,
        );
      case 'affirmations':
        return AffirmationsSettingsScreen(
          activity: activity,
          routineId: widget.routineId,
          onConfigChanged: onConfigChanged,
        );
      default:
        return Center(
          child: Text('Settings for $activityType are not available.'),
        );
    }
  }
}
