import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_data.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'activity_data_provider.g.dart';

@riverpod
class ActivityDataNotifier extends _$ActivityDataNotifier {
  @override
  Future<ActivityData?> build(int activityId, DateTime date) async {
    final db = ref.watch(journalDatabaseProvider);
    final records = await db.getActivityRecordsForDate(activityId, date);
    return records.isNotEmpty ? records.first : null;
  }

  Future<void> saveActivityData(Map<String, dynamic> data) async {
    final db = ref.read(journalDatabaseProvider);

    print('ActivityDataNotifier: Saving data: $data');

    // Process the data to ensure responses are properly formatted
    if (data.containsKey('responses')) {
      final responses = data['responses'];
      if (responses is Map<String, dynamic>) {
        // Make sure all response values are strings
        final processedResponses = <String, String>{};
        responses.forEach((key, value) {
          processedResponses[key] = value?.toString() ?? '';
        });

        // Replace with the processed responses
        data = Map<String, dynamic>.from(data);
        data['responses'] = processedResponses;

        print(
            'ActivityDataNotifier: Processed responses: ${processedResponses}');
      }
    }

    // Encode to JSON
    final encodedData = json.encode(data);
    print('ActivityDataNotifier: Encoded data: $encodedData');

    try {
      // Check if we already have a record for this activity and date
      final existingRecords =
          await db.getActivityRecordsForDate(activityId, date);

      if (existingRecords.isNotEmpty) {
        // Update the most recent existing record
        final mostRecent =
            existingRecords.reduce((a, b) => a.date.isAfter(b.date) ? a : b);

        print(
            'ActivityDataNotifier: Updating existing record ${mostRecent.id} for activity $activityId');

        await db.updateActivityRecord(
          ActivityData(
            id: mostRecent.id,
            activityId: activityId,
            date: date,
            data: data, // Use the Map directly, not the encoded string
            status: 'completed',
            completedAt: DateTime.now(),
          ),
        );
      } else {
        // Create a new record if none exists
        print(
            'ActivityDataNotifier: Creating new record for activity $activityId');
        await db.saveActivityRecord(
          ActivityRecordsCompanion(
            activityId: Value(activityId),
            date: Value(date),
            data: Value(encodedData),
            status: const Value('completed'),
            completedAt: Value(DateTime.now()),
          ),
        );
      }

      print(
          'ActivityDataNotifier: Successfully saved with activityId: $activityId, date: $date');
    } catch (e) {
      print('ActivityDataNotifier: Error saving: $e');
    }

    ref.invalidateSelf();
  }

  Future<void> updateActivityData(ActivityData data) async {
    final db = ref.read(journalDatabaseProvider);
    await db.updateActivityRecord(data);
    ref.invalidateSelf();
  }

  Future<List<ActivityData>> getDateRangeData(
      DateTime start, DateTime end) async {
    final db = ref.read(journalDatabaseProvider);
    return db.getActivityRecordsForDateRange(activityId, start, end);
  }

  Stream<List<ActivityData>> watchActivityData() {
    final db = ref.read(journalDatabaseProvider);
    return db.watchActivityRecords(activityId);
  }
}
