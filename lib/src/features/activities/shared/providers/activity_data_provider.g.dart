// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_data_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activityDataNotifierHash() =>
    r'49309e1b7b66d2a21eb025d16f1f3d9e1ed23379';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ActivityDataNotifier
    extends BuildlessAutoDisposeAsyncNotifier<ActivityData?> {
  late final int activityId;
  late final DateTime date;

  FutureOr<ActivityData?> build(
    int activityId,
    DateTime date,
  );
}

/// See also [ActivityDataNotifier].
@ProviderFor(ActivityDataNotifier)
const activityDataNotifierProvider = ActivityDataNotifierFamily();

/// See also [ActivityDataNotifier].
class ActivityDataNotifierFamily extends Family<AsyncValue<ActivityData?>> {
  /// See also [ActivityDataNotifier].
  const ActivityDataNotifierFamily();

  /// See also [ActivityDataNotifier].
  ActivityDataNotifierProvider call(
    int activityId,
    DateTime date,
  ) {
    return ActivityDataNotifierProvider(
      activityId,
      date,
    );
  }

  @override
  ActivityDataNotifierProvider getProviderOverride(
    covariant ActivityDataNotifierProvider provider,
  ) {
    return call(
      provider.activityId,
      provider.date,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'activityDataNotifierProvider';
}

/// See also [ActivityDataNotifier].
class ActivityDataNotifierProvider extends AutoDisposeAsyncNotifierProviderImpl<
    ActivityDataNotifier, ActivityData?> {
  /// See also [ActivityDataNotifier].
  ActivityDataNotifierProvider(
    int activityId,
    DateTime date,
  ) : this._internal(
          () => ActivityDataNotifier()
            ..activityId = activityId
            ..date = date,
          from: activityDataNotifierProvider,
          name: r'activityDataNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$activityDataNotifierHash,
          dependencies: ActivityDataNotifierFamily._dependencies,
          allTransitiveDependencies:
              ActivityDataNotifierFamily._allTransitiveDependencies,
          activityId: activityId,
          date: date,
        );

  ActivityDataNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.activityId,
    required this.date,
  }) : super.internal();

  final int activityId;
  final DateTime date;

  @override
  FutureOr<ActivityData?> runNotifierBuild(
    covariant ActivityDataNotifier notifier,
  ) {
    return notifier.build(
      activityId,
      date,
    );
  }

  @override
  Override overrideWith(ActivityDataNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: ActivityDataNotifierProvider._internal(
        () => create()
          ..activityId = activityId
          ..date = date,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        activityId: activityId,
        date: date,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ActivityDataNotifier, ActivityData?>
      createElement() {
    return _ActivityDataNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ActivityDataNotifierProvider &&
        other.activityId == activityId &&
        other.date == date;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, activityId.hashCode);
    hash = _SystemHash.combine(hash, date.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ActivityDataNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<ActivityData?> {
  /// The parameter `activityId` of this provider.
  int get activityId;

  /// The parameter `date` of this provider.
  DateTime get date;
}

class _ActivityDataNotifierProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ActivityDataNotifier,
        ActivityData?> with ActivityDataNotifierRef {
  _ActivityDataNotifierProviderElement(super.provider);

  @override
  int get activityId => (origin as ActivityDataNotifierProvider).activityId;
  @override
  DateTime get date => (origin as ActivityDataNotifierProvider).date;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
