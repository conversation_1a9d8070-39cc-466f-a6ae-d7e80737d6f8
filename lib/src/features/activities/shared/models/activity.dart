import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';

part 'activity.freezed.dart';
part 'activity.g.dart';

@Freezed(toJson: false) // Disable generated toJson
class Activity with _$Activity {
  const Activity._();

  const factory Activity({
    required int id,
    required String type,
    required String name,
    required ActivityConfig config,
    @Default(true) bool enabled,
    DateTime? lastUpdated,
  }) = _Activity;

  factory Activity.fromJson(Map<String, dynamic> json) =>
      _$ActivityFromJson(json);

  // Custom toJson method to properly serialize ActivityConfig
  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'name': name,
      'config': config.toJson(), // Use the custom toJson method
      'enabled': enabled,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }
}
