// // lib/src/features/journal/models/activity.dart
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';

part 'activity_data.freezed.dart';
part 'activity_data.g.dart';

@freezed
class ActivityData with _$ActivityData {
  const factory ActivityData({
    required int id,
    required int activityId,
    required DateTime date,
    required Map<String, dynamic> data,
    required String status,
    DateTime? completedAt,
  }) = _ActivityData;

  factory ActivityData.fromJson(Map<String, dynamic> json) =>
      _$ActivityDataFromJson(json);
}

// Extension for safe JSON conversion
extension ActivityConfigX on ActivityConfig {
  static ActivityConfig fromJsonSafe(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      if (!json.containsKey('runtimeType')) {
        throw FormatException('Missing runtimeType in config: $jsonString');
      }
      return ActivityConfig.fromJson(json);
    } catch (e) {
      debugPrint('Error parsing activity config: $e');
      debugPrint('Raw JSON string: $jsonString');
      rethrow;
    }
  }

  String toJsonString() {
    try {
      final json = toJson();
      if (!json.containsKey('runtimeType')) {
        throw FormatException('Missing runtimeType in config JSON');
      }
      return jsonEncode(json);
    } catch (e) {
      rethrow;
    }
  }
}
