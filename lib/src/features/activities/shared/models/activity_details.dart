import 'package:mimi_app/src/core/router/route_names.dart'; // Assuming RouteNames are globally accessible
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart'; // For ActivityConfig
import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart'; // For defaultBreathworkPatterns

class ActivityDetails {
  final String
      type; // Corresponds to Activity.type, e.g., "Meditation", "Breathwork"
  final String
      displayName; // User-facing name, e.g., "Meditation", "Breathwork Practice"
  final String settingsRouteName; // Route name from RouteNames enum/class
  final int activityDbId; // Global unique ID for this activity type
  final ActivityConfig Function()
      defaultConfigFactory; // Factory for default config

  const ActivityDetails({
    required this.type,
    required this.displayName,
    required this.settingsRouteName,
    required this.activityDbId,
    required this.defaultConfigFactory,
  });
}

final List<ActivityDetails> predefinedActivityDetailsList = [
  ActivityDetails(
    type: 'Breathwork', // Must match Activity.type
    displayName: 'Breathwork',
    settingsRouteName: RouteNames.breathworkSettings,
    activityDbId: 1, // Assign a unique ID
    defaultConfigFactory: () => ActivityConfig.breathwork(
      selectedPatternId:
          defaultBreathworkPatterns.first.id, // Use first pattern as default
      cycles: 5, // Default cycles
      availablePatterns:
          defaultBreathworkPatterns, // Provide available patterns
      // backgroundMusicId and cuesMuted will use their defaults if not specified here
    ),
  ),
  ActivityDetails(
    type: 'Meditation', // Must match Activity.type
    displayName: 'Meditation',
    settingsRouteName: RouteNames.meditationSettings,
    activityDbId: 2, // Assign a unique ID
    defaultConfigFactory: () =>
        ActivityConfig.meditation(), // Provide default config
  ),
  ActivityDetails(
    type: 'Journaling', // Must match Activity.type
    displayName: 'Journaling',
    settingsRouteName: RouteNames.journalingSettings,
    activityDbId: 3, // Assign a unique ID
    defaultConfigFactory: () =>
        ActivityConfig.journaling(), // Provide default config
  ),
  ActivityDetails(
    type: 'Affirmations', // Must match Activity.type
    displayName: 'Affirmations',
    settingsRouteName: RouteNames.affirmationsSettings,
    activityDbId: 4, // Assign a unique ID
    defaultConfigFactory: () =>
        ActivityConfig.affirmations(), // Provide default config
  ),
  ActivityDetails(
    type: 'MoodTracking', // Must match Activity.type
    displayName: 'Mood Tracking',
    settingsRouteName: RouteNames.moodTrackingSettings,
    activityDbId: 5, // Assign a unique ID
    defaultConfigFactory: () =>
        ActivityConfig.moodTracking(), // Provide default config
  ),
  ActivityDetails(
    type: 'Gratitude', // Must match Activity.type
    displayName: 'Gratitude',
    settingsRouteName: RouteNames.gratitudeSettings,
    activityDbId: 6, // Assign a unique ID
    defaultConfigFactory: () =>
        ActivityConfig.gratitude(), // Provide default config
  ),
];

ActivityDetails? getActivityDetailsByType(String activityType) {
  try {
    return predefinedActivityDetailsList
        .firstWhere((details) => details.type == activityType);
  } catch (e) {
    print('Error: ActivityDetails not found for type: $activityType');
    return null;
  }
}
