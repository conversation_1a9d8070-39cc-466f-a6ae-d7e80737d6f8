// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'activity_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ActivityData _$ActivityDataFromJson(Map<String, dynamic> json) {
  return _ActivityData.fromJson(json);
}

/// @nodoc
mixin _$ActivityData {
  int get id => throw _privateConstructorUsedError;
  int get activityId => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  Map<String, dynamic> get data => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;

  /// Serializes this ActivityData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ActivityData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ActivityDataCopyWith<ActivityData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActivityDataCopyWith<$Res> {
  factory $ActivityDataCopyWith(
          ActivityData value, $Res Function(ActivityData) then) =
      _$ActivityDataCopyWithImpl<$Res, ActivityData>;
  @useResult
  $Res call(
      {int id,
      int activityId,
      DateTime date,
      Map<String, dynamic> data,
      String status,
      DateTime? completedAt});
}

/// @nodoc
class _$ActivityDataCopyWithImpl<$Res, $Val extends ActivityData>
    implements $ActivityDataCopyWith<$Res> {
  _$ActivityDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ActivityData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? activityId = null,
    Object? date = null,
    Object? data = null,
    Object? status = null,
    Object? completedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      activityId: null == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ActivityDataImplCopyWith<$Res>
    implements $ActivityDataCopyWith<$Res> {
  factory _$$ActivityDataImplCopyWith(
          _$ActivityDataImpl value, $Res Function(_$ActivityDataImpl) then) =
      __$$ActivityDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      int activityId,
      DateTime date,
      Map<String, dynamic> data,
      String status,
      DateTime? completedAt});
}

/// @nodoc
class __$$ActivityDataImplCopyWithImpl<$Res>
    extends _$ActivityDataCopyWithImpl<$Res, _$ActivityDataImpl>
    implements _$$ActivityDataImplCopyWith<$Res> {
  __$$ActivityDataImplCopyWithImpl(
      _$ActivityDataImpl _value, $Res Function(_$ActivityDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ActivityData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? activityId = null,
    Object? date = null,
    Object? data = null,
    Object? status = null,
    Object? completedAt = freezed,
  }) {
    return _then(_$ActivityDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      activityId: null == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ActivityDataImpl implements _ActivityData {
  const _$ActivityDataImpl(
      {required this.id,
      required this.activityId,
      required this.date,
      required final Map<String, dynamic> data,
      required this.status,
      this.completedAt})
      : _data = data;

  factory _$ActivityDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ActivityDataImplFromJson(json);

  @override
  final int id;
  @override
  final int activityId;
  @override
  final DateTime date;
  final Map<String, dynamic> _data;
  @override
  Map<String, dynamic> get data {
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_data);
  }

  @override
  final String status;
  @override
  final DateTime? completedAt;

  @override
  String toString() {
    return 'ActivityData(id: $id, activityId: $activityId, date: $date, data: $data, status: $status, completedAt: $completedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActivityDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.activityId, activityId) ||
                other.activityId == activityId) &&
            (identical(other.date, date) || other.date == date) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, activityId, date,
      const DeepCollectionEquality().hash(_data), status, completedAt);

  /// Create a copy of ActivityData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ActivityDataImplCopyWith<_$ActivityDataImpl> get copyWith =>
      __$$ActivityDataImplCopyWithImpl<_$ActivityDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ActivityDataImplToJson(
      this,
    );
  }
}

abstract class _ActivityData implements ActivityData {
  const factory _ActivityData(
      {required final int id,
      required final int activityId,
      required final DateTime date,
      required final Map<String, dynamic> data,
      required final String status,
      final DateTime? completedAt}) = _$ActivityDataImpl;

  factory _ActivityData.fromJson(Map<String, dynamic> json) =
      _$ActivityDataImpl.fromJson;

  @override
  int get id;
  @override
  int get activityId;
  @override
  DateTime get date;
  @override
  Map<String, dynamic> get data;
  @override
  String get status;
  @override
  DateTime? get completedAt;

  /// Create a copy of ActivityData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ActivityDataImplCopyWith<_$ActivityDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
