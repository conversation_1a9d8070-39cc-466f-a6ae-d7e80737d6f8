// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'activity_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ActivityConfig {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> prompts, bool includeDate)
        journaling,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)
        meditation,
    required TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)
        breathwork,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)
        affirmations,
    required TResult Function(List<String> moods, bool includeNote)
        moodTracking,
    required TResult Function(List<String> prompts, int numberOfEntries)
        gratitude,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> prompts, bool includeDate)? journaling,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult? Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult? Function(List<String> moods, bool includeNote)? moodTracking,
    TResult? Function(List<String> prompts, int numberOfEntries)? gratitude,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> prompts, bool includeDate)? journaling,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult Function(List<String> moods, bool includeNote)? moodTracking,
    TResult Function(List<String> prompts, int numberOfEntries)? gratitude,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JournalingConfig value) journaling,
    required TResult Function(MeditationConfig value) meditation,
    required TResult Function(BreathworkConfig value) breathwork,
    required TResult Function(AffirmationsConfig value) affirmations,
    required TResult Function(MoodTrackingConfig value) moodTracking,
    required TResult Function(GratitudeConfig value) gratitude,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JournalingConfig value)? journaling,
    TResult? Function(MeditationConfig value)? meditation,
    TResult? Function(BreathworkConfig value)? breathwork,
    TResult? Function(AffirmationsConfig value)? affirmations,
    TResult? Function(MoodTrackingConfig value)? moodTracking,
    TResult? Function(GratitudeConfig value)? gratitude,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JournalingConfig value)? journaling,
    TResult Function(MeditationConfig value)? meditation,
    TResult Function(BreathworkConfig value)? breathwork,
    TResult Function(AffirmationsConfig value)? affirmations,
    TResult Function(MoodTrackingConfig value)? moodTracking,
    TResult Function(GratitudeConfig value)? gratitude,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActivityConfigCopyWith<$Res> {
  factory $ActivityConfigCopyWith(
          ActivityConfig value, $Res Function(ActivityConfig) then) =
      _$ActivityConfigCopyWithImpl<$Res, ActivityConfig>;
}

/// @nodoc
class _$ActivityConfigCopyWithImpl<$Res, $Val extends ActivityConfig>
    implements $ActivityConfigCopyWith<$Res> {
  _$ActivityConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$JournalingConfigImplCopyWith<$Res> {
  factory _$$JournalingConfigImplCopyWith(_$JournalingConfigImpl value,
          $Res Function(_$JournalingConfigImpl) then) =
      __$$JournalingConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> prompts, bool includeDate});
}

/// @nodoc
class __$$JournalingConfigImplCopyWithImpl<$Res>
    extends _$ActivityConfigCopyWithImpl<$Res, _$JournalingConfigImpl>
    implements _$$JournalingConfigImplCopyWith<$Res> {
  __$$JournalingConfigImplCopyWithImpl(_$JournalingConfigImpl _value,
      $Res Function(_$JournalingConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? prompts = null,
    Object? includeDate = null,
  }) {
    return _then(_$JournalingConfigImpl(
      prompts: null == prompts
          ? _value._prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<String>,
      includeDate: null == includeDate
          ? _value.includeDate
          : includeDate // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$JournalingConfigImpl extends JournalingConfig {
  const _$JournalingConfigImpl(
      {final List<String> prompts = const [], this.includeDate = true})
      : _prompts = prompts,
        super._();

  final List<String> _prompts;
  @override
  @JsonKey()
  List<String> get prompts {
    if (_prompts is EqualUnmodifiableListView) return _prompts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prompts);
  }

  @override
  @JsonKey()
  final bool includeDate;

  @override
  String toString() {
    return 'ActivityConfig.journaling(prompts: $prompts, includeDate: $includeDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JournalingConfigImpl &&
            const DeepCollectionEquality().equals(other._prompts, _prompts) &&
            (identical(other.includeDate, includeDate) ||
                other.includeDate == includeDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_prompts), includeDate);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JournalingConfigImplCopyWith<_$JournalingConfigImpl> get copyWith =>
      __$$JournalingConfigImplCopyWithImpl<_$JournalingConfigImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> prompts, bool includeDate)
        journaling,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)
        meditation,
    required TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)
        breathwork,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)
        affirmations,
    required TResult Function(List<String> moods, bool includeNote)
        moodTracking,
    required TResult Function(List<String> prompts, int numberOfEntries)
        gratitude,
  }) {
    return journaling(prompts, includeDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> prompts, bool includeDate)? journaling,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult? Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult? Function(List<String> moods, bool includeNote)? moodTracking,
    TResult? Function(List<String> prompts, int numberOfEntries)? gratitude,
  }) {
    return journaling?.call(prompts, includeDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> prompts, bool includeDate)? journaling,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult Function(List<String> moods, bool includeNote)? moodTracking,
    TResult Function(List<String> prompts, int numberOfEntries)? gratitude,
    required TResult orElse(),
  }) {
    if (journaling != null) {
      return journaling(prompts, includeDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JournalingConfig value) journaling,
    required TResult Function(MeditationConfig value) meditation,
    required TResult Function(BreathworkConfig value) breathwork,
    required TResult Function(AffirmationsConfig value) affirmations,
    required TResult Function(MoodTrackingConfig value) moodTracking,
    required TResult Function(GratitudeConfig value) gratitude,
  }) {
    return journaling(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JournalingConfig value)? journaling,
    TResult? Function(MeditationConfig value)? meditation,
    TResult? Function(BreathworkConfig value)? breathwork,
    TResult? Function(AffirmationsConfig value)? affirmations,
    TResult? Function(MoodTrackingConfig value)? moodTracking,
    TResult? Function(GratitudeConfig value)? gratitude,
  }) {
    return journaling?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JournalingConfig value)? journaling,
    TResult Function(MeditationConfig value)? meditation,
    TResult Function(BreathworkConfig value)? breathwork,
    TResult Function(AffirmationsConfig value)? affirmations,
    TResult Function(MoodTrackingConfig value)? moodTracking,
    TResult Function(GratitudeConfig value)? gratitude,
    required TResult orElse(),
  }) {
    if (journaling != null) {
      return journaling(this);
    }
    return orElse();
  }
}

abstract class JournalingConfig extends ActivityConfig {
  const factory JournalingConfig(
      {final List<String> prompts,
      final bool includeDate}) = _$JournalingConfigImpl;
  const JournalingConfig._() : super._();

  List<String> get prompts;
  bool get includeDate;

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JournalingConfigImplCopyWith<_$JournalingConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MeditationConfigImplCopyWith<$Res> {
  factory _$$MeditationConfigImplCopyWith(_$MeditationConfigImpl value,
          $Res Function(_$MeditationConfigImpl) then) =
      __$$MeditationConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<Map<String, dynamic>> audioTracks,
      int defaultDuration,
      Map<String, dynamic>? selectedTrack});
}

/// @nodoc
class __$$MeditationConfigImplCopyWithImpl<$Res>
    extends _$ActivityConfigCopyWithImpl<$Res, _$MeditationConfigImpl>
    implements _$$MeditationConfigImplCopyWith<$Res> {
  __$$MeditationConfigImplCopyWithImpl(_$MeditationConfigImpl _value,
      $Res Function(_$MeditationConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioTracks = null,
    Object? defaultDuration = null,
    Object? selectedTrack = freezed,
  }) {
    return _then(_$MeditationConfigImpl(
      audioTracks: null == audioTracks
          ? _value._audioTracks
          : audioTracks // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      defaultDuration: null == defaultDuration
          ? _value.defaultDuration
          : defaultDuration // ignore: cast_nullable_to_non_nullable
              as int,
      selectedTrack: freezed == selectedTrack
          ? _value._selectedTrack
          : selectedTrack // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$MeditationConfigImpl extends MeditationConfig {
  const _$MeditationConfigImpl(
      {final List<Map<String, dynamic>> audioTracks = const [],
      this.defaultDuration = 5,
      final Map<String, dynamic>? selectedTrack})
      : _audioTracks = audioTracks,
        _selectedTrack = selectedTrack,
        super._();

  final List<Map<String, dynamic>> _audioTracks;
  @override
  @JsonKey()
  List<Map<String, dynamic>> get audioTracks {
    if (_audioTracks is EqualUnmodifiableListView) return _audioTracks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_audioTracks);
  }

  @override
  @JsonKey()
  final int defaultDuration;
  final Map<String, dynamic>? _selectedTrack;
  @override
  Map<String, dynamic>? get selectedTrack {
    final value = _selectedTrack;
    if (value == null) return null;
    if (_selectedTrack is EqualUnmodifiableMapView) return _selectedTrack;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ActivityConfig.meditation(audioTracks: $audioTracks, defaultDuration: $defaultDuration, selectedTrack: $selectedTrack)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MeditationConfigImpl &&
            const DeepCollectionEquality()
                .equals(other._audioTracks, _audioTracks) &&
            (identical(other.defaultDuration, defaultDuration) ||
                other.defaultDuration == defaultDuration) &&
            const DeepCollectionEquality()
                .equals(other._selectedTrack, _selectedTrack));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_audioTracks),
      defaultDuration,
      const DeepCollectionEquality().hash(_selectedTrack));

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MeditationConfigImplCopyWith<_$MeditationConfigImpl> get copyWith =>
      __$$MeditationConfigImplCopyWithImpl<_$MeditationConfigImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> prompts, bool includeDate)
        journaling,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)
        meditation,
    required TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)
        breathwork,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)
        affirmations,
    required TResult Function(List<String> moods, bool includeNote)
        moodTracking,
    required TResult Function(List<String> prompts, int numberOfEntries)
        gratitude,
  }) {
    return meditation(audioTracks, defaultDuration, selectedTrack);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> prompts, bool includeDate)? journaling,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult? Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult? Function(List<String> moods, bool includeNote)? moodTracking,
    TResult? Function(List<String> prompts, int numberOfEntries)? gratitude,
  }) {
    return meditation?.call(audioTracks, defaultDuration, selectedTrack);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> prompts, bool includeDate)? journaling,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult Function(List<String> moods, bool includeNote)? moodTracking,
    TResult Function(List<String> prompts, int numberOfEntries)? gratitude,
    required TResult orElse(),
  }) {
    if (meditation != null) {
      return meditation(audioTracks, defaultDuration, selectedTrack);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JournalingConfig value) journaling,
    required TResult Function(MeditationConfig value) meditation,
    required TResult Function(BreathworkConfig value) breathwork,
    required TResult Function(AffirmationsConfig value) affirmations,
    required TResult Function(MoodTrackingConfig value) moodTracking,
    required TResult Function(GratitudeConfig value) gratitude,
  }) {
    return meditation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JournalingConfig value)? journaling,
    TResult? Function(MeditationConfig value)? meditation,
    TResult? Function(BreathworkConfig value)? breathwork,
    TResult? Function(AffirmationsConfig value)? affirmations,
    TResult? Function(MoodTrackingConfig value)? moodTracking,
    TResult? Function(GratitudeConfig value)? gratitude,
  }) {
    return meditation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JournalingConfig value)? journaling,
    TResult Function(MeditationConfig value)? meditation,
    TResult Function(BreathworkConfig value)? breathwork,
    TResult Function(AffirmationsConfig value)? affirmations,
    TResult Function(MoodTrackingConfig value)? moodTracking,
    TResult Function(GratitudeConfig value)? gratitude,
    required TResult orElse(),
  }) {
    if (meditation != null) {
      return meditation(this);
    }
    return orElse();
  }
}

abstract class MeditationConfig extends ActivityConfig {
  const factory MeditationConfig(
      {final List<Map<String, dynamic>> audioTracks,
      final int defaultDuration,
      final Map<String, dynamic>? selectedTrack}) = _$MeditationConfigImpl;
  const MeditationConfig._() : super._();

  List<Map<String, dynamic>> get audioTracks;
  int get defaultDuration;
  Map<String, dynamic>? get selectedTrack;

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MeditationConfigImplCopyWith<_$MeditationConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BreathworkConfigImplCopyWith<$Res> {
  factory _$$BreathworkConfigImplCopyWith(_$BreathworkConfigImpl value,
          $Res Function(_$BreathworkConfigImpl) then) =
      __$$BreathworkConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String selectedPatternId,
      int cycles,
      List<BreathePattern> availablePatterns,
      String? backgroundMusicId,
      bool cuesMuted});
}

/// @nodoc
class __$$BreathworkConfigImplCopyWithImpl<$Res>
    extends _$ActivityConfigCopyWithImpl<$Res, _$BreathworkConfigImpl>
    implements _$$BreathworkConfigImplCopyWith<$Res> {
  __$$BreathworkConfigImplCopyWithImpl(_$BreathworkConfigImpl _value,
      $Res Function(_$BreathworkConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedPatternId = null,
    Object? cycles = null,
    Object? availablePatterns = null,
    Object? backgroundMusicId = freezed,
    Object? cuesMuted = null,
  }) {
    return _then(_$BreathworkConfigImpl(
      selectedPatternId: null == selectedPatternId
          ? _value.selectedPatternId
          : selectedPatternId // ignore: cast_nullable_to_non_nullable
              as String,
      cycles: null == cycles
          ? _value.cycles
          : cycles // ignore: cast_nullable_to_non_nullable
              as int,
      availablePatterns: null == availablePatterns
          ? _value._availablePatterns
          : availablePatterns // ignore: cast_nullable_to_non_nullable
              as List<BreathePattern>,
      backgroundMusicId: freezed == backgroundMusicId
          ? _value.backgroundMusicId
          : backgroundMusicId // ignore: cast_nullable_to_non_nullable
              as String?,
      cuesMuted: null == cuesMuted
          ? _value.cuesMuted
          : cuesMuted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$BreathworkConfigImpl extends BreathworkConfig {
  const _$BreathworkConfigImpl(
      {required this.selectedPatternId,
      required this.cycles,
      required final List<BreathePattern> availablePatterns,
      this.backgroundMusicId,
      this.cuesMuted = false})
      : _availablePatterns = availablePatterns,
        super._();

  @override
  final String selectedPatternId;
  @override
  final int cycles;
  final List<BreathePattern> _availablePatterns;
  @override
  List<BreathePattern> get availablePatterns {
    if (_availablePatterns is EqualUnmodifiableListView)
      return _availablePatterns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availablePatterns);
  }

  @override
  final String? backgroundMusicId;
// New field
  @override
  @JsonKey()
  final bool cuesMuted;

  @override
  String toString() {
    return 'ActivityConfig.breathwork(selectedPatternId: $selectedPatternId, cycles: $cycles, availablePatterns: $availablePatterns, backgroundMusicId: $backgroundMusicId, cuesMuted: $cuesMuted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BreathworkConfigImpl &&
            (identical(other.selectedPatternId, selectedPatternId) ||
                other.selectedPatternId == selectedPatternId) &&
            (identical(other.cycles, cycles) || other.cycles == cycles) &&
            const DeepCollectionEquality()
                .equals(other._availablePatterns, _availablePatterns) &&
            (identical(other.backgroundMusicId, backgroundMusicId) ||
                other.backgroundMusicId == backgroundMusicId) &&
            (identical(other.cuesMuted, cuesMuted) ||
                other.cuesMuted == cuesMuted));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedPatternId,
      cycles,
      const DeepCollectionEquality().hash(_availablePatterns),
      backgroundMusicId,
      cuesMuted);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BreathworkConfigImplCopyWith<_$BreathworkConfigImpl> get copyWith =>
      __$$BreathworkConfigImplCopyWithImpl<_$BreathworkConfigImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> prompts, bool includeDate)
        journaling,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)
        meditation,
    required TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)
        breathwork,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)
        affirmations,
    required TResult Function(List<String> moods, bool includeNote)
        moodTracking,
    required TResult Function(List<String> prompts, int numberOfEntries)
        gratitude,
  }) {
    return breathwork(selectedPatternId, cycles, availablePatterns,
        backgroundMusicId, cuesMuted);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> prompts, bool includeDate)? journaling,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult? Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult? Function(List<String> moods, bool includeNote)? moodTracking,
    TResult? Function(List<String> prompts, int numberOfEntries)? gratitude,
  }) {
    return breathwork?.call(selectedPatternId, cycles, availablePatterns,
        backgroundMusicId, cuesMuted);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> prompts, bool includeDate)? journaling,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult Function(List<String> moods, bool includeNote)? moodTracking,
    TResult Function(List<String> prompts, int numberOfEntries)? gratitude,
    required TResult orElse(),
  }) {
    if (breathwork != null) {
      return breathwork(selectedPatternId, cycles, availablePatterns,
          backgroundMusicId, cuesMuted);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JournalingConfig value) journaling,
    required TResult Function(MeditationConfig value) meditation,
    required TResult Function(BreathworkConfig value) breathwork,
    required TResult Function(AffirmationsConfig value) affirmations,
    required TResult Function(MoodTrackingConfig value) moodTracking,
    required TResult Function(GratitudeConfig value) gratitude,
  }) {
    return breathwork(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JournalingConfig value)? journaling,
    TResult? Function(MeditationConfig value)? meditation,
    TResult? Function(BreathworkConfig value)? breathwork,
    TResult? Function(AffirmationsConfig value)? affirmations,
    TResult? Function(MoodTrackingConfig value)? moodTracking,
    TResult? Function(GratitudeConfig value)? gratitude,
  }) {
    return breathwork?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JournalingConfig value)? journaling,
    TResult Function(MeditationConfig value)? meditation,
    TResult Function(BreathworkConfig value)? breathwork,
    TResult Function(AffirmationsConfig value)? affirmations,
    TResult Function(MoodTrackingConfig value)? moodTracking,
    TResult Function(GratitudeConfig value)? gratitude,
    required TResult orElse(),
  }) {
    if (breathwork != null) {
      return breathwork(this);
    }
    return orElse();
  }
}

abstract class BreathworkConfig extends ActivityConfig {
  const factory BreathworkConfig(
      {required final String selectedPatternId,
      required final int cycles,
      required final List<BreathePattern> availablePatterns,
      final String? backgroundMusicId,
      final bool cuesMuted}) = _$BreathworkConfigImpl;
  const BreathworkConfig._() : super._();

  String get selectedPatternId;
  int get cycles;
  List<BreathePattern> get availablePatterns;
  String? get backgroundMusicId; // New field
  bool get cuesMuted;

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BreathworkConfigImplCopyWith<_$BreathworkConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AffirmationsConfigImplCopyWith<$Res> {
  factory _$$AffirmationsConfigImplCopyWith(_$AffirmationsConfigImpl value,
          $Res Function(_$AffirmationsConfigImpl) then) =
      __$$AffirmationsConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<Map<String, dynamic>> audioTracks,
      Map<String, dynamic>? selectedTrack,
      bool autoPlay});
}

/// @nodoc
class __$$AffirmationsConfigImplCopyWithImpl<$Res>
    extends _$ActivityConfigCopyWithImpl<$Res, _$AffirmationsConfigImpl>
    implements _$$AffirmationsConfigImplCopyWith<$Res> {
  __$$AffirmationsConfigImplCopyWithImpl(_$AffirmationsConfigImpl _value,
      $Res Function(_$AffirmationsConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioTracks = null,
    Object? selectedTrack = freezed,
    Object? autoPlay = null,
  }) {
    return _then(_$AffirmationsConfigImpl(
      audioTracks: null == audioTracks
          ? _value._audioTracks
          : audioTracks // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      selectedTrack: freezed == selectedTrack
          ? _value._selectedTrack
          : selectedTrack // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      autoPlay: null == autoPlay
          ? _value.autoPlay
          : autoPlay // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AffirmationsConfigImpl extends AffirmationsConfig {
  const _$AffirmationsConfigImpl(
      {final List<Map<String, dynamic>> audioTracks = const [],
      final Map<String, dynamic>? selectedTrack,
      this.autoPlay = true})
      : _audioTracks = audioTracks,
        _selectedTrack = selectedTrack,
        super._();

  final List<Map<String, dynamic>> _audioTracks;
  @override
  @JsonKey()
  List<Map<String, dynamic>> get audioTracks {
    if (_audioTracks is EqualUnmodifiableListView) return _audioTracks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_audioTracks);
  }

  final Map<String, dynamic>? _selectedTrack;
  @override
  Map<String, dynamic>? get selectedTrack {
    final value = _selectedTrack;
    if (value == null) return null;
    if (_selectedTrack is EqualUnmodifiableMapView) return _selectedTrack;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  @JsonKey()
  final bool autoPlay;

  @override
  String toString() {
    return 'ActivityConfig.affirmations(audioTracks: $audioTracks, selectedTrack: $selectedTrack, autoPlay: $autoPlay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AffirmationsConfigImpl &&
            const DeepCollectionEquality()
                .equals(other._audioTracks, _audioTracks) &&
            const DeepCollectionEquality()
                .equals(other._selectedTrack, _selectedTrack) &&
            (identical(other.autoPlay, autoPlay) ||
                other.autoPlay == autoPlay));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_audioTracks),
      const DeepCollectionEquality().hash(_selectedTrack),
      autoPlay);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AffirmationsConfigImplCopyWith<_$AffirmationsConfigImpl> get copyWith =>
      __$$AffirmationsConfigImplCopyWithImpl<_$AffirmationsConfigImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> prompts, bool includeDate)
        journaling,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)
        meditation,
    required TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)
        breathwork,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)
        affirmations,
    required TResult Function(List<String> moods, bool includeNote)
        moodTracking,
    required TResult Function(List<String> prompts, int numberOfEntries)
        gratitude,
  }) {
    return affirmations(audioTracks, selectedTrack, autoPlay);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> prompts, bool includeDate)? journaling,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult? Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult? Function(List<String> moods, bool includeNote)? moodTracking,
    TResult? Function(List<String> prompts, int numberOfEntries)? gratitude,
  }) {
    return affirmations?.call(audioTracks, selectedTrack, autoPlay);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> prompts, bool includeDate)? journaling,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult Function(List<String> moods, bool includeNote)? moodTracking,
    TResult Function(List<String> prompts, int numberOfEntries)? gratitude,
    required TResult orElse(),
  }) {
    if (affirmations != null) {
      return affirmations(audioTracks, selectedTrack, autoPlay);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JournalingConfig value) journaling,
    required TResult Function(MeditationConfig value) meditation,
    required TResult Function(BreathworkConfig value) breathwork,
    required TResult Function(AffirmationsConfig value) affirmations,
    required TResult Function(MoodTrackingConfig value) moodTracking,
    required TResult Function(GratitudeConfig value) gratitude,
  }) {
    return affirmations(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JournalingConfig value)? journaling,
    TResult? Function(MeditationConfig value)? meditation,
    TResult? Function(BreathworkConfig value)? breathwork,
    TResult? Function(AffirmationsConfig value)? affirmations,
    TResult? Function(MoodTrackingConfig value)? moodTracking,
    TResult? Function(GratitudeConfig value)? gratitude,
  }) {
    return affirmations?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JournalingConfig value)? journaling,
    TResult Function(MeditationConfig value)? meditation,
    TResult Function(BreathworkConfig value)? breathwork,
    TResult Function(AffirmationsConfig value)? affirmations,
    TResult Function(MoodTrackingConfig value)? moodTracking,
    TResult Function(GratitudeConfig value)? gratitude,
    required TResult orElse(),
  }) {
    if (affirmations != null) {
      return affirmations(this);
    }
    return orElse();
  }
}

abstract class AffirmationsConfig extends ActivityConfig {
  const factory AffirmationsConfig(
      {final List<Map<String, dynamic>> audioTracks,
      final Map<String, dynamic>? selectedTrack,
      final bool autoPlay}) = _$AffirmationsConfigImpl;
  const AffirmationsConfig._() : super._();

  List<Map<String, dynamic>> get audioTracks;
  Map<String, dynamic>? get selectedTrack;
  bool get autoPlay;

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AffirmationsConfigImplCopyWith<_$AffirmationsConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MoodTrackingConfigImplCopyWith<$Res> {
  factory _$$MoodTrackingConfigImplCopyWith(_$MoodTrackingConfigImpl value,
          $Res Function(_$MoodTrackingConfigImpl) then) =
      __$$MoodTrackingConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> moods, bool includeNote});
}

/// @nodoc
class __$$MoodTrackingConfigImplCopyWithImpl<$Res>
    extends _$ActivityConfigCopyWithImpl<$Res, _$MoodTrackingConfigImpl>
    implements _$$MoodTrackingConfigImplCopyWith<$Res> {
  __$$MoodTrackingConfigImplCopyWithImpl(_$MoodTrackingConfigImpl _value,
      $Res Function(_$MoodTrackingConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? moods = null,
    Object? includeNote = null,
  }) {
    return _then(_$MoodTrackingConfigImpl(
      moods: null == moods
          ? _value._moods
          : moods // ignore: cast_nullable_to_non_nullable
              as List<String>,
      includeNote: null == includeNote
          ? _value.includeNote
          : includeNote // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$MoodTrackingConfigImpl extends MoodTrackingConfig {
  const _$MoodTrackingConfigImpl(
      {final List<String> moods = const [
        '😊 Happy',
        '😐 Neutral',
        '😔 Sad',
        '😤 Angry',
        '😴 Tired'
      ],
      this.includeNote = true})
      : _moods = moods,
        super._();

  final List<String> _moods;
  @override
  @JsonKey()
  List<String> get moods {
    if (_moods is EqualUnmodifiableListView) return _moods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_moods);
  }

  @override
  @JsonKey()
  final bool includeNote;

  @override
  String toString() {
    return 'ActivityConfig.moodTracking(moods: $moods, includeNote: $includeNote)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MoodTrackingConfigImpl &&
            const DeepCollectionEquality().equals(other._moods, _moods) &&
            (identical(other.includeNote, includeNote) ||
                other.includeNote == includeNote));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_moods), includeNote);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MoodTrackingConfigImplCopyWith<_$MoodTrackingConfigImpl> get copyWith =>
      __$$MoodTrackingConfigImplCopyWithImpl<_$MoodTrackingConfigImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> prompts, bool includeDate)
        journaling,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)
        meditation,
    required TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)
        breathwork,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)
        affirmations,
    required TResult Function(List<String> moods, bool includeNote)
        moodTracking,
    required TResult Function(List<String> prompts, int numberOfEntries)
        gratitude,
  }) {
    return moodTracking(moods, includeNote);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> prompts, bool includeDate)? journaling,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult? Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult? Function(List<String> moods, bool includeNote)? moodTracking,
    TResult? Function(List<String> prompts, int numberOfEntries)? gratitude,
  }) {
    return moodTracking?.call(moods, includeNote);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> prompts, bool includeDate)? journaling,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult Function(List<String> moods, bool includeNote)? moodTracking,
    TResult Function(List<String> prompts, int numberOfEntries)? gratitude,
    required TResult orElse(),
  }) {
    if (moodTracking != null) {
      return moodTracking(moods, includeNote);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JournalingConfig value) journaling,
    required TResult Function(MeditationConfig value) meditation,
    required TResult Function(BreathworkConfig value) breathwork,
    required TResult Function(AffirmationsConfig value) affirmations,
    required TResult Function(MoodTrackingConfig value) moodTracking,
    required TResult Function(GratitudeConfig value) gratitude,
  }) {
    return moodTracking(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JournalingConfig value)? journaling,
    TResult? Function(MeditationConfig value)? meditation,
    TResult? Function(BreathworkConfig value)? breathwork,
    TResult? Function(AffirmationsConfig value)? affirmations,
    TResult? Function(MoodTrackingConfig value)? moodTracking,
    TResult? Function(GratitudeConfig value)? gratitude,
  }) {
    return moodTracking?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JournalingConfig value)? journaling,
    TResult Function(MeditationConfig value)? meditation,
    TResult Function(BreathworkConfig value)? breathwork,
    TResult Function(AffirmationsConfig value)? affirmations,
    TResult Function(MoodTrackingConfig value)? moodTracking,
    TResult Function(GratitudeConfig value)? gratitude,
    required TResult orElse(),
  }) {
    if (moodTracking != null) {
      return moodTracking(this);
    }
    return orElse();
  }
}

abstract class MoodTrackingConfig extends ActivityConfig {
  const factory MoodTrackingConfig(
      {final List<String> moods,
      final bool includeNote}) = _$MoodTrackingConfigImpl;
  const MoodTrackingConfig._() : super._();

  List<String> get moods;
  bool get includeNote;

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MoodTrackingConfigImplCopyWith<_$MoodTrackingConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GratitudeConfigImplCopyWith<$Res> {
  factory _$$GratitudeConfigImplCopyWith(_$GratitudeConfigImpl value,
          $Res Function(_$GratitudeConfigImpl) then) =
      __$$GratitudeConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> prompts, int numberOfEntries});
}

/// @nodoc
class __$$GratitudeConfigImplCopyWithImpl<$Res>
    extends _$ActivityConfigCopyWithImpl<$Res, _$GratitudeConfigImpl>
    implements _$$GratitudeConfigImplCopyWith<$Res> {
  __$$GratitudeConfigImplCopyWithImpl(
      _$GratitudeConfigImpl _value, $Res Function(_$GratitudeConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? prompts = null,
    Object? numberOfEntries = null,
  }) {
    return _then(_$GratitudeConfigImpl(
      prompts: null == prompts
          ? _value._prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<String>,
      numberOfEntries: null == numberOfEntries
          ? _value.numberOfEntries
          : numberOfEntries // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$GratitudeConfigImpl extends GratitudeConfig {
  const _$GratitudeConfigImpl(
      {final List<String> prompts = const ['What are you grateful for today?'],
      this.numberOfEntries = 3})
      : _prompts = prompts,
        super._();

  final List<String> _prompts;
  @override
  @JsonKey()
  List<String> get prompts {
    if (_prompts is EqualUnmodifiableListView) return _prompts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prompts);
  }

  @override
  @JsonKey()
  final int numberOfEntries;

  @override
  String toString() {
    return 'ActivityConfig.gratitude(prompts: $prompts, numberOfEntries: $numberOfEntries)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GratitudeConfigImpl &&
            const DeepCollectionEquality().equals(other._prompts, _prompts) &&
            (identical(other.numberOfEntries, numberOfEntries) ||
                other.numberOfEntries == numberOfEntries));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_prompts), numberOfEntries);

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GratitudeConfigImplCopyWith<_$GratitudeConfigImpl> get copyWith =>
      __$$GratitudeConfigImplCopyWithImpl<_$GratitudeConfigImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> prompts, bool includeDate)
        journaling,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)
        meditation,
    required TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)
        breathwork,
    required TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)
        affirmations,
    required TResult Function(List<String> moods, bool includeNote)
        moodTracking,
    required TResult Function(List<String> prompts, int numberOfEntries)
        gratitude,
  }) {
    return gratitude(prompts, numberOfEntries);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> prompts, bool includeDate)? journaling,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult? Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult? Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult? Function(List<String> moods, bool includeNote)? moodTracking,
    TResult? Function(List<String> prompts, int numberOfEntries)? gratitude,
  }) {
    return gratitude?.call(prompts, numberOfEntries);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> prompts, bool includeDate)? journaling,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            int defaultDuration, Map<String, dynamic>? selectedTrack)?
        meditation,
    TResult Function(
            String selectedPatternId,
            int cycles,
            List<BreathePattern> availablePatterns,
            String? backgroundMusicId,
            bool cuesMuted)?
        breathwork,
    TResult Function(List<Map<String, dynamic>> audioTracks,
            Map<String, dynamic>? selectedTrack, bool autoPlay)?
        affirmations,
    TResult Function(List<String> moods, bool includeNote)? moodTracking,
    TResult Function(List<String> prompts, int numberOfEntries)? gratitude,
    required TResult orElse(),
  }) {
    if (gratitude != null) {
      return gratitude(prompts, numberOfEntries);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JournalingConfig value) journaling,
    required TResult Function(MeditationConfig value) meditation,
    required TResult Function(BreathworkConfig value) breathwork,
    required TResult Function(AffirmationsConfig value) affirmations,
    required TResult Function(MoodTrackingConfig value) moodTracking,
    required TResult Function(GratitudeConfig value) gratitude,
  }) {
    return gratitude(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JournalingConfig value)? journaling,
    TResult? Function(MeditationConfig value)? meditation,
    TResult? Function(BreathworkConfig value)? breathwork,
    TResult? Function(AffirmationsConfig value)? affirmations,
    TResult? Function(MoodTrackingConfig value)? moodTracking,
    TResult? Function(GratitudeConfig value)? gratitude,
  }) {
    return gratitude?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JournalingConfig value)? journaling,
    TResult Function(MeditationConfig value)? meditation,
    TResult Function(BreathworkConfig value)? breathwork,
    TResult Function(AffirmationsConfig value)? affirmations,
    TResult Function(MoodTrackingConfig value)? moodTracking,
    TResult Function(GratitudeConfig value)? gratitude,
    required TResult orElse(),
  }) {
    if (gratitude != null) {
      return gratitude(this);
    }
    return orElse();
  }
}

abstract class GratitudeConfig extends ActivityConfig {
  const factory GratitudeConfig(
      {final List<String> prompts,
      final int numberOfEntries}) = _$GratitudeConfigImpl;
  const GratitudeConfig._() : super._();

  List<String> get prompts;
  int get numberOfEntries;

  /// Create a copy of ActivityConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GratitudeConfigImplCopyWith<_$GratitudeConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
