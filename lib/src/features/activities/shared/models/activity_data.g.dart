// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ActivityDataImpl _$$ActivityDataImplFromJson(Map<String, dynamic> json) =>
    _$ActivityDataImpl(
      id: (json['id'] as num).toInt(),
      activityId: (json['activityId'] as num).toInt(),
      date: DateTime.parse(json['date'] as String),
      data: json['data'] as Map<String, dynamic>,
      status: json['status'] as String,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$$ActivityDataImplToJson(_$ActivityDataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'activityId': instance.activityId,
      'date': instance.date.toIso8601String(),
      'data': instance.data,
      'status': instance.status,
      'completedAt': instance.completedAt?.toIso8601String(),
    };
