// // lib/src/features/journal/models/activity.dart

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';
import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart';

part 'activity_config.freezed.dart';

// part 'activity_config.g.dart';
@freezed
class ActivityConfig with _$ActivityConfig {
  const ActivityConfig._();

  // Journaling Config
  const factory ActivityConfig.journaling({
    @Default([]) List<String> prompts,
    @Default(true) bool includeDate,
  }) = JournalingConfig;

  // Updated Meditation Config
  const factory ActivityConfig.meditation({
    @Default([]) List<Map<String, dynamic>> audioTracks,
    @Default(5) int defaultDuration,
    Map<String, dynamic>? selectedTrack,
  }) = MeditationConfig;

  // Breathwork Config
  const factory ActivityConfig.breathwork({
    required String selectedPatternId,
    required int cycles,
    required List<BreathePattern> availablePatterns,
    String? backgroundMusicId, // New field
    @Default(false) bool cuesMuted, // New field
  }) = BreathworkConfig;

  // Updated Affirmations Config
  const factory ActivityConfig.affirmations({
    @Default([]) List<Map<String, dynamic>> audioTracks,
    Map<String, dynamic>? selectedTrack,
    @Default(true) bool autoPlay,
  }) = AffirmationsConfig;

  // MoodTracking Config
  const factory ActivityConfig.moodTracking({
    @Default(['😊 Happy', '😐 Neutral', '😔 Sad', '😤 Angry', '😴 Tired'])
    List<String> moods,
    @Default(true) bool includeNote,
  }) = MoodTrackingConfig;

  // Gratitude Config
  const factory ActivityConfig.gratitude({
    @Default(['What are you grateful for today?']) List<String> prompts,
    @Default(3) int numberOfEntries,
  }) = GratitudeConfig;

  factory ActivityConfig.fromJson(Map<String, dynamic> json) {
    final type = json['runtimeType'] as String?;
    return switch (type) {
      'journaling' => ActivityConfig.journaling(
          prompts: (json['prompts'] as List<dynamic>).cast<String>(),
          includeDate: json['includeDate'] as bool? ?? true,
        ),
      'meditation' => ActivityConfig.meditation(
          audioTracks: (json['audioTracks'] as List<dynamic>?)
                  ?.map((e) => e as Map<String, dynamic>)
                  .toList() ??
              [],
          defaultDuration: json['defaultDuration'] as int? ?? 5,
          selectedTrack: json['selectedTrack'] as Map<String, dynamic>?,
        ),
      'breathwork' => ActivityConfig.breathwork(
          selectedPatternId: json['selectedPatternId'] as String,
          cycles: json['cycles'] as int? ?? 5,
          availablePatterns: (json['availablePatterns'] as List<dynamic>?)
                  ?.map(
                      (e) => BreathePattern.fromJson(e as Map<String, dynamic>))
                  .toList() ??
              defaultBreathworkPatterns,
          backgroundMusicId: json['backgroundMusicId'] as String?, // Add this
          cuesMuted: json['cuesMuted'] as bool? ?? false, // Add this
        ),
      'affirmations' => ActivityConfig.affirmations(
          audioTracks: (json['audioTracks'] as List<dynamic>?)
                  ?.map((e) => e as Map<String, dynamic>)
                  .toList() ??
              [],
          selectedTrack: json['selectedTrack'] as Map<String, dynamic>?,
          autoPlay: json['autoPlay'] as bool? ?? true,
        ),
      'moodTracking' => ActivityConfig.moodTracking(
          moods: (json['moods'] as List<dynamic>).cast<String>(),
          includeNote: json['includeNote'] as bool? ?? true,
        ),
      'gratitude' => ActivityConfig.gratitude(
          prompts: (json['prompts'] as List<dynamic>?)?.cast<String>() ??
              ['What are you grateful for today?'],
          numberOfEntries: json['numberOfEntries'] as int? ?? 3,
        ),
      _ => throw ArgumentError('Unknown ActivityConfig type: $type'),
    };
  }

  Map<String, dynamic> toJson() {
    return when(
      journaling: (prompts, includeDate) => {
        'runtimeType': 'journaling',
        'prompts': prompts,
        'includeDate': includeDate,
      },
      meditation: (audioTracks, defaultDuration, selectedTrack) => {
        'runtimeType': 'meditation',
        'audioTracks': audioTracks,
        'defaultDuration': defaultDuration,
        'selectedTrack': selectedTrack,
      },
      breathwork: (selectedPatternId, cycles, availablePatterns,
              backgroundMusicId, cuesMuted) =>
          {
        'runtimeType': 'breathwork',
        'selectedPatternId': selectedPatternId,
        'cycles': cycles,
        'availablePatterns': availablePatterns.map((p) => p.toJson()).toList(),
        'backgroundMusicId': backgroundMusicId, // Add this
        'cuesMuted': cuesMuted, // Add this
      },
      affirmations: (audioTracks, selectedTrack, autoPlay) => {
        'runtimeType': 'affirmations',
        'audioTracks': audioTracks,
        'selectedTrack': selectedTrack,
        'autoPlay': autoPlay,
      },
      moodTracking: (moods, includeNote) => {
        'runtimeType': 'moodTracking',
        'moods': moods,
        'includeNote': includeNote,
      },
      gratitude: (prompts, numberOfEntries) => {
        'runtimeType': 'gratitude',
        'prompts': prompts,
        'numberOfEntries': numberOfEntries,
      },
    );
  }

  /// Validates the activity configuration and returns true if valid
  bool isValid() {
    return when(
      journaling: (prompts, includeDate) => prompts.isNotEmpty,
      meditation: (audioTracks, defaultDuration, selectedTrack) =>
          audioTracks.isNotEmpty,
      breathwork: (selectedPatternId, cycles, availablePatterns,
              backgroundMusicId, cuesMuted) =>
          selectedPatternId.isNotEmpty && availablePatterns.isNotEmpty,
      affirmations: (audioTracks, selectedTrack, autoPlay) =>
          audioTracks.isNotEmpty,
      moodTracking: (moods, includeNote) => true, // Always valid
      gratitude: (prompts, numberOfEntries) => true, // Always valid
    );
  }

  /// Returns a list of validation error messages
  List<String> getValidationErrors() {
    return when(
      journaling: (prompts, includeDate) {
        final errors = <String>[];
        if (prompts.isEmpty) {
          errors.add('Select journal prompts to continue');
        }
        return errors;
      },
      meditation: (audioTracks, defaultDuration, selectedTrack) {
        final errors = <String>[];
        if (audioTracks.isEmpty) {
          errors.add('Select meditation tracks to continue');
        }
        return errors;
      },
      breathwork: (selectedPatternId, cycles, availablePatterns,
          backgroundMusicId, cuesMuted) {
        final errors = <String>[];
        if (selectedPatternId.isEmpty) {
          errors.add('Select a breathing pattern to continue');
        }
        if (availablePatterns.isEmpty) {
          errors.add('No breathing patterns available');
        }
        return errors;
      },
      affirmations: (audioTracks, selectedTrack, autoPlay) {
        final errors = <String>[];
        if (audioTracks.isEmpty) {
          errors.add('Select affirmation tracks to continue');
        }
        return errors;
      },
      moodTracking: (moods, includeNote) => <String>[], // Always valid
      gratitude: (prompts, numberOfEntries) => <String>[], // Always valid
    );
  }
}

// @freezed
// class ActivityConfig with _$ActivityConfig {
//   const ActivityConfig._();

//   // Journaling Config remains the same
//   const factory ActivityConfig.journaling({
//     @Default([]) List<String> prompts,
//     @Default(true) bool includeDate,
//   }) = JournalingConfig;

//   // Updated Meditation Config
//   const factory ActivityConfig.meditation({
//     @Default([])
//     List<Map<String, dynamic>> audioTracks, // Changed to store full track info
//     @Default(5) int defaultDuration,
//     Map<String, dynamic>? selectedTrack, // Changed to store full track info
//   }) = MeditationConfig;

//   // Breathwork Config remains the same
//   const factory ActivityConfig.breathwork({
//     required String selectedPatternId,
//     @Default(5) int cycles,
//     @Default([]) List<BreathePattern> availablePatterns,
//   }) = BreathworkConfig;

//   // Updated Affirmations Config
//   const factory ActivityConfig.affirmations({
//     @Default([])
//     List<Map<String, dynamic>> audioTracks, // Changed to store full track info
//     Map<String, dynamic>? selectedTrack, // Changed to store full track info
//     @Default(true) bool autoPlay,
//   }) = AffirmationsConfig;

//   // MoodTracking Config remains the same
//   const factory ActivityConfig.moodTracking({
//     @Default(['😊 Happy', '😐 Neutral', '😔 Sad', '😤 Angry', '😴 Tired'])
//     List<String> moods,
//     @Default(true) bool includeNote,
//   }) = MoodTrackingConfig;

//   factory ActivityConfig.fromJson(Map<String, dynamic> json) {
//     final type = json['runtimeType'] as String?;
//     return switch (type) {
//       'journaling' => ActivityConfig.journaling(
//           prompts: (json['prompts'] as List<dynamic>).cast<String>(),
//           includeDate: json['includeDate'] as bool? ?? true,
//         ),
//       'meditation' => ActivityConfig.meditation(
//           audioTracks: (json['audioTracks'] as List<dynamic>?)
//                   ?.map((e) => e as Map<String, dynamic>)
//                   .toList() ??
//               [],
//           defaultDuration: json['defaultDuration'] as int? ?? 5,
//           selectedTrack: json['selectedTrack'] as Map<String, dynamic>?,
//         ),
//       'breathwork' => ActivityConfig.breathwork(
//           selectedPatternId: json['selectedPatternId'] as String,
//           cycles: json['cycles'] as int? ?? 5,
//           availablePatterns: (json['availablePatterns'] as List<dynamic>?)
//                   ?.map(
//                       (e) => BreathePattern.fromJson(e as Map<String, dynamic>))
//                   .toList() ??
//               defaultBreathworkPatterns,
//         ),
//       'affirmations' => ActivityConfig.affirmations(
//           audioTracks: (json['audioTracks'] as List<dynamic>?)
//                   ?.map((e) => e as Map<String, dynamic>)
//                   .toList() ??
//               [],
//           selectedTrack: json['selectedTrack'] as Map<String, dynamic>?,
//           autoPlay: json['autoPlay'] as bool? ?? true,
//         ),
//       'moodTracking' => ActivityConfig.moodTracking(
//           moods: (json['moods'] as List<dynamic>).cast<String>(),
//           includeNote: json['includeNote'] as bool? ?? true,
//         ),
//       _ => throw ArgumentError('Unknown ActivityConfig type: $type'),
//     };
//   }

//   Map<String, dynamic> toJson() {
//     return when(
//       journaling: (prompts, includeDate) => {
//         'runtimeType': 'journaling',
//         'prompts': prompts,
//         'includeDate': includeDate,
//       },
//       meditation: (audioTracks, defaultDuration, selectedTrack) => {
//         'runtimeType': 'meditation',
//         'audioTracks': audioTracks,
//         'defaultDuration': defaultDuration,
//         'selectedTrack': selectedTrack,
//       },
//       breathwork: (selectedPatternId, cycles, availablePatterns) => {
//         'runtimeType': 'breathwork',
//         'selectedPatternId': selectedPatternId,
//         'cycles': cycles,
//         'availablePatterns': availablePatterns.map((p) => p.toJson()).toList(),
//       },
//       affirmations: (audioTracks, selectedTrack, autoPlay) => {
//         'runtimeType': 'affirmations',
//         'audioTracks': audioTracks,
//         'selectedTrack': selectedTrack,
//         'autoPlay': autoPlay,
//       },
//       moodTracking: (moods, includeNote) => {
//         'runtimeType': 'moodTracking',
//         'moods': moods,
//         'includeNote': includeNote,
//       },
//     );
//   }
// }
// @freezed
// class ActivityConfig with _$ActivityConfig {
//   const ActivityConfig._(); // Add this for custom methods

//   // Journaling Config
//   const factory ActivityConfig.journaling({
//     @Default([]) List<String> prompts,
//     @Default(true) bool includeDate,
//     // @Default(true) bool includeMood,
//   }) = JournalingConfig;

//   // Meditation Config
//   const factory ActivityConfig.meditation({
//     @Default([]) List<String> audioAssets,
//     @Default(5) int defaultDuration,
//     String? selectedAudioAsset,
//   }) = MeditationConfig;

//   const factory ActivityConfig.breathwork({
//     required String selectedPatternId,
//     @Default(5) int cycles,
//     @Default([]) List<BreathePattern> availablePatterns,
//   }) = BreathworkConfig;

//   // Affirmations Config
//   const factory ActivityConfig.affirmations({
//     @Default([]) List<String> audioAssets,
//     String? selectedAudioAsset,
//     @Default(true) bool autoPlay,
//   }) = AffirmationsConfig;

//   // Mood Tracking Config
//   const factory ActivityConfig.moodTracking({
//     @Default(['😊 Happy', '😐 Neutral', '😔 Sad', '😤 Angry', '😴 Tired'])
//     List<String> moods,
//     @Default(true) bool includeNote,
//   }) = MoodTrackingConfig;

//   factory ActivityConfig.fromJson(Map<String, dynamic> json) {
//     final type = json['runtimeType'] as String?;
//     return switch (type) {
//       'journaling' => ActivityConfig.journaling(
//           prompts: (json['prompts'] as List<dynamic>).cast<String>(),
//           includeDate: json['includeDate'] as bool? ?? true,
//         ),
//       'meditation' => ActivityConfig.meditation(
//           audioAssets: (json['audioAssets'] as List<dynamic>).cast<String>(),
//           defaultDuration: json['defaultDuration'] as int? ?? 5,
//           selectedAudioAsset: json['selectedAudioAsset'] as String?,
//         ),
//       'breathwork' => ActivityConfig.breathwork(
//           selectedPatternId: json['selectedPatternId'] as String,
//           cycles: json['cycles'] as int? ?? 5,
//           availablePatterns: (json['availablePatterns'] as List<dynamic>?)
//                   ?.map(
//                       (e) => BreathePattern.fromJson(e as Map<String, dynamic>))
//                   .toList() ??
//               defaultBreathworkPatterns,
//         ),
//       'affirmations' => ActivityConfig.affirmations(
//           audioAssets: (json['audioAssets'] as List<dynamic>).cast<String>(),
//           selectedAudioAsset: json['selectedAudioAsset'] as String?,
//           autoPlay: json['autoPlay'] as bool? ?? true,
//         ),
//       'moodTracking' => ActivityConfig.moodTracking(
//           moods: (json['moods'] as List<dynamic>).cast<String>(),
//           includeNote: json['includeNote'] as bool? ?? true,
//         ),
//       _ => throw ArgumentError('Unknown ActivityConfig type: $type'),
//     };
//   }

//   Map<String, dynamic> toJson() {
//     return when(
//       journaling: (prompts, includeDate) => {
//         'runtimeType': 'journaling',
//         'prompts': prompts,
//         'includeDate': includeDate,
//       },
//       meditation: (audioAssets, defaultDuration, selectedAudioAsset) => {
//         'runtimeType': 'meditation',
//         'audioAssets': audioAssets,
//         'defaultDuration': defaultDuration,
//         'selectedAudioAsset': selectedAudioAsset,
//       },
//       breathwork: (selectedPatternId, cycles, availablePatterns) => {
//         'runtimeType': 'breathwork',
//         'selectedPatternId': selectedPatternId,
//         'cycles': cycles,
//         'availablePatterns': availablePatterns.map((p) => p.toJson()).toList(),
//       },
//       affirmations: (audioAssets, selectedAudioAsset, autoPlay) => {
//         'runtimeType': 'affirmations',
//         'audioAssets': audioAssets,
//         'selectedAudioAsset': selectedAudioAsset,
//         'autoPlay': autoPlay,
//       },
//       moodTracking: (moods, includeNote) => {
//         'runtimeType': 'moodTracking',
//         'moods': moods,
//         'includeNote': includeNote,
//       },
//     );
//   }
// }
