// // lib/src/features/onboarding/widgets/onboarding_progress_indicator.dart

// import 'package:flutter/material.dart';
// import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
// import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';

// class OnboardingProgressIndicator extends StatelessWidget {
//   final int currentStep;
//   final int totalSteps;

//   const OnboardingProgressIndicator({
//     super.key,
//     required this.currentStep,
//     required this.totalSteps,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         // Step indicator
//         Row(
//           children: [
//             for (int i = 1; i <= totalSteps; i++) ...[
//               _buildStepCircle(i, currentStep >= i),
//               if (i < totalSteps) _buildConnector(currentStep > i),
//             ],
//           ],
//         ),
//         const SizedBox(height: AppSizing.spaceS),

//         // Progress text
//         Text(
//           'Step $currentStep of $totalSteps',
//           style: Theme.of(context).textTheme.bodySmall?.copyWith(
//                 color: AppColors.textSecondary,
//               ),
//         ),
//       ],
//     );
//   }

//   Widget _buildStepCircle(int step, bool isCompleted) {
//     return Container(
//       width: 32,
//       height: 32,
//       decoration: BoxDecoration(
//         shape: BoxShape.circle,
//         color: isCompleted ? AppColors.primary : AppColors.surface,
//         border: Border.all(
//           color: isCompleted ? AppColors.primary : AppColors.border,
//           width: 2,
//         ),
//       ),
//       child: Center(
//         child: isCompleted
//             ? Icon(
//                 step < currentStep ? Icons.check : Icons.circle,
//                 color: Colors.white,
//                 size: 16,
//               )
//             : Text(
//                 step.toString(),
//                 style: TextStyle(
//                   color: AppColors.textSecondary,
//                   fontWeight: FontWeight.bold,
//                   fontSize: 14,
//                 ),
//               ),
//       ),
//     );
//   }

//   Widget _buildConnector(bool isCompleted) {
//     return Expanded(
//       child: Container(
//         height: 2,
//         color: isCompleted ? AppColors.primary : AppColors.border,
//       ),
//     );
//   }
// }
