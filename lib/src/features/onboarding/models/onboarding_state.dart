// lib/src/features/onboarding/models/onboarding_state.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'onboarding_state.freezed.dart';

@freezed
class OnboardingState with _$OnboardingState {
  const factory OnboardingState({
    @Default(OnboardingStep.welcome) OnboardingStep currentStep,
    @Default(0) int currentPage,
    @Default(false) bool isAuthenticated,
    @Default(false) bool hasCreatedIntention,
    @Default(false) bool hasCreatedRoutine,
    @Default(false) bool hasSubscribed,
    @Default(false) bool isLoading,
    String? error,
    int? createdIntentionId,
    int? createdRoutineId,
  }) = _OnboardingState;

  const OnboardingState._();

  bool get canProceedToNextStep {
    switch (currentStep) {
      case OnboardingStep.welcome:
        return true;
      case OnboardingStep.intentionHeader:
        return true;
      case OnboardingStep.intention:
        return hasCreatedIntention && createdIntentionId != null;
      case OnboardingStep.routineHeader:
        return true;
      case OnboardingStep.routineName:
        return hasCreatedRoutine && createdRoutineId != null;
      case OnboardingStep.routineTime:
        return true; // Time is set in the routine creation
      case OnboardingStep.activitiesHeader:
        return true;
      case OnboardingStep.activitiesSelection:
        return true; // Activities can be empty initially
      case OnboardingStep.activitiesConfig:
        return true; // Configuration is optional for some activities
      case OnboardingStep.trialHeader:
        return true; // Always allow proceeding from trial header
      case OnboardingStep.paywall:
        return hasSubscribed;
      case OnboardingStep.auth:
        return isAuthenticated;
      case OnboardingStep.complete:
        return true;
    }
  }

  bool get isOnboardingComplete {
    return currentStep == OnboardingStep.complete &&
        isAuthenticated &&
        hasCreatedIntention &&
        hasCreatedRoutine &&
        hasSubscribed;
  }
}

enum OnboardingStep {
  welcome,
  intentionHeader,
  intention,
  routineHeader,
  routineName,
  routineTime,
  activitiesHeader,
  activitiesSelection,
  activitiesConfig,
  trialHeader, // New step before paywall
  paywall,
  auth,
  complete,
}

extension OnboardingStepExtension on OnboardingStep {
  String get title {
    switch (this) {
      case OnboardingStep.welcome:
        return 'Welcome to Mimi';
      case OnboardingStep.intentionHeader:
        return 'Step 1 of 3';
      case OnboardingStep.intention:
        return 'Set Your Intention';
      case OnboardingStep.routineHeader:
        return 'Step 2 of 3';
      case OnboardingStep.routineName:
        return 'Create Your Check-in Routine';
      case OnboardingStep.routineTime:
        return 'Set Your Routine Time';
      case OnboardingStep.activitiesHeader:
        return 'Step 3 of 3';
      case OnboardingStep.activitiesSelection:
        return 'Add Activities to Your Routine';
      case OnboardingStep.activitiesConfig:
        return 'Configure Your Activities';
      case OnboardingStep.trialHeader:
        return 'Step 4';
      case OnboardingStep.paywall:
        return 'Start Your Journey';
      case OnboardingStep.auth:
        return 'Create Your Account';
      case OnboardingStep.complete:
        return 'Welcome Home';
    }
  }

  String get description {
    switch (this) {
      case OnboardingStep.welcome:
        return 'Your mindfulness journey starts here';
      case OnboardingStep.intentionHeader:
        return 'Set Your Intention - Brief explanation of why setting intentions matters';
      case OnboardingStep.intention:
        return 'What would you like to focus on?';
      case OnboardingStep.routineHeader:
        return 'Create Your Check-in Routine - Explain importance of consistent routines';
      case OnboardingStep.routineName:
        return 'Give your routine a meaningful name';
      case OnboardingStep.routineTime:
        return 'Choose the best time for your practice';
      case OnboardingStep.activitiesHeader:
        return 'Add Activities to Your Routine - Explain customization with meaningful activities';
      case OnboardingStep.activitiesSelection:
        return 'Choose activities that resonate with you';
      case OnboardingStep.activitiesConfig:
        return 'Customize your activity settings';
      case OnboardingStep.trialHeader:
        return 'Try Mimi Premium - Experience the full benefits with our free trial';
      case OnboardingStep.paywall:
        return 'Unlock your full potential';
      case OnboardingStep.auth:
        return 'Join the Mimi community';
      case OnboardingStep.complete:
        return 'Your journey begins now';
    }
  }

  int get stepNumber {
    switch (this) {
      case OnboardingStep.welcome:
        return 1;
      case OnboardingStep.intentionHeader:
        return 2;
      case OnboardingStep.intention:
        return 3;
      case OnboardingStep.routineHeader:
        return 4;
      case OnboardingStep.routineName:
        return 5;
      case OnboardingStep.routineTime:
        return 6;
      case OnboardingStep.activitiesHeader:
        return 7;
      case OnboardingStep.activitiesSelection:
        return 8;
      case OnboardingStep.activitiesConfig:
        return 9;
      case OnboardingStep.trialHeader:
        return 10;
      case OnboardingStep.paywall:
        return 11;
      case OnboardingStep.auth:
        return 12;
      case OnboardingStep.complete:
        return 13;
    }
  }

  static const int totalSteps = 13; // Update total steps
}
