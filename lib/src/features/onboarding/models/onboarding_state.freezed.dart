// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OnboardingState {
  OnboardingStep get currentStep => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  bool get isAuthenticated => throw _privateConstructorUsedError;
  bool get hasCreatedIntention => throw _privateConstructorUsedError;
  bool get hasCreatedRoutine => throw _privateConstructorUsedError;
  bool get hasSubscribed => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  int? get createdIntentionId => throw _privateConstructorUsedError;
  int? get createdRoutineId => throw _privateConstructorUsedError;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OnboardingStateCopyWith<OnboardingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingStateCopyWith<$Res> {
  factory $OnboardingStateCopyWith(
          OnboardingState value, $Res Function(OnboardingState) then) =
      _$OnboardingStateCopyWithImpl<$Res, OnboardingState>;
  @useResult
  $Res call(
      {OnboardingStep currentStep,
      int currentPage,
      bool isAuthenticated,
      bool hasCreatedIntention,
      bool hasCreatedRoutine,
      bool hasSubscribed,
      bool isLoading,
      String? error,
      int? createdIntentionId,
      int? createdRoutineId});
}

/// @nodoc
class _$OnboardingStateCopyWithImpl<$Res, $Val extends OnboardingState>
    implements $OnboardingStateCopyWith<$Res> {
  _$OnboardingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? currentPage = null,
    Object? isAuthenticated = null,
    Object? hasCreatedIntention = null,
    Object? hasCreatedRoutine = null,
    Object? hasSubscribed = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? createdIntentionId = freezed,
    Object? createdRoutineId = freezed,
  }) {
    return _then(_value.copyWith(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as OnboardingStep,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      isAuthenticated: null == isAuthenticated
          ? _value.isAuthenticated
          : isAuthenticated // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCreatedIntention: null == hasCreatedIntention
          ? _value.hasCreatedIntention
          : hasCreatedIntention // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCreatedRoutine: null == hasCreatedRoutine
          ? _value.hasCreatedRoutine
          : hasCreatedRoutine // ignore: cast_nullable_to_non_nullable
              as bool,
      hasSubscribed: null == hasSubscribed
          ? _value.hasSubscribed
          : hasSubscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      createdIntentionId: freezed == createdIntentionId
          ? _value.createdIntentionId
          : createdIntentionId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdRoutineId: freezed == createdRoutineId
          ? _value.createdRoutineId
          : createdRoutineId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OnboardingStateImplCopyWith<$Res>
    implements $OnboardingStateCopyWith<$Res> {
  factory _$$OnboardingStateImplCopyWith(_$OnboardingStateImpl value,
          $Res Function(_$OnboardingStateImpl) then) =
      __$$OnboardingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OnboardingStep currentStep,
      int currentPage,
      bool isAuthenticated,
      bool hasCreatedIntention,
      bool hasCreatedRoutine,
      bool hasSubscribed,
      bool isLoading,
      String? error,
      int? createdIntentionId,
      int? createdRoutineId});
}

/// @nodoc
class __$$OnboardingStateImplCopyWithImpl<$Res>
    extends _$OnboardingStateCopyWithImpl<$Res, _$OnboardingStateImpl>
    implements _$$OnboardingStateImplCopyWith<$Res> {
  __$$OnboardingStateImplCopyWithImpl(
      _$OnboardingStateImpl _value, $Res Function(_$OnboardingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? currentPage = null,
    Object? isAuthenticated = null,
    Object? hasCreatedIntention = null,
    Object? hasCreatedRoutine = null,
    Object? hasSubscribed = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? createdIntentionId = freezed,
    Object? createdRoutineId = freezed,
  }) {
    return _then(_$OnboardingStateImpl(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as OnboardingStep,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      isAuthenticated: null == isAuthenticated
          ? _value.isAuthenticated
          : isAuthenticated // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCreatedIntention: null == hasCreatedIntention
          ? _value.hasCreatedIntention
          : hasCreatedIntention // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCreatedRoutine: null == hasCreatedRoutine
          ? _value.hasCreatedRoutine
          : hasCreatedRoutine // ignore: cast_nullable_to_non_nullable
              as bool,
      hasSubscribed: null == hasSubscribed
          ? _value.hasSubscribed
          : hasSubscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      createdIntentionId: freezed == createdIntentionId
          ? _value.createdIntentionId
          : createdIntentionId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdRoutineId: freezed == createdRoutineId
          ? _value.createdRoutineId
          : createdRoutineId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$OnboardingStateImpl extends _OnboardingState {
  const _$OnboardingStateImpl(
      {this.currentStep = OnboardingStep.welcome,
      this.currentPage = 0,
      this.isAuthenticated = false,
      this.hasCreatedIntention = false,
      this.hasCreatedRoutine = false,
      this.hasSubscribed = false,
      this.isLoading = false,
      this.error,
      this.createdIntentionId,
      this.createdRoutineId})
      : super._();

  @override
  @JsonKey()
  final OnboardingStep currentStep;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final bool isAuthenticated;
  @override
  @JsonKey()
  final bool hasCreatedIntention;
  @override
  @JsonKey()
  final bool hasCreatedRoutine;
  @override
  @JsonKey()
  final bool hasSubscribed;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;
  @override
  final int? createdIntentionId;
  @override
  final int? createdRoutineId;

  @override
  String toString() {
    return 'OnboardingState(currentStep: $currentStep, currentPage: $currentPage, isAuthenticated: $isAuthenticated, hasCreatedIntention: $hasCreatedIntention, hasCreatedRoutine: $hasCreatedRoutine, hasSubscribed: $hasSubscribed, isLoading: $isLoading, error: $error, createdIntentionId: $createdIntentionId, createdRoutineId: $createdRoutineId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingStateImpl &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.isAuthenticated, isAuthenticated) ||
                other.isAuthenticated == isAuthenticated) &&
            (identical(other.hasCreatedIntention, hasCreatedIntention) ||
                other.hasCreatedIntention == hasCreatedIntention) &&
            (identical(other.hasCreatedRoutine, hasCreatedRoutine) ||
                other.hasCreatedRoutine == hasCreatedRoutine) &&
            (identical(other.hasSubscribed, hasSubscribed) ||
                other.hasSubscribed == hasSubscribed) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.createdIntentionId, createdIntentionId) ||
                other.createdIntentionId == createdIntentionId) &&
            (identical(other.createdRoutineId, createdRoutineId) ||
                other.createdRoutineId == createdRoutineId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentStep,
      currentPage,
      isAuthenticated,
      hasCreatedIntention,
      hasCreatedRoutine,
      hasSubscribed,
      isLoading,
      error,
      createdIntentionId,
      createdRoutineId);

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      __$$OnboardingStateImplCopyWithImpl<_$OnboardingStateImpl>(
          this, _$identity);
}

abstract class _OnboardingState extends OnboardingState {
  const factory _OnboardingState(
      {final OnboardingStep currentStep,
      final int currentPage,
      final bool isAuthenticated,
      final bool hasCreatedIntention,
      final bool hasCreatedRoutine,
      final bool hasSubscribed,
      final bool isLoading,
      final String? error,
      final int? createdIntentionId,
      final int? createdRoutineId}) = _$OnboardingStateImpl;
  const _OnboardingState._() : super._();

  @override
  OnboardingStep get currentStep;
  @override
  int get currentPage;
  @override
  bool get isAuthenticated;
  @override
  bool get hasCreatedIntention;
  @override
  bool get hasCreatedRoutine;
  @override
  bool get hasSubscribed;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  int? get createdIntentionId;
  @override
  int? get createdRoutineId;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
