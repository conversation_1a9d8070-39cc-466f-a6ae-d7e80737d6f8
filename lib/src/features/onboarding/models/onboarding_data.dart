// lib/features/onboarding/data/models/onboarding_data.dart

import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/theme/constants/image_strings.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_model.dart';

final List<OnboardingModel> onboardingData = [
  const OnboardingModel(
    title: AppStrings.onboardingTitle1,
    description: AppStrings.onboardingDescription1,
    imagePath: ImageStrings.onboardingImage1,
  ),
  const OnboardingModel(
    title: AppStrings.onboardingTitle2,
    description: AppStrings.onboardingDescription2,
    imagePath: ImageStrings.onboardingImage2,
  ),
  const OnboardingModel(
    title: AppStrings.onboardingTitle3,
    description: AppStrings.onboardingDescription3,
    imagePath: ImageStrings.onboardingImage3,
  ),
  const OnboardingModel(
    title: AppStrings.onboardingTitle4,
    description: AppStrings.onboardingDescription4,
    imagePath: ImageStrings.onboardingImage4,
  ),
  const OnboardingModel(
    title: AppStrings.onboardingTitle5,
    description: AppStrings.onboardingDescription5,
    imagePath: ImageStrings.onboardingImage5,
  ),
];
