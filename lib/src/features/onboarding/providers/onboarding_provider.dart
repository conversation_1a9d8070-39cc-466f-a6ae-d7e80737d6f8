// lib/features/onboarding/providers/onboarding_provider.dart

import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/router/router_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'onboarding_provider.g.dart';

// lib/features/onboarding/providers/onboarding_provider.dart

@riverpod
class OnboardingController extends _$OnboardingController {
  @override
  int build() => 0; // Current page index

  void setPage(int page) => state = page;

  void completeOnboarding() {
    // Navigate to auth screen instead of setting completion status
    ref.read(routerProvider).goNamed(RouteNames.login);
  }
}
