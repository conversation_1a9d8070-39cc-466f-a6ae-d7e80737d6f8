// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_onboarding_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isOnboardingCompleteHash() =>
    r'6c6679c952d9ffd0d1c460fbc001e6de3679f512';

/// See also [isOnboardingComplete].
@ProviderFor(isOnboardingComplete)
final isOnboardingCompleteProvider = AutoDisposeFutureProvider<bool>.internal(
  isOnboardingComplete,
  name: r'isOnboardingCompleteProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isOnboardingCompleteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsOnboardingCompleteRef = AutoDisposeFutureProviderRef<bool>;
String _$enhancedOnboardingControllerHash() =>
    r'77518d61c2cb3683ddb8ecc308d77d0008efd26d';

/// See also [EnhancedOnboardingController].
@ProviderFor(EnhancedOnboardingController)
final enhancedOnboardingControllerProvider = AutoDisposeNotifierProvider<
    EnhancedOnboardingController, OnboardingState>.internal(
  EnhancedOnboardingController.new,
  name: r'enhancedOnboardingControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$enhancedOnboardingControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EnhancedOnboardingController = AutoDisposeNotifier<OnboardingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
