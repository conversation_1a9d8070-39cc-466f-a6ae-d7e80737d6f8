// lib/src/features/onboarding/providers/enhanced_onboarding_provider.dart

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/auth/providers/auth_state_provider.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';
import 'package:mimi_app/src/features/purchases/providers/subscription_status_provider.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';

part 'enhanced_onboarding_provider.g.dart';

@riverpod
class EnhancedOnboardingController extends _$EnhancedOnboardingController {
  static const String _onboardingCompleteKey = 'onboarding_complete';
  static const String _onboardingDataKey = 'onboarding_data';

  @override
  OnboardingState build() {
    _initializeFromStorage();
    _listenToAuthChanges();
    return const OnboardingState();
  }

  void _initializeFromStorage() async {
    final prefs = await SharedPreferences.getInstance();
    final isComplete = prefs.getBool(_onboardingCompleteKey) ?? false;

    if (isComplete) {
      state = state.copyWith(
        currentStep: OnboardingStep.complete,
        isAuthenticated: true,
        hasCreatedIntention: true,
        hasCreatedRoutine: true,
        hasSubscribed: true,
      );
    } else {
      // Clear any incomplete onboarding data on app restart
      await _clearIncompleteOnboardingData();

      // Check if user is already authenticated and should skip to paywall
      await _checkAuthenticatedUserFlow();
    }
  }

  // Clear any incomplete onboarding data when app restarts
  Future<void> _clearIncompleteOnboardingData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stateJson = prefs.getString(_onboardingDataKey);

      if (stateJson != null) {
        // Parse the stored state to get any created IDs
        final stateData =
            Map<String, dynamic>.from(jsonDecode(stateJson) as Map);
        final createdIntentionId = stateData['createdIntentionId'] as int?;
        final createdRoutineId = stateData['createdRoutineId'] as int?;

        // Delete any partially created data
        if (createdRoutineId != null) {
          try {
            final routineNotifier =
                ref.read(routineCrudNotifierProvider.notifier);
            await routineNotifier.deleteRoutine(createdRoutineId);
            if (kDebugMode) {
              print('Cleaned up incomplete routine: $createdRoutineId');
            }
          } catch (e) {
            if (kDebugMode) {
              print('Error cleaning up routine $createdRoutineId: $e');
            }
          }
        }

        if (createdIntentionId != null) {
          try {
            final intentionNotifier =
                ref.read(intentionCrudNotifierProvider.notifier);
            await intentionNotifier.deleteIntention(createdIntentionId);
            if (kDebugMode) {
              print('Cleaned up incomplete intention: $createdIntentionId');
            }
          } catch (e) {
            if (kDebugMode) {
              print('Error cleaning up intention $createdIntentionId: $e');
            }
          }
        }

        // Clear the stored onboarding state
        await prefs.remove(_onboardingDataKey);
        if (kDebugMode) {
          print('Cleared incomplete onboarding state on app restart');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing incomplete onboarding data: $e');
      }
    }
  }

  Future<void> _saveOnboardingState() async {
    try {
      // Only save state if onboarding is actually in progress
      // Don't save state if we're at welcome screen or complete screen
      if (state.currentStep == OnboardingStep.welcome ||
          state.currentStep == OnboardingStep.complete) {
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final stateData = {
        'currentStep': state.currentStep.index,
        'hasCreatedIntention': state.hasCreatedIntention,
        'hasCreatedRoutine': state.hasCreatedRoutine,
        'hasSubscribed': state.hasSubscribed,
        'createdIntentionId': state.createdIntentionId,
        'createdRoutineId': state.createdRoutineId,
      };

      await prefs.setString(_onboardingDataKey, jsonEncode(stateData));
    } catch (e) {
      if (kDebugMode) {
        print('Error saving onboarding state: $e');
      }
    }
  }

  // Check if authenticated user should skip certain onboarding steps
  Future<void> _checkAuthenticatedUserFlow() async {
    final authState = ref.read(authStateNotifierProvider);
    final isAuthenticated = authState.maybeWhen(
      authenticated: (_) => true,
      orElse: () => false,
    );

    if (isAuthenticated) {
      // Check if user has premium access with fresh data
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final isPremium = await revenueCatService.refreshPremiumStatus();

      if (isPremium) {
        // User is authenticated and has premium, mark as complete
        await completeOnboarding();
      } else {
        // For authenticated users without premium, always start fresh onboarding
        // This ensures consistent behavior and prevents automatic paywall navigation
        state = state.copyWith(
          currentStep: OnboardingStep.welcome,
          isAuthenticated: true,
          hasCreatedIntention: false,
          hasCreatedRoutine: false,
          hasSubscribed: false,
          createdIntentionId: null,
          createdRoutineId: null,
        );
      }
    } else {
      // Not authenticated - start from welcome screen
      state = state.copyWith(
        currentStep: OnboardingStep.welcome,
        isAuthenticated: false,
        hasCreatedIntention: false,
        hasCreatedRoutine: false,
        hasSubscribed: false,
        createdIntentionId: null,
        createdRoutineId: null,
      );
    }
  }

  void _listenToAuthChanges() {
    ref.listen(authStateNotifierProvider, (previous, next) {
      next.when(
        initial: () {},
        loading: () {},
        authenticated: (user) async {
          state = state.copyWith(isAuthenticated: true);

          // If user just became authenticated during onboarding, check if they should skip steps
          if (state.currentStep == OnboardingStep.auth) {
            await _checkAuthenticatedUserFlow();
          }
        },
        unauthenticated: () {
          state = state.copyWith(isAuthenticated: false);
        },
        error: (error) {
          state = state.copyWith(
            error: error,
            isLoading: false,
          );
        },
      );
    });
  }

  // Navigation methods
  void nextStep() {
    if (!state.canProceedToNextStep) return;

    final nextStep = OnboardingStep.values[(state.currentStep.index + 1)
        .clamp(0, OnboardingStep.values.length - 1)];

    state = state.copyWith(currentStep: nextStep);
    _saveOnboardingState();
  }

  void previousStep() {
    final prevStep = OnboardingStep.values[(state.currentStep.index - 1)
        .clamp(0, OnboardingStep.values.length - 1)];

    state = state.copyWith(currentStep: prevStep);
    _saveOnboardingState();
  }

  void goToStep(OnboardingStep step) {
    state = state.copyWith(currentStep: step);
    _saveOnboardingState();
  }

  void setPage(int page) {
    state = state.copyWith(currentPage: page);
  }

  // Progress tracking methods
  void markIntentionCreated(int intentionId) {
    state = state.copyWith(
      hasCreatedIntention: true,
      createdIntentionId: intentionId,
    );
    _saveOnboardingState();
  }

  void markRoutineCreated(int routineId) {
    state = state.copyWith(
      hasCreatedRoutine: true,
      createdRoutineId: routineId,
    );
    _saveOnboardingState();
  }

  void markSubscribed() {
    state = state.copyWith(hasSubscribed: true);
    _saveOnboardingState();

    // Refresh subscription status when user subscribes
    ref.read(subscriptionStatusNotifierProvider.notifier).markAsPremium();
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  // Complete onboarding
  Future<void> completeOnboarding() async {
    if (!state.isOnboardingComplete) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingCompleteKey, true);

    // Clear temporary onboarding state since it's now complete
    await prefs.remove(_onboardingDataKey);

    state = state.copyWith(currentStep: OnboardingStep.complete);
  }

  // Reset onboarding (for testing or if user doesn't subscribe)
  Future<void> resetOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_onboardingCompleteKey);
    await prefs.remove(_onboardingDataKey);

    // Clear any created data if user didn't subscribe
    if (!state.hasSubscribed) {
      await _clearOnboardingData();
    }

    state = const OnboardingState();
  }

  Future<void> _clearOnboardingData() async {
    try {
      // Delete created routine if exists
      if (state.createdRoutineId != null) {
        final routineNotifier = ref.read(routineCrudNotifierProvider.notifier);
        await routineNotifier.deleteRoutine(state.createdRoutineId!);
      }

      // Delete created intention if exists
      if (state.createdIntentionId != null) {
        final intentionNotifier =
            ref.read(intentionCrudNotifierProvider.notifier);
        await intentionNotifier.deleteIntention(state.createdIntentionId!);
      }
    } catch (e) {
      // Log error silently - could use a proper logging framework here
    }
  }

  // Check if onboarding should be shown
  Future<bool> shouldShowOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    final isComplete = prefs.getBool(_onboardingCompleteKey) ?? false;

    // If onboarding is marked as complete, don't show it
    if (isComplete) {
      return false;
    }

    // Check if user is authenticated
    final authState = ref.read(authStateNotifierProvider);
    final isAuthenticated = authState.maybeWhen(
      authenticated: (_) => true,
      orElse: () => false,
    );

    // If user is authenticated, check if they have premium access
    if (isAuthenticated) {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final isPremium = await revenueCatService.refreshPremiumStatus();

      if (isPremium) {
        // User is authenticated and has premium, mark as complete and skip onboarding
        await prefs.setBool(_onboardingCompleteKey, true);
        return false;
      }
    }

    // Show onboarding for:
    // - Non-authenticated users
    // - Authenticated users without premium
    // - Users who haven't completed onboarding
    return true;
  }
}

// Provider to check if onboarding is complete
@riverpod
Future<bool> isOnboardingComplete(Ref ref) async {
  final controller = ref.read(enhancedOnboardingControllerProvider.notifier);
  return !(await controller.shouldShowOnboarding());
}
