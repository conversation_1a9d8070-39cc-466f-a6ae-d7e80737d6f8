import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_data.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';

class ActivityDetails {
  final String name;
  final String settingsRouteName;
  final String iconPath;

  ActivityDetails({
    required this.name,
    required this.settingsRouteName,
    required this.iconPath,
  });
}

class OnboardingActivitiesWrapper extends ConsumerStatefulWidget {
  const OnboardingActivitiesWrapper({super.key});

  @override
  ConsumerState<OnboardingActivitiesWrapper> createState() =>
      _OnboardingActivitiesWrapperState();
}

class _OnboardingActivitiesWrapperState
    extends ConsumerState<OnboardingActivitiesWrapper> {
  final Set<String> _selectedActivities = {};
  bool _isLoading = false;

  final List<ActivityDetails> _availableActivities = [
    ActivityDetails(
        name: 'Breathwork',
        settingsRouteName: RouteNames.breathworkSettings,
        iconPath: 'assets/icons/breathwork_fill.svg'),
    ActivityDetails(
        name: 'Meditation',
        settingsRouteName: RouteNames.meditationSettings,
        iconPath: 'assets/icons/meditation_fill.svg'),
    ActivityDetails(
        name: 'Journal',
        settingsRouteName: RouteNames.journalingSettings,
        iconPath: 'assets/icons/journal_fill.svg'),
    ActivityDetails(
        name: 'Affirmations',
        settingsRouteName: RouteNames.affirmationsSettings,
        iconPath: 'assets/icons/affirmation_fill.svg'),
    ActivityDetails(
        name: 'Mood Tracking',
        settingsRouteName: RouteNames.moodTrackingSettings,
        iconPath: 'assets/icons/mood_fill.svg'),
    ActivityDetails(
        name: 'Gratitude',
        settingsRouteName: RouteNames.gratitudeSettings,
        iconPath: 'assets/icons/gratitude_fill.svg'),
  ];

  Future<int?> _createOrFindActivityForName(String activityName, db) async {
    try {
      // Try to find existing activity first
      final existingActivities = await db.getAllActivities();
      final filteredActivities = existingActivities
          .where((a) => a.name.toLowerCase() == activityName.toLowerCase())
          .toList();

      final existingActivity =
          filteredActivities.isNotEmpty ? filteredActivities.first : null;

      if (existingActivity != null) {
        return existingActivity.id;
      }

      // Create new activity if not found
      final activityType = activityName.toLowerCase().replaceAll(' ', '_');
      final defaultConfig = _getDefaultConfigForActivityType(activityType);

      final activityId = await db.createActivity(
        name: activityName,
        type: activityType,
        config: defaultConfig.toJsonString(),
      );

      return activityId;
    } catch (e) {
      debugPrint('Error creating/finding activity $activityName: $e');
      return null;
    }
  }

  /// Helper method to get default configuration for activity type
  ActivityConfig _getDefaultConfigForActivityType(String activityType) {
    switch (activityType) {
      case 'breathwork':
        return const ActivityConfig.breathwork(
          selectedPatternId: 'box',
          cycles: 3,
          availablePatterns: [],
        );
      case 'meditation':
        return const ActivityConfig.meditation();
      case 'journaling':
      case 'journal':
        return const ActivityConfig.journaling();
      case 'affirmations':
        return const ActivityConfig.affirmations();
      case 'mood_tracking':
        return const ActivityConfig.moodTracking();
      case 'gratitude':
        return const ActivityConfig.gratitude();
      default:
        return const ActivityConfig.journaling(); // Fallback
    }
  }

  Future<void> _saveSelectedActivities() async {
    setState(() => _isLoading = true);

    try {
      final onboardingState = ref.read(enhancedOnboardingControllerProvider);
      final routineId = onboardingState.createdRoutineId;

      if (routineId == null) {
        throw Exception(
            'No routine found. Please go back and create a routine first.');
      }

      // Create or find activity IDs for the selected activity names
      final db = ref.read(journalDatabaseProvider);
      final activityIds = <int>[];

      for (final activityName in _selectedActivities) {
        final activityId = await _createOrFindActivityForName(activityName, db);
        if (activityId != null) {
          activityIds.add(activityId);
        }
      }

      // Save activity IDs to routine
      final activitiesJson = jsonEncode(activityIds);
      final notifier = ref.read(routineCrudNotifierProvider.notifier);
      await notifier.updateRoutine(
        routineId: routineId,
        activities: activitiesJson,
        name: null,
        type: null,
        scheduledTime: null,
        notificationEnabled: null,
      );

      if (mounted) {
        ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving activities: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref
                .read(enhancedOnboardingControllerProvider.notifier)
                .previousStep();
          },
        ),
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingState.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              ref
                  .read(enhancedOnboardingControllerProvider.notifier)
                  .resetOnboarding();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const FlowHeaderWidget(
                      title: 'Choose activities for your routine',
                      subtitle: 'Select the activities that resonate with you',
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Selected: ${_selectedActivities.length} activities',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                    ),
                    const SizedBox(height: 16),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _availableActivities.length,
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 1.6,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      itemBuilder: (context, index) {
                        final activity = _availableActivities[index];
                        final bool isSelected =
                            _selectedActivities.contains(activity.name);

                        // Get the order number for this activity if selected
                        final selectedList = _selectedActivities.toList();
                        final orderNumber = isSelected
                            ? selectedList.indexOf(activity.name) + 1
                            : null;

                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              if (isSelected) {
                                _selectedActivities.remove(activity.name);
                              } else {
                                _selectedActivities.add(activity.name);
                              }
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Theme.of(context)
                                      .colorScheme
                                      .primary
                                      .withValues(alpha: 0.1)
                                  : Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).colorScheme.outline,
                                width: isSelected ? 2 : 1,
                              ),
                            ),
                            child: Stack(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(12),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        activity.iconPath,
                                        width: 32,
                                        height: 32,
                                        colorFilter: ColorFilter.mode(
                                          isSelected
                                              ? Theme.of(context)
                                                  .colorScheme
                                                  .primary
                                              : Theme.of(context)
                                                  .colorScheme
                                                  .onSurface
                                                  .withValues(alpha: 0.7),
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        activity.name,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: isSelected
                                                  ? Theme.of(context)
                                                      .colorScheme
                                                      .primary
                                                  : Theme.of(context)
                                                      .colorScheme
                                                      .onSurface,
                                            ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                                if (isSelected && orderNumber != null)
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: Container(
                                      width: 24,
                                      height: 24,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: Text(
                                          orderNumber.toString(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            // Next button at bottom
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveSelectedActivities,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Next',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
