// lib/src/features/onboarding/presentation/onboarding_auth_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_form.dart';
import 'package:mimi_app/src/features/auth/providers/auth_state_provider.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/widgets/onboarding_progress_indicator.dart';

class OnboardingAuthScreen extends ConsumerStatefulWidget {
  const OnboardingAuthScreen({super.key});

  @override
  ConsumerState<OnboardingAuthScreen> createState() =>
      _OnboardingAuthScreenState();
}

class _OnboardingAuthScreenState extends ConsumerState<OnboardingAuthScreen> {
  bool _isSignUp = true;

  @override
  void initState() {
    super.initState();

    // Listen to auth state changes
    ref.listenManual(authStateNotifierProvider, (previous, next) {
      next.when(
        initial: () {},
        loading: () {},
        authenticated: (user) {
          // User is authenticated, move to next step
          ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
        },
        unauthenticated: () {},
        error: (error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(error),
              backgroundColor: Colors.red,
            ),
          );
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return GradientScaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref
                .read(enhancedOnboardingControllerProvider.notifier)
                .previousStep();
          },
        ),
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingState.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              ref
                  .read(enhancedOnboardingControllerProvider.notifier)
                  .resetOnboarding();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header
                    Text(
                      _isSignUp ? 'Create Your Account' : 'Welcome Back',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppSizing.spaceM),

                    Text(
                      _isSignUp
                          ? 'Join the Mimi community and start your mindfulness journey'
                          : 'Sign in to continue your journey',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppSizing.spaceXL),

                    // Auth form
                    AuthForm(
                      isSignUp: _isSignUp,
                      onSuccess: () {
                        // Navigation is handled by the auth state listener
                      },
                    ),
                    const SizedBox(height: AppSizing.spaceL),

                    // Toggle between sign up and sign in
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _isSignUp
                              ? 'Already have an account? '
                              : 'Don\'t have an account? ',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                        ),
                        TextButton(
                          onPressed: () {
                            setState(() {
                              _isSignUp = !_isSignUp;
                            });
                          },
                          child: Text(
                            _isSignUp ? 'Sign In' : 'Sign Up',
                            style: TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizing.spaceXL),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
