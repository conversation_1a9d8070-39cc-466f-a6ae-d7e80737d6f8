// lib/src/features/onboarding/presentation/paywall_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';

class PaywallScreen extends ConsumerStatefulWidget {
  const PaywallScreen({super.key});

  @override
  ConsumerState<PaywallScreen> createState() => _PaywallScreenState();
}

class _PaywallScreenState extends ConsumerState<PaywallScreen> {
  List<Package> _packages = [];
  bool _isLoading = true;
  Package? _selectedPackage;

  @override
  void initState() {
    super.initState();
    _loadPackages();
  }

  Future<void> _loadPackages() async {
    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final packages = await revenueCatService.getSubscriptionPackages();

      if (mounted) {
        setState(() {
          _packages = packages;
          _selectedPackage = packages.isNotEmpty ? packages.first : null;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading subscription options: $e')),
        );
      }
    }
  }

  Future<void> _purchaseSubscription() async {
    if (_selectedPackage == null) return;

    setState(() => _isLoading = true);
    ref.read(enhancedOnboardingControllerProvider.notifier).setLoading(true);

    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final success =
          await revenueCatService.purchaseSubscription(_selectedPackage!);

      if (mounted) {
        if (success) {
          ref
              .read(enhancedOnboardingControllerProvider.notifier)
              .markSubscribed();
          ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Purchase failed. Please try again.'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error during purchase: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
        ref
            .read(enhancedOnboardingControllerProvider.notifier)
            .setLoading(false);
      }
    }
  }

  Future<void> _restorePurchases() async {
    setState(() => _isLoading = true);

    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      await revenueCatService.restorePurchases();

      // Check if user is now premium
      final isPremium = await revenueCatService.isPremiumUser();

      if (mounted) {
        if (isPremium) {
          ref
              .read(enhancedOnboardingControllerProvider.notifier)
              .markSubscribed();
          ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No active subscriptions found')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error restoring purchases: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GradientScaffold(
      appBar: AppBar(
        title: const Text('Start Your Journey'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            ref
                .read(enhancedOnboardingControllerProvider.notifier)
                .resetOnboarding();
            // Navigate back to the first onboarding screen instead of login
            context.goNamed(RouteNames.onboarding);
          },
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppSizing.spaceL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  Text(
                    'Unlock Your Full Potential',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: AppTypography.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppSizing.spaceM),

                  Text(
                    'Get unlimited access to all features and start your mindfulness journey today.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.7),
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppSizing.spaceXL),

                  // Features list
                  _buildFeaturesList(),
                  const SizedBox(height: AppSizing.spaceXL),

                  // Subscription packages
                  if (_packages.isNotEmpty) ...[
                    Text(
                      'Choose Your Plan',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: AppTypography.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppSizing.spaceL),
                    ..._packages.map((package) => _buildPackageCard(package)),
                    const SizedBox(height: AppSizing.spaceXL),
                  ],

                  // Purchase button
                  ElevatedButton(
                    onPressed:
                        _selectedPackage != null ? _purchaseSubscription : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                          vertical: AppSizing.spaceM),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppSizing.radiusM),
                      ),
                    ),
                    child: Text(
                      'Start Free Trial',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: AppTypography.bold,
                            color: Theme.of(context).colorScheme.onPrimary,
                          ),
                    ),
                  ),
                  const SizedBox(height: AppSizing.spaceM),

                  // Restore purchases button
                  TextButton(
                    onPressed: _restorePurchases,
                    child: Text(
                      'Restore Purchases',
                      style: TextStyle(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.7)),
                    ),
                  ),
                  const SizedBox(height: AppSizing.spaceM),

                  // Terms and privacy
                  Text(
                    'By continuing, you agree to our Terms of Service and Privacy Policy',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.7),
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      'Unlimited meditation sessions',
      'Personalised routines',
      'Progress tracking',
      'AI-powered insights',
      'Premium content library',
      'Offline access',
    ];

    return Column(
      children: features
          .map((feature) => Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: AppSizing.spaceXS),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                      size: AppSizing.iconS,
                    ),
                    const SizedBox(width: AppSizing.spaceM),
                    Expanded(
                      child: Text(
                        feature,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }

  Widget _buildPackageCard(Package package) {
    final isSelected = _selectedPackage == package;

    return GestureDetector(
      onTap: () => setState(() => _selectedPackage = package),
      child: Container(
        margin: const EdgeInsets.only(bottom: AppSizing.spaceM),
        padding: const EdgeInsets.all(AppSizing.spaceM),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(AppSizing.radiusM),
        ),
        child: Row(
          children: [
            Radio<Package>(
              value: package,
              groupValue: _selectedPackage,
              onChanged: (value) => setState(() => _selectedPackage = value),
              activeColor: Theme.of(context).colorScheme.primary,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    package.storeProduct.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: AppTypography.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                  if (package.storeProduct.description.isNotEmpty)
                    Text(
                      package.storeProduct.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withValues(alpha: 0.7),
                          ),
                    ),
                ],
              ),
            ),
            Text(
              package.storeProduct.priceString,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: AppTypography.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
