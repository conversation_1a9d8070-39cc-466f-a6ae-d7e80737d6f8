import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';

class OnboardingRoutineNameWrapper extends ConsumerStatefulWidget {
  const OnboardingRoutineNameWrapper({super.key});

  @override
  ConsumerState<OnboardingRoutineNameWrapper> createState() =>
      _OnboardingRoutineNameWrapperState();
}

class _OnboardingRoutineNameWrapperState
    extends ConsumerState<OnboardingRoutineNameWrapper> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late FocusNode _focusNode;
  bool _isLoading = false;
  DateTime _scheduledTime = DateTime(DateTime.now().year, DateTime.now().month,
      DateTime.now().day, 8, 0); // Default to 8:00 AM

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: 'My Daily Practice');
    _focusNode = FocusNode();

    // Auto-focus the text field after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _saveRoutine() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    final notifier = ref.read(routineCrudNotifierProvider.notifier);
    final routineName = _nameController.text.trim();

    try {
      final onboardingState = ref.read(enhancedOnboardingControllerProvider);
      final intentionId = onboardingState.createdIntentionId;

      if (intentionId == null) {
        throw Exception(
            'No intention found. Please go back and create an intention first.');
      }

      // Create routine
      final routineId = await notifier.addRoutine(
        intentionId: intentionId,
        name: routineName,
        type: "Daily", // Default type
        scheduledTime: _scheduledTime, // Will be updated in next screen
      );

      if (mounted) {
        ref
            .read(enhancedOnboardingControllerProvider.notifier)
            .markRoutineCreated(routineId);
        ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating routine: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref
                .read(enhancedOnboardingControllerProvider.notifier)
                .previousStep();
          },
        ),
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingState.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              ref
                  .read(enhancedOnboardingControllerProvider.notifier)
                  .resetOnboarding();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    const FlowHeaderWidget.withTopPadding(
                      title: 'Give your check-in a name.',
                    ),
                    CustomTextField(
                      controller: _nameController,
                      focusNode: _focusNode,
                      hintText: 'e.g., Morning Reflection, End of Day Review',
                      style: CustomTextFieldStyle.withGradient,
                      maxLines: 1,
                      isFormField: true,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a name for your check-in.';
                        }
                        return null;
                      },
                    ),
                    const Spacer(),
                    // Next button at bottom
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveRoutine,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : Text(
                                  'Next',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
