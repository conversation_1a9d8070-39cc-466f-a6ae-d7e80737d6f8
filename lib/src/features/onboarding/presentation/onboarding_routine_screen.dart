// lib/src/features/onboarding/presentation/onboarding_routine_screen.dart

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart';

class OnboardingRoutineScreen extends ConsumerStatefulWidget {
  const OnboardingRoutineScreen({super.key});

  @override
  ConsumerState<OnboardingRoutineScreen> createState() =>
      _OnboardingRoutineScreenState();
}

class _OnboardingRoutineScreenState
    extends ConsumerState<OnboardingRoutineScreen> {
  final _formKey = GlobalKey<FormState>();
  final _routineController = TextEditingController();
  final _focusNode = FocusNode();
  bool _isLoading = false;
  TimeOfDay _selectedTime = const TimeOfDay(hour: 8, minute: 0);
  final Set<String> _selectedActivities = {'Meditation', 'Gratitude'};
  final List<String> _availableActivities = [
    'Meditation',
    'Breathwork',
    'Journaling',
    'Affirmations',
    'Gratitude',
    'Mood Tracking',
  ];

  @override
  void initState() {
    super.initState();
    _routineController.text = 'My Daily Practice';
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _routineController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _createRoutine() async {
    if (!_formKey.currentState!.validate() || _selectedActivities.isEmpty) {
      if (_selectedActivities.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least one activity'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    setState(() => _isLoading = true);
    ref.read(enhancedOnboardingControllerProvider.notifier).setLoading(true);

    try {
      final onboardingState = ref.read(enhancedOnboardingControllerProvider);
      final intentionId = onboardingState.createdIntentionId;

      if (intentionId == null) {
        throw Exception(
            'No intention found. Please go back and create an intention first.');
      }

      final routineName = _routineController.text.trim();
      final scheduledTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      // Create routine
      final routineNotifier = ref.read(routineCrudNotifierProvider.notifier);
      final routineId = await routineNotifier.addRoutine(
        intentionId: intentionId,
        name: routineName,
        type: 'Daily', // Default type since we removed period selection
        scheduledTime: scheduledTime,
      );

      // Create or find activity IDs for the selected activity names (new format)
      final db = ref.read(journalDatabaseProvider);
      final activityIds = <int>[];

      for (final activityName in _selectedActivities) {
        final activityId = await _createOrFindActivityForName(activityName, db);
        if (activityId != null) {
          activityIds.add(activityId);
        }
      }

      // Save activity IDs (new format) instead of activity names
      final activitiesJson = jsonEncode(activityIds);
      await routineNotifier.updateRoutine(
        routineId: routineId,
        activities: activitiesJson,
      );

      if (mounted) {
        ref
            .read(enhancedOnboardingControllerProvider.notifier)
            .markRoutineCreated(routineId);
        ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating routine: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
        ref
            .read(enhancedOnboardingControllerProvider.notifier)
            .setLoading(false);
      }
    }
  }

  /// Create or find activity ID for a given activity name
  Future<int?> _createOrFindActivityForName(
      String activityName, JournalDatabase db) async {
    try {
      // Normalize activity name and determine type
      final normalizedName = activityName.toLowerCase().trim();
      String activityType;
      String displayName;

      // Map activity names to types and display names
      switch (normalizedName) {
        case 'breathwork':
          activityType = 'breathwork';
          displayName = 'Breathwork';
          break;
        case 'meditation':
          activityType = 'meditation';
          displayName = 'Meditation';
          break;
        case 'journal':
        case 'journaling':
          activityType = 'journaling';
          displayName = 'Journal';
          break;
        case 'affirmations':
          activityType = 'affirmations';
          displayName = 'Affirmations';
          break;
        case 'mood tracking':
        case 'mood_tracking':
        case 'moodtracking':
          activityType = 'moodTracking';
          displayName = 'Mood Tracking';
          break;
        case 'gratitude':
          activityType = 'gratitude';
          displayName = 'Gratitude';
          break;
        default:
          debugPrint('⚠️ Unknown activity name: "$activityName"');
          return null;
      }

      // Check if activity already exists (get the first one if multiple exist)
      final existingActivities = await (db.select(db.checkInActivities)
            ..where((a) => a.type.equals(activityType))
            ..limit(1))
          .get();

      if (existingActivities.isNotEmpty) {
        return existingActivities.first.id;
      }

      // Create new activity with default configuration
      final defaultConfig = _getDefaultConfigForActivityType(activityType);

      final activityId = await db.into(db.checkInActivities).insert(
            CheckInActivitiesCompanion.insert(
              type: activityType,
              name: displayName,
              config: jsonEncode(defaultConfig.toJson()),
              lastUpdated: DateTime.now(),
            ),
          );

      debugPrint(
          '✅ Created new activity: ID $activityId, type "$activityType", name "$displayName"');
      return activityId;
    } catch (e) {
      debugPrint('❌ Error creating/finding activity for "$activityName": $e');
      return null;
    }
  }

  /// Get default configuration for activity type
  ActivityConfig _getDefaultConfigForActivityType(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'breathwork':
        return ActivityConfig.breathwork(
          selectedPatternId: defaultBreathworkPatterns.first.id,
          cycles: 3,
          availablePatterns: defaultBreathworkPatterns,
        );
      case 'meditation':
        return const ActivityConfig.meditation();
      case 'journaling':
        return const ActivityConfig.journaling();
      case 'affirmations':
        return const ActivityConfig.affirmations();
      case 'moodtracking':
        return const ActivityConfig.moodTracking();
      case 'gratitude':
        return const ActivityConfig.gratitude();
      default:
        return const ActivityConfig.journaling(); // fallback
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (picked != null) {
      setState(() => _selectedTime = picked);
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return GradientScaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref
                .read(enhancedOnboardingControllerProvider.notifier)
                .previousStep();
          },
        ),
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingState.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              ref
                  .read(enhancedOnboardingControllerProvider.notifier)
                  .resetOnboarding();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Header
                      Text(
                        'Create Your Routine',
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppSizing.spaceM),

                      Text(
                        'Build your daily mindfulness practice',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppSizing.spaceXL),

                      // Routine name
                      CustomTextField(
                        controller: _routineController,
                        focusNode: _focusNode,
                        hintText: 'e.g., My Daily Practice',
                        style: CustomTextFieldStyle.withGradient,
                        isFormField: true,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a routine name';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          // Optional: Handle text changes if needed
                        },
                      ),
                      const SizedBox(height: AppSizing.spaceL),

                      // Time selection
                      ListTile(
                        title: const Text('Reminder Time'),
                        subtitle: Text(_selectedTime.format(context)),
                        trailing: const Icon(Icons.access_time),
                        onTap: _selectTime,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppSizing.radiusM),
                          side: BorderSide(color: AppColors.border),
                        ),
                      ),
                      const SizedBox(height: AppSizing.spaceL),

                      // Activities selection
                      Text(
                        'Choose your activities (select at least one):',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                      ),
                      const SizedBox(height: AppSizing.spaceM),

                      Wrap(
                        spacing: AppSizing.spaceS,
                        runSpacing: AppSizing.spaceS,
                        children: _availableActivities
                            .map((activity) => _buildActivityChip(activity))
                            .toList(),
                      ),
                      const SizedBox(height: AppSizing.spaceXL),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom button
            Padding(
              padding: const EdgeInsets.all(AppSizing.spaceL),
              child: ElevatedButton(
                onPressed: _isLoading ? null : _createRoutine,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(vertical: AppSizing.spaceM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizing.radiusM),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : Text(
                        'Continue',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityChip(String activity) {
    final isSelected = _selectedActivities.contains(activity);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedActivities.remove(activity);
          } else {
            _selectedActivities.add(activity);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizing.spaceM,
          vertical: AppSizing.spaceS,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(AppSizing.radiusL),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSelected) ...[
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
                size: 16,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              activity,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
