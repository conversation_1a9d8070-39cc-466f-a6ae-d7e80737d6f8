import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/breathwork/screens/breathwork_settings_screen.dart';
import 'package:mimi_app/src/features/activities/meditation/screens/meditation_settings_screen.dart';
import 'package:mimi_app/src/features/activities/journaling/screens/journaling_settings_screen.dart';
import 'package:mimi_app/src/features/activities/affirmations/screen/affirmations_settings_screen.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';

class OnboardingActivitiesConfigWrapper extends ConsumerStatefulWidget {
  const OnboardingActivitiesConfigWrapper({super.key});

  @override
  ConsumerState<OnboardingActivitiesConfigWrapper> createState() =>
      _OnboardingActivitiesConfigWrapperState();
}

class _OnboardingActivitiesConfigWrapperState
    extends ConsumerState<OnboardingActivitiesConfigWrapper> {
  List<Activity> _activities = [];
  int _currentIndex = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadActivitiesFromRoutine();
  }

  Future<void> _loadActivitiesFromRoutine() async {
    try {
      final onboardingState = ref.read(enhancedOnboardingControllerProvider);
      final routineId = onboardingState.createdRoutineId;

      if (routineId == null) {
        throw Exception('No routine found');
      }

      final db = ref.read(journalDatabaseProvider);
      final routines = await db.getAllRoutines();
      final routine = routines.firstWhere((r) => r.id == routineId);

      if (routine.activities.isNotEmpty && routine.activities != '[]') {
        // Parse activity IDs from routine
        final activityIds = List<int>.from(jsonDecode(routine.activities));

        // Load activities from database
        final allActivities = await db.getAllActivities();
        final routineActivities = allActivities
            .where((activity) => activityIds.contains(activity.id))
            .toList();

        // Filter activities that need configuration
        final activitiesNeedingConfig = routineActivities.where((activity) {
          final type = activity.name.toLowerCase();
          return type == 'breathwork' ||
              type == 'meditation' ||
              type == 'journal' ||
              type == 'journaling' ||
              type == 'affirmations';
        }).toList();

        setState(() {
          _activities = activitiesNeedingConfig;
          _isLoading = false;
        });

        if (activitiesNeedingConfig.isEmpty) {
          _skipConfiguration();
        }
      } else {
        // No activities selected, skip configuration
        setState(() {
          _isLoading = false;
        });
        _skipConfiguration();
      }
    } catch (e) {
      debugPrint('Error loading activities: $e');
      setState(() {
        _isLoading = false;
      });
      _skipConfiguration();
    }
  }

  void _skipConfiguration() {
    // Skip to next step if no activities need configuration
    ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
  }

  void _goToNext() {
    if (_currentIndex < _activities.length - 1) {
      setState(() {
        _currentIndex++;
      });
    } else {
      // Configuration complete, proceed to next onboarding step
      ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
    }
  }

  void _goToPrevious() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
    } else {
      // Go back to previous onboarding step
      ref.read(enhancedOnboardingControllerProvider.notifier).previousStep();
    }
  }

  void _onConfigChanged(ActivityConfig newConfig) {
    setState(() {
      _activities[_currentIndex] =
          _activities[_currentIndex].copyWith(config: newConfig);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If no activities need configuration, skip
    if (_activities.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _skipConfiguration();
      });
      return const Scaffold(
        body: Center(
          child: Text('No activities to configure'),
        ),
      );
    }

    final onboardingState = ref.read(enhancedOnboardingControllerProvider);
    final routineId = onboardingState.createdRoutineId;

    if (routineId == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: const Center(
          child: Text(
              'Missing required data. Please restart the onboarding process.'),
        ),
      );
    }

    final currentActivity = _activities[_currentIndex];

    final onboardingStateForProgress =
        ref.watch(enhancedOnboardingControllerProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _goToPrevious,
        ),
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingStateForProgress.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              ref
                  .read(enhancedOnboardingControllerProvider.notifier)
                  .resetOnboarding();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Configuration form
            Expanded(
              child: _buildActivityConfigForm(currentActivity, routineId),
            ),

            // Next button at bottom
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _goToNext,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Next',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityConfigForm(Activity activity, int routineId) {
    final activityType = activity.name.toLowerCase();

    switch (activityType) {
      case 'breathwork':
        return BreathworkSettingsScreen(
          activity: activity,
          routineId: routineId,
          onConfigChanged: _onConfigChanged,
        );
      case 'meditation':
        return MeditationSettingsScreen(
          activity: activity,
          routineId: routineId,
          onConfigChanged: _onConfigChanged,
        );
      case 'journal':
      case 'journaling':
        return JournalingSettingsScreen(
          activity: activity,
          routineId: routineId,
          onConfigChanged: _onConfigChanged,
        );
      case 'affirmations':
        return AffirmationsSettingsScreen(
          activity: activity,
          routineId: routineId,
          onConfigChanged: _onConfigChanged,
        );
      default:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                size: 64,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                '${activity.name} is ready!',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Your ${activity.name.toLowerCase()} activity is configured with default settings.',
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
    }
  }
}
