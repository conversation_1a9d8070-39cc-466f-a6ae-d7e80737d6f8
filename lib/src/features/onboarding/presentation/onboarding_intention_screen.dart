// lib/src/features/onboarding/presentation/onboarding_intention_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';

class OnboardingIntentionScreen extends ConsumerStatefulWidget {
  const OnboardingIntentionScreen({super.key});

  @override
  ConsumerState<OnboardingIntentionScreen> createState() =>
      _OnboardingIntentionScreenState();
}

class _OnboardingIntentionScreenState
    extends ConsumerState<OnboardingIntentionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _intentionController = TextEditingController();
  final _focusNode = FocusNode();
  bool _isLoading = false;

  final List<String> _suggestedIntentions = [
    'Daily Mindfulness',
    'Stress Relief',
    'Better Sleep',
    'Focus & Productivity',
    'Emotional Balance',
    'Self-Compassion',
    'Gratitude Practice',
    'Inner Peace',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _intentionController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _createIntention() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    ref.read(enhancedOnboardingControllerProvider.notifier).setLoading(true);

    try {
      final intentionName = _intentionController.text.trim();
      final notifier = ref.read(intentionCrudNotifierProvider.notifier);
      final intentionId = await notifier.addIntention(intentionName);

      if (mounted) {
        ref
            .read(enhancedOnboardingControllerProvider.notifier)
            .markIntentionCreated(intentionId);
        ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating intention: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
        ref
            .read(enhancedOnboardingControllerProvider.notifier)
            .setLoading(false);
      }
    }
  }

  void _selectSuggestedIntention(String intention) {
    _intentionController.text = intention;
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return GradientScaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref
                .read(enhancedOnboardingControllerProvider.notifier)
                .previousStep();
          },
        ),
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingState.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              ref
                  .read(enhancedOnboardingControllerProvider.notifier)
                  .resetOnboarding();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Header
                      Text(
                        'Set Your Intention',
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppSizing.spaceM),

                      Text(
                        'What would you like to focus on in your mindfulness journey?',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppSizing.spaceXL),

                      // Intention input
                      CustomTextField(
                        controller: _intentionController,
                        focusNode: _focusNode,
                        hintText: 'e.g., Daily Mindfulness',
                        style: CustomTextFieldStyle.withGradient,
                        isFormField: true,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter your intention';
                          }
                          if (value.trim().length < 3) {
                            return 'Intention must be at least 3 characters';
                          }
                          return null;
                        },
                        textInputAction: TextInputAction.done,
                        onSubmitted: (_) => _createIntention(),
                        onChanged: (value) {
                          // Optional: Handle text changes if needed
                        },
                      ),
                      const SizedBox(height: AppSizing.spaceXL),

                      // Suggested intentions
                      Text(
                        'Or choose from these suggestions:',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                      ),
                      const SizedBox(height: AppSizing.spaceM),

                      Wrap(
                        spacing: AppSizing.spaceS,
                        runSpacing: AppSizing.spaceS,
                        children: _suggestedIntentions
                            .map((intention) => _buildSuggestionChip(intention))
                            .toList(),
                      ),
                      const SizedBox(height: AppSizing.spaceXL),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom button
            Padding(
              padding: const EdgeInsets.all(AppSizing.spaceL),
              child: ElevatedButton(
                onPressed: _isLoading ? null : _createIntention,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(vertical: AppSizing.spaceM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizing.radiusM),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : Text(
                        'Continue',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionChip(String intention) {
    return GestureDetector(
      onTap: () => _selectSuggestedIntention(intention),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizing.spaceM,
          vertical: AppSizing.spaceS,
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(AppSizing.radiusL),
        ),
        child: Text(
          intention,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
              ),
        ),
      ),
    );
  }
}
