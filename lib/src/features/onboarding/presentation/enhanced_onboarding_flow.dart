// lib/src/features/onboarding/presentation/enhanced_onboarding_flow.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_page.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_auth_screen.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_step_header_screen.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_intention_wrapper.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_routine_name_wrapper.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_routine_time_wrapper.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_activities_wrapper.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_activities_config_wrapper.dart';
import 'package:mimi_app/src/features/onboarding/presentation/revenuecat_paywall_screen.dart';
import 'package:mimi_app/src/features/onboarding/presentation/onboarding_complete_screen.dart';

class EnhancedOnboardingFlow extends ConsumerWidget {
  const EnhancedOnboardingFlow({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return Scaffold(
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _buildCurrentScreen(onboardingState.currentStep),
      ),
    );
  }

  Widget _buildCurrentScreen(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.welcome:
        return const OnboardingPage(); // Use carousel slides instead of GetStartedScreen
      case OnboardingStep.intentionHeader:
        return OnboardingStepHeaderScreen(
          step: step,
          title: 'Step 1',
          subtitle:
              ' Your intention shapes your energy. What you focus on is what you grow.',
          progressPercentage: 33,
        );
      case OnboardingStep.intention:
        return const OnboardingIntentionWrapper();
      case OnboardingStep.routineHeader:
        return OnboardingStepHeaderScreen(
          step: step,
          title: 'Step 2',
          subtitle:
              'A simple check-in multiple times a day, keeps you aligned, aware, and connected to your inner-self .',
          progressPercentage: 66,
        );
      case OnboardingStep.routineName:
        return const OnboardingRoutineNameWrapper();
      case OnboardingStep.routineTime:
        return const OnboardingRoutineTimeWrapper();
      case OnboardingStep.activitiesHeader:
        return OnboardingStepHeaderScreen(
          step: step,
          title: 'Step 3',
          subtitle:
              'Choose Practices that nourish your mind, body and spirit. You can have different practices for different routines.',
          progressPercentage: 100,
        );
      case OnboardingStep.activitiesSelection:
        return const OnboardingActivitiesWrapper();
      case OnboardingStep.activitiesConfig:
        return const OnboardingActivitiesConfigWrapper();
      case OnboardingStep.trialHeader:
        return OnboardingStepHeaderScreen(
          step: step,
          title: 'Step 4',
          subtitle:
              'Experience all premium features with our free trial. Unlock the full potential of your mindfulness journey.',
          progressPercentage: 100,
        );
      case OnboardingStep.paywall:
        return const RevenueCatPaywallScreen();
      case OnboardingStep.auth:
        return const OnboardingAuthScreen();
      case OnboardingStep.complete:
        return const OnboardingCompleteScreen();
    }
  }
}
