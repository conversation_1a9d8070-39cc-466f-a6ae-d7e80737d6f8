// lib/src/features/onboarding/presentation/revenuecat_paywall_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';
import 'package:flutter/foundation.dart';

class RevenueCatPaywallScreen extends ConsumerStatefulWidget {
  const RevenueCatPaywallScreen({super.key});

  @override
  ConsumerState<RevenueCatPaywallScreen> createState() =>
      _RevenueCatPaywallScreenState();
}

class _RevenueCatPaywallScreenState
    extends ConsumerState<RevenueCatPaywallScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Show the paywall immediately when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showPaywall();
    });
  }

  Future<void> _showPaywall() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final success = await revenueCatService.showPaywallIfNeeded(context);

      if (mounted) {
        setState(() => _isLoading = false);

        if (success) {
          // User purchased or already has entitlement
          ref
              .read(enhancedOnboardingControllerProvider.notifier)
              .markSubscribed();

          // Store subscription info for later linking during auth
          final revenueCatService = ref.read(revenueCatServiceProvider);
          final customerInfo =
              await revenueCatService.getDetailedSubscriptionStatus();

          if (mounted && kDebugMode) {
            // Log subscription details for debugging
            print('RevenueCat: Subscription completed before auth');
            print('RevenueCat: Customer ID: ${customerInfo['customer_id']}');
            print('RevenueCat: Is Premium: ${customerInfo['is_premium']}');
          }

          ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
        } else {
          // User dismissed paywall without purchasing
          _showDismissalDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error showing paywall: $e'),
            backgroundColor: AppColors.error,
          ),
        );
        _showDismissalDialog();
      }
    }
  }

  void _showDismissalDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Subscription Required'),
        content: const Text(
          'A subscription is required to continue using the app. Would you like to try again or exit?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetOnboarding();
            },
            child: const Text('Exit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showPaywall();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
            ),
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  void _resetOnboarding() {
    ref.read(enhancedOnboardingControllerProvider.notifier).resetOnboarding();
    // Navigate back to the first onboarding screen instead of login
    context.goNamed(RouteNames.onboarding);
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return GradientScaffold(
      appBar: AppBar(
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingState.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _resetOnboarding,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (_isLoading) ...[
                      const CircularProgressIndicator(),
                      const SizedBox(height: AppSizing.spaceL),
                      Text(
                        'Loading subscription options...',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppColors.textPrimary,
                                ),
                        textAlign: TextAlign.center,
                      ),
                    ] else ...[
                      Icon(
                        Icons.star,
                        size: 64,
                        color: AppColors.primary,
                      ),
                      const SizedBox(height: AppSizing.spaceL),
                      Text(
                        'Almost There!',
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppSizing.spaceM),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: AppSizing.spaceL),
                        child: Text(
                          'Complete your setup with a subscription to unlock all features and start your mindfulness journey.',
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: AppSizing.spaceXL),
                      ElevatedButton(
                        onPressed: _showPaywall,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.textOnPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizing.spaceXL,
                            vertical: AppSizing.spaceM,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppSizing.radiusM),
                          ),
                        ),
                        child: const Text('View Subscription Options'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
