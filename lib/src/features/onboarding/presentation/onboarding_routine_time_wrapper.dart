import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/onboarding/providers/enhanced_onboarding_provider.dart';
import 'package:mimi_app/src/features/onboarding/models/onboarding_state.dart';
import 'package:mimi_app/src/features/onboarding/presentation/widgets/onboarding_progress_indicator.dart';
import 'package:mimi_app/src/features/intentions/presentation/widgets/custom_time_picker.dart';

class OnboardingRoutineTimeWrapper extends ConsumerStatefulWidget {
  const OnboardingRoutineTimeWrapper({super.key});

  @override
  ConsumerState<OnboardingRoutineTimeWrapper> createState() =>
      _OnboardingRoutineTimeWrapperState();
}

class _OnboardingRoutineTimeWrapperState
    extends ConsumerState<OnboardingRoutineTimeWrapper> {
  TimeOfDay? _selectedTime;
  bool _notificationsEnabled = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedTime = const TimeOfDay(hour: 8, minute: 0); // Default to 8:00 AM
  }

  void _showTimePickerDialog() {
    final initialTime = _selectedTime ?? const TimeOfDay(hour: 8, minute: 0);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: CustomTimePicker(
            initialTime: initialTime,
            onTimeChanged: (TimeOfDay newTime) {
              setState(() {
                _selectedTime = newTime;
              });
              Navigator.of(context).pop();
            },
            onCancel: () {
              Navigator.of(context).pop();
            },
          ),
        );
      },
    );
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  Future<void> _saveReminderSettings() async {
    if (_selectedTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select a time for your reminder.')),
      );
      return;
    }

    setState(() => _isLoading = true);
    final notifier = ref.read(routineCrudNotifierProvider.notifier);

    try {
      final onboardingState = ref.read(enhancedOnboardingControllerProvider);
      final routineId = onboardingState.createdRoutineId;

      if (routineId == null) {
        throw Exception(
            'No routine found. Please go back and create a routine first.');
      }

      await notifier.updateRoutine(
        routineId: routineId,
        scheduledTime: _selectedTime != null
            ? DateTime(DateTime.now().year, DateTime.now().month,
                DateTime.now().day, _selectedTime!.hour, _selectedTime!.minute)
            : null,
        notificationEnabled: _notificationsEnabled,
        name: null, // Pass null if not changing name here
        type: 'Daily', // Default type
        activities: null, // Pass null if not changing activities here
      );

      if (mounted) {
        ref.read(enhancedOnboardingControllerProvider.notifier).nextStep();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving reminder: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(enhancedOnboardingControllerProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref
                .read(enhancedOnboardingControllerProvider.notifier)
                .previousStep();
          },
        ),
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
          child: OnboardingProgressIndicator(
            currentStep: onboardingState.currentStep.stepNumber,
            totalSteps: OnboardingStepExtension.totalSteps,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              ref
                  .read(enhancedOnboardingControllerProvider.notifier)
                  .resetOnboarding();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const FlowHeaderWidget(
                      title: 'When would you like to do your check-in?',
                      subtitle: 'Choose a time that works best for you',
                    ),
                    const SizedBox(height: 32),

                    // Time Selection
                    GestureDetector(
                      onTap: _showTimePickerDialog,
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: AppColors.border),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.access_time,
                              color: AppColors.primary,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _selectedTime != null
                                  ? _formatTime(_selectedTime!)
                                  : 'Select Time',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Icon(
                              Icons.keyboard_arrow_down,
                              color: AppColors.primary,
                              size: 24,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Notifications toggle
                    SwitchListTile(
                      title: const Text('Enable Notifications'),
                      subtitle: const Text(
                          'Get reminded when it\'s time for your check-in'),
                      value: _notificationsEnabled,
                      onChanged: (value) {
                        setState(() {
                          _notificationsEnabled = value;
                        });
                      },
                      activeColor: AppColors.primary,
                    ),
                  ],
                ),
              ),
            ),
            // Next button at bottom
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveReminderSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Next',
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
