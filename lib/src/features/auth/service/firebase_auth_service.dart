// lib/src/features/auth/service/firebase_auth_service.dart
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
//import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../model/auth_user.dart';

part 'firebase_auth_service.g.dart';

@riverpod
FirebaseAuthService firebaseAuthService(Ref ref) {
  return FirebaseAuthService(firebase_auth.FirebaseAuth.instance);
}

class FirebaseAuthService {
  final firebase_auth.FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;

  FirebaseAuthService(this._auth) : _googleSignIn = GoogleSignIn();

  Stream<AuthUser?> get authStateChanges =>
      _auth.authStateChanges().map(_mapToAuthUser);

  Future<AuthUser?> getCurrentUser() async {
    // This will return the current user from Firebase Auth's persistent storage
    final user = _auth.currentUser;
    return user != null ? _mapToAuthUser(user) : null;
  }

  Future<bool> isUserLoggedIn() async {
    final currentUser = _auth.currentUser;
    return currentUser != null && !currentUser.isAnonymous;
  }

  Future<AuthUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    final result = await _auth.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
    return _mapToAuthUser(result.user);
  }

  Future<AuthUser?> signInWithGoogle() async {
    final googleUser = await _googleSignIn.signIn();
    if (googleUser == null) return null;

    final googleAuth = await googleUser.authentication;
    final credential = firebase_auth.GoogleAuthProvider.credential(
      accessToken: googleAuth.accessToken,
      idToken: googleAuth.idToken,
    );

    // This will be persisted thanks to Firebase Auth's persistence setting
    final result = await _auth.signInWithCredential(credential);
    return _mapToAuthUser(result.user);
  }

  Future<AuthUser?> signInAnonymously() async {
    final result = await _auth.signInAnonymously();
    return _mapToAuthUser(result.user);
  }

  Future<AuthUser?> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    final result = await _auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
    return _mapToAuthUser(result.user);
  }

  Future<void> signOut() async {
    await Future.wait([
      _auth.signOut(),
      _googleSignIn.signOut(),
    ]);
  }

  Future<void> resetPassword(String email) async {
    await _auth.sendPasswordResetEmail(email: email);
  }

  AuthUser? _mapToAuthUser(firebase_auth.User? user) {
    if (user == null) return null;

    return AuthUser(
      uid: user.uid,
      email: user.email ?? '',
      photoUrl: user.photoURL,
      isEmailVerified: user.emailVerified,
      provider: _determineProvider(user),
    );
  }

  AuthProvider _determineProvider(firebase_auth.User user) {
    if (user.isAnonymous) return AuthProvider.anonymous;
    if (user.providerData.any((info) => info.providerId == 'google.com')) {
      return AuthProvider.google;
    }
    if (user.providerData.any((info) => info.providerId == 'apple.com')) {
      return AuthProvider.apple;
    }
    return AuthProvider.email;
  }

  Future<void> reauthenticateWithPassword(String password) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not found');

    final credential = firebase_auth.EmailAuthProvider.credential(
      email: user.email!,
      password: password,
    );

    await user.reauthenticateWithCredential(credential);
  }

  Future<void> updateEmail(String newEmail) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not found');

    await user.verifyBeforeUpdateEmail(newEmail);
  }

  Future<void> updatePassword(String newPassword) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not found');

    await user.updatePassword(newPassword);
  }
}
