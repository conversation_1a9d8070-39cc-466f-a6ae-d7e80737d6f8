// lib/src/features/auth/providers/auth_controller.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../repositories/auth_repository.dart';
import '../../user/repositories/user_repository.dart';
import '../../user/models/user.dart';
import '../../purchases/providers/revenuecat_provider.dart';
import '../../purchases/providers/subscription_status_provider.dart';
import '../../onboarding/providers/enhanced_onboarding_provider.dart';
import '../../../core/theme/providers/palette_provider.dart';

part 'auth_controller.g.dart';

@riverpod
class AuthController extends _$AuthController {
  late final AuthRepository _authRepository;

  @override
  FutureOr<void> build() {
    _authRepository = ref.watch(authRepositoryProvider.notifier);
  }

  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    print('🔐 AUTH_DEBUG: signInWithEmailAndPassword called for email: $email');
    print('🔐 AUTH_DEBUG: Current state isLoading: ${state.isLoading}');

    // Prevent multiple concurrent sign-in attempts
    if (state.isLoading) {
      print('🔐 AUTH_DEBUG: Already loading, returning early');
      return;
    }

    print('🔐 AUTH_DEBUG: Setting state to AsyncLoading');
    state = const AsyncLoading();

    print('🔐 AUTH_DEBUG: Calling Firebase auth signInWithEmailAndPassword');
    final authUser = await _authRepository.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
    print('🔐 AUTH_DEBUG: Firebase auth completed, user: ${authUser?.uid}');

    if (authUser == null) throw Exception('Failed to sign in');

    print('🔐 AUTH_DEBUG: Getting RevenueCat service');
    // Log in to RevenueCat with the authenticated user and handle subscription transfer
    final revenueCatService = ref.read(revenueCatServiceProvider);

    print(
        '🔐 AUTH_DEBUG: Calling RevenueCat loginUser for uid: ${authUser.uid}');
    await revenueCatService.loginUser(authUser.uid);
    print('🔐 AUTH_DEBUG: RevenueCat loginUser completed');

    print('🔐 AUTH_DEBUG: Calling RevenueCat handleSubscriptionTransfer');
    // Handle any subscription transfer issues
    await revenueCatService.handleSubscriptionTransfer();
    print('🔐 AUTH_DEBUG: RevenueCat handleSubscriptionTransfer completed');

    print('🔐 AUTH_DEBUG: Setting state to AsyncData(null) - SUCCESS');
    state = const AsyncData(null);
  }

  Future<void> signUp({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    print('🔐 AUTH_DEBUG: signUp called for email: $email');
    print('🔐 AUTH_DEBUG: Current state isLoading: ${state.isLoading}');

    // Prevent multiple concurrent sign-up attempts
    if (state.isLoading) {
      print('🔐 AUTH_DEBUG: Already loading, returning early');
      return;
    }

    print('🔐 AUTH_DEBUG: Setting state to AsyncLoading');
    state = const AsyncLoading();

    print(
        '🔐 AUTH_DEBUG: Calling Firebase auth createUserWithEmailAndPassword');
    // Create Firebase Auth user
    final authUser = await _authRepository.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
    print('🔐 AUTH_DEBUG: Firebase auth user created, user: ${authUser?.uid}');

    if (authUser == null) throw Exception('Failed to create user');

    print('🔐 AUTH_DEBUG: Getting RevenueCat service');
    // Log in to RevenueCat with the new user and handle subscription transfer
    final revenueCatService = ref.read(revenueCatServiceProvider);

    print(
        '🔐 AUTH_DEBUG: Calling RevenueCat loginUser for uid: ${authUser.uid}');
    await revenueCatService.loginUser(authUser.uid);
    print('🔐 AUTH_DEBUG: RevenueCat loginUser completed');

    print('🔐 AUTH_DEBUG: Calling RevenueCat handleSubscriptionTransfer');
    // Handle any subscription transfer issues (critical for onboarding flow)
    await revenueCatService.handleSubscriptionTransfer();
    print('🔐 AUTH_DEBUG: RevenueCat handleSubscriptionTransfer completed');

    print('🔐 AUTH_DEBUG: Creating user profile in Firestore');
    // Create user profile in Firestore
    final userProfile = User(
      auth: authUser,
      firstName: firstName,
      lastName: lastName,
    );

    await ref
        .read(userRepositoryProvider.notifier)
        .createOrUpdateUser(userProfile);
    print('🔐 AUTH_DEBUG: User profile created in Firestore');

    print('🔐 AUTH_DEBUG: Setting state to AsyncData(null) - SUCCESS');
    state = const AsyncData(null);
  }

  Future<void> signOut() async {
    // Prevent multiple concurrent sign-out attempts
    if (state.isLoading) return;

    state = const AsyncLoading();

    // Clear RevenueCat user session
    final revenueCatService = ref.read(revenueCatServiceProvider);
    await revenueCatService.logoutUser();

    // Mark user as free in subscription status
    ref.read(subscriptionStatusNotifierProvider.notifier).markAsFree();

    // Clear onboarding completion state
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('onboarding_complete');
    await prefs.remove('onboarding_data');

    // Reset palette preferences to Midnight
    await ref.read(paletteNotifierProvider.notifier).resetPalettePreferences();

    // Reset onboarding state
    ref.invalidate(enhancedOnboardingControllerProvider);

    // Clear any cached data
    ref.invalidate(userRepositoryProvider);

    // Perform the actual sign out
    await _authRepository.signOut();

    state = const AsyncData(null);
  }

  Future<void> reauthenticateWithPassword(String password) async {
    state = const AsyncLoading();
    await _authRepository.reauthenticateWithPassword(password);
    state = const AsyncData(null);
  }

  Future<void> updateEmail(String newEmail) async {
    state = const AsyncLoading();
    await _authRepository.updateEmail(newEmail);
    state = const AsyncData(null);
  }

  Future<void> updatePassword(String newPassword) async {
    state = const AsyncLoading();
    await _authRepository.updatePassword(newPassword);
    state = const AsyncData(null);
  }

  Future<void> resetPassword(String email) async {
    state = const AsyncLoading();
    await _authRepository.resetPassword(email);
    state = const AsyncData(null);
  }
}
