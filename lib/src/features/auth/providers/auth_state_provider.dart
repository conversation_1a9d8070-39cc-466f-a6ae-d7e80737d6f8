// lib/src/features/auth/providers/auth_state_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../model/auth_state.dart';
// import '../model/auth_user.dart';
import '../repositories/auth_repository.dart';

part 'auth_state_provider.g.dart';

@Riverpod(keepAlive: true)
class AuthStateNotifier extends _$AuthStateNotifier {
  @override
  AuthState build() {
    print('🔐 AUTH_STATE_DEBUG: AuthStateNotifier build() called');
    _listenToAuthChanges();
    _initializeAuthState();
    return const AuthState.initial();
  }

  void _initializeAuthState() async {
    print('🔐 AUTH_STATE_DEBUG: _initializeAuthState() called');
    // Give a small delay to allow Firebase Auth to initialize
    await Future.delayed(const Duration(milliseconds: 100));

    // Check if we already have a user from the stream
    final currentStreamState = ref.read(authRepositoryProvider);
    print(
        '🔐 AUTH_STATE_DEBUG: Current stream state: ${currentStreamState.runtimeType}');

    currentStreamState.when(
      data: (user) {
        print('🔐 AUTH_STATE_DEBUG: Stream has data, user: ${user?.uid}');
        if (user == null) {
          print('🔐 AUTH_STATE_DEBUG: Setting state to unauthenticated');
          state = const AuthState.unauthenticated();
        } else {
          print(
              '🔐 AUTH_STATE_DEBUG: Setting state to authenticated with user: ${user.uid}');
          state = AuthState.authenticated(user);
        }
      },
      error: (error, stack) {
        print('🔐 AUTH_STATE_DEBUG: Stream has error: $error');
        state = AuthState.error(error.toString());
      },
      loading: () {
        print('🔐 AUTH_STATE_DEBUG: Stream is loading, keeping loading state');
        // Keep loading state, the stream will update when ready
      },
    );
  }

  void _listenToAuthChanges() {
    print('🔐 AUTH_STATE_DEBUG: _listenToAuthChanges() called');

    // Listen to auth repository for user state changes
    ref.listen(authRepositoryProvider, (previous, next) {
      print('🔐 AUTH_STATE_DEBUG: Auth repository state changed');
      print('🔐 AUTH_STATE_DEBUG: Previous: ${previous?.runtimeType}');
      print('🔐 AUTH_STATE_DEBUG: Next: ${next.runtimeType}');

      next.when(
        data: (user) {
          print(
              '🔐 AUTH_STATE_DEBUG: Repository data changed, user: ${user?.uid}');
          if (user == null) {
            print('🔐 AUTH_STATE_DEBUG: Setting state to unauthenticated');
            state = const AuthState.unauthenticated();
          } else {
            print(
                '🔐 AUTH_STATE_DEBUG: Setting state to authenticated with user: ${user.uid}');
            state = AuthState.authenticated(user);
          }
        },
        error: (error, stack) {
          print('🔐 AUTH_STATE_DEBUG: Repository error: $error');
          state = AuthState.error(error.toString());
        },
        loading: () {
          print(
              '🔐 AUTH_STATE_DEBUG: Repository loading, setting state to loading');
          state = const AuthState.loading();
        },
      );
    });
  }
}
