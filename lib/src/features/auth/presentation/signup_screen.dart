// lib/src/features/auth/presentation/screens/signup_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_button.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_text_field.dart';
import 'package:mimi_app/src/features/auth/providers/auth_controller.dart';
import 'package:mimi_app/src/features/auth/providers/auth_state_provider.dart';
import 'package:mimi_app/src/features/auth/providers/signup_form_provider.dart';
import 'package:mimi_app/src/core/config/url_config.dart';
import 'package:mimi_app/src/core/utils/url_launcher_utils.dart';

class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  @override
  Widget build(BuildContext context) {
    final signupForm = ref.watch(signupFormProvider);
    final theme = Theme.of(context);

    final isAuthLoading = ref.watch(authControllerProvider).isLoading;

    // Listen to auth state changes for navigation
    ref.listen(authStateNotifierProvider, (previous, next) {
      next.whenOrNull(
        authenticated: (_) {
          if (mounted) {
            showAuthSuccessToast(context, 'Account created successfully!');
            context.goNamed(RouteNames.home);
          }
        },
        error: (message) {
          if (mounted) {
            showAuthErrorToast(context, message);
          }
        },
      );
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Account'),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppSizing.screenEdgeInsets,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: AppSizing.spaceL),
              AuthTextField(
                label: 'First Name',
                onChanged: ref.read(signupFormProvider.notifier).setFirstName,
                enabled: !signupForm.isLoading,
                validator: _validateName,
              ),
              SizedBox(height: AppSizing.spaceM),
              AuthTextField(
                label: 'Last Name',
                onChanged: ref.read(signupFormProvider.notifier).setLastName,
                enabled: !signupForm.isLoading,
                validator: _validateName,
              ),
              SizedBox(height: AppSizing.spaceM),
              AuthTextField(
                label: 'Email',
                onChanged: ref.read(signupFormProvider.notifier).setEmail,
                keyboardType: TextInputType.emailAddress,
                enabled: !signupForm.isLoading,
                validator: _validateEmail,
              ),
              SizedBox(height: AppSizing.spaceM),
              AuthTextField(
                label: 'Password',
                onChanged: ref.read(signupFormProvider.notifier).setPassword,
                obscureText: true,
                enabled: !signupForm.isLoading,
                validator: _validatePassword,
              ),
              SizedBox(height: AppSizing.spaceM),
              AuthTextField(
                label: 'Confirm Password',
                onChanged:
                    ref.read(signupFormProvider.notifier).setConfirmPassword,
                obscureText: true,
                enabled: !signupForm.isLoading,
                validator: (value) =>
                    _validateConfirmPassword(value, signupForm.password),
              ),
              SizedBox(height: AppSizing.spaceL),
              CheckboxListTile(
                value: signupForm.acceptedTerms,
                onChanged: signupForm.isLoading
                    ? null
                    : (value) => ref
                        .read(signupFormProvider.notifier)
                        .setAcceptedTerms(value ?? false),
                title: TextButton(
                  onPressed: () {
                    UrlLauncherUtils.launchURL(context, UrlConfig.termsUrl);
                  },
                  child: Text(
                    'I accept the Terms and Conditions',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                contentPadding: EdgeInsets.zero,
                controlAffinity: ListTileControlAffinity.leading,
              ),
              SizedBox(height: AppSizing.spaceM),
              if (signupForm.error != null)
                Padding(
                  padding: EdgeInsets.only(bottom: AppSizing.spaceM),
                  child: Text(
                    signupForm.error!,
                    style: TextStyle(
                      color: theme.colorScheme.error,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              AuthButton(
                label: 'Create Account',
                onPressed: (isAuthLoading ||
                        signupForm.isLoading ||
                        !_isFormValid(signupForm))
                    ? null
                    : () => _handleSignup(signupForm),
                isLoading: isAuthLoading || signupForm.isLoading,
              ),
              SizedBox(height: AppSizing.spaceL),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Already have an account?',
                      style: Theme.of(context).textTheme.bodySmall),
                  TextButton(
                    onPressed: () => context.goNamed(RouteNames.login),
                    child: Text('Login',
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(color: theme.colorScheme.primary)),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }

  bool _isFormValid(SignupFormState form) {
    return form.email.isNotEmpty &&
        form.password.isNotEmpty &&
        form.confirmPassword.isNotEmpty &&
        form.firstName.isNotEmpty &&
        form.lastName.isNotEmpty &&
        form.password == form.confirmPassword &&
        form.acceptedTerms &&
        _validateEmail(form.email) == null;
  }

  Future<void> _handleSignup(SignupFormState formState) async {
    if (!_isFormValid(formState)) {
      showFailureToast(
        context,
        title: 'Validation Error',
        description: 'Please fill in all required fields correctly.',
      );
      return;
    }

    ref.read(signupFormProvider.notifier).setLoading(true);

    try {
      await ref.read(authControllerProvider.notifier).signUp(
            email: formState.email,
            password: formState.password,
            firstName: formState.firstName,
            lastName: formState.lastName,
          );
    } catch (e) {
      if (mounted) {
        // Show error toast with user-friendly message
        showAuthErrorToast(context, e);
        ref.read(signupFormProvider.notifier).setError(getAuthErrorMessage(e));
      }
    } finally {
      ref.read(signupFormProvider.notifier).setLoading(false);
    }
  }
}
