// lib/src/features/auth/presentation/screens/forgot_password_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_button.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_text_field.dart';
import 'package:mimi_app/src/features/auth/providers/auth_controller.dart';
import 'package:mimi_app/src/features/auth/providers/forgot_password_provider.dart';

class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  ConsumerState<ForgotPasswordScreen> createState() =>
      _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(forgotPasswordFormProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Reset Password'),
      ),
      body: SafeArea(
        child: Padding(
          padding: AppSizing.screenEdgeInsets,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: AppSizing.spaceL),

              // Success message
              if (state.isSuccess)
                Container(
                  padding: EdgeInsets.all(AppSizing.spaceM),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(AppSizing.radiusM),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        color: theme.colorScheme.primary,
                        size: 48,
                      ),
                      SizedBox(height: AppSizing.spaceM),
                      Text(
                        'Password reset link sent!',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      SizedBox(height: AppSizing.spaceS),
                      Text(
                        'Check your email for instructions to reset your password.',
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyMedium,
                      ),
                      SizedBox(height: AppSizing.spaceM),
                      AuthButton(
                        label: 'Back to Login',
                        onPressed: () => context.goNamed(RouteNames.login),
                        isOutlined: true,
                      ),
                    ],
                  ),
                )
              else
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Enter your email address and we\'ll send you instructions to reset your password.',
                      style: theme.textTheme.bodyLarge,
                    ),

                    SizedBox(height: AppSizing.spaceXL),

                    // Email field
                    AuthTextField(
                      label: 'Email',
                      onChanged: ref
                          .read(forgotPasswordFormProvider.notifier)
                          .setEmail,
                      keyboardType: TextInputType.emailAddress,
                      enabled: !state.isLoading,
                      validator: _validateEmail,
                    ),

                    SizedBox(height: AppSizing.spaceM),

                    // Error message
                    if (state.error != null)
                      Padding(
                        padding: EdgeInsets.only(bottom: AppSizing.spaceM),
                        child: Text(
                          state.error!,
                          style: TextStyle(
                            color: theme.colorScheme.error,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                    // Submit button
                    AuthButton(
                      label: 'Send Reset Link',
                      onPressed: state.isLoading || !_isEmailValid(state.email)
                          ? null
                          : () => _handleResetPassword(),
                      isLoading: state.isLoading,
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Enter a valid email address';
    }
    return null;
  }

  bool _isEmailValid(String email) {
    return _validateEmail(email) == null;
  }

  Future<void> _handleResetPassword() async {
    final email = ref.read(forgotPasswordFormProvider).email;
    final notifier = ref.read(forgotPasswordFormProvider.notifier);

    notifier.setLoading(true);
    notifier.setError(null);

    try {
      await ref.read(authControllerProvider.notifier).resetPassword(email);
      if (mounted) {
        notifier.setSuccess(true);
        showAuthSuccessToast(
            context, 'Password reset link sent to your email!');
      }
    } catch (e) {
      // Silently handle error and show success message anyway
      if (mounted) {
        notifier.setSuccess(true);
        showAuthSuccessToast(
            context, 'Password reset link sent to your email!');
      }
    } finally {
      if (mounted) {
        notifier.setLoading(false);
      }
    }
  }
}
