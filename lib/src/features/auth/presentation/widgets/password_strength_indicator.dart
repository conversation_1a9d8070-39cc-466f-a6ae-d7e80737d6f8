// lib/src/features/auth/presentation/widgets/password_strength_indicator.dart
import 'package:flutter/material.dart';

class PasswordStrengthIndicator extends StatelessWidget {
  final String password;

  const PasswordStrengthIndicator({
    super.key,
    required this.password,
  });

  @override
  Widget build(BuildContext context) {
    final strength = _calculatePasswordStrength(password);
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LinearProgressIndicator(
          value: strength,
          backgroundColor: theme.colorScheme.surfaceContainerHighest,
          color: _getColorForStrength(strength, theme),
        ),
        const SizedBox(height: 4),
        Text(
          _getStrengthText(strength),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.outline,
          ),
        ),
      ],
    );
  }

  double _calculatePasswordStrength(String password) {
    int score = 0;
    if (password.isEmpty) return 0.0;

    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // Character type checks
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;

    return score / 6;
  }

  Color _getColorForStrength(double strength, ThemeData theme) {
    if (strength < 0.3) return theme.colorScheme.error;
    if (strength < 0.7) return theme.colorScheme.primary.withValues(alpha: 0.7);
    return theme.colorScheme.primary;
  }

  String _getStrengthText(double strength) {
    if (strength < 0.3) return 'Weak password';
    if (strength < 0.7) return 'Moderate password';
    return 'Strong password';
  }
}
