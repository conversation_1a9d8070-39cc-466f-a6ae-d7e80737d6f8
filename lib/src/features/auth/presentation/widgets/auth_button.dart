// lib/src/features/auth/presentation/widgets/auth_button.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/constants/sizing_constants.dart';

class AuthButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final IconData? icon;

  const AuthButton({
    super.key,
    required this.label,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    final buttonStyle = isOutlined
        ? OutlinedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: AppSizing.spaceM),
            side: BorderSide(color: colorScheme.primary),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSizing.radiusM),
            ),
          )
        : ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: AppSizing.spaceM),
            backgroundColor: colorScheme.primary,
            foregroundColor: colorScheme.onPrimary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSizing.radiusM),
            ),
          );

    final child = isLoading
        ? SizedBox(
            height: AppSizing.iconM,
            width: AppSizing.iconM,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: isOutlined ? colorScheme.primary : colorScheme.onPrimary,
            ),
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null) ...[
                Icon(icon),
                SizedBox(width: AppSizing.spaceS),
              ],
              Text(label,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge
                      ?.copyWith(color: Colors.white)),
            ],
          );

    return SizedBox(
      width: double.infinity,
      child: isOutlined
          ? OutlinedButton(
              onPressed: isLoading ? null : onPressed,
              style: buttonStyle,
              child: child,
            )
          : ElevatedButton(
              onPressed: isLoading ? null : onPressed,
              style: buttonStyle,
              child: child,
            ),
    );
  }
}
