// lib/src/features/auth/presentation/widgets/auth_form.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_button.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_text_field.dart';
import 'package:mimi_app/src/features/auth/providers/auth_controller.dart';
import 'package:mimi_app/src/features/auth/providers/login_form_provider.dart';
import 'package:mimi_app/src/features/auth/providers/signup_form_provider.dart';

class AuthForm extends ConsumerStatefulWidget {
  final bool isSignUp;
  final VoidCallback? onSuccess;

  const AuthForm({
    super.key,
    required this.isSignUp,
    this.onSuccess,
  });

  @override
  ConsumerState<AuthForm> createState() => _AuthFormState();
}

class _AuthFormState extends ConsumerState<AuthForm> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final isAuthLoading = ref.watch(authControllerProvider).isLoading;

    if (widget.isSignUp) {
      return _buildSignUpForm(isAuthLoading);
    } else {
      return _buildSignInForm(isAuthLoading);
    }
  }

  Widget _buildSignUpForm(bool isAuthLoading) {
    final signupForm = ref.watch(signupFormProvider);

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AuthTextField(
            label: 'First Name',
            onChanged: ref.read(signupFormProvider.notifier).setFirstName,
            enabled: !signupForm.isLoading,
            validator: _validateName,
          ),
          const SizedBox(height: AppSizing.spaceM),
          AuthTextField(
            label: 'Last Name',
            onChanged: ref.read(signupFormProvider.notifier).setLastName,
            enabled: !signupForm.isLoading,
            validator: _validateName,
          ),
          const SizedBox(height: AppSizing.spaceM),
          AuthTextField(
            label: 'Email',
            keyboardType: TextInputType.emailAddress,
            onChanged: ref.read(signupFormProvider.notifier).setEmail,
            enabled: !signupForm.isLoading,
            validator: _validateEmail,
          ),
          const SizedBox(height: AppSizing.spaceM),
          AuthTextField(
            label: 'Password',
            obscureText: true,
            onChanged: ref.read(signupFormProvider.notifier).setPassword,
            enabled: !signupForm.isLoading,
            validator: _validatePassword,
          ),
          const SizedBox(height: AppSizing.spaceM),
          AuthTextField(
            label: 'Confirm Password',
            obscureText: true,
            onChanged: ref.read(signupFormProvider.notifier).setConfirmPassword,
            enabled: !signupForm.isLoading,
            validator: (value) =>
                _validateConfirmPassword(value, signupForm.password),
          ),
          const SizedBox(height: AppSizing.spaceL),
          if (signupForm.error != null)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSizing.spaceM),
              child: Text(
                signupForm.error!,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          AuthButton(
            label: 'Create Account',
            onPressed: (isAuthLoading ||
                    signupForm.isLoading ||
                    !_isSignUpFormValid(signupForm))
                ? null
                : () => _handleSignUp(signupForm),
            isLoading: isAuthLoading || signupForm.isLoading,
          ),
        ],
      ),
    );
  }

  Widget _buildSignInForm(bool isAuthLoading) {
    final loginForm = ref.watch(loginFormProvider);

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AuthTextField(
            label: 'Email',
            keyboardType: TextInputType.emailAddress,
            onChanged: ref.read(loginFormProvider.notifier).setEmail,
            enabled: !loginForm.isLoading,
            validator: _validateEmail,
          ),
          const SizedBox(height: AppSizing.spaceM),
          AuthTextField(
            label: 'Password',
            obscureText: true,
            onChanged: ref.read(loginFormProvider.notifier).setPassword,
            enabled: !loginForm.isLoading,
            validator: (value) =>
                value?.isEmpty == true ? 'Password is required' : null,
          ),
          const SizedBox(height: AppSizing.spaceL),
          if (loginForm.error != null)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSizing.spaceM),
              child: Text(
                loginForm.error!,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          AuthButton(
            label: 'Sign In',
            onPressed: (isAuthLoading ||
                    loginForm.isLoading ||
                    !_isSignInFormValid(loginForm))
                ? null
                : () => _handleSignIn(loginForm),
            isLoading: isAuthLoading || loginForm.isLoading,
          ),
        ],
      ),
    );
  }

  // Validation methods
  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }
    if (value.trim().length < 2) {
      return 'Must be at least 2 characters';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }

  bool _isSignUpFormValid(SignupFormState formState) {
    return formState.firstName.trim().isNotEmpty &&
        formState.lastName.trim().isNotEmpty &&
        formState.email.trim().isNotEmpty &&
        formState.password.isNotEmpty &&
        formState.confirmPassword.isNotEmpty &&
        formState.password == formState.confirmPassword &&
        _validateEmail(formState.email) == null &&
        _validatePassword(formState.password) == null;
  }

  bool _isSignInFormValid(LoginFormState formState) {
    return formState.email.trim().isNotEmpty &&
        formState.password.isNotEmpty &&
        _validateEmail(formState.email) == null;
  }

  Future<void> _handleSignUp(SignupFormState formState) async {
    if (!_formKey.currentState!.validate()) return;
    if (!mounted) return;

    ref.read(signupFormProvider.notifier).setLoading(true);

    try {
      await ref.read(authControllerProvider.notifier).signUp(
            email: formState.email,
            password: formState.password,
            firstName: formState.firstName,
            lastName: formState.lastName,
          );

      if (mounted) {
        widget.onSuccess?.call();
      }
    } catch (e) {
      if (mounted) {
        // Show error toast with user-friendly message
        showAuthErrorToast(context, e);
        ref.read(signupFormProvider.notifier).setError(getAuthErrorMessage(e));
      }
    } finally {
      if (mounted) {
        ref.read(signupFormProvider.notifier).setLoading(false);
      }
    }
  }

  Future<void> _handleSignIn(LoginFormState formState) async {
    if (!_formKey.currentState!.validate()) return;
    if (!mounted) return;

    ref.read(loginFormProvider.notifier).setLoading(true);
    ref.read(loginFormProvider.notifier).setError(null);

    try {
      await ref
          .read(authControllerProvider.notifier)
          .signInWithEmailAndPassword(
            email: formState.email,
            password: formState.password,
          );

      if (mounted) {
        widget.onSuccess?.call();
      }
    } catch (e) {
      if (mounted) {
        // Show error toast with user-friendly message
        showAuthErrorToast(context, e);
        ref.read(loginFormProvider.notifier).setError(getAuthErrorMessage(e));
      }
    } finally {
      if (mounted) {
        ref.read(loginFormProvider.notifier).setLoading(false);
      }
    }
  }
}
