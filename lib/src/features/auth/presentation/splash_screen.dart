// lib/src/features/auth/presentation/screens/splash_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/features/auth/providers/auth_state_provider.dart';
import 'package:mimi_app/src/features/auth/model/auth_state.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  bool _hasNavigated = false;
  bool _splashDelayCompleted = false;

  @override
  void initState() {
    super.initState();
    _startSplashDelay();
  }

  Future<void> _startSplashDelay() async {
    // Add minimum delay for splash screen
    await Future.delayed(const Duration(seconds: 2));
    if (mounted) {
      setState(() {
        _splashDelayCompleted = true;
      });
    }
  }

  void _handleAuthState(AuthState authState) {
    if (!mounted || _hasNavigated || !_splashDelayCompleted) return;

    authState.when(
      initial: () {
        // Still initializing, wait for a definitive state
      },
      loading: () {
        // Still loading, wait for a definitive state
      },
      authenticated: (_) {
        _navigateToHome();
      },
      unauthenticated: () {
        _navigateToOnboarding();
      },
      error: (_) {
        _navigateToOnboarding();
      },
    );
  }

  void _navigateToHome() {
    if (mounted && !_hasNavigated) {
      _hasNavigated = true;
      context.goNamed(RouteNames.home);
    }
  }

  void _navigateToOnboarding() {
    if (mounted && !_hasNavigated) {
      _hasNavigated = true;
      context.goNamed(RouteNames.onboarding);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Listen to auth state changes and handle navigation
    ref.listen<AuthState>(authStateNotifierProvider, (previous, next) {
      _handleAuthState(next);
    });

    // Also check current state if splash delay is completed
    // Use addPostFrameCallback to avoid calling navigation during build
    if (_splashDelayCompleted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currentState = ref.read(authStateNotifierProvider);
        _handleAuthState(currentState);
      });
    }

    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo
            Image.asset('assets/images/logo.png', height: 150),

            SizedBox(height: AppSizing.spaceL),

            // Loading indicator
            CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),

            SizedBox(height: AppSizing.spaceL),

            // Loading text
            Text(
              'Loading...',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
