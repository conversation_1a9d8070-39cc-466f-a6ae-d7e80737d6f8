// lib/src/features/auth/presentation/screens/login_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/config/app_config.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_button.dart';
import 'package:mimi_app/src/features/auth/presentation/widgets/auth_text_field.dart';
// import 'package:mimi_app/src/features/auth/presentation/widgets/social_auth_buttons.dart';
import 'package:mimi_app/src/features/auth/providers/auth_controller.dart';
import 'package:mimi_app/src/features/auth/providers/auth_state_provider.dart';
import 'package:mimi_app/src/features/auth/providers/login_form_provider.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  @override
  Widget build(BuildContext context) {
    final loginForm = ref.watch(loginFormProvider);

    ref.listen(authStateNotifierProvider, (previous, next) {
      next.whenOrNull(
        authenticated: (_) {
          if (mounted) {
            showAuthSuccessToast(context, 'Welcome back!');
            context.goNamed(RouteNames.home);
          }
        },
        error: (message) {
          if (mounted) {
            showAuthErrorToast(context, message);
          }
        },
      );
    });

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppSizing.screenEdgeInsets,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: AppSizing.spaceXL),

              // Logo or branding
              Image.asset('assets/images/logo.png', height: 150),

              SizedBox(height: AppSizing.spaceXL),

              // Email field
              AuthTextField(
                label: 'Email',
                onChanged: ref.read(loginFormProvider.notifier).setEmail,
                keyboardType: TextInputType.emailAddress,
                enabled: !loginForm.isLoading,
              ),

              SizedBox(height: AppSizing.spaceM),

              // Password field
              AuthTextField(
                label: 'Password',
                onChanged: ref.read(loginFormProvider.notifier).setPassword,
                obscureText: true,
                enabled: !loginForm.isLoading,
              ),

              SizedBox(height: AppSizing.spaceS),

              // Forgot password link
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => context.pushNamed(RouteNames.forgotPassword),
                  child: Text(
                    'Forgot Password?',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),
                ),
              ),

              SizedBox(height: AppSizing.spaceM),

              // Login button
              AuthButton(
                onPressed: loginForm.isLoading ? null : () => _handleLogin(),
                label: 'Login',
                isLoading: loginForm.isLoading,
              ),

              SizedBox(height: AppSizing.spaceL),

              // Social auth divider
              if (AppConfig.enableGoogleSignIn || AppConfig.enableAppleSignIn)
                const Row(
                  children: [
                    Expanded(child: Divider()),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text('OR'),
                    ),
                    Expanded(child: Divider()),
                  ],
                ),

              SizedBox(height: AppSizing.spaceL),

              // Social auth buttons
              // const SocialAuthButtons(),

              SizedBox(height: AppSizing.spaceXL),

              // Sign up link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("Don't have an account?",
                      style: Theme.of(context).textTheme.bodySmall),
                  TextButton(
                    onPressed: () => context.pushNamed(RouteNames.signup),
                    child: Text(
                      'Sign Up',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    print('🔐 LOGIN_SCREEN_DEBUG: _handleLogin called');

    final formState = ref.read(loginFormProvider);
    final controller = ref.read(authControllerProvider.notifier);

    print('🔐 LOGIN_SCREEN_DEBUG: Email: ${formState.email}');
    print(
        '🔐 LOGIN_SCREEN_DEBUG: Password length: ${formState.password.length}');

    // Basic validation
    if (formState.email.isEmpty || formState.password.isEmpty) {
      print(
          '🔐 LOGIN_SCREEN_DEBUG: Validation failed - empty email or password');
      showFailureToast(
        context,
        title: 'Validation Error',
        description: 'Please enter both email and password.',
      );
      return;
    }

    print('🔐 LOGIN_SCREEN_DEBUG: Setting form loading to true');
    ref.read(loginFormProvider.notifier).setLoading(true);
    ref.read(loginFormProvider.notifier).setError(null);

    try {
      print(
          '🔐 LOGIN_SCREEN_DEBUG: Calling controller.signInWithEmailAndPassword');
      await controller.signInWithEmailAndPassword(
        email: formState.email,
        password: formState.password,
      );
      print(
          '🔐 LOGIN_SCREEN_DEBUG: controller.signInWithEmailAndPassword completed successfully');
    } catch (e, stackTrace) {
      print('🔐 LOGIN_SCREEN_DEBUG: Error in _handleLogin: $e');
      print('🔐 LOGIN_SCREEN_DEBUG: Stack trace: $stackTrace');

      // Show error toast with user-friendly message
      if (mounted) {
        showAuthErrorToast(context, e);
        ref.read(loginFormProvider.notifier).setError(getAuthErrorMessage(e));
      }
    } finally {
      print('🔐 LOGIN_SCREEN_DEBUG: Setting form loading to false');
      if (mounted) {
        ref.read(loginFormProvider.notifier).setLoading(false);
      }
    }
  }
}
