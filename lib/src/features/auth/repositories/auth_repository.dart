// lib/features/authentication/data/repositories/auth_repository.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mimi_app/src/core/config/app_config.dart';
import 'package:mimi_app/src/features/auth/model/auth_user.dart';
import 'package:mimi_app/src/features/auth/repositories/i_auth_repository.dart';
import 'package:mimi_app/src/features/auth/service/firebase_auth_service.dart';
import 'package:mimi_app/src/features/user/models/user.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_repository.g.dart';

@Riverpod(keepAlive: true)
class AuthRepository extends _$AuthRepository implements IAuthRepository {
  late final FirebaseAuthService _authService;

  @override
  Stream<AuthUser?> build() {
    _authService = ref.watch(firebaseAuthServiceProvider);
    return _authService.authStateChanges;
  }

  @override
  Stream<AuthUser?> get authStateChanges => _authService.authStateChanges;

  @override
  Future<AuthUser?> getCurrentUser() async {
    return _authService.getCurrentUser();
  }

  @override
  Future<AuthUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    print(
        '🔐 AUTH_REPO_DEBUG: signInWithEmailAndPassword called for email: $email');
    print('🔐 AUTH_REPO_DEBUG: Current state: ${state.runtimeType}');

    if (!AppConfig.enableEmailSignIn) {
      throw UnimplementedError('Email sign in is disabled');
    }

    print('🔐 AUTH_REPO_DEBUG: Calling Firebase auth service');
    final user = await _authService.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
    print(
        '🔐 AUTH_REPO_DEBUG: Firebase auth service returned user: ${user?.uid}');

    print('🔐 AUTH_REPO_DEBUG: Setting repository state to AsyncValue.data');
    state = AsyncValue.data(user);
    print('🔐 AUTH_REPO_DEBUG: Repository state updated');

    return user;
  }

  @override
  Future<AuthUser?> signInWithGoogle() async {
    if (!AppConfig.enableGoogleSignIn) {
      throw UnimplementedError('Google sign in is disabled');
    }
    final user = await _authService.signInWithGoogle();
    state = AsyncValue.data(user);
    return user;
  }

  @override
  Future<AuthUser?> signInAnonymously() async {
    if (!AppConfig.enableAnonymousSignIn) {
      throw UnimplementedError('Anonymous sign in is disabled');
    }
    final user = await _authService.signInAnonymously();
    state = AsyncValue.data(user);
    return user;
  }

  @override
  Future<AuthUser?> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    print(
        '🔐 AUTH_REPO_DEBUG: createUserWithEmailAndPassword called for email: $email');
    print('🔐 AUTH_REPO_DEBUG: Current state: ${state.runtimeType}');

    if (!AppConfig.enableEmailSignIn) {
      throw UnimplementedError('Email sign up is disabled');
    }

    print('🔐 AUTH_REPO_DEBUG: Calling Firebase auth service createUser');
    final user = await _authService.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
    print(
        '🔐 AUTH_REPO_DEBUG: Firebase auth service created user: ${user?.uid}');

    print('🔐 AUTH_REPO_DEBUG: Setting repository state to AsyncValue.data');
    state = AsyncValue.data(user);
    print('🔐 AUTH_REPO_DEBUG: Repository state updated');

    return user;
  }

  @override
  Future<void> signOut() async {
    await _authService.signOut();
    state = const AsyncValue.data(null);
  }

  @override
  Future<void> resetPassword(String email) async {
    if (!AppConfig.enablePasswordReset) {
      throw UnimplementedError('Password reset is disabled');
    }
    await _authService.resetPassword(email);
  }

  @override
  Future<void> reauthenticateWithPassword(String password) async {
    await _authService.reauthenticateWithPassword(password);
  }

  @override
  Future<void> updateEmail(String newEmail) async {
    await _authService.updateEmail(newEmail);
  }

  @override
  Future<void> updatePassword(String newPassword) async {
    await _authService.updatePassword(newPassword);
  }

  Future<void> createUserProfile({
    required String uid,
    required String firstName,
    required String lastName,
    required String email,
  }) async {
    final firestore = FirebaseFirestore.instance;
    final user = User(
      auth: AuthUser(uid: uid, email: email),
      firstName: firstName,
      lastName: lastName,
    );

    // Assuming you're using Firestore
    await firestore.collection('users').doc(uid).set(user.toJson());
  }
}
