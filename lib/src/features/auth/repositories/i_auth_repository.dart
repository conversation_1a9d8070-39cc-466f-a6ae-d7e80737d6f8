// lib/features/authentication/domain/repositories/i_auth_repository.dart

import 'package:mimi_app/src/features/auth/model/auth_user.dart';

abstract class IAuthRepository {
  Stream<AuthUser?> get authStateChanges;

  Future<AuthUser?> getCurrentUser();

  Future<AuthUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<AuthUser?> signInWithGoogle();

  Future<AuthUser?> signInAnonymously();

  Future<AuthUser?> createUserWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<void> signOut();

  Future<void> resetPassword(String email);
  Future<void> reauthenticateWithPassword(String password);
  Future<void> updateEmail(String newEmail);
  Future<void> updatePassword(String newPassword);
}
