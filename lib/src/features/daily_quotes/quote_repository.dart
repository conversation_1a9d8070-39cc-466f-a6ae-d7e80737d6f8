import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mimi_app/src/features/daily_quotes/quote_model.dart';

class QuoteRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _userUid;

  QuoteRepository(this._userUid);

  Stream<List<Quote>> watchQuotes() {
    return _firestore
        .collection('quotes')
        .snapshots()
        .asyncMap((quotesSnapshot) async {
      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(_userUid)
          .collection('favorites')
          .get();

      final favoriteIds = favoritesSnapshot.docs.map((doc) => doc.id).toSet();

      return quotesSnapshot.docs.map((doc) {
        return Quote.fromFirestore(
          doc,
          isFavorite: favoriteIds.contains(doc.id),
        );
      }).toList();
    });
  }

  Future<void> toggleFavorite(Quote quote) async {
    final favoriteRef = _firestore
        .collection('users')
        .doc(_userUid)
        .collection('favorites')
        .doc(quote.id);

    if (quote.isFavorite) {
      await favoriteRef.delete();
    } else {
      await favoriteRef.set({
        'timestamp': FieldValue.serverTimestamp(),
      });
    }
  }

  Stream<List<Quote>> watchFavorites() {
    return _firestore
        .collection('users')
        .doc(_userUid)
        .collection('favorites')
        .snapshots()
        .asyncMap((favoritesSnapshot) async {
      final quoteIds = favoritesSnapshot.docs.map((doc) => doc.id).toList();

      if (quoteIds.isEmpty) return [];

      final quotesSnapshot = await _firestore
          .collection('quotes')
          .where(FieldPath.documentId, whereIn: quoteIds)
          .get();

      return quotesSnapshot.docs
          .map((doc) => Quote.fromFirestore(doc, isFavorite: true))
          .toList();
    });
  }
}
