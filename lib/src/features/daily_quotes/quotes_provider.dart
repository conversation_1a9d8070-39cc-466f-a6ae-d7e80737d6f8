import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/daily_quotes/quote_model.dart';
import 'package:mimi_app/src/features/daily_quotes/quote_repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../admin/providers/firebase_background_provider.dart';

final quoteRepositoryProvider = Provider<QuoteRepository>((ref) {
  final auth = FirebaseAuth.instance;
  return QuoteRepository(auth.currentUser!.uid);
});

final quotesProvider = StreamProvider<List<Quote>>((ref) {
  final repository = ref.watch(quoteRepositoryProvider);
  return repository.watchQuotes();
});

final favoritesProvider = StreamProvider<List<Quote>>((ref) {
  final repository = ref.watch(quoteRepositoryProvider);
  return repository.watchFavorites();
});

final currentQuoteIndexProvider = StateProvider<int>((ref) => 0);

enum QuoteFilterType { general, favorites }

final quoteFilterTypeProvider =
    StateProvider<QuoteFilterType>((ref) => QuoteFilterType.general);
final quoteCategoryFilterProvider = StateProvider<String?>((ref) => null);

final quoteBackgroundsProvider =
    FutureProvider<List<Map<String, String>>>((ref) async {
  try {
    // Try to get Firebase background images first
    final firebaseImages =
        await ref.watch(firebaseBackgroundImagesProvider.future);

    if (firebaseImages.isNotEmpty) {
      // Convert Firebase images to the expected format
      return firebaseImages
          .map((image) => {
                'id': image.id,
                'name': image.name,
                'assetPath': image
                    .imageUrl, // Use imageUrl instead of assetPath for Firebase images
              })
          .toList();
    }
  } catch (e) {
    // If Firebase fails, fall back to local assets
  }

  // Fallback to local assets
  return [
    {
      'id': 'ocean',
      'name': 'Ocean Waves',
      'assetPath': 'assets/images/breathwork/ocean.jpeg',
    },
    {
      'id': 'forest',
      'name': 'Forest',
      'assetPath': 'assets/images/breathwork/forest.jpeg',
    },
    {
      'id': 'mountain',
      'name': 'Mountain View',
      'assetPath': 'assets/images/breathwork/mountain.jpeg',
    },
    {
      'id': 'space',
      'name': 'Space',
      'assetPath': 'assets/images/breathwork/space.jpeg',
    },
  ];
});

final quoteBackgroundIdProvider =
    StateNotifierProvider<QuoteBackgroundIdNotifier, String?>(
        (ref) => QuoteBackgroundIdNotifier());

class QuoteBackgroundIdNotifier extends StateNotifier<String?> {
  static const _prefsKey = 'quote_background_image';
  QuoteBackgroundIdNotifier() : super(null) {
    _load();
  }
  Future<void> _load() async {
    final prefs = await SharedPreferences.getInstance();
    state = prefs.getString(_prefsKey);
  }

  Future<void> set(String? id) async {
    final prefs = await SharedPreferences.getInstance();
    if (id == null) {
      await prefs.remove(_prefsKey);
    } else {
      await prefs.setString(_prefsKey, id);
    }
    state = id;
  }
}
