import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/features/daily_quotes/quotes_provider.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';

class QuoteSettingsScreen extends ConsumerStatefulWidget {
  const QuoteSettingsScreen({super.key});

  @override
  ConsumerState<QuoteSettingsScreen> createState() =>
      _QuoteSettingsScreenState();
}

class _QuoteSettingsScreenState extends ConsumerState<QuoteSettingsScreen> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return GradientScaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.close, color: AppColors.textPrimary),
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        children: [
          // Custom segmented control
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _selectedTabIndex = 0),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: _selectedTabIndex == 0
                            ? AppColors.primary
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        'categories',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _selectedTabIndex == 0
                              ? AppColors.textOnPrimary
                              : AppColors.textSecondary,
                          fontWeight: AppTypography.medium,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _selectedTabIndex = 1),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: _selectedTabIndex == 1
                            ? AppColors.primary
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        'themes',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _selectedTabIndex == 1
                              ? AppColors.textOnPrimary
                              : AppColors.textSecondary,
                          fontWeight: AppTypography.medium,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: _selectedTabIndex == 0
                ? const _CategoriesTab()
                : const _BackgroundTab(),
          ),
        ],
      ),
    );
  }
}

class _CategoriesTab extends ConsumerWidget {
  const _CategoriesTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final quotesAsync = ref.watch(quotesProvider);
    final categoryFilter = ref.watch(quoteCategoryFilterProvider);
    final filterType = ref.watch(quoteFilterTypeProvider);

    return quotesAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Error: $err')),
      data: (quotes) {
        final categories = quotes
            .map((q) => q.category)
            .where((c) => c.isNotEmpty)
            .toSet()
            .toList();

        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              // Favorites category - full width
              _CategoryCard(
                title: 'Favorites',
                selected: filterType == QuoteFilterType.favorites,
                onTap: () {
                  if (filterType == QuoteFilterType.favorites) {
                    // If already selected, deselect
                    ref.read(quoteFilterTypeProvider.notifier).state =
                        QuoteFilterType.general;
                  } else {
                    // Select favorites
                    ref.read(quoteFilterTypeProvider.notifier).state =
                        QuoteFilterType.favorites;
                  }
                  // Clear category filter when switching to/from favorites
                  ref.read(quoteCategoryFilterProvider.notifier).state = null;
                },
                isFullWidth: true,
              ),
              const SizedBox(height: 16),
              // Categories grid - 2 per row
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.5,
                ),
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  return _CategoryCard(
                    title: category,
                    selected: categoryFilter == category &&
                        filterType == QuoteFilterType.general,
                    onTap: () {
                      // Ensure we're in general mode when selecting a category
                      ref.read(quoteFilterTypeProvider.notifier).state =
                          QuoteFilterType.general;
                      ref.read(quoteCategoryFilterProvider.notifier).state =
                          categoryFilter == category ? null : category;
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class _CategoryCard extends StatelessWidget {
  final String title;
  final bool selected;
  final VoidCallback onTap;
  final bool isFullWidth;

  const _CategoryCard({
    required this.title,
    required this.selected,
    required this.onTap,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: isFullWidth
            ? const EdgeInsets.symmetric(vertical: 4)
            : const EdgeInsets.all(8),
        height: isFullWidth ? 60 : null,
        decoration: BoxDecoration(
          color: selected ? AppColors.primary : AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.border.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            Center(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: isFullWidth
                      ? AppTypography.bodyMedium
                      : AppTypography.bodySmall,
                  fontWeight: AppTypography.medium,
                  color: selected
                      ? AppColors.textOnPrimary
                      : AppColors.textPrimary,
                ),
              ),
            ),
            if (selected)
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    Icons.check_circle,
                    color: AppColors.textOnPrimary,
                    size: AppTypography.bodyLarge,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _BackgroundTab extends ConsumerWidget {
  const _BackgroundTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final backgroundsAsync = ref.watch(quoteBackgroundsProvider);
    final selectedId = ref.watch(quoteBackgroundIdProvider);

    return backgroundsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
      data: (backgrounds) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Random option - full width
              _BackgroundCard(
                id: 'random',
                title: 'Random',
                assetPath: null, // No specific image for random
                selected: selectedId == 'random',
                onTap: () {
                  ref.read(quoteBackgroundIdProvider.notifier).set('random');
                },
                isFullWidth: true,
              ),
              const SizedBox(height: 16),
              // Background themes grid - 3 per row
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 0.6,
                ),
                itemCount: backgrounds.length,
                itemBuilder: (context, index) {
                  final bg = backgrounds[index];
                  return _BackgroundCard(
                    id: bg['id']!,
                    title: bg['name']!,
                    assetPath: bg['assetPath']!,
                    selected: bg['id'] == selectedId,
                    onTap: () {
                      ref
                          .read(quoteBackgroundIdProvider.notifier)
                          .set(bg['id']);
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class _BackgroundCard extends StatelessWidget {
  final String id;
  final String title;
  final String? assetPath;
  final bool selected;
  final VoidCallback onTap;
  final bool isFullWidth;

  const _BackgroundCard({
    required this.id,
    required this.title,
    this.assetPath,
    required this.selected,
    required this.onTap,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: isFullWidth
            ? const EdgeInsets.symmetric(vertical: 4)
            : EdgeInsets.zero,
        height: isFullWidth ? 60 : null,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: selected ? AppColors.primary : Colors.transparent,
            width: 3,
          ),
          image: assetPath != null
              ? DecorationImage(
                  image: assetPath!.startsWith('http')
                      ? NetworkImage(assetPath!) as ImageProvider
                      : AssetImage(assetPath!),
                  fit: BoxFit.cover,
                )
              : null,
          color: assetPath == null ? AppColors.surface : null,
        ),
        child: Stack(
          children: [
            if (assetPath == null)
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shuffle,
                      color: selected
                          ? AppColors.textOnPrimary
                          : AppColors.textPrimary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: AppTypography.bodyMedium,
                        fontWeight: AppTypography.medium,
                        color: selected
                            ? AppColors.textOnPrimary
                            : AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            if (selected)
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    Icons.check_circle,
                    color: AppColors.textOnPrimary,
                    size: AppTypography.headlineSmall,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
