import 'package:cloud_firestore/cloud_firestore.dart';

class Quote {
  final String id;
  final String text;
  final String author;
  final String category;
  final bool isFavorite;

  Quote({
    required this.id,
    required this.text,
    required this.author,
    required this.category,
    this.isFavorite = false,
  });

  factory Quote.fromFirestore(DocumentSnapshot doc, {bool isFavorite = false}) {
    final data = doc.data() as Map<String, dynamic>;
    return Quote(
      id: doc.id,
      text: data['text'] ?? '',
      author: data['author'] ?? '',
      category: data['category'] ?? '',
      isFavorite: isFavorite,
    );
  }
}
