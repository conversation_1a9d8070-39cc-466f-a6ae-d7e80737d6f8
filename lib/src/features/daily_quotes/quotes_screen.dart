import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/daily_quotes/quotes_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui';

class QuoteScreen extends ConsumerStatefulWidget {
  const QuoteScreen({super.key});

  @override
  ConsumerState<QuoteScreen> createState() => _QuoteScreenState();
}

class _QuoteScreenState extends ConsumerState<QuoteScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _animateQuoteTransition() {
    _controller.reset();
    _controller.forward();
  }

  void _openSettings() async {
    await context.push('/quotes/settings');
    setState(() {}); // Refresh after settings
  }

  @override
  Widget build(BuildContext context) {
    final filterType = ref.watch(quoteFilterTypeProvider);
    final categoryFilter = ref.watch(quoteCategoryFilterProvider);
    final backgroundId = ref.watch(quoteBackgroundIdProvider);
    final backgroundsAsync = ref.watch(quoteBackgroundsProvider);
    final quotesAsync = filterType == QuoteFilterType.favorites
        ? ref.watch(favoritesProvider)
        : ref.watch(quotesProvider);
    final currentIndex = ref.watch(currentQuoteIndexProvider);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: context.pop,
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _openSettings,
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: backgroundsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) =>
            Center(child: Text('Error loading backgrounds: $err')),
        data: (backgrounds) {
          final background = backgrounds.firstWhere(
            (bg) => bg['id'] == backgroundId,
            orElse: () => backgrounds.first,
          );

          return quotesAsync.when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (err, stack) => Center(child: Text('Error: $err')),
            data: (quotes) {
              // Filter by category if set
              final filteredQuotes = categoryFilter == null
                  ? quotes
                  : quotes.where((q) => q.category == categoryFilter).toList();
              if (filteredQuotes.isEmpty) {
                return const Center(child: Text('No quotes available'));
              }
              // Reset to first quote if current index is out of range.
              final safeIndex =
                  currentIndex >= filteredQuotes.length ? 0 : currentIndex;
              final currentQuote = filteredQuotes[safeIndex];

              return Stack(
                fit: StackFit.expand,
                children: [
                  // Background image with overlay
                  background['assetPath']!.startsWith('http')
                      ? Image.network(
                          background['assetPath']!,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Colors.grey[800],
                            child: const Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 50,
                            ),
                          ),
                        )
                      : Image.asset(
                          background['assetPath']!,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Colors.grey[800],
                            child: const Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 50,
                            ),
                          ),
                        ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.3),
                          Colors.black.withOpacity(0.4),
                        ],
                      ),
                    ),
                  ),
                  // Main content
                  SafeArea(
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onVerticalDragEnd: (details) {
                        if (details.primaryVelocity != null) {
                          if (details.primaryVelocity! < 0) {
                            if (safeIndex < filteredQuotes.length - 1) {
                              ref
                                  .read(currentQuoteIndexProvider.notifier)
                                  .state++;
                              _animateQuoteTransition();
                            }
                          } else if (details.primaryVelocity! > 0) {
                            if (safeIndex > 0) {
                              ref
                                  .read(currentQuoteIndexProvider.notifier)
                                  .state--;
                              _animateQuoteTransition();
                            }
                          }
                        }
                      },
                      child: Center(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 30),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (currentQuote.category.isNotEmpty) ...[
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.18),
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Text(
                                        currentQuote.category,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                  Text(
                                    currentQuote.text,
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      height: 1.3,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  if (currentQuote.author.isNotEmpty) ...[
                                    const SizedBox(height: 16),
                                    Text(
                                      '- ${currentQuote.author}',
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontStyle: FontStyle.italic,
                                        color: Colors.white70,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Bottom icons (share, favorite)
                  Positioned(
                    bottom: 30,
                    left: 0,
                    right: 0,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.share_outlined,
                                color: Colors.white),
                            onPressed: () {},
                          ),
                          IconButton(
                            icon: Icon(
                              currentQuote.isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: Colors.white,
                            ),
                            onPressed: () async {
                              // Toggle favorite and invalidate both providers to force refresh
                              await ref
                                  .read(quoteRepositoryProvider)
                                  .toggleFavorite(currentQuote);
                              ref.invalidate(quotesProvider);
                              ref.invalidate(favoritesProvider);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Navigation arrows
                  if (safeIndex < filteredQuotes.length - 1)
                    Positioned(
                      bottom: 40,
                      left: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () {
                          if (safeIndex < filteredQuotes.length - 1) {
                            ref
                                .read(currentQuoteIndexProvider.notifier)
                                .state++;
                            _animateQuoteTransition();
                          }
                        },
                        child: Center(
                          child: Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.white.withOpacity(0.7),
                            size: 32,
                          ),
                        ),
                      ),
                    ),
                  if (safeIndex > 0)
                    Positioned(
                      top: 60,
                      left: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () {
                          if (safeIndex > 0) {
                            ref
                                .read(currentQuoteIndexProvider.notifier)
                                .state--;
                            _animateQuoteTransition();
                          }
                        },
                        child: Center(
                          child: Icon(
                            Icons.keyboard_arrow_up,
                            color: Colors.white.withOpacity(0.7),
                            size: 32,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
