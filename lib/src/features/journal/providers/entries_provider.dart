// lib/src/features/journal/providers/entries_provider.dart
import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/journal/models/entry_models.dart';

part 'entries_provider.g.dart';

@riverpod
class WeeklyEntriesNotifier extends _$WeeklyEntriesNotifier {
  @override
  Future<WeeklyEntries> build(DateTime weekStart) async {
    final db = ref.watch(journalDatabaseProvider);
    final weekEnd = weekStart.weekEnd;

    // Get all activity records for the week
    final activityRecordsQuery = await db.customSelect(
      '''
      SELECT ar.*, ca.type, ca.name
      FROM activity_records ar
      JOIN check_in_activities ca ON ar.activity_id = ca.id
      WHERE ar.date BETWEEN ? AND ?
      AND (ca.type = 'journal' OR ca.type = 'journaling' OR ca.type = 'gratitude' OR ca.type = 'moodTracking' OR ca.type = 'mood_tracking')
      ORDER BY ar.date DESC, ar.completed_at DESC
      ''',
      variables: [
        Variable.withDateTime(weekStart),
        Variable.withDateTime(weekEnd),
      ],
    ).get();

    // Convert to unified entries
    final Map<DateTime, List<UnifiedEntry>> entriesByDay = {};

    for (final row in activityRecordsQuery) {
      try {
        final date = row.read<DateTime>('date');
        final dayKey = DateTime(date.year, date.month, date.day);
        final activityType = row.read<String>('type');
        final data =
            json.decode(row.read<String>('data')) as Map<String, dynamic>;

        String content = '';
        String? mood;

        if (activityType == 'journal' || activityType == 'journaling') {
          // Extract journal responses
          final responses = data['responses'] as Map<String, dynamic>? ?? {};
          content = responses.values.join('\n\n');
          mood = data['mood'] as String?;
        } else if (activityType == 'gratitude') {
          // Extract gratitude entries
          final entries = data['entries'] as List<dynamic>? ?? [];
          content =
              entries.where((e) => e.toString().trim().isNotEmpty).join('\n• ');
          if (content.isNotEmpty) {
            content = '• $content';
          }
        } else if (activityType == 'moodTracking' ||
            activityType == 'mood_tracking') {
          // Extract mood tracking data
          final moodLabel = data['mood_label'] as String? ?? '';
          final moodEmoji = data['mood_emoji'] as String? ?? '';
          final note = data['note'] as String? ?? '';

          content = '$moodEmoji $moodLabel';
          if (note.trim().isNotEmpty) {
            content += '\n\n$note';
          }
          mood = moodLabel;
        }

        if (content.trim().isNotEmpty) {
          final entry = UnifiedEntry(
            id: row.read<int>('id'),
            date: date,
            type: activityType,
            content: content,
            mood: mood,
            completedAt: row.readNullable<DateTime>('completed_at'),
          );

          entriesByDay.putIfAbsent(dayKey, () => []).add(entry);
        }
      } catch (e) {
        // Skip invalid entries
        continue;
      }
    }

    return WeeklyEntries(
      weekStart: weekStart,
      weekEnd: weekEnd,
      entriesByDay: entriesByDay,
    );
  }
}

@riverpod
class SelectedWeekNotifier extends _$SelectedWeekNotifier {
  @override
  DateTime build() {
    return DateTime.now().weekStart;
  }

  void previousWeek() {
    state = state.subtract(const Duration(days: 7));
  }

  void nextWeek() {
    state = state.add(const Duration(days: 7));
  }

  void selectWeek(DateTime weekStart) {
    state = weekStart.weekStart;
  }
}
