// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statistics_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$weeklyMoodTrendHash() => r'0086b22354ffa7c7dcf52364b2085bd1fb6fdeff';

/// See also [weeklyMoodTrend].
@ProviderFor(weeklyMoodTrend)
final weeklyMoodTrendProvider =
    AutoDisposeFutureProvider<List<MoodEntry>>.internal(
  weeklyMoodTrend,
  name: r'weeklyMoodTrendProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$weeklyMoodTrendHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WeeklyMoodTrendRef = AutoDisposeFutureProviderRef<List<MoodEntry>>;
String _$activityStatsHash() => r'6331f8a8b305da54624181a4a320be394496f680';

/// See also [activityStats].
@ProviderFor(activityStats)
final activityStatsProvider =
    AutoDisposeFutureProvider<Map<String, ActivityStats>>.internal(
  activityStats,
  name: r'activityStatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activityStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActivityStatsRef
    = AutoDisposeFutureProviderRef<Map<String, ActivityStats>>;
String _$meditationStatsHash() => r'ebc93bb56a75d43d1f24f14fda5b2f1f2e6e8132';

/// See also [meditationStats].
@ProviderFor(meditationStats)
final meditationStatsProvider =
    AutoDisposeFutureProvider<List<MeditationStats>>.internal(
  meditationStats,
  name: r'meditationStatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$meditationStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MeditationStatsRef
    = AutoDisposeFutureProviderRef<List<MeditationStats>>;
String _$streakStatsHash() => r'cbca3ce953f22766512e8e91da6fa5068e8a724b';

/// See also [streakStats].
@ProviderFor(streakStats)
final streakStatsProvider = AutoDisposeFutureProvider<DailyStreak>.internal(
  streakStats,
  name: r'streakStatsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$streakStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StreakStatsRef = AutoDisposeFutureProviderRef<DailyStreak>;
String _$journalEntriesForMonthHash() =>
    r'eacadd658d290663e8c354c2ee7ba32d1ce2e7b4';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [journalEntriesForMonth].
@ProviderFor(journalEntriesForMonth)
const journalEntriesForMonthProvider = JournalEntriesForMonthFamily();

/// See also [journalEntriesForMonth].
class JournalEntriesForMonthFamily
    extends Family<AsyncValue<List<JournalEntry>>> {
  /// See also [journalEntriesForMonth].
  const JournalEntriesForMonthFamily();

  /// See also [journalEntriesForMonth].
  JournalEntriesForMonthProvider call(
    DateTime month,
  ) {
    return JournalEntriesForMonthProvider(
      month,
    );
  }

  @override
  JournalEntriesForMonthProvider getProviderOverride(
    covariant JournalEntriesForMonthProvider provider,
  ) {
    return call(
      provider.month,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'journalEntriesForMonthProvider';
}

/// See also [journalEntriesForMonth].
class JournalEntriesForMonthProvider
    extends AutoDisposeFutureProvider<List<JournalEntry>> {
  /// See also [journalEntriesForMonth].
  JournalEntriesForMonthProvider(
    DateTime month,
  ) : this._internal(
          (ref) => journalEntriesForMonth(
            ref as JournalEntriesForMonthRef,
            month,
          ),
          from: journalEntriesForMonthProvider,
          name: r'journalEntriesForMonthProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$journalEntriesForMonthHash,
          dependencies: JournalEntriesForMonthFamily._dependencies,
          allTransitiveDependencies:
              JournalEntriesForMonthFamily._allTransitiveDependencies,
          month: month,
        );

  JournalEntriesForMonthProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<List<JournalEntry>> Function(JournalEntriesForMonthRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: JournalEntriesForMonthProvider._internal(
        (ref) => create(ref as JournalEntriesForMonthRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<JournalEntry>> createElement() {
    return _JournalEntriesForMonthProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is JournalEntriesForMonthProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin JournalEntriesForMonthRef
    on AutoDisposeFutureProviderRef<List<JournalEntry>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _JournalEntriesForMonthProviderElement
    extends AutoDisposeFutureProviderElement<List<JournalEntry>>
    with JournalEntriesForMonthRef {
  _JournalEntriesForMonthProviderElement(super.provider);

  @override
  DateTime get month => (origin as JournalEntriesForMonthProvider).month;
}

String _$overviewStatsHash() => r'ced2c5d3986117a31eccadda96e962fc3759b656';

/// See also [overviewStats].
@ProviderFor(overviewStats)
final overviewStatsProvider = AutoDisposeFutureProvider<OverviewStats>.internal(
  overviewStats,
  name: r'overviewStatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$overviewStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OverviewStatsRef = AutoDisposeFutureProviderRef<OverviewStats>;
String _$weeklyMoodEntriesHash() => r'1a8accaf552971e7d041e64db55ad9307652df97';

/// See also [weeklyMoodEntries].
@ProviderFor(weeklyMoodEntries)
final weeklyMoodEntriesProvider =
    AutoDisposeFutureProvider<List<MoodEntryWithTime>>.internal(
  weeklyMoodEntries,
  name: r'weeklyMoodEntriesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$weeklyMoodEntriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WeeklyMoodEntriesRef
    = AutoDisposeFutureProviderRef<List<MoodEntryWithTime>>;
String _$weeklyFocusStatsHash() => r'56a8a3590b078a965ca9752c538ae9956a874932';

/// See also [weeklyFocusStats].
@ProviderFor(weeklyFocusStats)
final weeklyFocusStatsProvider =
    AutoDisposeFutureProvider<List<FocusStats>>.internal(
  weeklyFocusStats,
  name: r'weeklyFocusStatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$weeklyFocusStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WeeklyFocusStatsRef = AutoDisposeFutureProviderRef<List<FocusStats>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
