// lib/src/features/journal/providers/statistics_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/journal/models/statistics.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

part 'statistics_providers.g.dart';

@riverpod
Future<List<MoodEntry>> weeklyMoodTrend(Ref ref) async {
  final db = ref.watch(journalDatabaseProvider);
  final endDate = DateTime.now();
  final startDate = endDate.subtract(const Duration(days: 7));

  return db.getMoodEntriesForDateRange(startDate, endDate);
}

@riverpod
Future<Map<String, ActivityStats>> activityStats(Ref ref) async {
  final db = ref.watch(journalDatabaseProvider);
  final endDate = DateTime.now();
  final startDate = endDate.subtract(const Duration(days: 30));

  return db.getActivityStats(startDate, endDate);
}

@riverpod
Future<List<MeditationStats>> meditationStats(Ref ref) async {
  final db = ref.watch(journalDatabaseProvider);
  final endDate = DateTime.now();
  final startDate = endDate.subtract(const Duration(days: 7));

  return db.getMeditationStats(startDate, endDate);
}

@riverpod
Future<DailyStreak> streakStats(Ref ref) async {
  final db = ref.watch(journalDatabaseProvider);
  return db.getStreak();
}

// Add to your statistics_providers.dart
@riverpod
Future<List<JournalEntry>> journalEntriesForMonth(
  Ref ref,
  DateTime month,
) async {
  final db = ref.watch(journalDatabaseProvider);
  final startOfMonth = DateTime(month.year, month.month, 1);
  final endOfMonth = DateTime(month.year, month.month + 1, 0);

  return db.getEntriesForDateRange(startOfMonth, endOfMonth);
}

@riverpod
Future<OverviewStats> overviewStats(Ref ref) async {
  final db = ref.watch(journalDatabaseProvider);
  final endDate = DateTime.now();
  final startDate = endDate.subtract(const Duration(days: 30));

  return db.getOverviewStats(startDate, endDate);
}

@riverpod
Future<List<MoodEntryWithTime>> weeklyMoodEntries(Ref ref) async {
  final db = ref.watch(journalDatabaseProvider);
  final endDate = DateTime.now();
  final startDate = endDate.subtract(const Duration(days: 7));

  return db.getMoodEntriesWithTimeForDateRange(startDate, endDate);
}

@riverpod
Future<List<FocusStats>> weeklyFocusStats(Ref ref) async {
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return [];

  final now = DateTime.now();
  final weekStart = now.subtract(Duration(days: now.weekday - 1));
  final weekEnd = weekStart.add(const Duration(days: 6));

  final querySnapshot = await FirebaseFirestore.instance
      .collection('users')
      .doc(user.uid)
      .collection('pomodoro_sessions')
      .where('startTime', isGreaterThanOrEqualTo: Timestamp.fromDate(weekStart))
      .where('startTime', isLessThanOrEqualTo: Timestamp.fromDate(weekEnd))
      .get();

  // Group sessions by day
  final Map<DateTime, List<FocusSession>> sessionsByDay = {};

  for (final doc in querySnapshot.docs) {
    final session = FocusSession.fromFirestore(doc.data());
    final dayKey = DateTime(
        session.startTime.year, session.startTime.month, session.startTime.day);

    sessionsByDay.putIfAbsent(dayKey, () => []).add(session);
  }

  // Create FocusStats for each day of the week
  final List<FocusStats> weeklyStats = [];
  for (int i = 0; i < 7; i++) {
    final day = weekStart.add(Duration(days: i));
    final dayKey = DateTime(day.year, day.month, day.day);
    final sessions = sessionsByDay[dayKey] ?? [];

    final totalMinutes =
        sessions.fold<int>(0, (total, session) => total + session.totalMinutes);

    weeklyStats.add(FocusStats(
      date: day,
      totalMinutes: totalMinutes,
      sessionsCount: sessions.length,
      sessions: sessions,
    ));
  }

  return weeklyStats;
}
