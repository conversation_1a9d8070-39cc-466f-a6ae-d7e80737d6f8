// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'entries_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$weeklyEntriesNotifierHash() =>
    r'230a998f9447f72d1d8ed3ca1e009360cc106252';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$WeeklyEntriesNotifier
    extends BuildlessAutoDisposeAsyncNotifier<WeeklyEntries> {
  late final DateTime weekStart;

  FutureOr<WeeklyEntries> build(
    DateTime weekStart,
  );
}

/// See also [WeeklyEntriesNotifier].
@ProviderFor(WeeklyEntriesNotifier)
const weeklyEntriesNotifierProvider = WeeklyEntriesNotifierFamily();

/// See also [WeeklyEntriesNotifier].
class WeeklyEntriesNotifierFamily extends Family<AsyncValue<WeeklyEntries>> {
  /// See also [WeeklyEntriesNotifier].
  const WeeklyEntriesNotifierFamily();

  /// See also [WeeklyEntriesNotifier].
  WeeklyEntriesNotifierProvider call(
    DateTime weekStart,
  ) {
    return WeeklyEntriesNotifierProvider(
      weekStart,
    );
  }

  @override
  WeeklyEntriesNotifierProvider getProviderOverride(
    covariant WeeklyEntriesNotifierProvider provider,
  ) {
    return call(
      provider.weekStart,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'weeklyEntriesNotifierProvider';
}

/// See also [WeeklyEntriesNotifier].
class WeeklyEntriesNotifierProvider
    extends AutoDisposeAsyncNotifierProviderImpl<WeeklyEntriesNotifier,
        WeeklyEntries> {
  /// See also [WeeklyEntriesNotifier].
  WeeklyEntriesNotifierProvider(
    DateTime weekStart,
  ) : this._internal(
          () => WeeklyEntriesNotifier()..weekStart = weekStart,
          from: weeklyEntriesNotifierProvider,
          name: r'weeklyEntriesNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$weeklyEntriesNotifierHash,
          dependencies: WeeklyEntriesNotifierFamily._dependencies,
          allTransitiveDependencies:
              WeeklyEntriesNotifierFamily._allTransitiveDependencies,
          weekStart: weekStart,
        );

  WeeklyEntriesNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.weekStart,
  }) : super.internal();

  final DateTime weekStart;

  @override
  FutureOr<WeeklyEntries> runNotifierBuild(
    covariant WeeklyEntriesNotifier notifier,
  ) {
    return notifier.build(
      weekStart,
    );
  }

  @override
  Override overrideWith(WeeklyEntriesNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: WeeklyEntriesNotifierProvider._internal(
        () => create()..weekStart = weekStart,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        weekStart: weekStart,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<WeeklyEntriesNotifier, WeeklyEntries>
      createElement() {
    return _WeeklyEntriesNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WeeklyEntriesNotifierProvider &&
        other.weekStart == weekStart;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, weekStart.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WeeklyEntriesNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<WeeklyEntries> {
  /// The parameter `weekStart` of this provider.
  DateTime get weekStart;
}

class _WeeklyEntriesNotifierProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<WeeklyEntriesNotifier,
        WeeklyEntries> with WeeklyEntriesNotifierRef {
  _WeeklyEntriesNotifierProviderElement(super.provider);

  @override
  DateTime get weekStart => (origin as WeeklyEntriesNotifierProvider).weekStart;
}

String _$selectedWeekNotifierHash() =>
    r'db1a93e71306eb53b5ba9f9a0e576b8de7be8c04';

/// See also [SelectedWeekNotifier].
@ProviderFor(SelectedWeekNotifier)
final selectedWeekNotifierProvider =
    AutoDisposeNotifierProvider<SelectedWeekNotifier, DateTime>.internal(
  SelectedWeekNotifier.new,
  name: r'selectedWeekNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedWeekNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedWeekNotifier = AutoDisposeNotifier<DateTime>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
