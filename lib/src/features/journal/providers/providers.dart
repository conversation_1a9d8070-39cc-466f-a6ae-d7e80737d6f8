// lib/src/features/journal/data/providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'dart:convert';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';

import 'package:mimi_app/src/features/journal/service/checkin_service.dart';

// Stream providers for real-time updates
final todayEntriesProvider = StreamProvider<List<JournalEntry>>((ref) {
  final db = ref.watch(journalDatabaseProvider);
  return db.watchTodayEntries();
});

final checkinServiceProvider = Provider<CheckinService>((ref) {
  final db = ref.watch(journalDatabaseProvider);
  return CheckinService(ref, db); // Updated to pass database instance
});

// Initialization provider
final initializeJournalProvider = FutureProvider<void>((ref) async {
  final checkinService = ref.read(checkinServiceProvider);
  await checkinService.initialize();
});

final allRoutinesProvider = StreamProvider<List<CheckInRoutine>>((ref) {
  final db = ref.watch(journalDatabaseProvider);
  return db.watchAllRoutines();
});

// Notifier for journal operations
class JournalNotifier extends Notifier<AsyncValue<void>> {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  Future<void> createRoutine({
    required String name,
    required String type,
    required DateTime scheduledTime,
    required List<Activity> activities,
    required int intentionId, // Added intentionId parameter
  }) async {
    state = const AsyncValue.loading();
    try {
      final db = ref.read(journalDatabaseProvider);
      await db.createRoutine(
        CheckInRoutinesCompanion.insert(
          name: name,
          type: type,
          scheduledTime: scheduledTime,
          activities: json.encode(activities.map((a) => a.id).toList()),
          lastUpdated: DateTime.now(),
          notificationEnabled: const Value(true),
          intentionId: intentionId, // Corrected: pass directly
        ),
      );
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow; // Add this to propagate error
    }
  }

  Future<void> deleteAllRoutines() async {
    state = const AsyncValue.loading();
    try {
      final db = ref.watch(journalDatabaseProvider);
      final routines = await db.getAllRoutines();

      for (final routine in routines) {
        await db.deleteRoutine(routine.id);
      }

      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow; // Add this to propagate error
    }
  }

  Future<void> createJournalEntry({
    required int routineId,
    required Map<String, dynamic> responses, // Updated to use typed responses
    required String status,
    String? mood,
    int? meditationMinutes,
    DateTime? completedAt,
  }) async {
    state = const AsyncValue.loading();
    try {
      final db = ref.read(journalDatabaseProvider);
      await db.createEntry(
        JournalEntriesCompanion.insert(
          routineId: routineId,
          date: DateTime.now(),
          responses: json.encode(responses), // Properly encode responses
          mood: Value(mood),
          meditationMinutes: Value(meditationMinutes),
          status: status,
          completedAt: Value(completedAt ?? DateTime.now()),
        ),
      );
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> updateActivity({
    required int id,
    required String name,
    required String type,
    required ActivityConfig config,
  }) async {
    state = const AsyncValue.loading();
    try {
      final db = ref.read(journalDatabaseProvider);
      await db.updateActivity(
        id: id,
        name: name,
        type: type,
        config: config,
      );
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  String getRoutineStatus(DateTime scheduledTime) {
    final now = DateTime.now();
    if (now.isBefore(scheduledTime)) {
      return 'pending';
    } else if (now.isAfter(scheduledTime.add(const Duration(hours: 3)))) {
      return 'missed';
    }
    return 'available';
  }
}

final journalNotifierProvider =
    NotifierProvider<JournalNotifier, AsyncValue<void>>(() {
  return JournalNotifier();
});

// State provider for date selection
final selectedDateProvider = StateProvider<DateTime>((ref) => DateTime.now());

// Provider for filtered entries
final filteredEntriesProvider = Provider<List<JournalEntry>>((ref) {
  final selectedDate = ref.watch(selectedDateProvider);
  final allEntries = ref.watch(todayEntriesProvider);

  return allEntries.when(
    data: (entries) {
      return entries.where((entry) {
        final entryDate = entry.date;
        return entryDate.year == selectedDate.year &&
            entryDate.month == selectedDate.month &&
            entryDate.day == selectedDate.day;
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Provider for routine status
final routineStatusProvider = Provider.family<String, int>((ref, routineId) {
  final allRoutines = ref.watch(allRoutinesProvider);

  return allRoutines.when(
    data: (routines) {
      final routine = routines.firstWhere((r) => r.id == routineId,
          orElse: () => throw Exception('Routine not found'));
      final notifier = ref.read(journalNotifierProvider.notifier);
      return notifier.getRoutineStatus(routine.scheduledTime);
    },
    loading: () => 'loading',
    error: (_, __) => 'error',
  );
});

// New providers for activities
final allActivitiesProvider = StreamProvider<List<Activity>>((ref) {
  final db = ref.watch(journalDatabaseProvider);
  return db.getAllActivities().asStream();
});

final activityProvider = Provider.family<Future<Activity?>, int>((ref, id) {
  final db = ref.watch(journalDatabaseProvider);
  return db.getActivityById(id);
});
