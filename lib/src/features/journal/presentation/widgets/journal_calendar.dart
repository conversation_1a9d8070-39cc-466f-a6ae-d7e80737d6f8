// lib/src/features/journal/presentation/widgets/journal_calendar.dart
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/journal/providers/statistics_providers.dart';
import 'package:table_calendar/table_calendar.dart';

import 'package:intl/intl.dart';

class JournalCalendarView extends ConsumerStatefulWidget {
  const JournalCalendarView({super.key});

  @override
  ConsumerState<JournalCalendarView> createState() =>
      _JournalCalendarViewState();
}

class _JournalCalendarViewState extends ConsumerState<JournalCalendarView> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;

  @override
  Widget build(BuildContext context) {
    // Get entries for the current month
    final entriesAsync = ref.watch(journalEntriesForMonthProvider(_focusedDay));

    return Column(
      children: [
        entriesAsync.when(
          data: (entries) => TableCalendar(
            firstDay: DateTime.utc(2024, 1, 1),
            lastDay: DateTime.now(),
            focusedDay: _focusedDay,
            calendarFormat: _calendarFormat,
            selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
            eventLoader: (day) {
              return entries
                  .where((entry) => isSameDay(entry.date, day))
                  .toList();
            },
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _selectedDay = selectedDay;
                _focusedDay = focusedDay;
              });
              _showEntriesForDay(selectedDay, entries);
            },
            onFormatChanged: (format) {
              setState(() {
                _calendarFormat = format;
              });
            },
            onPageChanged: (focusedDay) {
              _focusedDay = focusedDay;
            },
            calendarStyle: CalendarStyle(
              markersMaxCount: 1,
              markerDecoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
              ),
            ),
          ),
          loading: () => const CircularProgressIndicator(),
          error: (error, stack) => Text('Error: $error'),
        ),
        if (_selectedDay != null) ...[
          const SizedBox(height: 16),
          entriesAsync.when(
            data: (entries) => _EntriesForDayList(
              entries: entries
                  .where((entry) => isSameDay(entry.date, _selectedDay!))
                  .toList(),
            ),
            loading: () => const CircularProgressIndicator(),
            error: (error, stack) => Text('Error: $error'),
          ),
        ],
      ],
    );
  }

  void _showEntriesForDay(DateTime day, List<JournalEntry> entries) {
    final entriesForDay =
        entries.where((entry) => isSameDay(entry.date, day)).toList();

    if (entriesForDay.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _JournalEntriesBottomSheet(
        date: day,
        entries: entriesForDay,
      ),
    );
  }
}

class _EntriesForDayList extends StatelessWidget {
  final List<JournalEntry> entries;

  const _EntriesForDayList({required this.entries});

  @override
  Widget build(BuildContext context) {
    if (entries.isEmpty) {
      return const Center(
        child: Text('No entries for this day'),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: entries.length,
      itemBuilder: (context, index) {
        final entry = entries[index];
        return _JournalEntryCard(entry: entry);
      },
    );
  }
}

class _JournalEntryCard extends StatelessWidget {
  final JournalEntry entry;

  const _JournalEntryCard({required this.entry});

  @override
  Widget build(BuildContext context) {
    final responses = json.decode(entry.responses) as Map<String, dynamic>;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Icon(
                //   _getCheckInTypeIcon(entry.type),
                //   color: Theme.of(context).primaryColor,
                // ),
                const SizedBox(width: 8),
                // Text(
                //   entry.type.toUpperCase(),
                //   style: Theme.of(context).textTheme.titleMedium,
                // ),
                const Spacer(),
                Text(
                  DateFormat.jm().format(entry.date),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            if (entry.mood != null) ...[
              const SizedBox(height: 8),
              Text('Mood: ${entry.mood}'),
            ],
            const SizedBox(height: 16),
            ...responses.entries
                .where((e) => e.key.startsWith('journal_'))
                .map((response) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(response.value.toString()),
                    )),
          ],
        ),
      ),
    );
  }

  // IconData _getCheckInTypeIcon(String type) {
  //   return switch (type) {
  //     'morning' => Icons.wb_sunny,
  //     'noon' => Icons.wb_twilight,
  //     'night' => Icons.nightlight_round,
  //     _ => Icons.check_circle,
  //   };
  // }
}

class _JournalEntriesBottomSheet extends StatelessWidget {
  final DateTime date;
  final List<JournalEntry> entries;

  const _JournalEntriesBottomSheet({
    required this.date,
    required this.entries,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            DateFormat.yMMMd().format(date),
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: entries.length,
              itemBuilder: (context, index) {
                return _JournalEntryCard(entry: entries[index]);
              },
            ),
          ),
        ],
      ),
    );
  }
}
