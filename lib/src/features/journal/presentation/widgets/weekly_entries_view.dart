// lib/src/features/journal/presentation/widgets/weekly_entries_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/journal/models/entry_models.dart';
import 'package:mimi_app/src/features/journal/providers/entries_provider.dart';
import 'package:mimi_app/src/features/journal/presentation/screens/category_detail_screen.dart';

class WeeklyEntriesView extends ConsumerWidget {
  const WeeklyEntriesView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedWeek = ref.watch(selectedWeekNotifierProvider);
    final weeklyEntriesAsync =
        ref.watch(weeklyEntriesNotifierProvider(selectedWeek));

    return Column(
      children: [
        // Week navigation header
        _WeekNavigationHeader(selectedWeek: selectedWeek),

        // Entries list
        Expanded(
          child: weeklyEntriesAsync.when(
            data: (weeklyEntries) => _EntriesList(weeklyEntries: weeklyEntries),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading entries',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _WeekNavigationHeader extends ConsumerWidget {
  final DateTime selectedWeek;

  const _WeekNavigationHeader({required this.selectedWeek});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final weekEnd = selectedWeek.weekEnd;
    final isCurrentWeek =
        DateTime.now().weekStart.isAtSameMomentAs(selectedWeek);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: AppColors.border.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Previous week button
          IconButton(
            onPressed: () {
              ref.read(selectedWeekNotifierProvider.notifier).previousWeek();
            },
            icon: Icon(
              Icons.chevron_left,
              color: AppColors.textPrimary,
            ),
          ),

          // Week range display
          Expanded(
            child: Column(
              children: [
                Text(
                  '${DateFormat('MMM d').format(selectedWeek)} - ${DateFormat('MMM d, y').format(weekEnd)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                if (isCurrentWeek)
                  Text(
                    'This Week',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),

          // Next week button (disabled if current week or future)
          IconButton(
            onPressed: isCurrentWeek
                ? null
                : () {
                    ref.read(selectedWeekNotifierProvider.notifier).nextWeek();
                  },
            icon: Icon(
              Icons.chevron_right,
              color: isCurrentWeek
                  ? AppColors.textSecondary
                  : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}

class _EntriesList extends StatelessWidget {
  final WeeklyEntries weeklyEntries;

  const _EntriesList({required this.weeklyEntries});

  @override
  Widget build(BuildContext context) {
    if (weeklyEntries.entriesByDay.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.article_outlined,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No entries this week',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Complete your daily activities\nto see your entries here',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Sort days in descending order (most recent first)
    final sortedDays = weeklyEntries.entriesByDay.keys.toList()
      ..sort((a, b) => b.compareTo(a));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedDays.length,
      itemBuilder: (context, index) {
        final day = sortedDays[index];
        final entries = weeklyEntries.entriesByDay[day]!;

        return _DayEntriesCard(
          day: day,
          entries: entries,
        );
      },
    );
  }
}

class _DayEntriesCard extends StatelessWidget {
  final DateTime day;
  final List<UnifiedEntry> entries;

  const _DayEntriesCard({
    required this.day,
    required this.entries,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = DateTime.now().isSameDay(day);
    final dayName = DateFormat('EEEE').format(day);
    final dayDate = DateFormat('MMM d').format(day);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isToday
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Text(
                  dayName,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: isToday ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  dayDate,
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        isToday ? AppColors.primary : AppColors.textSecondary,
                  ),
                ),
                if (isToday) ...[
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Today',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textOnPrimary,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Entry categories
          ..._buildCategoryTiles(entries),
        ],
      ),
    );
  }

  List<Widget> _buildCategoryTiles(List<UnifiedEntry> entries) {
    // Group entries by type
    final Map<String, List<UnifiedEntry>> entriesByType = {};
    for (final entry in entries) {
      final type = _normalizeType(entry.type);
      entriesByType.putIfAbsent(type, () => []).add(entry);
    }

    // Create category tiles
    return entriesByType.entries.map((typeEntry) {
      final type = typeEntry.key;
      final typeEntries = typeEntry.value;
      return _CategoryTile(
        type: type,
        entries: typeEntries,
        day: day,
      );
    }).toList();
  }

  String _normalizeType(String type) {
    if (type == 'journal' || type == 'journaling') return 'journal';
    if (type == 'moodTracking' || type == 'mood_tracking') return 'mood';
    return type; // gratitude stays as is
  }
}

class _CategoryTile extends StatelessWidget {
  final String type;
  final List<UnifiedEntry> entries;
  final DateTime day;

  const _CategoryTile({
    required this.type,
    required this.entries,
    required this.day,
  });

  @override
  Widget build(BuildContext context) {
    IconData icon;
    Color iconColor;
    String typeLabel;

    switch (type) {
      case 'gratitude':
        icon = Icons.favorite;
        iconColor = AppColors.accent3;
        typeLabel = 'Gratitude';
        break;
      case 'mood':
        icon = Icons.mood;
        iconColor = AppColors.accent4;
        typeLabel = 'Mood';
        break;
      default: // journal
        icon = Icons.article;
        iconColor = AppColors.accent1;
        typeLabel = 'Journal';
        break;
    }

    final entryCount = entries.length;
    final latestEntry =
        entries.first; // entries are already sorted by date desc

    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.border.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToDetailScreen(context),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    size: 20,
                    color: iconColor,
                  ),
                ),

                const SizedBox(width: 16),

                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            typeLabel,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (entryCount > 1)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: iconColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '$entryCount entries',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: iconColor,
                                ),
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // Preview of latest entry
                      Text(
                        _getPreviewText(latestEntry),
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      if (latestEntry.completedAt != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          DateFormat('h:mm a').format(latestEntry.completedAt!),
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                AppColors.textSecondary.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Arrow
                Icon(
                  Icons.chevron_right,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getPreviewText(UnifiedEntry entry) {
    if (entry.content.length <= 100) {
      return entry.content;
    }
    return '${entry.content.substring(0, 100)}...';
  }

  void _navigateToDetailScreen(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CategoryDetailScreen(
          type: type,
          entries: entries,
          day: day,
        ),
      ),
    );
  }
}
