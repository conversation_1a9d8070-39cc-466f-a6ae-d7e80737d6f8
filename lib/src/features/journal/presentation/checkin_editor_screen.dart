import 'package:drift/drift.dart' hide Column;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/utils/snackbar_utils.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/screens/activity_settings.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'dart:convert';

class CheckinEditorScreen extends ConsumerStatefulWidget {
  final int? routineId;
  final int intentionId;

  const CheckinEditorScreen({
    super.key,
    required this.routineId,
    required this.intentionId,
  });

  @override
  ConsumerState<CheckinEditorScreen> createState() =>
      _CheckinEditorScreenState();
}

class _CheckinEditorScreenState extends ConsumerState<CheckinEditorScreen> {
  late TextEditingController _nameController;
  TimeOfDay? _scheduledTime;
  List<Activity> _selectedActivities = [];
  List<Activity> _availableActivities = [];
  bool _isLoading = true;
  bool _isNew = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _loadAvailableActivities();
    if (widget.routineId != null) {
      _loadRoutineData();
    } else {
      _isNew = true;
      _isLoading = false;
    }
  }

  Future<void> _loadAvailableActivities() async {
    try {
      final db = ref.read(journalDatabaseProvider);
      final activities = await db.getAllActivities();
      setState(() {
        _availableActivities = activities;
      });
    } catch (e) {
      if (mounted) {
        showFailureToast(context,
            title: 'Error', description: 'Error loading activities: $e');
      }
    }
  }

  Future<void> _loadRoutineData() async {
    setState(() => _isLoading = true);
    try {
      final db = ref.read(journalDatabaseProvider);

      // Load routine data
      final routine = await db.getRoutineById(widget.routineId!);
      if (routine != null) {
        _nameController.text = routine.name;
        _scheduledTime = TimeOfDay.fromDateTime(routine.scheduledTime);

        // Get the ordered list of activity IDs
        final selectedActivityIds = jsonDecode(routine.activities) as List;

        // Create ordered list of activities maintaining the saved order
        _selectedActivities = selectedActivityIds
            .map((id) => _availableActivities
                .firstWhere((activity) => activity.id == id))
            .toList();

        setState(() {});
      }
    } catch (e) {
      // Error loading routine - silently handle
      debugPrint('Error loading routine: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveRoutine() async {
    try {
      if (!_validateForm()) return;

      final db = ref.read(journalDatabaseProvider);
      final now = DateTime.now();
      final scheduledTime = DateTime(
        now.year,
        now.month,
        now.day,
        _scheduledTime!.hour,
        _scheduledTime!.minute,
      );
      final orderedActivityIds = _selectedActivities.map((a) => a.id).toList();

      if (_isNew) {
        // Create new routine
        final routineId = await db.createRoutine(
          CheckInRoutinesCompanion.insert(
            intentionId: widget.intentionId,
            type: 'custom',
            name: _nameController.text,
            scheduledTime: scheduledTime,
            activities: jsonEncode(orderedActivityIds),
            lastUpdated: DateTime.now(),
          ),
        );
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        // Update existing routine
        final routine = await db.getRoutineById(widget.routineId!);
        await db.updateRoutine(
          CheckInRoutinesCompanion(
            id: Value(widget.routineId!),
            type: Value(routine!.type),
            name: Value(_nameController.text),
            scheduledTime: Value(scheduledTime),
            activities: Value(jsonEncode(orderedActivityIds)),
            lastUpdated: Value(DateTime.now()),
            enabled: const Value(true),
            notificationEnabled: const Value(true),
          ),
        );
        if (mounted) {
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      // Error saving routine - silently handle
      debugPrint('Error saving routine: $e');
    }
  }

  Future<bool> _onWillPop() async {
    if (!_isNew) {
      // For existing, just save and pop
      _saveRoutine();
      return false;
    }
    // For new, show dialog
    final shouldSave = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save Check-In?'),
        content: const Text('Do you want to save this check-in or discard it?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Discard'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Save'),
          ),
        ],
      ),
    );
    if (shouldSave == true) {
      await _saveRoutine();
    } else {
      Navigator.pop(context, false);
    }
    return false;
  }

  bool _validateForm() {
    if (_nameController.text.isEmpty) {
      showFailureToast(context,
          title: 'Error', description: 'Please enter a name');
      return false;
    }

    if (_scheduledTime == null) {
      showFailureToast(context,
          title: 'Error', description: 'Please select a time');
      return false;
    }

    if (_selectedActivities.isEmpty) {
      // ScaffoldMessenger.of(context).showSnackBar(
      //   const SnackBar(content: Text('Please select at least one activity')),
      // );
      return true;
    }

    return true;
  }

  void _showActivityPicker() {
    // First ensure we have no duplicates in available activities using ID-based comparison
    final uniqueAvailable = <Activity>[];
    final seenIds = <int>{};
    for (var activity in _availableActivities) {
      if (seenIds.add(activity.id)) {
        // returns true if the id was not in the set
        uniqueAvailable.add(activity);
      }
    }

    // Then filter out already selected activities
    final availableToAdd = uniqueAvailable
        .where((activity) =>
            !_selectedActivities.any((selected) => selected.id == activity.id))
        .toList();

    showModalBottomSheet(
      context: context,
      builder: (context) => _ActivityPickerSheet(
        activities: availableToAdd,
        onActivitySelected: (activity) {
          setState(() {
            _selectedActivities.add(activity);
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          leading:
              IconButton(onPressed: _onWillPop, icon: Icon(Icons.arrow_back)),
          title: const Text('Edit Check-In'),
          actions: [
            TextButton(
              onPressed: _saveRoutine,
              child: const Text('Save'),
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // Main content area
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextField(
                            controller: _nameController,
                            decoration: const InputDecoration(
                              labelText:
                                  'Eg. Morning Check-In, Noon Check-In etc.',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 24),
                          ListTile(
                            title: const Text('Scheduled Time'),
                            subtitle: Text(
                                _scheduledTime?.format(context) ?? 'Not set'),
                            trailing: const Icon(Icons.access_time),
                            onTap: () async {
                              final time = await showTimePicker(
                                context: context,
                                initialTime: _scheduledTime ?? TimeOfDay.now(),
                              );
                              if (time != null) {
                                setState(() => _scheduledTime = time);
                              }
                            },
                          ),
                          const SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Activities',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              TextButton.icon(
                                onPressed: _showActivityPicker,
                                icon: const Icon(Icons.add),
                                label: const Text('Add Activity'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (_selectedActivities.isEmpty)
                            const Center(
                              child: Text('No activities selected'),
                            )
                          else
                            ReorderableListView(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              children: _selectedActivities.map((activity) {
                                return Dismissible(
                                    key: ValueKey(activity.id),
                                    direction: DismissDirection.endToStart,
                                    background: Container(
                                      color: Colors.red,
                                      alignment: Alignment.centerRight,
                                      padding: const EdgeInsets.only(right: 16),
                                      child: const Icon(
                                        Icons.delete,
                                        color: Colors.white,
                                      ),
                                    ),
                                    onDismissed: (_) {
                                      setState(() {
                                        _selectedActivities.remove(activity);
                                      });
                                    },
                                    child: Card(
                                      child: ListTile(
                                        leading: const Icon(Icons.drag_handle),
                                        title: Text(activity.name),
                                        trailing: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            IconButton(
                                              icon: const Icon(Icons.settings),
                                              onPressed: () async {
                                                // If routineId is null, save the routine first to get an ID
                                                int? tempRoutineId =
                                                    widget.routineId;
                                                if (tempRoutineId == null) {
                                                  if (!_validateForm()) return;

                                                  try {
                                                    final db = ref.read(
                                                        journalDatabaseProvider);
                                                    final now = DateTime.now();
                                                    final scheduledTime =
                                                        DateTime(
                                                      now.year,
                                                      now.month,
                                                      now.day,
                                                      _scheduledTime!.hour,
                                                      _scheduledTime!.minute,
                                                    );
                                                    final orderedActivityIds =
                                                        _selectedActivities
                                                            .map((a) => a.id)
                                                            .toList();

                                                    tempRoutineId =
                                                        await db.createRoutine(
                                                      CheckInRoutinesCompanion
                                                          .insert(
                                                        intentionId:
                                                            widget.intentionId,
                                                        type: 'custom',
                                                        name: _nameController
                                                            .text,
                                                        scheduledTime:
                                                            scheduledTime,
                                                        activities: jsonEncode(
                                                            orderedActivityIds),
                                                        lastUpdated:
                                                            DateTime.now(),
                                                      ),
                                                    );
                                                    _isNew = false;
                                                  } catch (e) {
                                                    // Error saving routine - silently handle
                                                    debugPrint(
                                                        'Error saving routine: $e');
                                                    return;
                                                  }
                                                }

                                                if (mounted) {
                                                  final routineId =
                                                      tempRoutineId;
                                                  await Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          ActivitySettingsScreen(
                                                        activity: activity,
                                                        routineId: routineId,
                                                      ),
                                                    ),
                                                  );
                                                }
                                              },
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.delete),
                                              onPressed: () {
                                                setState(() {
                                                  _selectedActivities
                                                      .remove(activity);
                                                });
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ));
                              }).toList(),
                              onReorder: (oldIndex, newIndex) {
                                setState(() {
                                  if (newIndex > oldIndex) {
                                    newIndex -= 1;
                                  }
                                  final activity =
                                      _selectedActivities.removeAt(oldIndex);
                                  _selectedActivities.insert(
                                      newIndex, activity);
                                });
                              },
                            ),
                        ],
                      ),
                    ),
                  ),

                  // Bottom navigation buttons
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: 100,
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Back'),
                          ),
                        ),
                        SizedBox(
                          width: 100,
                          child: ElevatedButton(
                            onPressed: () async {
                              await _saveRoutine();
                            },
                            child: const Text('Next'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

class _ActivityPickerSheet extends StatelessWidget {
  final List<Activity> activities;
  final ValueChanged<Activity> onActivitySelected;

  const _ActivityPickerSheet({
    required this.activities,
    required this.onActivitySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Select Activity',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: activities.length,
              itemBuilder: (context, index) {
                final activity = activities[index];
                return ListTile(
                  leading: Icon(_getActivityIcon(activity.type)),
                  title: Text(activity.name),
                  onTap: () => onActivitySelected(activity),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'journaling':
        return Icons.edit_note;
      case 'meditation':
        return Icons.self_improvement;
      case 'breathwork':
        return Icons.air;
      case 'affirmations':
        return Icons.record_voice_over;
      case 'moodTracking':
        return Icons.mood;
      default:
        return Icons.category;
    }
  }
}
