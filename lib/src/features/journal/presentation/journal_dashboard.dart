// // lib/src/features/journal/screens/journal_dashboard_screen.dart
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:mimi_app/src/core/router/route_names.dart';
// import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
// import 'package:mimi_app/src/features/journal/providers/providers.dart';
// import 'package:mimi_app/src/features/user/providers/intention_provider.dart';
// import 'dart:convert';
// import '../../database/providers/database_provider.dart';

// class JournalDashboardScreen extends ConsumerWidget {
//   const JournalDashboardScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final intentionAsync = ref.watch(intentionControllerProvider);
//     final allRoutines = ref.watch(allRoutinesProvider);

//     return Scaffold(
//       appBar: AppBar(
//         title: Text(
//           'Intentions',
//           style: Theme.of(context).textTheme.headlineSmall?.copyWith(
//                 fontWeight: FontWeight.w600,
//               ),
//         ),
//         centerTitle: false,
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.info_outline),
//             onPressed: () {
//               context.pushNamed(RouteNames.intentionsInfo);
//             },
//           ),
//         ],
//       ),
//       body: intentionAsync.when(
//         data: (intention) {
//           if (intention == null) {
//             return const IntentionSetupView();
//           }
//           return RoutinesListView(
//             intention: intention,
//             routinesAsync: allRoutines,
//           );
//         },
//         loading: () => const Center(child: CircularProgressIndicator()),
//         error: (error, stack) => Center(child: Text('Error: $error')),
//       ),
//     );
//   }
// }

// class IntentionSetupView extends ConsumerStatefulWidget {
//   const IntentionSetupView({super.key});

//   @override
//   ConsumerState<IntentionSetupView> createState() => _IntentionSetupViewState();
// }

// class _IntentionSetupViewState extends ConsumerState<IntentionSetupView> {
//   final _intentionController = TextEditingController();
//   bool _isSubmitting = false;

//   @override
//   void dispose() {
//     _intentionController.dispose();
//     super.dispose();
//   }

//   Future<void> _submitIntention() async {
//     if (_intentionController.text.isEmpty) return;
//     setState(() => _isSubmitting = true);

//     try {
//       // Just set the intention without creating any routines
//       await ref
//           .read(intentionControllerProvider.notifier)
//           .setIntention(_intentionController.text);

//       if (!mounted) return;
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(
//           content: Text('Intention set successfully!'),
//           behavior: SnackBarBehavior.floating,
//         ),
//       );
//     } catch (error) {
//       if (!mounted) return;
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('Error setting intention: $error'),
//           backgroundColor: Theme.of(context).colorScheme.error,
//           behavior: SnackBarBehavior.floating,
//         ),
//       );
//     } finally {
//       if (mounted) {
//         setState(() => _isSubmitting = false);
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'Set Your Intention',
//             style: Theme.of(context).textTheme.headlineSmall,
//           ),
//           const SizedBox(height: 16),
//           Text(
//             'What do you want to focus on?',
//             style: Theme.of(context).textTheme.bodyLarge,
//           ),
//           const SizedBox(height: 24),
//           TextField(
//             controller: _intentionController,
//             decoration: const InputDecoration(
//               hintText: 'Enter your intention...',
//               border: OutlineInputBorder(),
//             ),
//             maxLines: 3,
//             onChanged: (_) => setState(() {}),
//           ),
//           const SizedBox(height: 24),
//           SizedBox(
//             width: double.infinity,
//             child: ElevatedButton(
//               onPressed: _isSubmitting || _intentionController.text.isEmpty
//                   ? null
//                   : _submitIntention,
//               child: _isSubmitting
//                   ? const SizedBox(
//                       height: 20,
//                       width: 20,
//                       child: CircularProgressIndicator(strokeWidth: 2),
//                     )
//                   : Text(
//                       'Set Intention',
//                       style: Theme.of(context).textTheme.bodyMedium!.copyWith(
//                             color: Colors.white,
//                           ),
//                     ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class RoutinesListView extends ConsumerWidget {
//   final String intention;
//   final AsyncValue<List<CheckInRoutine>> routinesAsync;

//   const RoutinesListView({
//     required this.intention,
//     required this.routinesAsync,
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return ListView(
//       padding: const EdgeInsets.all(16),
//       children: [
//         // Intention Card with Edit/Delete
//         Card(
//           child: Padding(
//             padding: const EdgeInsets.all(16),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       'My  Intention',
//                       style: Theme.of(context).textTheme.titleMedium,
//                     ),
//                     PopupMenuButton<String>(
//                       onSelected: (value) async {
//                         if (value == 'edit') {
//                           await _showEditDialog(context, ref, intention);
//                         } else if (value == 'delete') {
//                           await _showDeleteDialog(context, ref);
//                         }
//                       },
//                       itemBuilder: (BuildContext context) => [
//                         const PopupMenuItem(
//                           value: 'edit',
//                           child: Row(
//                             children: [
//                               Icon(Icons.edit),
//                               SizedBox(width: 8),
//                               Text('Edit Intention'),
//                             ],
//                           ),
//                         ),
//                         const PopupMenuItem(
//                           value: 'delete',
//                           child: Row(
//                             children: [
//                               Icon(Icons.delete),
//                               SizedBox(width: 8),
//                               Text('Delete Intention'),
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//                 const SizedBox(height: 8),
//                 Text(
//                   intention,
//                   style: Theme.of(context).textTheme.bodyLarge,
//                 ),
//               ],
//             ),
//           ),
//         ),
//         const SizedBox(height: 24),
//         _buildAddCheckinCard(context, ref, intention),
//         const SizedBox(height: 16),
//         routinesAsync.when(
//           data: (routines) => Column(
//             children: routines
//                 .map((routine) => _buildRoutineCard(context, ref, routine))
//                 .toList(),
//           ),
//           loading: () => const Center(child: CircularProgressIndicator()),
//           error: (error, stack) => Center(child: Text('Error: $error')),
//         ),
//       ],
//     );
//   }

//   Widget _buildRoutineCard(
//       BuildContext context, WidgetRef ref, CheckInRoutine routine) {
//     return Stack(
//       children: [
//         _CheckInCard(routine: routine),
//         Positioned(
//           top: 8,
//           right: 8,
//           child: IconButton(
//             icon: const Icon(Icons.remove_circle_outline),
//             color: Theme.of(context).colorScheme.error,
//             onPressed: () => _showRemoveCheckinDialog(context, ref, routine),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildAddCheckinCard(BuildContext context, WidgetRef ref, String intention) {
//     return Card(
//       margin: const EdgeInsets.only(bottom: 16),
//       child: InkWell(
//         onTap: () async {
//           // Use go_router for navigation
//           context.goNamed(
//             RouteNames.checkinCreator,
//             pathParameters: {'intentionId': intention},
//           );
//         },
//         child: Padding(
//           padding: const EdgeInsets.all(16),
//           child: Row(
//             children: [
//               Icon(
//                 Icons.add_circle_outline,
//                 color: Theme.of(context).colorScheme.primary,
//               ),
//               const SizedBox(width: 8),
//               Text(
//                 'Add Check-In',
//                 style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//                   color: Theme.of(context).colorScheme.primary,
//                   fontWeight: FontWeight.w600,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Future<void> _showRemoveCheckinDialog(
//       BuildContext context, WidgetRef ref, CheckInRoutine routine) async {
//     final confirmed = await showDialog<bool>(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Remove Check-In'),
//         content: Text('Are you sure you want to remove the ${routine.name}?'),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(false),
//             child: const Text('Cancel'),
//           ),
//           FilledButton(
//             onPressed: () => Navigator.of(context).pop(true),
//             style: FilledButton.styleFrom(
//               backgroundColor: Theme.of(context).colorScheme.error,
//             ),
//             child: const Text('Remove'),
//           ),
//         ],
//       ),
//     );

//     if (confirmed == true && context.mounted) {
//       final db = ref.read(journalDatabaseProvider);
//       await db.deleteRoutine(routine.id);
//     }
//   }

//   Future<void> _showEditDialog(
//     BuildContext context,
//     WidgetRef ref,
//     String currentIntention,
//   ) async {
//     final controller = TextEditingController(text: currentIntention);

//     return showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Edit Intention'),
//         content: TextField(
//           controller: controller,
//           decoration: const InputDecoration(
//             hintText: 'Enter your intention...',
//             border: OutlineInputBorder(),
//           ),
//           maxLines: 3,
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.pop(context),
//             child: const Text('Cancel'),
//           ),
//           FilledButton(
//             onPressed: () async {
//               if (controller.text.isNotEmpty) {
//                 await ref
//                     .read(intentionControllerProvider.notifier)
//                     .setIntention(controller.text);
//                 if (context.mounted) {
//                   Navigator.pop(context);
//                 }
//               }
//             },
//             child: const Text(
//               'Save',
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<void> _showDeleteDialog(BuildContext context, WidgetRef ref) async {
//     return showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: Text(
//           'Delete Intention',
//           style: Theme.of(context).textTheme.bodySmall,
//         ),
//         content: const Text(
//           'Are you sure you want to delete this intention?',
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.pop(context),
//             child: Text(
//               'Cancel',
//               style: Theme.of(context).textTheme.bodySmall,
//             ),
//           ),
//           FilledButton(
//             onPressed: () async {
//               await ref
//                   .read(intentionControllerProvider.notifier)
//                   .clearIntention();
//               if (context.mounted) {
//                 Navigator.pop(context);
//               }
//             },
//             style: FilledButton.styleFrom(
//               backgroundColor: Theme.of(context).colorScheme.error,
//             ),
//             child: Text(
//               'Delete',
//               style: Theme.of(context).textTheme.bodySmall?.copyWith(
//                     color: Colors.white,
//                   ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class _CheckInCard extends ConsumerWidget {
//   final CheckInRoutine routine;

//   const _CheckInCard({
//     required this.routine,
//   });

//   IconData _getTypeIcon(String type) {
//     switch (type) {
//       case 'morning':
//         return Icons.wb_sunny;
//       case 'noon':
//         return Icons.wb_twilight;
//       case 'night':
//         return Icons.nightlight_round;
//       default:
//         return Icons.access_time;
//     }
//   }

//   Color _getCardColor(String type, ThemeData theme) {
//     switch (type) {
//       case 'morning':
//         return Color.fromARGB(255, 46, 44, 43);
//       case 'noon':
//         return Color.fromARGB(255, 37, 39, 41);
//       case 'night':
//         return Color.fromARGB(255, 48, 47, 50);
//       default:
//         return theme.cardColor;
//     }
//   }

//   Color _getIconColor(String type) {
//     switch (type) {
//       case 'morning':
//         return Color(0xFFFF9839);
//       case 'noon':
//         return Color(0xFF4A8FE7);
//       case 'night':
//         return Color(0xFF7E6DD8);
//       default:
//         return Colors.grey;
//     }
//   }

//   IconData _getActivityIcon(String type) {
//     switch (type) {
//       case 'journaling':
//         return Icons.edit_note;
//       case 'meditation':
//         return Icons.self_improvement;
//       case 'breathwork':
//         return Icons.air;
//       case 'affirmations':
//         return Icons.record_voice_over;
//       case 'moodTracking':
//         return Icons.mood;
//       default:
//         return Icons.category;
//     }
//   }

//   Future<List<Activity>> _loadActivities(WidgetRef ref) async {
//     final db = ref.read(journalDatabaseProvider);
//     final selectedIds = jsonDecode(routine.activities) as List;
//     final allActivities = await db.getAllActivities();

//     // Create a map for quick activity lookup
//     final activityMap = {for (var a in allActivities) a.id: a};

//     // Return activities in the same order as selectedIds
//     return selectedIds
//         .map((id) => activityMap[id])
//         .whereType<Activity>()
//         .toList();
//   }

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final theme = Theme.of(context);

//     return Card(
//       margin: const EdgeInsets.only(bottom: 16),
//       color: _getCardColor(routine.type, theme),
//       elevation: 1,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(16),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               children: [
//                 Icon(
//                   _getTypeIcon(routine.type),
//                   color: _getIconColor(routine.type),
//                 ),
//                 const SizedBox(width: 8),
//                 Text(
//                   routine.name,
//                   style: Theme.of(context).textTheme.bodyLarge?.copyWith(
//                         fontWeight: FontWeight.w600,
//                       ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 16),
//             Text(
//               'Create the ritual to improve your awareness in the ${routine.type}.',
//               style: theme.textTheme.bodySmall,
//             ),
//             const SizedBox(height: 16),
//             FutureBuilder<List<Activity>>(
//               future: _loadActivities(ref),
//               builder: (context, snapshot) {
//                 if (!snapshot.hasData) {
//                   return const SizedBox.shrink();
//                 }

//                 final activities = snapshot.data!;
//                 if (activities.isEmpty) {
//                   return const SizedBox.shrink();
//                 }

//                 return SingleChildScrollView(
//                   scrollDirection: Axis.horizontal,
//                   child: Row(
//                     children: activities.map((activity) {
//                       return Padding(
//                         padding: const EdgeInsets.only(right: 16),
//                         child: Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Icon(
//                               _getActivityIcon(activity.type),
//                               size: 20,
//                               color: _getIconColor(routine.type),
//                             ),
//                             const SizedBox(width: 4),
//                             Text(
//                               activity.name,
//                               style: theme.textTheme.bodySmall?.copyWith(
//                                 fontWeight: FontWeight.w600,
//                                 color: _getIconColor(routine.type),
//                               ),
//                             ),
//                           ],
//                         ),
//                       );
//                     }).toList(),
//                   ),
//                 );
//               },
//             ),
//             const SizedBox(height: 16),
//             SizedBox(
//               width: double.infinity,
//               child: TextButton.icon(
//                 icon: const Icon(Icons.edit),
//                 label: Text('Edit Check-In',
//                     style: Theme.of(context).textTheme.bodyMedium),
//                 onPressed: () {
//                   // Use go_router for navigation
//                   context.goNamed(
//                     RouteNames.checkinEditor,
//                     pathParameters: {
//                       'intentionId': routine.intentionId.toString(),
//                       'routineId': routine.id.toString(),
//                     },
//                   );
//                 },
//                 style: TextButton.styleFrom(
//                   foregroundColor: _getIconColor(routine.type),
//                   alignment: Alignment.centerLeft,
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
