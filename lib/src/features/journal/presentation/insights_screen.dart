// lib/src/features/journal/presentation/screens/insights_screen.dart
import 'dart:math';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/activities/mood_tracking/screens/moodtracking_activity.dart';
import 'package:mimi_app/src/features/journal/models/statistics.dart';
import 'package:intl/intl.dart';
import 'package:mimi_app/src/features/journal/presentation/widgets/weekly_entries_view.dart';
import 'package:mimi_app/src/features/journal/providers/statistics_providers.dart';
import 'package:table_calendar/table_calendar.dart';

class InsightsScreen extends ConsumerStatefulWidget {
  const InsightsScreen({super.key});

  @override
  ConsumerState<InsightsScreen> createState() => _InsightsScreenState();
}

class _InsightsScreenState extends ConsumerState<InsightsScreen> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Insights',
        ),
      ),
      body: Column(
        children: [
          const SizedBox(height: 16),
          // Custom Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Row(
              children: [
                _buildTab('Stats', 0),
                _buildTab('Entries', 1),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Tab Content
          Expanded(
            child: IndexedStack(
              index: _selectedTabIndex,
              children: [
                _StatsTab(),
                _EntriesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTabIndex = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(30),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: AppTypography.medium,
            ),
          ),
        ),
      ),
    );
  }
}

class _StatsTab extends ConsumerWidget {
  const _StatsTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final overviewStatsAsync = ref.watch(overviewStatsProvider);
    final weeklyMoodAsync = ref.watch(weeklyMoodEntriesProvider);
    final meditationStatsAsync = ref.watch(meditationStatsProvider);
    final streakStatsAsync = ref.watch(streakStatsProvider);
    final weeklyFocusAsync = ref.watch(weeklyFocusStatsProvider);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Streak Widget Section
        streakStatsAsync.when(
          data: (streak) => _StreakWidget(streak: streak),
          loading: () => const _LoadingCard(),
          error: (err, st) => _ErrorCard(message: err.toString()),
        ),
        const SizedBox(height: 16),

        // Overview Stats Section
        overviewStatsAsync.when(
          data: (stats) => _OverviewStatsCard(stats: stats),
          loading: () => const _LoadingCard(),
          error: (err, st) => _ErrorCard(message: err.toString()),
        ),
        const SizedBox(height: 16),

        // Mood Analysis Section
        weeklyMoodAsync.when(
          data: (moodEntries) => _MoodAnalysisCard(moodEntries: moodEntries),
          loading: () => const _LoadingCard(),
          error: (err, st) => _ErrorCard(message: err.toString()),
        ),
        const SizedBox(height: 16),

        // Focus Widget Section
        weeklyFocusAsync.when(
          data: (focusStats) => _FocusWidget(focusStats: focusStats),
          loading: () => const _LoadingCard(),
          error: (err, st) => _ErrorCard(message: err.toString()),
        ),
        const SizedBox(height: 16),

        // Meditation Section
        meditationStatsAsync.when(
          data: (stats) => Column(
            children: [
              _MeditationOverviewCard(stats: stats),
              const SizedBox(height: 16),
            ],
          ),
          loading: () => const _LoadingCard(),
          error: (err, st) => _ErrorCard(message: err.toString()),
        ),

        // Bottom spacing to avoid overlap with navigation bar
        const SizedBox(height: 20),
      ],
    );
  }
}

class _EntriesTab extends ConsumerWidget {
  const _EntriesTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const WeeklyEntriesView();
  }
}

class _StreakWidget extends StatelessWidget {
  final DailyStreak streak;

  const _StreakWidget({required this.streak});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Text(
              'Streak',
              style: TextStyle(
                fontSize: AppTypography.bodyLarge,
                fontWeight: AppTypography.bold,
                color: AppColors.textOnPrimary,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStreakItem(
                  'Current',
                  streak.currentStreak.toString(),
                  'Day',
                ),
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Transform.rotate(
                    angle: 0.785398, // 45 degrees in radians
                    child: Icon(
                      Icons.flash_on,
                      size: 32,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                _buildStreakItem(
                  'Best',
                  streak.longestStreak.toString(),
                  'Day',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakItem(String label, String value, String unit) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: AppTypography.titleSmall,
            color: AppColors.textPrimary.withValues(alpha: 0.8),
            fontWeight: AppTypography.medium,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: AppTypography.headlineMedium,
            fontWeight: AppTypography.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            fontSize: AppTypography.titleSmall,
            color: AppColors.textPrimary.withValues(alpha: 0.8),
            fontWeight: AppTypography.medium,
          ),
        ),
      ],
    );
  }
}

class _FocusWidget extends StatelessWidget {
  final List<FocusStats> focusStats;

  const _FocusWidget({required this.focusStats});

  @override
  Widget build(BuildContext context) {
    final maxMinutes = focusStats.isEmpty
        ? 0
        : focusStats
            .map((stat) => stat.totalMinutes)
            .reduce((a, b) => a > b ? a : b);

    final totalWeeklyMinutes =
        focusStats.fold<int>(0, (total, stat) => total + stat.totalMinutes);
    final totalSessions =
        focusStats.fold<int>(0, (total, stat) => total + stat.sessionsCount);

    return InsightCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Focus Sessions',
            style: TextStyle(
              fontSize: AppTypography.bodyLarge,
              fontWeight: AppTypography.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 24),

          // Weekly bar chart
          SizedBox(
            height: 120,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: focusStats.asMap().entries.map((entry) {
                final index = entry.key;
                final stat = entry.value;
                final dayName =
                    ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index];
                final barHeight = maxMinutes > 0
                    ? (stat.totalMinutes / maxMinutes) * 80
                    : 0.0;

                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          height:
                              barHeight + 10, // Minimum height for visibility
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: stat.totalMinutes > 0
                                  ? [
                                      AppColors.primary.withValues(alpha: 0.8),
                                      AppColors.primary
                                    ]
                                  : [AppColors.border, AppColors.border],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(4)),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          dayName,
                          style: TextStyle(
                            fontSize: AppTypography.labelMedium,
                            color: AppColors.textSecondary,
                            fontWeight: AppTypography.medium,
                          ),
                        ),
                        Text(
                          '${stat.totalMinutes}m',
                          style: TextStyle(
                            fontSize: AppTypography.labelSmall,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),

          const SizedBox(height: 24),

          // Summary stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildSummaryItem(
                  'Total Time', '${totalWeeklyMinutes}m', Icons.timer),
              _buildSummaryItem(
                  'Sessions', '$totalSessions', Icons.play_circle_outline),
              _buildSummaryItem('Avg/Day',
                  '${(totalWeeklyMinutes / 7).round()}m', Icons.trending_up),
            ],
          ),

          if (focusStats.any((stat) => stat.sessions.isNotEmpty)) ...[
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),

            // Recent sessions
            Text(
              'Recent Sessions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),

            ...focusStats
                .expand((stat) => stat.sessions)
                .take(3)
                .map((session) => _buildSessionItem(session)),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: AppColors.primary,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSessionItem(FocusSession session) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              session.taskDescription.isNotEmpty
                  ? session.taskDescription
                  : 'Focus Session',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '${session.totalMinutes}m',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}

class _LoadingCard extends StatelessWidget {
  const _LoadingCard();

  @override
  Widget build(BuildContext context) {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Center(child: CircularProgressIndicator()),
      ),
    );
  }
}

class _ErrorCard extends StatelessWidget {
  final String message;

  const _ErrorCard({required this.message});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    );
  }
}

// class _TotalMeditationCard extends StatelessWidget {
//   final List<MeditationStats> stats;

//   const _TotalMeditationCard({required this.stats});

//   @override
//   Widget build(BuildContext context) {
//     final totalMinutes = stats.fold<int>(0, (sum, stat) => sum + stat.minutes);
//     final averageMinutes = stats.isEmpty ? 0 : totalMinutes ~/ stats.length;
//     final longestSession =
//         stats.isEmpty ? 0 : stats.map((s) => s.minutes).reduce(max);

//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'Meditation Overview',
//               style: Theme.of(context).textTheme.titleLarge,
//             ),
//             const SizedBox(height: 24),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceAround,
//               children: [
//                 _StatItem(
//                   label: 'Total Time',
//                   value: '${totalMinutes}m',
//                   icon: Icons.timer,
//                 ),
//                 _StatItem(
//                   label: 'Daily Average',
//                   value: '${averageMinutes}m',
//                   icon: Icons.calendar_today,
//                 ),
//                 _StatItem(
//                   label: 'Longest',
//                   value: '${longestSession}m',
//                   icon: Icons.star,
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class _WeeklyMeditationChart extends StatelessWidget {
//   final List<MeditationStats> stats;

//   const _WeeklyMeditationChart({required this.stats});

//   @override
//   Widget build(BuildContext context) {
//     final weeklyData = _processWeeklyData(stats);

//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'Weekly Progress',
//               style: Theme.of(context).textTheme.titleLarge,
//             ),
//             const SizedBox(height: 24),
//             SizedBox(
//               height: 200,
//               child: BarChart(
//                 BarChartData(
//                   alignment: BarChartAlignment.spaceEvenly,
//                   maxY:
//                       weeklyData.map((d) => d.minutes).reduce(max).toDouble() +
//                           5,
//                   barGroups: weeklyData.asMap().entries.map((entry) {
//                     return BarChartGroupData(
//                       x: entry.key,
//                       barRods: [
//                         BarChartRodData(
//                           toY: entry.value.minutes.toDouble(),
//                           color: Theme.of(context).primaryColor,
//                           width: 20,
//                           borderRadius: const BorderRadius.vertical(
//                             top: Radius.circular(4),
//                           ),
//                         ),
//                       ],
//                     );
//                   }).toList(),
//                   titlesData: FlTitlesData(
//                     leftTitles: const AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                     rightTitles: const AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                     topTitles: const AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                     bottomTitles: AxisTitles(
//                       sideTitles: SideTitles(
//                         showTitles: true,
//                         getTitlesWidget: (value, meta) {
//                           final date = DateTime.now()
//                               .subtract(Duration(days: 6 - value.toInt()));
//                           return Padding(
//                             padding: const EdgeInsets.all(8.0),
//                             child: Text(DateFormat('E').format(date)),
//                           );
//                         },
//                       ),
//                     ),
//                   ),
//                   borderData: FlBorderData(show: false),
//                   gridData: const FlGridData(show: false),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   List<MeditationStats> _processWeeklyData(List<MeditationStats> stats) {
//     final lastWeek = List.generate(7, (index) {
//       final date = DateTime.now().subtract(Duration(days: 6 - index));
//       final dayStats = stats.where((stat) => isSameDay(stat.date, date));
//       final minutes = dayStats.fold<int>(0, (sum, stat) => sum + stat.minutes);
//       return MeditationStats(date: date, minutes: minutes);
//     });
//     return lastWeek;
//   }
// }

// class _DailyMeditationList extends StatelessWidget {
//   final List<MeditationStats> stats;

//   const _DailyMeditationList({required this.stats});

//   @override
//   Widget build(BuildContext context) {
//     final sortedStats = [...stats]..sort((a, b) => b.date.compareTo(a.date));

//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'Recent Sessions',
//               style: Theme.of(context).textTheme.titleLarge,
//             ),
//             const SizedBox(height: 16),
//             ...sortedStats.map((stat) => _DailyMeditationTile(stat: stat)),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class _DailyMeditationTile extends StatelessWidget {
//   final MeditationStats stat;

//   const _DailyMeditationTile({required this.stat});

//   @override
//   Widget build(BuildContext context) {
//     return ListTile(
//       leading: CircleAvatar(
//         child: Text('${stat.minutes}m'),
//       ),
//       title: Text(DateFormat.yMMMd().format(stat.date)),
//       subtitle: Text(DateFormat.jm().format(stat.date)),
//       trailing: Icon(
//         Icons.check_circle,
//         color: Theme.of(context).primaryColor,
//       ),
//     );
//   }
// }

// class _StatItem extends StatelessWidget {
//   final String label;
//   final String value;
//   final IconData icon;

//   const _StatItem({
//     required this.label,
//     required this.value,
//     required this.icon,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Icon(icon, size: 32, color: Theme.of(context).primaryColor),
//         const SizedBox(height: 8),
//         Text(
//           value,
//           style: Theme.of(context).textTheme.titleLarge,
//         ),
//         Text(
//           label,
//           style: Theme.of(context).textTheme.bodySmall,
//         ),
//       ],
//     );
//   }
// }

class InsightCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;

  const InsightCard({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: padding ?? EdgeInsets.zero,
        child: child,
      ),
    );
  }
}

class _MeditationOverviewCard extends StatelessWidget {
  final List<MeditationStats> stats;

  const _MeditationOverviewCard({required this.stats});

  @override
  Widget build(BuildContext context) {
    final totalMinutes = stats.fold<int>(0, (sum, stat) => sum + stat.minutes);
    final averageMinutes = stats.isEmpty ? 0 : totalMinutes ~/ stats.length;
    final longestSession =
        stats.isEmpty ? 0 : stats.map((s) => s.minutes).reduce(max);

    return InsightCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Meditation Overview',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                context,
                'Total Time',
                '${totalMinutes}m',
                Icons.timer,
              ),
              _buildStatItem(
                context,
                'Daily Average',
                '${averageMinutes}m',
                Icons.calendar_today,
              ),
              _buildStatItem(
                context,
                'Longest',
                '${longestSession}m',
                Icons.star,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 32, color: AppColors.primary),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}

class MoodCalendarCard extends StatefulWidget {
  final List<MoodEntry> moodEntries;
  final List<MoodOption> moodOptions;
  final double circleDiameter;

  const MoodCalendarCard({
    super.key,
    required this.moodEntries,
    required this.moodOptions,
    this.circleDiameter = 32.0,
  });

  @override
  State<MoodCalendarCard> createState() => _MoodCalendarCardState();
}

class _MoodCalendarCardState extends State<MoodCalendarCard> {
  late DateTime _focusedDay;
  late DateTime _selectedDay;

  @override
  void initState() {
    super.initState();
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
  }

  @override
  Widget build(BuildContext context) {
    return InsightCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Monthly Mood',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildCalendar(context),
          const SizedBox(height: 24),
          _buildMoodSummary(context),
        ],
      ),
    );
  }

  Widget _buildCalendarDay(
    BuildContext context,
    DateTime date, {
    bool isToday = false,
    bool isSelected = false,
    bool isOutside = false,
  }) {
    final moodEntry = widget.moodEntries
        .firstWhereOrNull((entry) => isSameDay(entry.date, date));

    final moodOption = moodEntry?.mood != null
        ? widget.moodOptions
            .firstWhereOrNull((option) => option.label == moodEntry?.mood)
        : null;

    final isFuture = date.isAfter(DateTime.now());

    // Show date for future days or days without mood
    if (moodOption == null) {
      return Center(
        child: Container(
          width: widget.circleDiameter,
          height: widget.circleDiameter,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: isToday || isSelected
                ? Border.all(
                    color: Theme.of(context).primaryColor,
                    width: isSelected ? 2 : 1,
                  )
                : null,
          ),
          child: Center(
            child: Text(
              '${date.day}',
              style: TextStyle(
                fontSize: 14,
                color: isOutside
                    ? AppColors.textSecondary.withValues(alpha: 0.5)
                    : isFuture
                        ? AppColors.textSecondary.withValues(alpha: 0.7)
                        : AppColors.textSecondary,
                fontWeight: isToday || isSelected ? FontWeight.bold : null,
              ),
            ),
          ),
        ),
      );
    }

    // Show colored circle for days with mood and handle tap
    return Center(
      child: InkWell(
        onTap: () {
          if (moodEntry != null) {
            showDialog(
              context: context,
              builder: (context) => MoodNoteDialog(
                moodEntry: moodEntry,
                moodOption: moodOption,
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(widget.circleDiameter / 2),
        child: Container(
          width: widget.circleDiameter,
          height: widget.circleDiameter,
          decoration: BoxDecoration(
            color: moodOption.color.withValues(alpha: isOutside ? 0.3 : 0.7),
            shape: BoxShape.circle,
            border: isToday || isSelected
                ? Border.all(
                    color: Theme.of(context).primaryColor,
                    width: isSelected ? 2 : 1,
                  )
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildCalendar(BuildContext context) {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, now.month - 3, 1);
    final lastDay = DateTime(now.year, now.month + 3, 0);

    return TableCalendar(
      firstDay: firstDay,
      lastDay: lastDay,
      focusedDay: _focusedDay,
      selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
      calendarFormat: CalendarFormat.month,
      availableCalendarFormats: const {
        CalendarFormat.month: 'Month',
      },
      onDaySelected: (selectedDay, focusedDay) {
        setState(() {
          _selectedDay = selectedDay;
          _focusedDay = focusedDay;
        });
      },
      onPageChanged: (focusedDay) {
        setState(() {
          _focusedDay = focusedDay;
        });
      },
      headerStyle: HeaderStyle(
        formatButtonVisible: false,
        titleCentered: true,
        titleTextStyle:
            const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        leftChevronIcon: Icon(
          Icons.chevron_left,
          color: AppColors.primary,
        ),
        rightChevronIcon: Icon(
          Icons.chevron_right,
          color: AppColors.primary,
        ),
      ),
      calendarStyle: CalendarStyle(
        outsideDaysVisible: true,
        defaultDecoration: const BoxDecoration(shape: BoxShape.circle),
        selectedDecoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(
            color: AppColors.primary,
            width: 2,
          ),
          shape: BoxShape.circle,
        ),
        todayDecoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(
            color: AppColors.primary,
            width: 1,
          ),
          shape: BoxShape.circle,
        ),
        // Hide all text styles, we handle them in builder
        defaultTextStyle: const TextStyle(fontSize: 0),
        weekendTextStyle: const TextStyle(fontSize: 0),
        selectedTextStyle: const TextStyle(fontSize: 0),
        todayTextStyle: const TextStyle(fontSize: 0),
        outsideTextStyle: const TextStyle(fontSize: 0),
      ),
      calendarBuilders: CalendarBuilders(
        defaultBuilder: (context, date, _) {
          return _buildCalendarDay(context, date);
        },
        todayBuilder: (context, date, _) {
          return _buildCalendarDay(context, date, isToday: true);
        },
        selectedBuilder: (context, date, _) {
          return _buildCalendarDay(context, date, isSelected: true);
        },
        outsideBuilder: (context, date, _) {
          return _buildCalendarDay(context, date, isOutside: true);
        },
      ),
    );
  }

  Widget _buildMoodSummary(BuildContext context) {
    // Calculate mood counts for the focused month
    final monthMoods = widget.moodEntries
        .where((entry) =>
            entry.date.year == _focusedDay.year &&
            entry.date.month == _focusedDay.month)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Monthly Summary',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 16,
          runSpacing: 12,
          children: widget.moodOptions.map((option) {
            final count =
                monthMoods.where((entry) => entry.mood == option.label).length;

            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: option.color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${option.label}: $count days',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }
}

class _OverviewStatsCard extends StatelessWidget {
  final OverviewStats stats;

  const _OverviewStatsCard({required this.stats});

  @override
  Widget build(BuildContext context) {
    return InsightCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Overview',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  context,
                  'Checkins',
                  '${stats.totalCheckins}',
                  Icons.check_circle_outline,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Streak',
                  '${stats.currentStreak}',
                  Icons.local_fire_department,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Meditation',
                  '${stats.totalMeditationMinutes}m',
                  Icons.self_improvement,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  context,
                  'Focus',
                  '${stats.totalFocusSessions}',
                  Icons.timer,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Journal',
                  '${stats.totalJournalEntries}',
                  Icons.book,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Gratitude',
                  '${stats.totalGratitudeEntries}',
                  Icons.favorite,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: AppColors.primary,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class _MoodAnalysisCard extends StatefulWidget {
  final List<MoodEntryWithTime> moodEntries;

  const _MoodAnalysisCard({required this.moodEntries});

  @override
  State<_MoodAnalysisCard> createState() => _MoodAnalysisCardState();
}

class _MoodAnalysisCardState extends State<_MoodAnalysisCard> {
  int _currentWeekOffset = 0;

  @override
  Widget build(BuildContext context) {
    final weekStart = DateTime.now()
        .subtract(Duration(days: DateTime.now().weekday - 1))
        .subtract(Duration(days: _currentWeekOffset * 7));

    return InsightCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Mood Analysis',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: _currentWeekOffset < 4
                        ? () {
                            setState(() {
                              _currentWeekOffset++;
                            });
                          }
                        : null,
                    icon: const Icon(Icons.chevron_left),
                    iconSize: 20,
                  ),
                  Text(
                    _currentWeekOffset == 0
                        ? 'This week'
                        : '${_currentWeekOffset + 1} weeks ago',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  IconButton(
                    onPressed: _currentWeekOffset > 0
                        ? () {
                            setState(() {
                              _currentWeekOffset--;
                            });
                          }
                        : null,
                    icon: const Icon(Icons.chevron_right),
                    iconSize: 20,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildWeeklyMoodChart(weekStart),
        ],
      ),
    );
  }

  Widget _buildWeeklyMoodChart(DateTime weekStart) {
    final weekDays =
        List.generate(7, (index) => weekStart.add(Duration(days: index)));

    return SizedBox(
      height: 120,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: weekDays.map((day) {
          final dayMoods = widget.moodEntries
              .where((entry) => isSameDay(entry.date, day))
              .toList();

          return _buildDayColumn(day, dayMoods);
        }).toList(),
      ),
    );
  }

  Widget _buildDayColumn(DateTime day, List<MoodEntryWithTime> dayMoods) {
    final dayName = DateFormat('E').format(day).substring(0, 3).toUpperCase();

    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (dayMoods.isNotEmpty) ...[
            SizedBox(
              height: 50,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: dayMoods.take(2).map((mood) {
                  final moodOption = getMoodOptionsWithSvg(context).firstWhere(
                    (option) => option.label == mood.label,
                    orElse: () => getMoodOptionsWithSvg(context).first,
                  );

                  return Container(
                    width: 20,
                    height: 20,
                    margin: const EdgeInsets.only(bottom: 2),
                    decoration: BoxDecoration(
                      color: moodOption.color,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        moodOption.emoji,
                        style: const TextStyle(fontSize: 10),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ] else ...[
            SizedBox(
              height: 50,
              child: Center(
                child: Icon(
                  Icons.remove,
                  color: AppColors.textSecondary,
                  size: 12,
                ),
              ),
            ),
          ],
          const SizedBox(height: 8),
          Text(
            dayName,
            style: TextStyle(
              fontSize: 9,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}

class MoodNoteDialog extends StatelessWidget {
  final MoodEntry moodEntry;
  final MoodOption moodOption;

  const MoodNoteDialog({
    super.key,
    required this.moodEntry,
    required this.moodOption,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: AppColors.surface,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: moodOption.color.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        DateFormat('MMMM d, yyyy').format(moodEntry.date),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${moodOption.emoji} ${moodOption.label}',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Text(
                        DateFormat.jm().format(moodEntry.date),
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (moodEntry.note != null && moodEntry.note!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              Text(
                'Note',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                moodEntry.note!,
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
            const SizedBox(height: 24),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
