import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/journal/providers/statistics_providers.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';

class JournalHistoryScreen extends ConsumerStatefulWidget {
  const JournalHistoryScreen({super.key});

  @override
  ConsumerState<JournalHistoryScreen> createState() =>
      _JournalHistoryScreenState();
}

class _JournalHistoryScreenState extends ConsumerState<JournalHistoryScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  Widget build(BuildContext context) {
    final entriesAsync = ref.watch(journalEntriesForMonthProvider(_focusedDay));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Journal History'),
      ),
      body: Column(
        children: [
          entriesAsync.when(
            data: (entries) => _buildCalendar(context, entries),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
          ),
          if (_selectedDay != null)
            Expanded(
              child: entriesAsync.when(
                data: (entries) => _EntriesForDayWithRoutine(
                  entries: entries
                      .where((entry) => isSameDay(entry.date, _selectedDay!))
                      .toList(),
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(child: Text('Error: $error')),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCalendar(BuildContext context, List<JournalEntry> entries) {
    return TableCalendar<JournalEntry>(
      firstDay: DateTime.utc(2024, 1, 1),
      lastDay: DateTime.now(),
      focusedDay: _focusedDay,
      selectedDayPredicate: (day) =>
          _selectedDay != null && isSameDay(_selectedDay!, day),
      eventLoader: (day) {
        return entries.where((entry) => isSameDay(entry.date, day)).toList();
      },
      onDaySelected: (selectedDay, focusedDay) {
        setState(() {
          _selectedDay = selectedDay;
          _focusedDay = focusedDay;
        });
      },
      onPageChanged: (focusedDay) {
        setState(() {
          _focusedDay = focusedDay;
        });
      },
      calendarStyle: CalendarStyle(
        markersMaxCount: 1,
        markerDecoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

class _EntriesForDayWithRoutine extends ConsumerWidget {
  final List<JournalEntry> entries;

  const _EntriesForDayWithRoutine({required this.entries});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (entries.isEmpty) {
      return const Center(
        child: Text('No entries for this day'),
      );
    }
    return ListView.builder(
      itemCount: entries.length,
      itemBuilder: (context, index) {
        final entry = entries[index];
        return FutureBuilder<CheckInRoutine?>(
          future:
              ref.read(journalDatabaseProvider).getRoutineById(entry.routineId),
          builder: (context, snapshot) {
            final routineName = snapshot.data?.name ?? 'Check-In';
            final routineType = snapshot.data?.type ?? 'morning';
            return _JournalEntryCardWithRoutine(
                entry: entry,
                routineName: routineName,
                routineType: routineType);
          },
        );
      },
    );
  }
}

class _JournalEntryCardWithRoutine extends ConsumerWidget {
  final JournalEntry entry;
  final String routineName;
  final String routineType;

  const _JournalEntryCardWithRoutine({
    required this.entry,
    required this.routineName,
    required this.routineType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Carefully parse the responses to handle different formats
    Map<String, dynamic> responses;
    try {
      // First try to parse the raw JSON string
      responses = json.decode(entry.responses) as Map<String, dynamic>;

      // If we have a nested 'responses' key, use that instead
      if (responses.containsKey('responses')) {
        final nestedResponses = responses['responses'];
        if (nestedResponses is Map<String, dynamic>) {
          responses = nestedResponses;
        } else if (nestedResponses is String) {
          // Handle double-encoded responses
          try {
            responses = json.decode(nestedResponses) as Map<String, dynamic>;
          } catch (e) {
            // Keep original if decode fails
          }
        }
      }
    } catch (e) {
      print('Error decoding responses: $e');
      responses = {}; // Fallback to empty map
    }

    return FutureBuilder<CheckInRoutine?>(
      future: _getRoutine(entry.routineId, ref),
      builder: (context, routineSnapshot) {
        if (!routineSnapshot.hasData) {
          return const Card(
            margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }
        final routine = routineSnapshot.data!;
        final activityIds = List<int>.from(jsonDecode(routine.activities));
        return FutureBuilder<List<Activity>>(
          future: _getActivities(activityIds, ref),
          builder: (context, activitiesSnapshot) {
            if (!activitiesSnapshot.hasData) {
              return const Card(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(child: CircularProgressIndicator()),
                ),
              );
            }
            final activities = activitiesSnapshot.data!;
            final journalingActivity = activities.firstWhere(
              (a) => a.type == 'journaling',
              orElse: () => Activity(
                id: -1,
                type: 'journaling',
                name: 'Journaling',
                config: ActivityConfig.journaling(),
              ),
            );
            if (journalingActivity.id == -1) {
              return const SizedBox.shrink();
            }
            return FutureBuilder<ActivityConfig?>(
              future:
                  _getJournalingConfig(routine.id, journalingActivity.id, ref),
              builder: (context, configSnapshot) {
                if (!configSnapshot.hasData) {
                  return const Card(
                    margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                  );
                }
                final config = configSnapshot.data;
                final prompts = config?.maybeWhen(
                      journaling: (prompts, _) => prompts,
                      orElse: () => [],
                    ) ??
                    [];

                // If no prompts found but we have responses, create generic prompt labels
                final effectivePrompts = prompts.isNotEmpty
                    ? prompts
                    : List.generate(
                        responses.length, (i) => 'Journal Entry ${i + 1}');

                // Debug: Print the responses to see what keys are actually stored
                print('==== JOURNAL HISTORY DEBUG ====');
                print('Entry ID: ${entry.id}');
                print('Routine ID: ${entry.routineId}');
                print('Date: ${entry.date}');
                print('Raw Responses JSON: ${entry.responses}');
                print('Decoded Responses: $responses');
                print('Response Keys: ${responses.keys.toList()}');
                print('Prompts Count: ${prompts.length}');
                print('Effective Prompts Count: ${effectivePrompts.length}');
                effectivePrompts.asMap().forEach((i, prompt) {
                  print('Prompt $i: $prompt');
                  print('Response for prompt_$i: ${responses['prompt_$i']}');
                });
                print('================================');

                return Card(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              routineName,
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const Spacer(),
                            Text(
                              DateFormat.jm().format(entry.date),
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete_outline),
                              onPressed: () =>
                                  _showDeleteConfirmation(context, ref),
                              color: Colors.red,
                            ),
                          ],
                        ),
                        if (entry.mood != null) ...[
                          const SizedBox(height: 8),
                          Text('Mood: ${entry.mood}'),
                        ],
                        const SizedBox(height: 16),
                        if (effectivePrompts.isEmpty) ...[
                          // Handle case when there are no prompts configured
                          Text(
                            'No journal prompts configured for this check-in.',
                            style: TextStyle(
                              color: Colors.grey,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ] else ...[
                          // Display prompts with responses
                          ...List.generate(effectivePrompts.length, (i) {
                            final prompt = effectivePrompts[i];

                            // First try with the exact matching key
                            final exactKey = 'prompt_$i';
                            String answer = '';

                            if (responses.containsKey(exactKey)) {
                              answer = responses[exactKey] ?? '';
                              print(
                                  'Found exact match for prompt $i with key: $exactKey = $answer');
                            } else {
                              // Try all possible key formats
                              final possibleKeys = [
                                'prompt$i', // No underscore
                                'response_$i', // Alternative naming
                                'response$i', // Alternative without underscore
                                'answer_$i', // Another alternative
                                'answer$i', // Without underscore
                                '$i', // Just the index
                                'p$i', // Short form
                                'q$i', // Question format
                                'question_$i', // Full question format
                                'journal_$i', // Journal prefix
                              ];

                              // Try each possible key format
                              for (final key in possibleKeys) {
                                if (responses.containsKey(key)) {
                                  answer = responses[key] ?? '';
                                  print(
                                      'Found match for prompt $i with key: $key = $answer');
                                  break;
                                }
                              }
                            }

                            // If still not found and it's the first prompt, check if there's any response at all
                            if (answer.isEmpty &&
                                i == 0 &&
                                responses.isNotEmpty) {
                              final firstKey = responses.keys.first;
                              if (responses[firstKey]?.isNotEmpty == true) {
                                answer = responses[firstKey]!;
                                print(
                                    'Using first available response for prompt 0: $firstKey = $answer');
                              }
                            }

                            // If still empty, check if there's another entry with this index in the name
                            if (answer.isEmpty) {
                              final indexKeys = responses.keys
                                  .where((key) =>
                                      key.contains('$i') ||
                                      key.contains('${i + 1}'))
                                  .toList();

                              for (final key in indexKeys) {
                                final value = responses[key];
                                if (value != null && value.isNotEmpty) {
                                  answer = value;
                                  print(
                                      'Found response with index in key: $key = $answer');
                                  break;
                                }
                              }
                            }

                            // If still empty, show "No answer provided"
                            if (answer.isEmpty) {
                              answer = 'No answer provided';
                            }

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    prompt,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    answer,
                                    style: TextStyle(
                                      color: answer == 'No answer provided'
                                          ? Colors.grey
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyLarge
                                              ?.color,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }),
                        ],
                      ],
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Future<CheckInRoutine?> _getRoutine(int routineId, WidgetRef ref) async {
    final db = ref.read(journalDatabaseProvider);
    return db.getRoutineById(routineId);
  }

  Future<List<Activity>> _getActivities(
      List<int> activityIds, WidgetRef ref) async {
    final db = ref.read(journalDatabaseProvider);
    final allActivities = await db.getAllActivities();
    return activityIds
        .map((id) => allActivities.firstWhere((a) => a.id == id))
        .toList();
  }

  Future<ActivityConfig?> _getJournalingConfig(
      int routineId, int activityId, WidgetRef ref) async {
    final db = ref.read(journalDatabaseProvider);
    return db.getRoutineActivityConfig(routineId, activityId);
  }

  Future<void> _showDeleteConfirmation(
      BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Entry'),
        content: const Text(
            'Are you sure you want to delete this journal entry? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      try {
        final db = ref.read(journalDatabaseProvider);
        await db.deleteEntriesForRoutine(entry.routineId);
        ref.invalidate(journalEntriesForMonthProvider);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Journal entry deleted')),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting entry: $e')),
          );
        }
      }
    }
  }
}

bool isSameDay(DateTime a, DateTime b) {
  return a.year == b.year && a.month == b.month && a.day == b.day;
}
