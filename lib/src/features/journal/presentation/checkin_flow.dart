// lib/src/features/journal/screens/checkin_flow_screen.dart
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/activities/affirmations/screen/affirmations_activity.dart';
import 'package:mimi_app/src/features/activities/breathwork/screens/breathwork_activity.dart';
import 'package:mimi_app/src/features/activities/journaling/screens/journal_activity.dart';
import 'package:mimi_app/src/features/activities/meditation/screens/meditation_activity.dart';
import 'package:mimi_app/src/features/activities/breathwork/providers/breathwork_provider.dart';
import 'package:mimi_app/src/features/activities/mood_tracking/screens/moodtracking_activity.dart';
import 'package:mimi_app/src/features/activities/gratitude/screens/gratitude_activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/base_activity.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'dart:convert';

import 'package:mimi_app/src/features/activities/shared/models/activity_step.dart';
import 'package:mimi_app/src/features/activities/shared/widgets/step_indicator.dart';
import 'package:mimi_app/src/features/navigation/providers/navigation_provider.dart';
import 'package:drift/drift.dart' as drift;
import 'package:mimi_app/src/features/activities/shared/providers/activity_data_provider.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';

class CheckinFlowScreen extends ConsumerStatefulWidget {
  final int routineId;

  const CheckinFlowScreen({
    super.key,
    required this.routineId,
  });

  @override
  ConsumerState<CheckinFlowScreen> createState() => _CheckinFlowScreenState();
}

class _CheckinFlowScreenState extends ConsumerState<CheckinFlowScreen> {
  List<Activity> _activities = [];
  int _currentActivityIndex = 0;
  bool _isLoading = true;
  String _routineName = '';
  final Map<int, bool> _completedActivities = {};

  // New variables for step tracking
  int _totalSteps = 0;
  int _currentGlobalStep = 0;
  late List<ActivityStep> _stepMap;

  // Key to access current activity's state
  final _currentActivityKey = GlobalKey<BaseActivityState>();

  @override
  void initState() {
    super.initState();
    _loadCheckinData();
  }

  Future<void> _loadCheckinData() async {
    setState(() => _isLoading = true);
    try {
      final db = ref.read(journalDatabaseProvider);

      final routine = await db.getRoutineById(widget.routineId);
      if (routine == null) throw Exception('Routine not found');

      _routineName = routine.name;

      // Get all activities for lookup by name or ID
      final allActivities = await db.getAllActivities();

      // Handle activities data from routine
      dynamic activitiesData = routine.activities;
      List<dynamic> decodedActivities = [];

      // Parse the activities data
      if (activitiesData is String && activitiesData.isNotEmpty) {
        try {
          // Try to decode as JSON
          final decoded = jsonDecode(activitiesData);
          if (decoded is List) {
            decodedActivities = decoded;
          } else if (decoded is Map) {
            decodedActivities = decoded.values.toList();
          }
        } catch (e) {
          // If JSON decode fails, try to handle as a comma-separated string
          decodedActivities = activitiesData
              .toString()
              .split(',')
              .map((e) => e.trim())
              .where((s) => s.isNotEmpty)
              .toList();
        }
      } else if (activitiesData is List) {
        // If it's already a list
        decodedActivities = activitiesData;
      }

      if (decodedActivities.isEmpty) {
        throw Exception('No activities found in this routine');
      }

      // Process each activity - could be ID (int) or name (String)
      _activities = [];

      for (final item in decodedActivities) {
        if (item is int || (item is String && int.tryParse(item) != null)) {
          // It's an ID (or string that can be parsed as int)
          final id = item is int ? item : int.parse(item);
          final activity = allActivities.firstWhere(
            (a) => a.id == id,
            orElse: () {
              debugPrint('Activity with id $id not found');
              return Activity(
                id: -1,
                type: '',
                name: '',
                config: ActivityConfig.journaling(),
              );
            },
          );
          if (activity.id != -1) {
            _activities.add(activity);
            debugPrint(
                'Loaded activity by ID: ${activity.id} (${activity.type})');
          }
        } else if (item is String) {
          // It's a name - find by name or type
          final matchingActivities = allActivities
              .where((a) =>
                  a.name.toLowerCase() == item.toLowerCase() ||
                  a.type.toLowerCase() == item.toLowerCase())
              .toList();

          if (matchingActivities.isNotEmpty) {
            _activities.add(matchingActivities.first);
            debugPrint(
                'Loaded activity by name: ${matchingActivities.first.id} (${matchingActivities.first.type})');
          } else {
            debugPrint('Warning: Activity with name "$item" not found');
            // Don't throw exception here, just log and continue
          }
        }
      }

      if (_activities.isEmpty) {
        throw Exception('No valid activities found in this routine');
      }

      await _calculateSteps();

      // Initialize to the first step after the widget is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _stepMap.isNotEmpty) {
          // Always start at global step 0
          final currentStep = _stepMap[_currentGlobalStep];
          print('=== CHECKIN FLOW INITIALIZATION ===');
          print('Total steps: $_totalSteps');
          print('Starting at global step: $_currentGlobalStep');
          print(
              'Current step: globalIndex=${currentStep.globalIndex}, localIndex=${currentStep.localIndex}, activityIndex=${currentStep.activityIndex}');

          // Set the current activity index to match the current step
          setState(() {
            _currentActivityIndex = currentStep.activityIndex;
          });

          // Initialize the current activity to the correct local step
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final currentState =
                _currentActivityKey.currentState as BaseActivityState?;
            if (currentState != null) {
              print('=== INITIALIZATION DEBUG ===');
              print(
                  'Activity Type: ${_activities[_currentActivityIndex].type}');
              print(
                  'Activity Name: ${_activities[_currentActivityIndex].name}');
              print('Current Global Step: $_currentGlobalStep');
              print('Current Activity Index: $_currentActivityIndex');
              print('Current Step Local Index: ${currentStep.localIndex}');
              print('Total Steps in Activity: ${currentState.getTotalSteps()}');
              print(
                  'Current Step in Activity: ${currentState.getCurrentStep()}');
              print(
                  'Calling moveToStep(${currentStep.localIndex}) on activity state for initialization');

              final result = currentState.moveToStep(currentStep.localIndex);

              print('moveToStep returned: $result');
              print(
                  'After moveToStep - Activity Current Step: ${currentState.getCurrentStep()}');
              print(
                  'After initialization - Current global step: $_currentGlobalStep');
              print('=== END INITIALIZATION DEBUG ===');

              // IMPORTANT: Do not call _moveToNextStep() here automatically
              // The activity should be positioned at the correct step and stay there
              // until the user explicitly navigates
            }
          });
          print('=== END INITIALIZATION ===');
        }
      });
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Error loading activities: $e',
        );
        // Navigate back if there's an error
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
  // Future<void> _loadCheckinData() async {
  //   setState(() => _isLoading = true);
  //   try {
  //     final db = ref.read(journalDatabaseProvider);

  //     final routine = await db.getRoutineById(widget.routineId);
  //     if (routine == null) throw Exception('Routine not found');

  //     _routineName = routine.name;

  //     final activityIds = List<int>.from(jsonDecode(routine.activities));
  //     final allActivities = await db.getAllActivities();

  //     _activities = activityIds

  Future<void> _calculateSteps() async {
    _totalSteps = 0;
    _stepMap = [];

    for (var activityIndex = 0;
        activityIndex < _activities.length;
        activityIndex++) {
      final activity = _activities[activityIndex];
      // Normalize activity type to handle various formats
      final normalizedType = activity.type.toLowerCase().replaceAll(' ', '_');

      switch (normalizedType) {
        case 'journaling':
        case 'journal':
          // Get steps from activity configuration
          final prompts = await _getActivityPrompts(activity);
          final steps = prompts.length;
          for (var localStep = 0; localStep < steps; localStep++) {
            _stepMap.add(ActivityStep(
              globalIndex: _totalSteps + localStep,
              localIndex: localStep,
              activityIndex: activityIndex,
            ));
          }
          _totalSteps += steps;
          break;
        case 'moodtracking':
        case 'mood_tracking':
          // Mood tracking has 2 steps: mood selection and note
          for (var localStep = 0; localStep < 2; localStep++) {
            _stepMap.add(ActivityStep(
              globalIndex: _totalSteps + localStep,
              localIndex: localStep,
              activityIndex: activityIndex,
            ));
          }
          _totalSteps += 2;
          break;
        default:
          // Single step activities
          _stepMap.add(ActivityStep(
            globalIndex: _totalSteps,
            localIndex: 0,
            activityIndex: activityIndex,
          ));
          _totalSteps++;
      }
    }
  }

  Future<List<String>> _getActivityPrompts(Activity activity) async {
    // First try to get routine-specific configuration
    final db = ref.read(journalDatabaseProvider);
    final routineSpecificConfig = await db.getRoutineActivityConfig(
      widget.routineId,
      activity.id,
    );

    final config = routineSpecificConfig ?? activity.config;
    return config.maybeWhen(
      journaling: (prompts, _) => prompts,
      orElse: () => [],
    );
  }

  Widget _buildActivityWidget(Activity activity) {
    // Normalize activity type to handle various formats
    final normalizedType = activity.type.toLowerCase().replaceAll(' ', '_');

    switch (normalizedType) {
      case 'journaling':
      case 'journal':
        return JournalActivityScreen(
          key: _currentActivityKey,
          activity: activity,
          routineId: widget.routineId,
          onComplete: () => _markActivityComplete(activity.id),
        );
      case 'meditation':
        return MeditationActivity(
          key: _currentActivityKey,
          activity: activity,
          routineId: widget.routineId,
          onComplete: () => _markActivityComplete(activity.id),
        );
      case 'breathwork':
        return BreathworkActivity(
          key: _currentActivityKey,
          activity: activity,
          routineId: widget.routineId,
          onComplete: () => _markActivityComplete(activity.id),
        );
      case 'affirmations':
        return AffirmationsActivity(
          key: _currentActivityKey,
          activity: activity,
          routineId: widget.routineId,
          onComplete: () => _markActivityComplete(activity.id),
        );
      case 'moodtracking':
      case 'mood_tracking':
        return MoodTrackingActivity(
          key: _currentActivityKey,
          activity: activity,
          routineId: widget.routineId,
          onComplete: () => _markActivityComplete(activity.id),
        );
      case 'gratitude':
        return GratitudeActivity(
          key: _currentActivityKey,
          activity: activity,
          routineId: widget.routineId,
          onComplete: () => _markActivityComplete(activity.id),
        );
      default:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'Activity type "${activity.type}" not supported',
                style: TextStyle(fontSize: 16, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'Activity: ${activity.name}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // Refresh the checkin flow to reload activities
                  setState(() {
                    _isLoading = true;
                  });
                  _loadCheckinData();
                },
                child: Text('Refresh'),
              ),
            ],
          ),
        );
    }
  }

  void _markActivityComplete(int activityId) {
    print('=== _markActivityComplete called ===');
    print('Activity ID: $activityId');
    print('Current global step: $_currentGlobalStep');
    print('Current activity index: $_currentActivityIndex');
    print('Current activity type: ${_activities[_currentActivityIndex].type}');

    // Guard against automatic completion during initialization
    // For journaling activities, only allow completion if we're at the last step of the activity
    final currentActivity = _activities[_currentActivityIndex];
    print('Guard check - Activity type: ${currentActivity.type}');
    if (currentActivity.type == 'journaling' ||
        currentActivity.type == 'journal') {
      final currentState =
          _currentActivityKey.currentState as BaseActivityState?;
      print('Guard check - Current state: $currentState');
      if (currentState != null) {
        final currentStep = currentState.getCurrentStep();
        final totalSteps = currentState.getTotalSteps();
        print(
            'Guard check - Journaling activity - Current step: $currentStep, Total steps: $totalSteps');

        // Only allow completion if we're at the last step
        if (currentStep < totalSteps - 1) {
          print(
              'BLOCKING automatic completion - journaling activity not at last step');
          return;
        } else {
          print('ALLOWING completion - journaling activity at last step');
        }
      } else {
        print(
            'BLOCKING automatic completion - journaling activity state is null');
        return;
      }
    }

    setState(() {
      _completedActivities[activityId] = true;
      print(
          'Marked activity $activityId as complete, calling _moveToNextStep()');
      _moveToNextStep();
    });

    print('=== _markActivityComplete end ===');
  }

  bool _moveToNextStep() {
    print('=== _moveToNextStep called ===');
    print('Current global step: $_currentGlobalStep');
    print('Total steps: $_totalSteps');

    if (_currentGlobalStep >= _totalSteps - 1) {
      print('Completing checkin - at last step');
      _completeCheckin();
      return false;
    }

    // Don't auto-save for journal activities as it causes them to skip
    // Journal activities will save their responses when the user manually navigates
    // or when the activity is completed

    final nextStep = _stepMap[_currentGlobalStep + 1];
    final currentState = _currentActivityKey.currentState as BaseActivityState;

    print(
        'Next step: globalIndex=${nextStep.globalIndex}, localIndex=${nextStep.localIndex}, activityIndex=${nextStep.activityIndex}');
    print('Current activity index: $_currentActivityIndex');

    // Don't call onNextStep automatically as it causes some activities to auto-complete
    // Activities should handle their own saving logic when appropriate

    if (nextStep.activityIndex != _currentActivityIndex) {
      print('Moving to different activity');
      setState(() {
        _currentActivityIndex = nextStep.activityIndex;
        _currentGlobalStep++;
      });
    } else if (currentState.moveToStep(nextStep.localIndex)) {
      print('Moving to next step within same activity');
      setState(() => _currentGlobalStep++);
    }

    print('After move - Current global step: $_currentGlobalStep');
    print('=== _moveToNextStep end ===');

    return true;
  }

  Future<void> _completeCheckin() async {
    try {
      // Save data for the current activity before completing checkin
      final currentState =
          _currentActivityKey.currentState as BaseActivityState?;
      currentState?.onNextStep();

      final db = ref.read(journalDatabaseProvider);
      // Gather all journaling answers for this routine and date
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      final routine = await db.getRoutineById(widget.routineId);
      final activityIds = List<int>.from(jsonDecode(routine!.activities));
      final allActivities = await db.getAllActivities();
      final journalingActivities = activityIds
          .map((id) => allActivities.firstWhere((a) => a.id == id))
          .where((a) => a.type == 'journaling')
          .toList();

      // Log the journaling activities we found
      print('Found ${journalingActivities.length} journaling activities');
      for (var activity in journalingActivities) {
        print('Activity ID: ${activity.id}, Name: ${activity.name}');
      }

      final Map<String, dynamic> allResponses = {};

      // For each journaling activity, get the most recently updated record
      for (final activity in journalingActivities) {
        final records =
            await db.getActivityRecordsForDate(activity.id, startOfDay);
        print(
            'Regular date range: Found ${records.length} records for activity ${activity.id}');

        // Sort records by date (most recent first)
        if (records.isNotEmpty) {
          records.sort((a, b) => b.date.compareTo(a.date));

          // Use the most recent record
          final record = records.first;
          print(
              'Using most recent record ID: ${record.id}, Date: ${record.date}');

          try {
            final dataMap = record.data;
            print('Data map: $dataMap');

            if (dataMap.containsKey('responses')) {
              final responses = dataMap['responses'];
              if (responses is Map<String, dynamic>) {
                print('==== CHECKIN FLOW SAVE DEBUG ====');
                print('Activity ID: ${activity.id}');
                print('Activity Name: ${activity.name}');
                print('Responses (map): $responses');
                print('Response Keys: ${responses.keys.toList()}');

                // Check each prompt index - increase max to 20 to catch all prompts
                for (int i = 0; i < 20; i++) {
                  final promptKey = 'prompt_$i';
                  if (responses.containsKey(promptKey)) {
                    final value = responses[promptKey]?.toString() ?? '';
                    print(
                        'Found response with index in key: $promptKey = $value');

                    // Get the actual prompts to match the index
                    final prompts = await _getActivityPrompts(activity);
                    print('Activity has ${prompts.length} prompts');
                    if (i < prompts.length) {
                      print(
                          'Found exact match for prompt $i with key: $promptKey = $value');
                      allResponses[promptKey] = value;
                    } else {
                      // Store it anyway - it might be a valid prompt
                      print(
                          'Storing response outside of known prompt range: $promptKey = $value');
                      allResponses[promptKey] = value;
                    }
                  }
                }

                print('================================');
              }
            }
          } catch (e) {
            print('Error processing record: $e');
          }
        }
      }

      // If no records were found in the exact date range, try a broader range
      if (allResponses.isEmpty) {
        print('==== FALLBACK RESPONSE COLLECTION ====');
        // Get all journaling activities for today
        for (final activity in journalingActivities) {
          // Try a broader date range
          final broadStartOfDay =
              startOfDay.subtract(const Duration(hours: 12));
          final broadEndOfDay = endOfDay.add(const Duration(hours: 12));

          final records = await db.getActivityRecordsForDateRange(
              activity.id, broadStartOfDay, broadEndOfDay);

          print(
              'Found ${records.length} records for activity ${activity.id} in broader date range');

          // Try a direct approach to get the most recent record
          if (records.isNotEmpty) {
            // Sort by date descending to get most recent first
            records.sort((a, b) => b.date.compareTo(a.date));

            // Use the most recent record
            final record = records.first;
            print(
                'Record ID: ${record.id}, Activity ID: ${record.activityId}, Date: ${record.date}');
            print('Data map: ${record.data}');

            if (record.data.containsKey('responses')) {
              final responses = record.data['responses'];
              if (responses is Map<String, dynamic>) {
                // Add all prompt responses
                for (final entry in responses.entries) {
                  final value = entry.value.toString();
                  allResponses[entry.key] = value;
                  print('Found ${entry.key} = "$value"');
                }
                print(
                    'Added responses from record ${record.id}: ${responses.keys.toList()}');
              }
            }
          }
        }
        print('================================');
      }

      // Debug: Print the final merged responses
      print('==== FINAL MERGED RESPONSES ====');
      print('All Responses: $allResponses');
      print('All Response Keys: ${allResponses.keys.toList()}');

      // Verify that we have at least some content in the responses
      bool hasActualContent = false;
      for (final entry in allResponses.entries) {
        final key = entry.key;
        final value = entry.value;
        if (value is String && value.trim().isNotEmpty) {
          hasActualContent = true;
          print('Found non-empty content for $key: "$value"');
        }
      }

      // If we found no responses with content, fetch from each activity
      if (!hasActualContent) {
        print(
            'No actual content found in responses, checking activity records directly');

        // Try to retrieve the most recently updated journaling activity response
        for (final activity in journalingActivities) {
          try {
            // Get direct activity data
            final activityData = await ref
                .read(activityDataNotifierProvider(activity.id, DateTime.now())
                    .notifier)
                .build(activity.id, DateTime.now());

            if (activityData != null) {
              final data = activityData.data;
              print('Direct activity data for activity ${activity.id}: $data');

              if (data.containsKey('responses')) {
                final responses = data['responses'];
                if (responses is Map<String, dynamic>) {
                  // Add all responses to our collection
                  for (final entry in responses.entries) {
                    final value = entry.value.toString();
                    allResponses[entry.key] = value;
                    if (value.trim().isNotEmpty) {
                      hasActualContent = true;
                      print(
                          'Found non-empty direct content: ${entry.key} = "$value"');
                    }
                  }
                }
              }
            }
          } catch (e) {
            print('Error accessing activity data: $e');
          }
        }
      }

      print('Final response map: $allResponses');
      print('================================');

      // If somehow we still have no responses with content, create a placeholder
      if (!hasActualContent && allResponses.isEmpty) {
        print('WARNING: No responses found, creating a placeholder');
        allResponses['system_note'] =
            'Journal entry recorded with no responses';
      }

      // Validate and sanitize the responses before encoding
      final sanitizedResponses = <String, dynamic>{};
      for (final entry in allResponses.entries) {
        final key = entry.key;
        final value = entry.value;

        // Ensure key is not empty and value is properly formatted
        if (key.isNotEmpty && value != null) {
          sanitizedResponses[key] = value.toString();
        }
      }

      // Ensure we have at least an empty object for JSON encoding
      if (sanitizedResponses.isEmpty) {
        sanitizedResponses['completed'] = 'true';
        sanitizedResponses['timestamp'] = DateTime.now().toIso8601String();
      }

      print('Sanitized responses for database: $sanitizedResponses');

      // Validate JSON encoding before saving to database
      String responsesJson;
      try {
        responsesJson = json.encode(sanitizedResponses);
        print('Successfully encoded responses to JSON: $responsesJson');
      } catch (e) {
        print('Error encoding responses to JSON: $e');
        // Fallback to minimal valid JSON
        responsesJson = json.encode({
          'error': 'Failed to encode responses',
          'timestamp': DateTime.now().toIso8601String(),
          'completed': 'true'
        });
      }

      // Validate routine ID exists before saving
      final currentRoutine = await db.getRoutineById(widget.routineId);
      if (currentRoutine == null) {
        throw Exception('Routine with ID ${widget.routineId} not found');
      }

      print('Creating journal entry with routine ID: ${widget.routineId}');
      print('Responses JSON length: ${responsesJson.length}');

      // Save the journal entry with all journaling answers
      await db.createEntry(
        JournalEntriesCompanion.insert(
          routineId: widget.routineId,
          date: DateTime.now(),
          responses: responsesJson,
          status: 'completed',
          completedAt: drift.Value(DateTime.now()),
        ),
      );

      print('Successfully created journal entry');

      // Update the routine lastUpdated field
      await db.updateRoutine(
        CheckInRoutinesCompanion(
          id: drift.Value(widget.routineId),
          intentionId: drift.Value(currentRoutine.intentionId),
          type: drift.Value(currentRoutine.type),
          name: drift.Value(currentRoutine.name),
          enabled: drift.Value(currentRoutine.enabled),
          activities: drift.Value(currentRoutine.activities),
          scheduledTime: drift.Value(currentRoutine.scheduledTime),
          notificationEnabled: drift.Value(currentRoutine.notificationEnabled),
          lastUpdated: drift.Value(DateTime.now()),
        ),
      );

      print('Successfully updated routine');

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: 'Check-in completed!',
        );

        // Navigate to home screen and update bottom navigation
        final navItems = ref.read(navigationItemsProvider);
        final homeIndex =
            navItems.indexWhere((item) => item.path == RouteNames.home);
        if (homeIndex != -1) {
          ref.read(selectedNavIndexProvider.notifier).setIndex(homeIndex);
        }
        context.go(RouteNames.home);
      }
    } catch (e, stackTrace) {
      print('Error in _completeCheckin: $e');
      print('Stack trace: $stackTrace');

      if (mounted) {
        showFailureToast(
          context,
          title: 'Error Completing Checkin',
          description: 'Unable to save checkin data. Please try again.',
        );
      }
    }
  }

  // Helper method to get artwork URL for meditation activity
  String? _getMeditationArtworkUrl(Activity activity) {
    return activity.config.maybeWhen(
      meditation: (audioTracks, defaultDuration, selectedTrack) {
        if (selectedTrack != null && selectedTrack['artworkUrl'] != null) {
          return selectedTrack['artworkUrl'] as String?;
        }
        // Fallback to first track with artwork
        for (final trackMap in audioTracks) {
          if (trackMap['artworkUrl'] != null) {
            return trackMap['artworkUrl'] as String?;
          }
        }
        return null;
      },
      orElse: () => null,
    );
  }

  // Helper method to get artwork URL for affirmation activity
  String? _getAffirmationArtworkUrl(Activity activity) {
    return activity.config.maybeWhen(
      affirmations: (audioTracks, selectedTrack, autoPlay) {
        if (selectedTrack != null && selectedTrack['artworkUrl'] != null) {
          return selectedTrack['artworkUrl'] as String?;
        }
        // Fallback to first track with artwork
        for (final trackMap in audioTracks) {
          if (trackMap['artworkUrl'] != null) {
            return trackMap['artworkUrl'] as String?;
          }
        }
        return null;
      },
      orElse: () => null,
    );
  }

  Future<void> _showExitConfirmationDialog() async {
    final shouldExit = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Check-In?'),
        content: const Text(
            'Do you want to leave the current check-in? Your progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Stay'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Leave'),
          ),
        ],
      ),
    );

    if (shouldExit == true && mounted) {
      Navigator.of(context).pop();
    }
  }

  List<Widget> _buildAppBarActions() {
    final isLastStep = _currentGlobalStep == _totalSteps - 1;

    return [
      if (isLastStep)
        TextButton(
          onPressed: () => _completeCheckin(),
          child: Text(
            'Complete',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
          ),
        )
      else
        TextButton(
          onPressed: () {
            print('=== NEXT BUTTON PRESSED ===');
            print('Current global step before: $_currentGlobalStep');
            print('Is last step: $isLastStep');
            print(
                'Current activity: ${_activities[_currentActivityIndex].type}');
            final result = _moveToNextStep();
            print('moveToNextStep returned: $result');
            print('Current global step after: $_currentGlobalStep');
            print('=== NEXT BUTTON END ===');
          },
          child: Text(
            'Next',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_activities.isEmpty) {
      return GradientScaffold(
        //showMiniPlayer: false,
        appBar: AppBar(
          title: Text(_routineName),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: _showExitConfirmationDialog,
          ),
          actions: _buildAppBarActions(),
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Center(
                child: const Text('No activities configured for this checkin')),
            Center(
              child: TextButton(
                  onPressed: () {
                    final navItems = ref.read(navigationItemsProvider);
                    final chatIndex = navItems
                        .indexWhere((item) => item.path == RouteNames.journal);
                    if (chatIndex != -1) {
                      ref
                          .read(selectedNavIndexProvider.notifier)
                          .setIndex(chatIndex);
                    }
                    context.goNamed(RouteNames.journal);
                  },
                  child: Text('Add Activities')),
            ),
          ],
        ),
      );
    }

    final currentActivity = _activities[_currentActivityIndex];
    final activityType = currentActivity.type.toLowerCase();
    final isBreathworkActivity = activityType == 'breathwork';
    final isMeditationActivity = activityType == 'meditation';
    final isAffirmationActivity = activityType == 'affirmations';

    // If current activity is breathwork, use full-screen blur background
    if (isBreathworkActivity) {
      return Consumer(
        builder: (context, ref, child) {
          final state = ref.watch(breathworkStateProvider);
          final backgroundsAsync = ref.watch(breathworkBackgroundsProvider);

          return backgroundsAsync.when(
            data: (backgrounds) {
              final selectedBackground = backgrounds.firstWhere(
                (bg) => bg.id == state.backgroundImageId,
                orElse: () => backgrounds.first,
              );

              return Scaffold(
                body: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Blurred background image
                    Image.asset(
                      selectedBackground.assetPath,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                    BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        color: Colors.white.withValues(alpha: 0.1),
                        width: double.infinity,
                        height: double.infinity,
                      ),
                    ),
                    // Content with transparent scaffold
                    Scaffold(
                      backgroundColor: Colors.transparent,
                      resizeToAvoidBottomInset: false,
                      appBar: AppBar(
                        title: Text(_routineName),
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        leading: IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: _showExitConfirmationDialog,
                        ),
                        actions: _buildAppBarActions(),
                      ),
                      body: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: StepIndicator(
                              totalSteps: _totalSteps,
                              currentStep: _currentGlobalStep,
                            ),
                          ),
                          Expanded(
                            child: _buildActivityWidget(currentActivity),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
            loading: () => const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
            error: (_, __) => const Scaffold(
              body: Center(child: Text('Error loading background')),
            ),
          );
        },
      );
    }

    // If current activity is meditation, use full-screen blur background with track artwork
    if (isMeditationActivity) {
      final artworkUrl = _getMeditationArtworkUrl(currentActivity);

      return Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            // Blurred background image from track artwork
            if (artworkUrl != null)
              Image.asset(
                artworkUrl,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (_, __, ___) => Container(
                  color: Theme.of(context).colorScheme.surface,
                ),
              )
            else
              Container(
                color: Theme.of(context).colorScheme.surface,
              ),
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                color: Colors.white.withValues(alpha: 0.1),
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            // Content with transparent scaffold
            Scaffold(
              backgroundColor: Colors.transparent,
              resizeToAvoidBottomInset: false,
              appBar: AppBar(
                title: Text(_routineName),
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _showExitConfirmationDialog,
                ),
                actions: _buildAppBarActions(),
              ),
              body: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: StepIndicator(
                      totalSteps: _totalSteps,
                      currentStep: _currentGlobalStep,
                    ),
                  ),
                  Expanded(
                    child: _buildActivityWidget(currentActivity),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    // If current activity is affirmations, use full-screen blur background with track artwork
    if (isAffirmationActivity) {
      final artworkUrl = _getAffirmationArtworkUrl(currentActivity);

      return Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            // Blurred background image from track artwork
            if (artworkUrl != null)
              Image.asset(
                artworkUrl,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (_, __, ___) => Container(
                  color: Theme.of(context).colorScheme.surface,
                ),
              )
            else
              Container(
                color: Theme.of(context).colorScheme.surface,
              ),
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                color: Colors.white.withValues(alpha: 0.1),
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            // Content with transparent scaffold
            Scaffold(
              backgroundColor: Colors.transparent,
              resizeToAvoidBottomInset: false,
              appBar: AppBar(
                title: Text(_routineName),
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _showExitConfirmationDialog,
                ),
                actions: _buildAppBarActions(),
              ),
              body: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: StepIndicator(
                      totalSteps: _totalSteps,
                      currentStep: _currentGlobalStep,
                    ),
                  ),
                  Expanded(
                    child: _buildActivityWidget(currentActivity),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    // For non-audio activities, use the regular GradientScaffold
    return GradientScaffold(
      //showMiniPlayer: false,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(_routineName),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: _showExitConfirmationDialog,
        ),
        actions: _buildAppBarActions(),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: StepIndicator(
              totalSteps: _totalSteps,
              currentStep: _currentGlobalStep,
            ),
          ),
          Expanded(
            child: _buildActivityWidget(currentActivity),
          ),
        ],
      ),
    );
  }
}
