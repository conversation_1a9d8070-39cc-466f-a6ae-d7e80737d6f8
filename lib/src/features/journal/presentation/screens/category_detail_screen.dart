// lib/src/features/journal/presentation/screens/category_detail_screen.dart
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/journal/models/entry_models.dart';

class CategoryDetailScreen extends StatelessWidget {
  final String type;
  final List<UnifiedEntry> entries;
  final DateTime day;

  const CategoryDetailScreen({
    super.key,
    required this.type,
    required this.entries,
    required this.day,
  });

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final isToday =
        now.year == day.year && now.month == day.month && now.day == day.day;
    final dayName = DateFormat('EEEE, MMM d, y').format(day);

    IconData icon;
    Color iconColor;
    String typeLabel;

    switch (type) {
      case 'gratitude':
        icon = Icons.favorite;
        iconColor = AppColors.accent3;
        typeLabel = 'Gratitude';
        break;
      case 'mood':
        icon = Icons.mood;
        iconColor = AppColors.accent4;
        typeLabel = 'Mood';
        break;
      default: // journal
        icon = Icons.article;
        iconColor = AppColors.accent1;
        typeLabel = 'Journal';
        break;
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.textPrimary,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  size: 20,
                  color: iconColor,
                ),
                const SizedBox(width: 8),
                Text(
                  typeLabel,
                  style: TextStyle(
                    fontSize: AppTypography.bodyMedium,
                    fontWeight: AppTypography.semiBold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            Text(
              isToday ? 'Today' : dayName,
              style: TextStyle(
                fontSize: AppTypography.titleSmall,
                color: AppColors.textSecondary,
                fontWeight: AppTypography.regular,
              ),
            ),
          ],
        ),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: entries.length,
        itemBuilder: (context, index) {
          final entry = entries[index];
          return _EntryDetailCard(
            entry: entry,
            iconColor: iconColor,
            isLast: index == entries.length - 1,
          );
        },
      ),
    );
  }
}

class _EntryDetailCard extends StatelessWidget {
  final UnifiedEntry entry;
  final Color iconColor;
  final bool isLast;

  const _EntryDetailCard({
    required this.entry,
    required this.iconColor,
    required this.isLast,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with time
            if (entry.completedAt != null)
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    DateFormat('h:mm a').format(entry.completedAt!),
                    style: TextStyle(
                      fontSize: AppTypography.titleSmall,
                      color: AppColors.textSecondary,
                      fontWeight: AppTypography.medium,
                    ),
                  ),
                ],
              ),

            if (entry.completedAt != null) const SizedBox(height: 16),

            // Content
            Text(
              entry.content,
              style: TextStyle(
                fontSize: AppTypography.bodySmall,
                height: AppTypography.heightLarge,
                color: AppColors.textPrimary,
              ),
            ),

            // Mood if available
            if (entry.mood != null) ...[
              const SizedBox(height: 16),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.mood,
                      size: 18,
                      color: iconColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      entry.mood!,
                      style: TextStyle(
                        fontSize: AppTypography.titleSmall,
                        fontWeight: AppTypography.semiBold,
                        color: iconColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
