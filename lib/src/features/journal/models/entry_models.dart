// lib/src/features/journal/models/entry_models.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'entry_models.freezed.dart';
part 'entry_models.g.dart';

@freezed
class UnifiedEntry with _$UnifiedEntry {
  const factory UnifiedEntry({
    required int id,
    required DateTime date,
    required String type, // 'journal', 'gratitude'
    required String content,
    String? mood,
    DateTime? completedAt,
  }) = _UnifiedEntry;

  factory UnifiedEntry.fromJson(Map<String, dynamic> json) =>
      _$UnifiedEntryFromJson(json);
}

@freezed
class WeeklyEntries with _$WeeklyEntries {
  const factory WeeklyEntries({
    required DateTime weekStart,
    required DateTime weekEnd,
    required Map<DateTime, List<UnifiedEntry>> entriesByDay,
  }) = _WeeklyEntries;

  factory WeeklyEntries.fromJson(Map<String, dynamic> json) =>
      _$WeeklyEntriesFromJson(json);
}

extension DateTimeExtensions on DateTime {
  /// Get the start of the week (Monday)
  DateTime get weekStart {
    final daysFromMonday = weekday - 1;
    return subtract(Duration(days: daysFromMonday))
        .copyWith(hour: 0, minute: 0, second: 0, microsecond: 0);
  }

  /// Get the end of the week (Sunday)
  DateTime get weekEnd {
    final daysToSunday = 7 - weekday;
    return add(Duration(days: daysToSunday))
        .copyWith(hour: 23, minute: 59, second: 59, microsecond: 999);
  }

  /// Check if two dates are the same day
  bool isSameDay(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }
}
