// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'entry_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UnifiedEntryImpl _$$UnifiedEntryImplFromJson(Map<String, dynamic> json) =>
    _$UnifiedEntryImpl(
      id: (json['id'] as num).toInt(),
      date: DateTime.parse(json['date'] as String),
      type: json['type'] as String,
      content: json['content'] as String,
      mood: json['mood'] as String?,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$$UnifiedEntryImplToJson(_$UnifiedEntryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'date': instance.date.toIso8601String(),
      'type': instance.type,
      'content': instance.content,
      'mood': instance.mood,
      'completedAt': instance.completedAt?.toIso8601String(),
    };

_$WeeklyEntriesImpl _$$WeeklyEntriesImplFromJson(Map<String, dynamic> json) =>
    _$WeeklyEntriesImpl(
      weekStart: DateTime.parse(json['weekStart'] as String),
      weekEnd: DateTime.parse(json['weekEnd'] as String),
      entriesByDay: (json['entriesByDay'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            DateTime.parse(k),
            (e as List<dynamic>)
                .map((e) => UnifiedEntry.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
    );

Map<String, dynamic> _$$WeeklyEntriesImplToJson(_$WeeklyEntriesImpl instance) =>
    <String, dynamic>{
      'weekStart': instance.weekStart.toIso8601String(),
      'weekEnd': instance.weekEnd.toIso8601String(),
      'entriesByDay':
          instance.entriesByDay.map((k, e) => MapEntry(k.toIso8601String(), e)),
    };
