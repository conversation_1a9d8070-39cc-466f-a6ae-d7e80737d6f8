// lib/src/features/journal/models/statistics.dart
class MoodEntry {
  final DateTime date;
  final String mood;
  final String? note;

  MoodEntry({
    required this.date,
    required this.mood,
    this.note,
  });
}

class ActivityStats {
  final String name;
  int totalCount;
  int completedCount;
  final double completionRate;

  ActivityStats({
    required this.name,
    this.totalCount = 0,
    this.completedCount = 0,
  }) : completionRate = totalCount > 0 ? completedCount / totalCount : 0;
}

class MeditationStats {
  final DateTime date;
  final int minutes;

  MeditationStats({
    required this.date,
    required this.minutes,
  });
}

class OverviewStats {
  final int totalCheckins;
  final int currentStreak;
  final int totalMeditationMinutes;
  final int totalFocusSessions;
  final int totalJournalEntries;
  final int totalGratitudeEntries;

  OverviewStats({
    required this.totalCheckins,
    required this.currentStreak,
    required this.totalMeditationMinutes,
    required this.totalFocusSessions,
    required this.totalJournalEntries,
    required this.totalGratitudeEntries,
  });
}

class MoodEntryWithTime {
  final DateTime date;
  final String mood;
  final String? note;
  final String emoji;
  final String label;

  MoodEntryWithTime({
    required this.date,
    required this.mood,
    this.note,
    required this.emoji,
    required this.label,
  });
}

class DailyStreak {
  final int currentStreak;
  final int longestStreak;
  final DateTime lastCompletedDate;

  DailyStreak({
    required this.currentStreak,
    required this.longestStreak,
    required this.lastCompletedDate,
  });
}

class FocusStats {
  final DateTime date;
  final int totalMinutes;
  final int sessionsCount;
  final List<FocusSession> sessions;

  FocusStats({
    required this.date,
    required this.totalMinutes,
    required this.sessionsCount,
    required this.sessions,
  });
}

class FocusSession {
  final String id;
  final String taskDescription;
  final DateTime startTime;
  final DateTime endTime;
  final int workDurationMinutes;
  final int completedCycles;

  FocusSession({
    required this.id,
    required this.taskDescription,
    required this.startTime,
    required this.endTime,
    required this.workDurationMinutes,
    required this.completedCycles,
  });

  int get totalMinutes => workDurationMinutes * completedCycles;

  factory FocusSession.fromFirestore(Map<String, dynamic> data) {
    return FocusSession(
      id: data['id'] ?? '',
      taskDescription: data['taskDescription'] ?? '',
      startTime: DateTime.parse(data['startTime']),
      endTime: DateTime.parse(data['endTime']),
      workDurationMinutes: data['workDurationMinutes'] ?? 25,
      completedCycles: data['completedCycles'] ?? 0,
    );
  }
}
