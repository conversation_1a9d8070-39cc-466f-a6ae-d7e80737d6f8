// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'entry_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UnifiedEntry _$UnifiedEntryFromJson(Map<String, dynamic> json) {
  return _UnifiedEntry.fromJson(json);
}

/// @nodoc
mixin _$UnifiedEntry {
  int get id => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  String get type =>
      throw _privateConstructorUsedError; // 'journal', 'gratitude'
  String get content => throw _privateConstructorUsedError;
  String? get mood => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;

  /// Serializes this UnifiedEntry to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UnifiedEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UnifiedEntryCopyWith<UnifiedEntry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnifiedEntryCopyWith<$Res> {
  factory $UnifiedEntryCopyWith(
          UnifiedEntry value, $Res Function(UnifiedEntry) then) =
      _$UnifiedEntryCopyWithImpl<$Res, UnifiedEntry>;
  @useResult
  $Res call(
      {int id,
      DateTime date,
      String type,
      String content,
      String? mood,
      DateTime? completedAt});
}

/// @nodoc
class _$UnifiedEntryCopyWithImpl<$Res, $Val extends UnifiedEntry>
    implements $UnifiedEntryCopyWith<$Res> {
  _$UnifiedEntryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UnifiedEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? date = null,
    Object? type = null,
    Object? content = null,
    Object? mood = freezed,
    Object? completedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      mood: freezed == mood
          ? _value.mood
          : mood // ignore: cast_nullable_to_non_nullable
              as String?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UnifiedEntryImplCopyWith<$Res>
    implements $UnifiedEntryCopyWith<$Res> {
  factory _$$UnifiedEntryImplCopyWith(
          _$UnifiedEntryImpl value, $Res Function(_$UnifiedEntryImpl) then) =
      __$$UnifiedEntryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      DateTime date,
      String type,
      String content,
      String? mood,
      DateTime? completedAt});
}

/// @nodoc
class __$$UnifiedEntryImplCopyWithImpl<$Res>
    extends _$UnifiedEntryCopyWithImpl<$Res, _$UnifiedEntryImpl>
    implements _$$UnifiedEntryImplCopyWith<$Res> {
  __$$UnifiedEntryImplCopyWithImpl(
      _$UnifiedEntryImpl _value, $Res Function(_$UnifiedEntryImpl) _then)
      : super(_value, _then);

  /// Create a copy of UnifiedEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? date = null,
    Object? type = null,
    Object? content = null,
    Object? mood = freezed,
    Object? completedAt = freezed,
  }) {
    return _then(_$UnifiedEntryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      mood: freezed == mood
          ? _value.mood
          : mood // ignore: cast_nullable_to_non_nullable
              as String?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UnifiedEntryImpl implements _UnifiedEntry {
  const _$UnifiedEntryImpl(
      {required this.id,
      required this.date,
      required this.type,
      required this.content,
      this.mood,
      this.completedAt});

  factory _$UnifiedEntryImpl.fromJson(Map<String, dynamic> json) =>
      _$$UnifiedEntryImplFromJson(json);

  @override
  final int id;
  @override
  final DateTime date;
  @override
  final String type;
// 'journal', 'gratitude'
  @override
  final String content;
  @override
  final String? mood;
  @override
  final DateTime? completedAt;

  @override
  String toString() {
    return 'UnifiedEntry(id: $id, date: $date, type: $type, content: $content, mood: $mood, completedAt: $completedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnifiedEntryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.mood, mood) || other.mood == mood) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, date, type, content, mood, completedAt);

  /// Create a copy of UnifiedEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnifiedEntryImplCopyWith<_$UnifiedEntryImpl> get copyWith =>
      __$$UnifiedEntryImplCopyWithImpl<_$UnifiedEntryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UnifiedEntryImplToJson(
      this,
    );
  }
}

abstract class _UnifiedEntry implements UnifiedEntry {
  const factory _UnifiedEntry(
      {required final int id,
      required final DateTime date,
      required final String type,
      required final String content,
      final String? mood,
      final DateTime? completedAt}) = _$UnifiedEntryImpl;

  factory _UnifiedEntry.fromJson(Map<String, dynamic> json) =
      _$UnifiedEntryImpl.fromJson;

  @override
  int get id;
  @override
  DateTime get date;
  @override
  String get type; // 'journal', 'gratitude'
  @override
  String get content;
  @override
  String? get mood;
  @override
  DateTime? get completedAt;

  /// Create a copy of UnifiedEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnifiedEntryImplCopyWith<_$UnifiedEntryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WeeklyEntries _$WeeklyEntriesFromJson(Map<String, dynamic> json) {
  return _WeeklyEntries.fromJson(json);
}

/// @nodoc
mixin _$WeeklyEntries {
  DateTime get weekStart => throw _privateConstructorUsedError;
  DateTime get weekEnd => throw _privateConstructorUsedError;
  Map<DateTime, List<UnifiedEntry>> get entriesByDay =>
      throw _privateConstructorUsedError;

  /// Serializes this WeeklyEntries to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WeeklyEntries
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WeeklyEntriesCopyWith<WeeklyEntries> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WeeklyEntriesCopyWith<$Res> {
  factory $WeeklyEntriesCopyWith(
          WeeklyEntries value, $Res Function(WeeklyEntries) then) =
      _$WeeklyEntriesCopyWithImpl<$Res, WeeklyEntries>;
  @useResult
  $Res call(
      {DateTime weekStart,
      DateTime weekEnd,
      Map<DateTime, List<UnifiedEntry>> entriesByDay});
}

/// @nodoc
class _$WeeklyEntriesCopyWithImpl<$Res, $Val extends WeeklyEntries>
    implements $WeeklyEntriesCopyWith<$Res> {
  _$WeeklyEntriesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WeeklyEntries
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekStart = null,
    Object? weekEnd = null,
    Object? entriesByDay = null,
  }) {
    return _then(_value.copyWith(
      weekStart: null == weekStart
          ? _value.weekStart
          : weekStart // ignore: cast_nullable_to_non_nullable
              as DateTime,
      weekEnd: null == weekEnd
          ? _value.weekEnd
          : weekEnd // ignore: cast_nullable_to_non_nullable
              as DateTime,
      entriesByDay: null == entriesByDay
          ? _value.entriesByDay
          : entriesByDay // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, List<UnifiedEntry>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WeeklyEntriesImplCopyWith<$Res>
    implements $WeeklyEntriesCopyWith<$Res> {
  factory _$$WeeklyEntriesImplCopyWith(
          _$WeeklyEntriesImpl value, $Res Function(_$WeeklyEntriesImpl) then) =
      __$$WeeklyEntriesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime weekStart,
      DateTime weekEnd,
      Map<DateTime, List<UnifiedEntry>> entriesByDay});
}

/// @nodoc
class __$$WeeklyEntriesImplCopyWithImpl<$Res>
    extends _$WeeklyEntriesCopyWithImpl<$Res, _$WeeklyEntriesImpl>
    implements _$$WeeklyEntriesImplCopyWith<$Res> {
  __$$WeeklyEntriesImplCopyWithImpl(
      _$WeeklyEntriesImpl _value, $Res Function(_$WeeklyEntriesImpl) _then)
      : super(_value, _then);

  /// Create a copy of WeeklyEntries
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekStart = null,
    Object? weekEnd = null,
    Object? entriesByDay = null,
  }) {
    return _then(_$WeeklyEntriesImpl(
      weekStart: null == weekStart
          ? _value.weekStart
          : weekStart // ignore: cast_nullable_to_non_nullable
              as DateTime,
      weekEnd: null == weekEnd
          ? _value.weekEnd
          : weekEnd // ignore: cast_nullable_to_non_nullable
              as DateTime,
      entriesByDay: null == entriesByDay
          ? _value._entriesByDay
          : entriesByDay // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, List<UnifiedEntry>>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WeeklyEntriesImpl implements _WeeklyEntries {
  const _$WeeklyEntriesImpl(
      {required this.weekStart,
      required this.weekEnd,
      required final Map<DateTime, List<UnifiedEntry>> entriesByDay})
      : _entriesByDay = entriesByDay;

  factory _$WeeklyEntriesImpl.fromJson(Map<String, dynamic> json) =>
      _$$WeeklyEntriesImplFromJson(json);

  @override
  final DateTime weekStart;
  @override
  final DateTime weekEnd;
  final Map<DateTime, List<UnifiedEntry>> _entriesByDay;
  @override
  Map<DateTime, List<UnifiedEntry>> get entriesByDay {
    if (_entriesByDay is EqualUnmodifiableMapView) return _entriesByDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_entriesByDay);
  }

  @override
  String toString() {
    return 'WeeklyEntries(weekStart: $weekStart, weekEnd: $weekEnd, entriesByDay: $entriesByDay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WeeklyEntriesImpl &&
            (identical(other.weekStart, weekStart) ||
                other.weekStart == weekStart) &&
            (identical(other.weekEnd, weekEnd) || other.weekEnd == weekEnd) &&
            const DeepCollectionEquality()
                .equals(other._entriesByDay, _entriesByDay));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, weekStart, weekEnd,
      const DeepCollectionEquality().hash(_entriesByDay));

  /// Create a copy of WeeklyEntries
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WeeklyEntriesImplCopyWith<_$WeeklyEntriesImpl> get copyWith =>
      __$$WeeklyEntriesImplCopyWithImpl<_$WeeklyEntriesImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WeeklyEntriesImplToJson(
      this,
    );
  }
}

abstract class _WeeklyEntries implements WeeklyEntries {
  const factory _WeeklyEntries(
          {required final DateTime weekStart,
          required final DateTime weekEnd,
          required final Map<DateTime, List<UnifiedEntry>> entriesByDay}) =
      _$WeeklyEntriesImpl;

  factory _WeeklyEntries.fromJson(Map<String, dynamic> json) =
      _$WeeklyEntriesImpl.fromJson;

  @override
  DateTime get weekStart;
  @override
  DateTime get weekEnd;
  @override
  Map<DateTime, List<UnifiedEntry>> get entriesByDay;

  /// Create a copy of WeeklyEntries
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WeeklyEntriesImplCopyWith<_$WeeklyEntriesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
