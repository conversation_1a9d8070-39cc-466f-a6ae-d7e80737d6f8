// // lib/src/features/journal/widgets/activities/mood_tracking_activity.dart
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
// import 'package:mimi_app/src/features/activities/shared/providers/activity_data_provider.dart';

// import '../../../activities/shared/screens/base_activity.dart';

// class MoodTrackingActivity extends BaseActivity {
//   const MoodTrackingActivity({
//     super.key,
//     required super.activity,
//     super.onComplete,
//     super.standalone = false,
//   });

//   @override
//   ConsumerState<MoodTrackingActivity> createState() =>
//       _MoodTrackingActivityState();
// }

// class _MoodTrackingActivityState
//     extends BaseActivityState<MoodTrackingActivity> {
//   String? _selectedMood;
//   final TextEditingController _noteController = TextEditingController();
//   late final ActivityDataNotifier _dataNotifier;

//   MoodTrackingConfig get _config => widget.activity.config.maybeWhen(
//         moodTracking: (moods, includeNote) => MoodTrackingConfig(
//           moods: moods,
//           includeNote: includeNote,
//         ),
//         orElse: () => throw Exception('Invalid config type'),
//       );

//   @override
//   void initState() {
//     super.initState();
//     _dataNotifier = ref.read(
//       activityDataNotifierProvider(widget.activity.id, DateTime.now()).notifier,
//     );
//     _loadExistingMood();
//   }

//   @override
//   void dispose() {
//     _noteController.dispose();
//     super.dispose();
//   }

//   Future<void> _loadExistingMood() async {
//     final data = await _dataNotifier.build(widget.activity.id, DateTime.now());
//     if (data != null) {
//       setState(() {
//         _selectedMood = data.data['mood'] as String?;
//         _noteController.text = data.data['note'] as String? ?? '';
//       });
//     }
//   }

//   Future<void> _saveMood() async {
//     if (_selectedMood != null) {
//       await _dataNotifier.saveActivityData({
//         'mood': _selectedMood,
//         if (_config.includeNote) 'note': _noteController.text,
//         'timestamp': DateTime.now().toIso8601String(),
//       });
//       markComplete();
//     }
//   }

//   void _showNoteDialog() async {
//     if (!_config.includeNote) {
//       _saveMood();
//       return;
//     }

//     final result = await showDialog<bool>(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Add a note'),
//         content: TextField(
//           controller: _noteController,
//           decoration: const InputDecoration(
//             hintText: 'What made you feel this way?',
//           ),
//           maxLines: 3,
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.pop(context, false),
//             child: const Text('Cancel'),
//           ),
//           TextButton(
//             onPressed: () => Navigator.pop(context, true),
//             child: const Text('Save'),
//           ),
//         ],
//       ),
//     );

//     if (result == true) {
//       _saveMood();
//     }
//   }

//   @override
//   Widget buildActivityContent(BuildContext context) {
//     return widget.activity.config.maybeWhen(
//       moodTracking: (moods, includeNote) => Container(
//         color: Theme.of(context).scaffoldBackgroundColor,
//         child: SafeArea(
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 20),
//             child: Column(
//               children: [
//                 const SizedBox(height: 40),
//                 const Text(
//                   'How Do You Feel Today?',
//                   style: TextStyle(
//                     fontSize: 24,
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//                 const SizedBox(height: 40),
//                 // Circular mood display
//                 Container(
//                   width: 280,
//                   height: 280,
//                   decoration: BoxDecoration(
//                     shape: BoxShape.circle,
//                     color: _selectedMood?.moodColor.withOpacity(0.3) ??
//                         Colors.grey.shade200,
//                     boxShadow: [
//                       BoxShadow(
//                         color: (_selectedMood?.moodColor ?? Colors.grey)
//                             .withOpacity(0.1),
//                         blurRadius: 15,
//                         spreadRadius: 5,
//                       ),
//                     ],
//                   ),
//                   child: Center(
//                     child: Container(
//                       width: 200,
//                       height: 200,
//                       decoration: BoxDecoration(
//                         shape: BoxShape.circle,
//                         color: _selectedMood?.moodColor.withOpacity(0.3) ??
//                             Colors.grey.shade200,
//                         boxShadow: [
//                           BoxShadow(
//                             color: (_selectedMood?.moodColor ?? Colors.grey)
//                                 .withOpacity(0.3),
//                             blurRadius: 15,
//                             spreadRadius: 5,
//                           ),
//                         ],
//                       ),
//                       child: Center(
//                         child: Container(
//                           width: 120,
//                           height: 120,
//                           decoration: BoxDecoration(
//                             shape: BoxShape.circle,
//                             color: _selectedMood?.moodColor ??
//                                 Colors.grey.shade300,
//                             boxShadow: [
//                               BoxShadow(
//                                 color:
//                                     (_selectedMood?.moodColor ?? Colors.grey),
//                                 blurRadius: 15,
//                                 spreadRadius: 5,
//                               ),
//                             ],
//                           ),
//                           child: Center(
//                             child: Text(
//                               _selectedMood?.emoji ?? '😐',
//                               style: const TextStyle(fontSize: 60),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//                 const SizedBox(height: 60),
//                 // Mood selection row
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                   children: moods.map((mood) {
//                     return GestureDetector(
//                       onTap: () => setState(() => _selectedMood = mood),
//                       child: Column(
//                         children: [
//                           Container(
//                             width: 50,
//                             height: 50,
//                             decoration: BoxDecoration(
//                               shape: BoxShape.circle,
//                               color: mood.moodColor,
//                               border: Border.all(
//                                 color: _selectedMood == mood
//                                     ? Colors.white
//                                     : Colors.transparent,
//                                 width: 3,
//                               ),
//                             ),
//                             child: Center(
//                               child: Text(
//                                 mood.emoji,
//                                 style: const TextStyle(fontSize: 24),
//                               ),
//                             ),
//                           ),
//                           const SizedBox(height: 8),
//                           Text(
//                             mood.label,
//                             style: const TextStyle(
//                               fontSize: 12,
//                               color: Colors.black87,
//                             ),
//                           ),
//                         ],
//                       ),
//                     );
//                   }).toList(),
//                 ),
//                 const SizedBox(height: 30),
//                 // Save button
//                 SizedBox(
//                   width: double.infinity,
//                   child: ElevatedButton(
//                     onPressed: _selectedMood != null ? _showNoteDialog : null,
//                     style: ElevatedButton.styleFrom(
//                       padding: const EdgeInsets.symmetric(vertical: 16),
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(8),
//                       ),
//                     ),
//                     child: const Text(
//                       'Note Mood',
//                       style: TextStyle(
//                         fontSize: 16,
//                         fontWeight: FontWeight.w600,
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//       orElse: () => throw Exception('Invalid config type'),
//     );
//   }
// }

// // Add this to your theme or constants file
// class MoodColors {
//   static const Color unhappy = Color(0xFFFF5252);
//   static const Color sad = Color(0xFF64B5F6);
//   static const Color normal = Color(0xFF9575CD);
//   static const Color good = Color(0xFF81C784);
//   static const Color happy = Color(0xFFFFD54F);
// }

// extension MoodExtension on String {
//   Color get moodColor {
//     switch (this.toLowerCase()) {
//       case 'unhappy':
//         return MoodColors.unhappy;
//       case 'sad':
//         return MoodColors.sad;
//       case 'normal':
//         return MoodColors.normal;
//       case 'good':
//         return MoodColors.good;
//       case 'happy':
//         return MoodColors.happy;
//       default:
//         return MoodColors.normal;
//     }
//   }

//   String get emoji {
//     switch (this.toLowerCase()) {
//       case 'unhappy':
//         return '😢';
//       case 'sad':
//         return '😕';
//       case 'normal':
//         return '😐';
//       case 'good':
//         return '🙂';
//       case 'happy':
//         return '😊';
//       default:
//         return '😐';
//     }
//   }

//   String get label => this[0].toUpperCase() + this.substring(1).toLowerCase();
// }
