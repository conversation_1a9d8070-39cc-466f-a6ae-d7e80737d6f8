// // lib/src/features/journal/widgets/activities/meditation_activity.dart
// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:just_audio/just_audio.dart';

// import '../../../activities/shared/screens/base_activity.dart';

// class MeditationActivity extends BaseActivity {
//   const MeditationActivity({
//     super.key,
//     required super.activity,
//     super.onComplete,
//     super.standalone = false,
//   });

//   @override
//   ConsumerState<MeditationActivity> createState() => _MeditationActivityState();
// }

// class _MeditationActivityState extends BaseActivityState<MeditationActivity> {
//   late final AudioPlayer _player;
//   Timer? _timer;
//   int _remainingSeconds = 0;
//   bool _isPlaying = false;
//   String? _selectedAudio;

//   MeditationConfig get _config => widget.activity.config.maybeWhen(
//         meditation: (audioAssets, defaultDuration, selectedAudioAsset) =>
//             MeditationConfig(
//           audioAssets: audioAssets,
//           defaultDuration: defaultDuration,
//           selectedAudioAsset: selectedAudioAsset,
//         ),
//         orElse: () => throw Exception('Invalid config type'),
//       );

//   @override
//   void initState() {
//     super.initState();
//     _player = AudioPlayer();
//     widget.activity.config.whenOrNull(
//       meditation: (audioAssets, defaultDuration, selectedAudioAsset) {
//         _remainingSeconds = defaultDuration * 60;
//         _selectedAudio = selectedAudioAsset;
//       },
//     );
//     _initializeAudio();
//   }

//   Future<void> _initializeAudio() async {
//     if (_selectedAudio != null) {
//       try {
//         await _player.setAsset(_selectedAudio!);
//         await _player.setLoopMode(LoopMode.one);
//       } catch (e) {
//         debugPrint('Error loading audio: $e');
//       }
//     }
//   }

//   void _startMeditation() async {
//     if (_selectedAudio != null) {
//       await _player.play();
//     }
//     setState(() => _isPlaying = true);
//     _startTimer();
//   }

//   void _startTimer() {
//     _timer?.cancel();
//     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
//       setState(() {
//         if (_remainingSeconds > 0) {
//           _remainingSeconds--;
//         } else {
//           _timer?.cancel();
//           _player.stop();
//           _isPlaying = false;
//           markComplete();
//         }
//       });
//     });
//   }

//   void _pauseMeditation() {
//     _timer?.cancel();
//     _player.pause();
//     setState(() => _isPlaying = false);
//   }

//   void _resumeMeditation() {
//     _player.play();
//     setState(() => _isPlaying = true);
//     _startTimer();
//   }

//   String _formatTime(int seconds) {
//     final minutes = seconds ~/ 60;
//     final remainingSeconds = seconds % 60;
//     return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
//   }

//   @override
//   void dispose() {
//     _timer?.cancel();
//     _player.dispose();
//     super.dispose();
//   }

//   @override
//   IconData getActivityIcon() => Icons.self_improvement;

//   @override
//   Widget buildActivityContent(BuildContext context) {
//     return widget.activity.config.when(
//       meditation: (audioAssets, defaultDuration, selectedAudioAsset) => Column(
//         children: [
//           if (audioAssets.isNotEmpty) ...[
//             DropdownButton<String>(
//               value: _selectedAudio,
//               hint: const Text('Select meditation audio'),
//               isExpanded: true,
//               items: audioAssets.map((asset) {
//                 return DropdownMenuItem(
//                   value: asset,
//                   child: Text(asset.split('/').last),
//                 );
//               }).toList(),
//               onChanged: !_isPlaying
//                   ? (value) async {
//                       setState(() => _selectedAudio = value);
//                       await _initializeAudio();
//                     }
//                   : null,
//             ),
//             const SizedBox(height: 24),
//           ],
//           SizedBox(
//             height: 200,
//             child: Stack(
//               alignment: Alignment.center,
//               children: [
//                 SizedBox(
//                   width: 200,
//                   height: 200,
//                   child: CircularProgressIndicator(
//                     value: 1 - (_remainingSeconds / (defaultDuration * 60)),
//                     strokeWidth: 10,
//                     backgroundColor: Colors.grey.withOpacity(0.2),
//                   ),
//                 ),
//                 Text(
//                   _formatTime(_remainingSeconds),
//                   style: Theme.of(context).textTheme.displayLarge,
//                 ),
//               ],
//             ),
//           ),
//           const SizedBox(height: 24),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               if (!_isPlaying && _remainingSeconds == defaultDuration * 60)
//                 ElevatedButton(
//                   onPressed: _selectedAudio != null ? _startMeditation : null,
//                   child: const Text('Start Meditation'),
//                 )
//               else
//                 Row(
//                   children: [
//                     IconButton(
//                       icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
//                       onPressed:
//                           _isPlaying ? _pauseMeditation : _resumeMeditation,
//                       iconSize: 48,
//                     ),
//                     const SizedBox(width: 16),
//                     if (_selectedAudio != null)
//                       StreamBuilder<double>(
//                         stream: _player.volumeStream,
//                         builder: (context, snapshot) {
//                           final volume = snapshot.data ?? 1.0;
//                           return Slider(
//                             value: volume,
//                             onChanged: (value) => _player.setVolume(value),
//                           );
//                         },
//                       ),
//                   ],
//                 ),
//             ],
//           ),
//         ],
//       ),
//       journaling: (_, __, ___) => throw Exception('Invalid config type'),
//       breathwork: (_, __, ___) => throw Exception('Invalid config type'),
//       affirmations: (_, __, ___) => throw Exception('Invalid config type'),
//       moodTracking: (_, __) => throw Exception('Invalid config type'),
//     );
//   }
// }

// // This class is just for internal use
// class MeditationConfig {
//   final List<String> audioAssets;
//   final int defaultDuration;
//   final String? selectedAudioAsset;

//   MeditationConfig({
//     required this.audioAssets,
//     required this.defaultDuration,
//     this.selectedAudioAsset,
//   });
// }

// // class MeditationConfig {
// //   final int defaultDuration;
// //   final List<String> audioAssets;
// //   final String? selectedAudioAsset;

// //   MeditationConfig({
// //     required this.defaultDuration,
// //     required this.audioAssets,
// //     this.selectedAudioAsset,
// //   });

// //   factory MeditationConfig.fromJson(Map<String, dynamic> json) {
// //     return MeditationConfig(
// //       defaultDuration: json['defaultDuration'] as int,
// //       audioAssets: List<String>.from(json['audioAssets'] ?? []),
// //       selectedAudioAsset: json['selectedAudioAsset'] as String?,
// //     );
// //   }
// // }
