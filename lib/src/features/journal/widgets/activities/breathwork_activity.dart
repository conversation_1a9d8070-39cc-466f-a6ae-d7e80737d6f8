// // lib/src/features/journal/widgets/activities/breathwork_activity.dart
// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mimi_app/src/features/activities/breathwork/screens/breathwork_settings_screen.dart';
// import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
// import 'package:mimi_app/src/features/home/<USER>';
// import 'package:mimi_app/src/features/activities/breathwork/models/breathe_pattern.dart';
// import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart';

// // class BreathworkActivity extends BaseActivity {
// //   const BreathworkActivity({
// //     super.key,
// //     required super.activity,
// //     super.onComplete,
// //     super.standalone = false,
// //   });

// //   @override
// //   ConsumerState<BreathworkActivity> createState() => _BreathworkActivityState();
// // }

// // class _BreathworkActivityState extends BaseActivityState<BreathworkActivity> {
// //   late int _currentCycle;
// //   late String _phase;
// //   Timer? _timer;
// //   double _progress = 0;
// //   int _totalSeconds = 0;
// //   int _elapsedSeconds = 0;

// //   BreathworkConfig get _config => widget.activity.config.maybeWhen(
// //         breathwork: (inhaleSeconds, holdSeconds, exhaleSeconds, cycles) =>
// //             BreathworkConfig(
// //           inhaleSeconds: inhaleSeconds,
// //           holdSeconds: holdSeconds,
// //           exhaleSeconds: exhaleSeconds,
// //           cycles: cycles,
// //         ),
// //         orElse: () => throw Exception('Invalid config type'),
// //       );

// //   @override
// //   void initState() {
// //     super.initState();
// //     _currentCycle = 0;
// //     _phase = 'ready';
// //     _calculateTotalSeconds();
// //   }

// //   void _calculateTotalSeconds() {
// //     widget.activity.config.whenOrNull(
// //       breathwork: (inhaleSeconds, holdSeconds, exhaleSeconds, cycles) {
// //         _totalSeconds = cycles * (inhaleSeconds + holdSeconds + exhaleSeconds);
// //       },
// //     );
// //   }

// //   void _startBreathwork() {
// //     setState(() {
// //       _phase = 'inhale';
// //       _currentCycle = 1;
// //     });
// //     _startTimer();
// //   }

// //   void _startTimer() {
// //     _timer?.cancel();
// //     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
// //       setState(() {
// //         _elapsedSeconds++;
// //         _progress = _elapsedSeconds / _totalSeconds;

// //         widget.activity.config.whenOrNull(
// //           breathwork: (inhaleSeconds, holdSeconds, exhaleSeconds, cycles) {
// //             final cycleSeconds = inhaleSeconds + holdSeconds + exhaleSeconds;
// //             final cycleProgress = _elapsedSeconds % cycleSeconds;

// //             if (cycleProgress < inhaleSeconds) {
// //               _phase = 'inhale';
// //             } else if (cycleProgress < inhaleSeconds + holdSeconds) {
// //               _phase = 'hold';
// //             } else {
// //               _phase = 'exhale';
// //             }

// //             if (_elapsedSeconds >= _totalSeconds) {
// //               _timer?.cancel();
// //               _phase = 'complete';
// //               markComplete();
// //             }
// //           },
// //         );
// //       });
// //     });
// //   }

// //   void _pauseBreathwork() {
// //     _timer?.cancel();
// //     setState(() => _phase = 'paused');
// //   }

// //   void _resumeBreathwork() {
// //     _startTimer();
// //     setState(() => _phase = 'inhale');
// //   }

// //   @override
// //   void dispose() {
// //     _timer?.cancel();
// //     super.dispose();
// //   }

// //   @override
// //   IconData getActivityIcon() => Icons.air;

// //   @override
// //   Widget buildActivityContent(BuildContext context) {
// //     return widget.activity.config.when(
// //       breathwork: (inhaleSeconds, holdSeconds, exhaleSeconds, cycles) {
// //         return Column(
// //           children: [
// //             SizedBox(
// //               height: 200,
// //               child: Stack(
// //                 alignment: Alignment.center,
// //                 children: [
// //                   SizedBox(
// //                     width: 200,
// //                     height: 200,
// //                     child: CircularProgressIndicator(
// //                       value: _progress,
// //                       strokeWidth: 10,
// //                       backgroundColor: Colors.grey.withOpacity(0.2),
// //                     ),
// //                   ),
// //                   AnimatedContainer(
// //                     duration: const Duration(milliseconds: 500),
// //                     width: _phase == 'inhale'
// //                         ? 150
// //                         : _phase == 'hold'
// //                             ? 100
// //                             : 80,
// //                     height: _phase == 'inhale'
// //                         ? 150
// //                         : _phase == 'hold'
// //                             ? 100
// //                             : 80,
// //                     decoration: BoxDecoration(
// //                       shape: BoxShape.circle,
// //                       color: Theme.of(context).primaryColor.withOpacity(0.2),
// //                     ),
// //                   ),
// //                   Text(
// //                     _phase.toUpperCase(),
// //                     style: Theme.of(context).textTheme.headlineMedium,
// //                   ),
// //                 ],
// //               ),
// //             ),
// //             const SizedBox(height: 24),
// //             if (_phase == 'ready')
// //               ElevatedButton(
// //                 onPressed: _startBreathwork,
// //                 child: const Text('Start'),
// //               )
// //             else if (_phase != 'complete')
// //               Row(
// //                 mainAxisAlignment: MainAxisAlignment.center,
// //                 children: [
// //                   if (_phase == 'paused')
// //                     ElevatedButton(
// //                       onPressed: _resumeBreathwork,
// //                       child: const Text('Resume'),
// //                     )
// //                   else
// //                     ElevatedButton(
// //                       onPressed: _pauseBreathwork,
// //                       child: const Text('Pause'),
// //                     ),
// //                   const SizedBox(width: 16),
// //                   Text(
// //                     'Cycle $_currentCycle of $cycles',
// //                     style: Theme.of(context).textTheme.titleMedium,
// //                   ),
// //                 ],
// //               ),
// //           ],
// //         );
// //       },
// //       journaling: (_, __, ___) => throw Exception('Invalid config type'),
// //       meditation: (_, __, ___) => throw Exception('Invalid config type'),
// //       affirmations: (_, __, ___) => throw Exception('Invalid config type'),
// //       moodTracking: (_, __) => throw Exception('Invalid config type'),
// //     );
// //   }
// // }

// // lib/src/features/journal/widgets/activities/breathwork_activity.dart

// // class BreathworkActivity extends BaseActivity {
// //   const BreathworkActivity({
// //     super.key,
// //     required super.activity,
// //     super.onComplete,
// //     super.standalone = false,
// //   });

// //   @override
// //   ConsumerState<BreathworkActivity> createState() => _BreathworkActivityState();
// // }

// // class _BreathworkActivityState extends BaseActivityState<BreathworkActivity> {
// //   late int _currentCycle;
// //   late String _phase;
// //   Timer? _timer;
// //   double _progress = 0;
// //   int _totalSeconds = 0;
// //   int _elapsedSeconds = 0;
// //   late BreathePattern _currentPattern;

// //   BreathworkConfig get _config => widget.activity.config.maybeWhen(
// //         breathwork: (selectedPatternId, cycles, availablePatterns) {
// //           final pattern = availablePatterns.firstWhere(
// //             (p) => p.id == selectedPatternId,
// //             orElse: () => defaultBreathworkPatterns.first,
// //           );
// //           _currentPattern = pattern;
// //           return BreathworkConfig(
// //             selectedPatternId: selectedPatternId,
// //             cycles: cycles,
// //             availablePatterns: availablePatterns,
// //           );
// //         },
// //         orElse: () => throw Exception('Invalid config type'),
// //       );

// //   @override
// //   void initState() {
// //     super.initState();
// //     _currentCycle = 0;
// //     _phase = 'ready';
// //     _calculateTotalSeconds();
// //   }

// //   void _calculateTotalSeconds() {
// //     final config = _config;
// //     final pattern = config.availablePatterns.firstWhere(
// //       (p) => p.id == config.selectedPatternId,
// //       orElse: () => defaultBreathworkPatterns.first,
// //     );

// //     _totalSeconds = config.cycles *
// //         (pattern.inhaleSeconds +
// //             pattern.holdSeconds +
// //             pattern.exhaleSeconds +
// //             pattern.holdAfterExhaleSeconds);
// //   }

// //   void _startBreathwork() {
// //     setState(() {
// //       _phase = 'inhale';
// //       _currentCycle = 1;
// //     });
// //     _startTimer();
// //   }

// //   void _startTimer() {
// //     _timer?.cancel();
// //     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
// //       setState(() {
// //         _elapsedSeconds++;
// //         _progress = _elapsedSeconds / _totalSeconds;

// //         final pattern = _currentPattern;
// //         final cycleSeconds = pattern.inhaleSeconds +
// //             pattern.holdSeconds +
// //             pattern.exhaleSeconds +
// //             pattern.holdAfterExhaleSeconds;
// //         final cycleProgress = _elapsedSeconds % cycleSeconds;

// //         if (cycleProgress < pattern.inhaleSeconds) {
// //           _phase = 'inhale';
// //         } else if (cycleProgress <
// //             pattern.inhaleSeconds + pattern.holdSeconds) {
// //           _phase = 'hold';
// //         } else if (cycleProgress <
// //             pattern.inhaleSeconds +
// //                 pattern.holdSeconds +
// //                 pattern.exhaleSeconds) {
// //           _phase = 'exhale';
// //         } else {
// //           _phase = 'hold after exhale';
// //         }

// //         // Update current cycle
// //         _currentCycle = (_elapsedSeconds ~/ cycleSeconds) + 1;

// //         if (_elapsedSeconds >= _totalSeconds) {
// //           _timer?.cancel();
// //           _phase = 'complete';
// //           markComplete();
// //         }
// //       });
// //     });
// //   }

// //   void _pauseBreathwork() {
// //     _timer?.cancel();
// //     setState(() => _phase = 'paused');
// //   }

// //   void _resumeBreathwork() {
// //     _startTimer();
// //     setState(() => _phase = 'inhale');
// //   }

// //   @override
// //   void dispose() {
// //     _timer?.cancel();
// //     super.dispose();
// //   }

// //   @override
// //   IconData getActivityIcon() => Icons.air;

// //   @override
// //   Widget buildActivityContent(BuildContext context) {
// //     return widget.activity.config.when(
// //       breathwork: (selectedPatternId, cycles, availablePatterns) {
// //         final pattern = availablePatterns.firstWhere(
// //           (p) => p.id == selectedPatternId,
// //           orElse: () => defaultBreathworkPatterns.first,
// //         );

// //         return Column(
// //           children: [
// //             Text(
// //               pattern.name,
// //               style: Theme.of(context).textTheme.titleLarge,
// //             ),
// //             const SizedBox(height: 8),
// //             Text(
// //               pattern.description,
// //               style: Theme.of(context).textTheme.bodyMedium,
// //               textAlign: TextAlign.center,
// //             ),
// //             const SizedBox(height: 24),
// //             SizedBox(
// //               height: 200,
// //               child: Stack(
// //                 alignment: Alignment.center,
// //                 children: [
// //                   SizedBox(
// //                     width: 200,
// //                     height: 200,
// //                     child: CircularProgressIndicator(
// //                       value: _progress,
// //                       strokeWidth: 10,
// //                       backgroundColor: Colors.grey.withOpacity(0.2),
// //                     ),
// //                   ),
// //                   AnimatedContainer(
// //                     duration: const Duration(milliseconds: 500),
// //                     width: _getAnimationSize(_phase),
// //                     height: _getAnimationSize(_phase),
// //                     decoration: BoxDecoration(
// //                       shape: BoxShape.circle,
// //                       color: Theme.of(context).primaryColor.withOpacity(0.2),
// //                     ),
// //                   ),
// //                   Text(
// //                     _phase.toUpperCase(),
// //                     style: Theme.of(context).textTheme.headlineMedium,
// //                   ),
// //                 ],
// //               ),
// //             ),
// //             const SizedBox(height: 24),
// //             if (_phase == 'ready')
// //               ElevatedButton(
// //                 onPressed: _startBreathwork,
// //                 child: const Text('Start'),
// //               )
// //             else if (_phase != 'complete')
// //               Row(
// //                 mainAxisAlignment: MainAxisAlignment.center,
// //                 children: [
// //                   if (_phase == 'paused')
// //                     ElevatedButton(
// //                       onPressed: _resumeBreathwork,
// //                       child: const Text('Resume'),
// //                     )
// //                   else
// //                     ElevatedButton(
// //                       onPressed: _pauseBreathwork,
// //                       child: const Text('Pause'),
// //                     ),
// //                   const SizedBox(width: 16),
// //                   Text(
// //                     'Cycle $_currentCycle of ${_config.cycles}',
// //                     style: Theme.of(context).textTheme.titleMedium,
// //                   ),
// //                 ],
// //               ),
// //           ],
// //         );
// //       },
// //       journaling: (_, __, ___) => throw Exception('Invalid config type'),
// //       meditation: (_, __, ___) => throw Exception('Invalid config type'),
// //       affirmations: (_, __, ___) => throw Exception('Invalid config type'),
// //       moodTracking: (_, __) => throw Exception('Invalid config type'),
// //     );
// //   }

// //   double _getAnimationSize(String phase) {
// //     switch (phase.toLowerCase()) {
// //       case 'inhale':
// //         return 150;
// //       case 'hold':
// //       case 'hold after exhale':
// //         return 100;
// //       case 'exhale':
// //         return 80;
// //       default:
// //         return 100;
// //     }
// //   }
// // }
// // lib/src/features/journal/widgets/activities/breathwork_activity.dart

// import 'dart:math' as math;

// // class BreathworkActivity extends ConsumerStatefulWidget {
// //   final Activity activity;
// //   final VoidCallback? onComplete;
// //   final bool standalone;

// //   const BreathworkActivity({
// //     super.key,
// //     required this.activity,
// //     this.onComplete,
// //     this.standalone = false,
// //   });

// //   @override
// //   ConsumerState<BreathworkActivity> createState() => _BreathworkActivityState();
// // }

// // class _BreathworkActivityState extends ConsumerState<BreathworkActivity>
// //     with SingleTickerProviderStateMixin {
// //   late AnimationController _animationController;
// //   Timer? _timer;
// //   int _currentCycle = 0;
// //   String _phase = 'ready';
// //   bool _isPaused = false;
// //   int _totalSeconds = 0;
// //   int _elapsedSeconds = 0;
// //   late BreathePattern _currentPattern;

// //   @override
// //   void initState() {
// //     super.initState();
// //     _animationController = AnimationController(
// //       vsync: this,
// //       duration: const Duration(seconds: 1),
// //     )..repeat();
// //     _initializeBreathwork();
// //   }

// //   void _initializeBreathwork() {
// //     widget.activity.config.maybeWhen(
// //       breathwork: (selectedPatternId, cycles, availablePatterns) {
// //         _currentPattern = availablePatterns.firstWhere(
// //           (p) => p.id == selectedPatternId,
// //           orElse: () => defaultBreathworkPatterns.first,
// //         );
// //         _calculateTotalSeconds(cycles);
// //       },
// //       orElse: () {},
// //     );
// //   }

// //   void _calculateTotalSeconds(int cycles) {
// //     _totalSeconds = cycles *
// //         (_currentPattern.inhaleSeconds +
// //             _currentPattern.holdSeconds +
// //             _currentPattern.exhaleSeconds +
// //             _currentPattern.holdAfterExhaleSeconds);
// //   }

// //   void _startBreathwork() {
// //     setState(() {
// //       _phase = 'inhale';
// //       _currentCycle = 1;
// //       _isPaused = false;
// //     });
// //     _startTimer();
// //   }

// //   void _togglePause() {
// //     setState(() {
// //       _isPaused = !_isPaused;
// //       if (_isPaused) {
// //         _timer?.cancel();
// //       } else {
// //         _startTimer();
// //       }
// //     });
// //   }

// //   void _startTimer() {
// //     _timer?.cancel();
// //     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
// //       if (!_isPaused) {
// //         setState(() {
// //           _elapsedSeconds++;
// //           final cycleSeconds = _currentPattern.inhaleSeconds +
// //               _currentPattern.holdSeconds +
// //               _currentPattern.exhaleSeconds +
// //               _currentPattern.holdAfterExhaleSeconds;

// //           final cycleProgress = _elapsedSeconds % cycleSeconds;

// //           if (cycleProgress < _currentPattern.inhaleSeconds) {
// //             _phase = 'breathe in';
// //           } else if (cycleProgress <
// //               _currentPattern.inhaleSeconds + _currentPattern.holdSeconds) {
// //             _phase = 'hold';
// //           } else if (cycleProgress <
// //               _currentPattern.inhaleSeconds +
// //                   _currentPattern.holdSeconds +
// //                   _currentPattern.exhaleSeconds) {
// //             _phase = 'breathe out';
// //           } else {
// //             _phase = 'hold';
// //           }

// //           _currentCycle = (_elapsedSeconds ~/ cycleSeconds) + 1;

// //           if (_elapsedSeconds >= _totalSeconds) {
// //             _timer?.cancel();
// //             _phase = 'complete';
// //             widget.onComplete?.call();
// //           }
// //         });
// //       }
// //     });
// //   }

// //   @override
// //   void dispose() {
// //     _timer?.cancel();
// //     _animationController.dispose();
// //     super.dispose();
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     return Material(
// //       color: Colors.black,
// //       child: SafeArea(
// //         child: Column(
// //           mainAxisAlignment: MainAxisAlignment.center,
// //           children: [
// //             // Timer display
// //             Padding(
// //               padding: const EdgeInsets.symmetric(horizontal: 16),
// //               child: Row(
// //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
// //                 children: [
// //                   IconButton(
// //                     icon: const Icon(Icons.settings, color: Colors.white),
// //                     onPressed: () {
// //                       // Show settings bottom sheet
// //                     },
// //                   ),
// //                   Text(
// //                     '${(_totalSeconds - _elapsedSeconds) ~/ 60}:${((_totalSeconds - _elapsedSeconds) % 60).toString().padLeft(2, '0')}',
// //                     style: const TextStyle(
// //                       fontSize: 24,
// //                       color: Colors.white,
// //                       fontWeight: FontWeight.w500,
// //                     ),
// //                   ),
// //                   IconButton(
// //                     icon: const Icon(Icons.close, color: Colors.white),
// //                     onPressed: () => Navigator.of(context).pop(),
// //                   ),
// //                 ],
// //               ),
// //             ),

// //             const Spacer(),

// //             // Breathing visualization
// //             SizedBox(
// //               height: 300,
// //               width: 300,
// //               child: Stack(
// //                 alignment: Alignment.center,
// //                 children: [
// //                   // Background gradient circle
// //                   Container(
// //                     decoration: BoxDecoration(
// //                       shape: BoxShape.circle,
// //                       gradient: LinearGradient(
// //                         begin: Alignment.topLeft,
// //                         end: Alignment.bottomRight,
// //                         colors: [
// //                           Colors.purple.shade400,
// //                           Colors.blue.shade400,
// //                         ],
// //                       ),
// //                     ),
// //                   ),

// //                   // Progress circle with fixed points
// //                   AnimatedBuilder(
// //                     animation: _animationController,
// //                     builder: (context, child) {
// //                       return CustomPaint(
// //                         size: const Size(280, 280),
// //                         painter: BreathworkProgressPainter(
// //                           progress: _elapsedSeconds / _totalSeconds,
// //                           pattern: _currentPattern,
// //                         ),
// //                       );
// //                     },
// //                   ),

// //                   // Phase text
// //                   Text(
// //                     _phase,
// //                     style: const TextStyle(
// //                       color: Colors.white,
// //                       fontSize: 32,
// //                       fontWeight: FontWeight.w300,
// //                     ),
// //                   ),
// //                 ],
// //               ),
// //             ),

// //             const Spacer(),

// //             // Controls
// //             Padding(
// //               padding: const EdgeInsets.only(bottom: 32),
// //               child: Row(
// //                 mainAxisAlignment: MainAxisAlignment.center,
// //                 children: [
// //                   if (_phase == 'ready')
// //                     IconButton(
// //                       icon: const Icon(Icons.play_circle_outline,
// //                           size: 64, color: Colors.white),
// //                       onPressed: _startBreathwork,
// //                     )
// //                   else
// //                     IconButton(
// //                       icon: Icon(
// //                         _isPaused
// //                             ? Icons.play_circle_outline
// //                             : Icons.pause_circle_outline,
// //                         size: 64,
// //                         color: Colors.white,
// //                       ),
// //                       onPressed: _togglePause,
// //                     ),
// //                 ],
// //               ),
// //             ),
// //           ],
// //         ),
// //       ),
// //     );
// //   }
// // }

// // class BreathworkProgressPainter extends CustomPainter {
// //   final double progress;
// //   final BreathePattern pattern;

// //   BreathworkProgressPainter({
// //     required this.progress,
// //     required this.pattern,
// //   });

// //   @override
// //   void paint(Canvas canvas, Size size) {
// //     final center = Offset(size.width / 2, size.height / 2);
// //     final radius = size.width / 2;

// //     // Draw track
// //     final trackPaint = Paint()
// //       ..color = Colors.white.withOpacity(0.2)
// //       ..style = PaintingStyle.stroke
// //       ..strokeWidth = 4;

// //     canvas.drawCircle(center, radius, trackPaint);

// //     // Draw progress
// //     final progressPaint = Paint()
// //       ..color = Colors.white
// //       ..style = PaintingStyle.stroke
// //       ..strokeWidth = 4
// //       ..strokeCap = StrokeCap.round;

// //     final progressRect = Rect.fromCircle(center: center, radius: radius);
// //     canvas.drawArc(
// //       progressRect,
// //       -math.pi / 2,
// //       2 * math.pi * progress,
// //       false,
// //       progressPaint,
// //     );

// //     // Draw fixed points
// //     final pointPaint = Paint()
// //       ..color = Colors.white
// //       ..style = PaintingStyle.fill;

// //     final totalDuration = pattern.inhaleSeconds +
// //         pattern.holdSeconds +
// //         pattern.exhaleSeconds +
// //         pattern.holdAfterExhaleSeconds;

// //     void drawPoint(double angle) {
// //       final pointCenter = Offset(
// //         center.dx + radius * math.cos(angle - math.pi / 2),
// //         center.dy + radius * math.sin(angle - math.pi / 2),
// //       );
// //       canvas.drawCircle(pointCenter, 4, pointPaint);
// //     }

// //     // Draw points at phase transitions
// //     drawPoint(0); // Start/inhale
// //     drawPoint(2 * math.pi * pattern.inhaleSeconds / totalDuration); // Hold
// //     drawPoint(2 *
// //         math.pi *
// //         (pattern.inhaleSeconds + pattern.holdSeconds) /
// //         totalDuration); // Exhale
// //     drawPoint(2 *
// //         math.pi *
// //         (pattern.inhaleSeconds + pattern.holdSeconds + pattern.exhaleSeconds) /
// //         totalDuration); // Hold after exhale
// //   }

// //   @override
// //   bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
// // }

// // lib/src/features/journal/widgets/activities/breathwork_activity.dart
// // lib/src/features/journal/widgets/activities/breathwork_activity.dart

// // class BreathworkActivity extends ConsumerStatefulWidget {
// //   final Activity activity;
// //   final VoidCallback? onComplete;
// //   final bool standalone;

// //   const BreathworkActivity({
// //     super.key,
// //     required this.activity,
// //     this.onComplete,
// //     this.standalone = false,
// //   });

// //   @override
// //   ConsumerState<BreathworkActivity> createState() => _BreathworkActivityState();
// // }

// // class _BreathworkActivityState extends ConsumerState<BreathworkActivity>
// //     with SingleTickerProviderStateMixin {
// //   late AnimationController _animationController;
// //   Timer? _timer;
// //   int _currentCycle = 0;
// //   String _phase = 'ready';
// //   bool _isPaused = false;
// //   int _totalSeconds = 0;
// //   int _elapsedSeconds = 0;
// //   late BreathePattern _currentPattern;

// //   @override
// //   void initState() {
// //     super.initState();
// //     _animationController = AnimationController(
// //       vsync: this,
// //       duration: const Duration(seconds: 1),
// //     )..repeat();
// //     _initializeBreathwork();
// //   }

// //   void _initializeBreathwork() {
// //     widget.activity.config.maybeWhen(
// //       breathwork: (selectedPatternId, cycles, availablePatterns) {
// //         _currentPattern = availablePatterns.firstWhere(
// //           (p) => p.id == selectedPatternId,
// //           orElse: () => defaultBreathworkPatterns.first,
// //         );
// //         _calculateTotalSeconds(cycles);
// //       },
// //       orElse: () {},
// //     );
// //   }

// //   void _calculateTotalSeconds(int cycles) {
// //     _totalSeconds = cycles *
// //         (_currentPattern.inhaleSeconds +
// //             _currentPattern.holdSeconds +
// //             _currentPattern.exhaleSeconds +
// //             _currentPattern.holdAfterExhaleSeconds);
// //   }

// //   void _startBreathwork() {
// //     setState(() {
// //       _phase = 'inhale';
// //       _currentCycle = 1;
// //       _isPaused = false;
// //     });
// //     _startTimer();
// //   }

// //   void _togglePause() {
// //     setState(() {
// //       _isPaused = !_isPaused;
// //       if (_isPaused) {
// //         _timer?.cancel();
// //       } else {
// //         _startTimer();
// //       }
// //     });
// //   }

// //   void _startTimer() {
// //     _timer?.cancel();
// //     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
// //       if (!_isPaused) {
// //         setState(() {
// //           _elapsedSeconds++;
// //           final cycleSeconds = _currentPattern.inhaleSeconds +
// //               _currentPattern.holdSeconds +
// //               _currentPattern.exhaleSeconds +
// //               _currentPattern.holdAfterExhaleSeconds;

// //           final cycleProgress = _elapsedSeconds % cycleSeconds;

// //           if (cycleProgress < _currentPattern.inhaleSeconds) {
// //             _phase = 'breathe in';
// //           } else if (cycleProgress <
// //               _currentPattern.inhaleSeconds + _currentPattern.holdSeconds) {
// //             _phase = _currentPattern.holdSeconds > 0 ? 'hold' : 'breathe out';
// //           } else if (cycleProgress <
// //               _currentPattern.inhaleSeconds +
// //                   _currentPattern.holdSeconds +
// //                   _currentPattern.exhaleSeconds) {
// //             _phase = 'breathe out';
// //           } else {
// //             _phase = _currentPattern.holdAfterExhaleSeconds > 0
// //                 ? 'hold'
// //                 : 'breathe in';
// //           }

// //           _currentCycle = (_elapsedSeconds ~/ cycleSeconds) + 1;

// //           if (_elapsedSeconds >= _totalSeconds) {
// //             _timer?.cancel();
// //             _phase = 'complete';
// //             widget.onComplete?.call();
// //           }
// //         });
// //       }
// //     });
// //   }

// //   @override
// //   void dispose() {
// //     _timer?.cancel();
// //     _animationController.dispose();
// //     super.dispose();
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     return Material(
// //       color: Colors.black,
// //       child: SafeArea(
// //         child: Column(
// //           mainAxisAlignment: MainAxisAlignment.center,
// //           children: [
// //             // Timer display
// //             Padding(
// //               padding: const EdgeInsets.symmetric(horizontal: 16),
// //               child: Row(
// //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
// //                 children: [
// //                   IconButton(
// //                     icon: const Icon(Icons.settings, color: Colors.white),
// //                     onPressed: () async {
// //                       final result = await Navigator.push<Activity>(
// //                         context,
// //                         MaterialPageRoute(
// //                           builder: (context) => BreathworkSettingsScreen(
// //                             activity: widget.activity,
// //                           ),
// //                         ),
// //                       );

// //                       if (result != null) {
// //                         setState(() {
// //                           _initializeBreathwork();
// //                         });
// //                       }
// //                     },
// //                   ),
// //                   Text(
// //                     '${(_totalSeconds - _elapsedSeconds) ~/ 60}:${((_totalSeconds - _elapsedSeconds) % 60).toString().padLeft(2, '0')}',
// //                     style: const TextStyle(
// //                       fontSize: 24,
// //                       color: Colors.white,
// //                       fontWeight: FontWeight.w500,
// //                     ),
// //                   ),
// //                   IconButton(
// //                     icon: const Icon(Icons.close, color: Colors.white),
// //                     onPressed: () => Navigator.of(context).pop(),
// //                   ),
// //                 ],
// //               ),
// //             ),

// //             const Spacer(),

// //             // Breathing visualization
// //             SizedBox(
// //               height: 300,
// //               width: 300,
// //               child: Stack(
// //                 alignment: Alignment.center,
// //                 children: [
// //                   // Background gradient circle
// //                   Container(
// //                     decoration: BoxDecoration(
// //                       shape: BoxShape.circle,
// //                       gradient: LinearGradient(
// //                         begin: Alignment.topLeft,
// //                         end: Alignment.bottomRight,
// //                         colors: [
// //                           Colors.purple.shade400,
// //                           Colors.blue.shade400,
// //                         ],
// //                       ),
// //                     ),
// //                   ),

// //                   // Progress circle with fixed points
// //                   AnimatedBuilder(
// //                     animation: _animationController,
// //                     builder: (context, child) {
// //                       return CustomPaint(
// //                         size: const Size(280, 280),
// //                         painter: BreathworkProgressPainter(
// //                           totalSeconds: _totalSeconds,
// //                           progress: _elapsedSeconds,
// //                           pattern: _currentPattern,
// //                         ),
// //                       );
// //                     },
// //                   ),

// //                   // Phase text
// //                   Text(
// //                     _phase,
// //                     style: const TextStyle(
// //                       color: Colors.white,
// //                       fontSize: 32,
// //                       fontWeight: FontWeight.w300,
// //                     ),
// //                   ),
// //                 ],
// //               ),
// //             ),

// //             const Spacer(),

// //             // Controls
// //             Padding(
// //               padding: const EdgeInsets.only(bottom: 32),
// //               child: Row(
// //                 mainAxisAlignment: MainAxisAlignment.center,
// //                 children: [
// //                   if (_phase == 'ready')
// //                     IconButton(
// //                       icon: const Icon(Icons.play_circle_outline,
// //                           size: 64, color: Colors.white),
// //                       onPressed: _startBreathwork,
// //                     )
// //                   else
// //                     IconButton(
// //                       icon: Icon(
// //                         _isPaused
// //                             ? Icons.play_circle_outline
// //                             : Icons.pause_circle_outline,
// //                         size: 64,
// //                         color: Colors.white,
// //                       ),
// //                       onPressed: _togglePause,
// //                     ),
// //                 ],
// //               ),
// //             ),
// //           ],
// //         ),
// //       ),
// //     );
// //   }
// // }

// // class BreathworkProgressPainter extends CustomPainter {
// //   final int progress;
// //   final BreathePattern pattern;
// //   final int totalSeconds;

// //   BreathworkProgressPainter({
// //     required this.progress,
// //     required this.pattern,
// //     required this.totalSeconds,
// //   });

// //   @override
// //   void paint(Canvas canvas, Size size) {
// //     final center = Offset(size.width / 2, size.height / 2);
// //     final radius = size.width / 2;

// //     // Draw track
// //     final trackPaint = Paint()
// //       ..color = Colors.white.withOpacity(0.2)
// //       ..style = PaintingStyle.stroke
// //       ..strokeWidth = 4;

// //     canvas.drawCircle(center, radius, trackPaint);

// //     // Draw progress
// //     final progressPaint = Paint()
// //       ..color = Colors.white
// //       ..style = PaintingStyle.stroke
// //       ..strokeWidth = 4
// //       ..strokeCap = StrokeCap.round;

// //     final cycleSeconds = pattern.inhaleSeconds +
// //         pattern.holdSeconds +
// //         pattern.exhaleSeconds +
// //         pattern.holdAfterExhaleSeconds;

// //     // Calculate progress within the current cycle
// //     final cycleProgress =
// //         (progress * totalSeconds) % cycleSeconds / cycleSeconds;

// //     final progressRect = Rect.fromCircle(center: center, radius: radius);
// //     canvas.drawArc(
// //       progressRect,
// //       -math.pi / 2,
// //       2 * math.pi * cycleProgress,
// //       false,
// //       progressPaint,
// //     );

// //     // Draw phase transition points
// //     final pointPaint = Paint()
// //       ..color = Colors.white
// //       ..style = PaintingStyle.fill;

// //     void drawPoint(double angle) {
// //       final pointCenter = Offset(
// //         center.dx + radius * math.cos(angle - math.pi / 2),
// //         center.dy + radius * math.sin(angle - math.pi / 2),
// //       );
// //       canvas.drawCircle(pointCenter, 4, pointPaint);
// //     }

// //     // Always draw inhale start point
// //     drawPoint(0);

// //     var currentAngle = pattern.inhaleSeconds / cycleSeconds * 2 * math.pi;

// //     // Draw hold point only if there's a hold phase
// //     if (pattern.holdSeconds > 0) {
// //       drawPoint(currentAngle);
// //       currentAngle += pattern.holdSeconds / cycleSeconds * 2 * math.pi;
// //     }

// //     // Draw exhale point
// //     drawPoint(currentAngle);
// //     currentAngle += pattern.exhaleSeconds / cycleSeconds * 2 * math.pi;

// //     // Draw hold after exhale point only if there's a hold phase
// //     if (pattern.holdAfterExhaleSeconds > 0) {
// //       drawPoint(currentAngle);
// //     }
// //   }

// //   @override
// //   bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
// // }

// // lib/src/features/journal/widgets/activities/breathwork_activity.dart

// class BreathworkActivity extends ConsumerStatefulWidget {
//   final Activity activity;
//   final VoidCallback? onComplete;
//   final bool standalone;

//   const BreathworkActivity({
//     super.key,
//     required this.activity,
//     this.onComplete,
//     this.standalone = false,
//   });

//   @override
//   ConsumerState<BreathworkActivity> createState() => _BreathworkActivityState();
// }

// class _BreathworkActivityState extends ConsumerState<BreathworkActivity>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _animationController;
//   Timer? _timer;
//   int _currentCycle = 0;
//   String _phase = 'ready';
//   bool _isPaused = false;
//   int _totalSeconds = 0;
//   int _elapsedSeconds = 0;
//   late BreathePattern _currentPattern;

//   @override
//   void initState() {
//     super.initState();
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(seconds: 1),
//     )..repeat();
//     _initializeBreathwork();
//   }

//   void _initializeBreathwork() {
//     widget.activity.config.maybeWhen(
//       breathwork: (selectedPatternId, cycles, availablePatterns) {
//         _currentPattern = availablePatterns.firstWhere(
//           (p) => p.id == selectedPatternId,
//           orElse: () => defaultBreathworkPatterns.first,
//         );
//         _calculateTotalSeconds(cycles);
//       },
//       orElse: () {},
//     );
//   }

//   void _calculateTotalSeconds(int cycles) {
//     _totalSeconds = cycles *
//         (_currentPattern.inhaleSeconds +
//             _currentPattern.holdSeconds +
//             _currentPattern.exhaleSeconds +
//             _currentPattern.holdAfterExhaleSeconds);
//   }

//   void _startBreathwork() {
//     setState(() {
//       _phase = 'inhale';
//       _currentCycle = 1;
//       _isPaused = false;
//     });
//     _startTimer();
//   }

//   void _togglePause() {
//     setState(() {
//       _isPaused = !_isPaused;
//       if (_isPaused) {
//         _timer?.cancel();
//       } else {
//         _startTimer();
//       }
//     });
//   }

//   void _startTimer() {
//     _timer?.cancel();
//     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
//       if (!_isPaused) {
//         setState(() {
//           _elapsedSeconds++;
//           final cycleSeconds = _currentPattern.inhaleSeconds +
//               _currentPattern.holdSeconds +
//               _currentPattern.exhaleSeconds +
//               _currentPattern.holdAfterExhaleSeconds;

//           final cycleProgress = _elapsedSeconds % cycleSeconds;

//           if (cycleProgress < _currentPattern.inhaleSeconds) {
//             _phase = 'breathe in';
//           } else if (cycleProgress <
//               _currentPattern.inhaleSeconds + _currentPattern.holdSeconds) {
//             _phase = _currentPattern.holdSeconds > 0 ? 'hold' : 'breathe out';
//           } else if (cycleProgress <
//               _currentPattern.inhaleSeconds +
//                   _currentPattern.holdSeconds +
//                   _currentPattern.exhaleSeconds) {
//             _phase = 'breathe out';
//           } else {
//             _phase = _currentPattern.holdAfterExhaleSeconds > 0
//                 ? 'hold'
//                 : 'breathe in';
//           }

//           _currentCycle = (_elapsedSeconds ~/ cycleSeconds) + 1;

//           if (_elapsedSeconds >= _totalSeconds) {
//             _timer?.cancel();
//             _phase = 'complete';
//             widget.onComplete?.call();
//           }
//         });
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _timer?.cancel();
//     _animationController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Material(
//       color: Colors.black,
//       child: SafeArea(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             // Timer display
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 16),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   IconButton(
//                     icon: const Icon(Icons.settings, color: Colors.white),
//                     onPressed: () async {
//                       final result = await Navigator.push<Activity>(
//                         context,
//                         MaterialPageRoute(
//                           builder: (context) => BreathworkSettingsScreen(
//                             activity: widget.activity,
//                           ),
//                         ),
//                       );

//                       if (result != null) {
//                         setState(() {
//                           _initializeBreathwork();
//                         });
//                       }
//                     },
//                   ),
//                   Padding(
//                     padding: const EdgeInsets.symmetric(
//                         horizontal: 24, vertical: 16),
//                     child: Column(
//                       children: [
//                         Text(
//                           _currentPattern.name,
//                           style: const TextStyle(
//                             color: Colors.white,
//                             fontSize: 24,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                         const SizedBox(height: 8),
//                         Text(
//                           _currentPattern.description,
//                           style: TextStyle(
//                             color: Colors.white.withOpacity(0.8),
//                             fontSize: 16,
//                           ),
//                           textAlign: TextAlign.center,
//                         ),
//                       ],
//                     ),
//                   ),
//                   // Text(
//                   //   '${(_totalSeconds - _elapsedSeconds) ~/ 60}:${((_totalSeconds - _elapsedSeconds) % 60).toString().padLeft(2, '0')}',
//                   //   style: const TextStyle(
//                   //     fontSize: 24,
//                   //     color: Colors.white,
//                   //     fontWeight: FontWeight.w500,
//                   //   ),
//                   // ),
//                   IconButton(
//                     icon: const Icon(Icons.close, color: Colors.white),
//                     onPressed: () => Navigator.of(context).pop(),
//                   ),
//                 ],
//               ),
//             ),

//             const Spacer(),

//             // Breathing visualization
//             SizedBox(
//               height: 300,
//               width: 300,
//               child: Stack(
//                 alignment: Alignment.center,
//                 children: [
//                   // Background gradient circle
//                   Container(
//                     decoration: BoxDecoration(
//                       shape: BoxShape.circle,
//                       gradient: LinearGradient(
//                         begin: Alignment.topLeft,
//                         end: Alignment.bottomRight,
//                         colors: [
//                           Colors.purple.shade400,
//                           Colors.blue.shade400,
//                         ],
//                       ),
//                     ),
//                   ),

//                   // Progress circle with fixed points
//                   AnimatedBuilder(
//                     animation: _animationController,
//                     builder: (context, child) {
//                       return CustomPaint(
//                         size: const Size(280, 280),
//                         painter: BreathworkProgressPainter(
//                           totalSeconds: _totalSeconds,
//                           progress: _elapsedSeconds,
//                           pattern: _currentPattern,
//                         ),
//                       );
//                     },
//                   ),

//                   // Phase text
//                   Text(
//                     _phase,
//                     style: const TextStyle(
//                       color: Colors.white,
//                       fontSize: 32,
//                       fontWeight: FontWeight.w300,
//                     ),
//                   ),
//                 ],
//               ),
//             ),

//             const Spacer(),

//             // Controls
//             Padding(
//               padding: const EdgeInsets.only(bottom: 32),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   if (_phase == 'ready')
//                     IconButton(
//                       icon: const Icon(Icons.play_circle_outline,
//                           size: 64, color: Colors.white),
//                       onPressed: _startBreathwork,
//                     )
//                   else
//                     IconButton(
//                       icon: Icon(
//                         _isPaused
//                             ? Icons.play_circle_outline
//                             : Icons.pause_circle_outline,
//                         size: 64,
//                         color: Colors.white,
//                       ),
//                       onPressed: _togglePause,
//                     ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class BreathworkProgressPainter extends CustomPainter {
//   final int progress;
//   final BreathePattern pattern;
//   final int totalSeconds;

//   BreathworkProgressPainter({
//     required this.progress,
//     required this.pattern,
//     required this.totalSeconds,
//   });

//   @override
//   void paint(Canvas canvas, Size size) {
//     final center = Offset(size.width / 2, size.height / 2);
//     final radius = size.width / 2;

//     // Draw track
//     final trackPaint = Paint()
//       ..color = Colors.white.withOpacity(0.2)
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = 4;

//     canvas.drawCircle(center, radius, trackPaint);

//     // Draw progress
//     final progressPaint = Paint()
//       ..color = Colors.white
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = 4
//       ..strokeCap = StrokeCap.round;

//     final cycleSeconds = pattern.inhaleSeconds +
//         pattern.holdSeconds +
//         pattern.exhaleSeconds +
//         pattern.holdAfterExhaleSeconds;

//     // Calculate progress within the current cycle
//     final cycleProgress =
//         (progress * totalSeconds) % cycleSeconds / cycleSeconds;

//     final progressRect = Rect.fromCircle(center: center, radius: radius);
//     canvas.drawArc(
//       progressRect,
//       -math.pi / 2,
//       2 * math.pi * cycleProgress,
//       false,
//       progressPaint,
//     );

//     // Draw phase transition points
//     final pointPaint = Paint()
//       ..color = Colors.white
//       ..style = PaintingStyle.fill;

//     void drawPoint(double angle) {
//       final pointCenter = Offset(
//         center.dx + radius * math.cos(angle - math.pi / 2),
//         center.dy + radius * math.sin(angle - math.pi / 2),
//       );
//       canvas.drawCircle(pointCenter, 4, pointPaint);
//     }

//     // Always draw inhale start point
//     drawPoint(0);

//     var currentAngle = pattern.inhaleSeconds / cycleSeconds * 2 * math.pi;

//     // Draw hold point only if there's a hold phase
//     if (pattern.holdSeconds > 0) {
//       drawPoint(currentAngle);
//       currentAngle += pattern.holdSeconds / cycleSeconds * 2 * math.pi;
//     }

//     // Draw exhale point
//     drawPoint(currentAngle);
//     currentAngle += pattern.exhaleSeconds / cycleSeconds * 2 * math.pi;

//     // Draw hold after exhale point only if there's a hold phase
//     if (pattern.holdAfterExhaleSeconds > 0) {
//       drawPoint(currentAngle);
//     }
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
// }
