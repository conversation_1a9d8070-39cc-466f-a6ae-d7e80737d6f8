// // lib/src/features/journal/widgets/activities/affirmations_activity.dart
// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:just_audio/just_audio.dart';
// import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';

// import '../../../activities/shared/screens/base_activity.dart';

// class AffirmationsActivity extends BaseActivity {
//   const AffirmationsActivity({
//     super.key,
//     required super.activity,
//     super.onComplete,
//     super.standalone = false,
//   });

//   @override
//   ConsumerState<AffirmationsActivity> createState() =>
//       _AffirmationsActivityState();
// }

// class _AffirmationsActivityState
//     extends BaseActivityState<AffirmationsActivity> {
//   late final AudioPlayer _player;
//   bool _isPlaying = false;
//   String? _selectedAudio;
//   int _playCount = 0;
//   Duration _currentDuration = Duration.zero;
//   Duration _totalDuration = Duration.zero;
//   StreamSubscription<PlayerState>? _playerStateSubscription;

//   AffirmationsConfig get _config => widget.activity.config.maybeWhen(
//         affirmations: (audioAssets, selectedAudioAsset, autoPlay) =>
//             AffirmationsConfig(
//           audioAssets: audioAssets,
//           selectedAudioAsset: selectedAudioAsset,
//           autoPlay: autoPlay,
//         ),
//         orElse: () => throw Exception('Invalid config type'),
//       );

//   @override
//   void initState() {
//     super.initState();
//     _player = AudioPlayer();
//     widget.activity.config.whenOrNull(
//       affirmations: (audioAssets, selectedAudioAsset, autoPlay) {
//         _selectedAudio = selectedAudioAsset;
//       },
//     );
//     _initializeAudio();
//     _setupPlayerListeners();
//   }

//   Future<void> _initializeAudio() async {
//     widget.activity.config.whenOrNull(
//       affirmations: (audioAssets, selectedAudioAsset, autoPlay) async {
//         if (selectedAudioAsset != null) {
//           try {
//             await _player.setAsset(selectedAudioAsset);
//             _totalDuration = (await _player.duration) ?? Duration.zero;
//             if (autoPlay) {
//               _startPlaying();
//             }
//           } catch (e) {
//             debugPrint('Error loading audio: $e');
//           }
//         }
//       },
//     );
//   }

//   void _setupPlayerListeners() {
//     _playerStateSubscription = _player.playerStateStream.listen((state) {
//       if (state.processingState == ProcessingState.completed) {
//         _playCount++;
//         if (_playCount >= 3) {
//           markComplete();
//         } else {
//           widget.activity.config.whenOrNull(
//             affirmations: (_, __, autoPlay) {
//               if (autoPlay) {
//                 _player.seek(Duration.zero);
//                 _player.play();
//               }
//             },
//           );
//         }
//       }
//     });

//     _player.positionStream.listen((position) {
//       setState(() => _currentDuration = position);
//     });
//   }

//   void _startPlaying() async {
//     if (_selectedAudio != null) {
//       await _player.seek(Duration.zero);
//       await _player.play();
//       setState(() => _isPlaying = true);
//     }
//   }

//   void _togglePlayPause() async {
//     if (_isPlaying) {
//       await _player.pause();
//     } else {
//       await _player.play();
//     }
//     setState(() => _isPlaying = !_isPlaying);
//   }

//   String _formatDuration(Duration duration) {
//     String twoDigits(int n) => n.toString().padLeft(2, '0');
//     final minutes = twoDigits(duration.inMinutes.remainder(60));
//     final seconds = twoDigits(duration.inSeconds.remainder(60));
//     return '$minutes:$seconds';
//   }

//   @override
//   void dispose() {
//     _playerStateSubscription?.cancel();
//     _player.dispose();
//     super.dispose();
//   }

//   @override
//   IconData getActivityIcon() => Icons.record_voice_over;

//   @override
//   Widget buildActivityContent(BuildContext context) {
//     return widget.activity.config.when(
//       affirmations: (audioAssets, selectedAudioAsset, autoPlay) {
//         final theme = Theme.of(context);

//         return Column(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             if (audioAssets.isNotEmpty) ...[
//               DropdownButton<String>(
//                 value: _selectedAudio,
//                 hint: const Text('Select affirmations'),
//                 isExpanded: true,
//                 items: audioAssets.map((asset) {
//                   return DropdownMenuItem(
//                     value: asset,
//                     child: Text(asset.split('/').last),
//                   );
//                 }).toList(),
//                 onChanged: !_isPlaying
//                     ? (value) async {
//                         setState(() => _selectedAudio = value);
//                         await _initializeAudio();
//                       }
//                     : null,
//               ),
//               const SizedBox(height: 24),
//             ],
//             Card(
//               elevation: 4,
//               child: Padding(
//                 padding: const EdgeInsets.all(16),
//                 child: Column(
//                   children: [
//                     Text(
//                       'Listen to your daily affirmations',
//                       style: theme.textTheme.titleMedium,
//                     ),
//                     const SizedBox(height: 16),
//                     Text(
//                       'Times Played: $_playCount/3',
//                       style: theme.textTheme.bodyLarge,
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             const SizedBox(height: 24),
//             if (_selectedAudio != null) ...[
//               Slider(
//                 value: _currentDuration.inSeconds.toDouble(),
//                 max: _totalDuration.inSeconds.toDouble(),
//                 onChanged: (value) {
//                   _player.seek(Duration(seconds: value.toInt()));
//                 },
//               ),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(_formatDuration(_currentDuration)),
//                   Text(_formatDuration(_totalDuration)),
//                 ],
//               ),
//               const SizedBox(height: 16),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   IconButton(
//                     icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
//                     onPressed: _togglePlayPause,
//                     iconSize: 48,
//                   ),
//                   const SizedBox(width: 16),
//                   StreamBuilder<double>(
//                     stream: _player.volumeStream,
//                     builder: (context, snapshot) {
//                       final volume = snapshot.data ?? 1.0;
//                       return Row(
//                         children: [
//                           Icon(
//                             volume == 0
//                                 ? Icons.volume_off
//                                 : volume < 0.5
//                                     ? Icons.volume_down
//                                     : Icons.volume_up,
//                           ),
//                           SizedBox(
//                             width: 100,
//                             child: Slider(
//                               value: volume,
//                               onChanged: (value) => _player.setVolume(value),
//                             ),
//                           ),
//                         ],
//                       );
//                     },
//                   ),
//                 ],
//               ),
//             ],
//           ],
//         );
//       },
//       journaling: (_, __, ___) => throw Exception('Invalid config type'),
//       meditation: (_, __, ___) => throw Exception('Invalid config type'),
//       breathwork: (_, __, ___) => throw Exception('Invalid config type'),
//       moodTracking: (_, __) => throw Exception('Invalid config type'),
//     );
//   }
// }
