// lib/src/features/journal/services/checkin_service.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_data.dart';

import 'package:mimi_app/src/features/journal/providers/providers.dart';

import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart';

import '../../database/providers/database_provider.dart';

class CheckinService {
  final Ref ref;
  final JournalDatabase database;

  CheckinService(this.ref, this.database);

  Future<void> initialize() async {
    try {
      await _initializeDefaultActivities();
    } catch (e) {
      throw Exception('Failed to initialize Check-Ins: $e');
    }
  }

  Future<void> _initializeDefaultActivities() async {
    final defaultActivities = [
      (
        'journaling',
        'Journal Entry',
        ActivityConfig.journaling(
          prompts: [],
          includeDate: true,
        ),
      ),
      (
        'meditation',
        'Meditation',
        ActivityConfig.meditation(
          audioTracks: [],
        ),
      ),
      (
        'breathwork',
        'Breathwork',
        ActivityConfig.breathwork(
          selectedPatternId: defaultBreathworkPatterns.first.id,
          availablePatterns: defaultBreathworkPatterns,
          cycles: 5,
        ),
      ),
      (
        'affirmations',
        'Daily Affirmations',
        ActivityConfig.affirmations(
          audioTracks: [],
          autoPlay: true,
        ),
      ),
      (
        'moodTracking',
        'Mood Check',
        ActivityConfig.moodTracking(
          moods: ['😊 Happy', '😐 Neutral', '😔 Sad', '😤 Angry', '😴 Tired'],
          includeNote: true,
        ),
      ),
      (
        'gratitude',
        'Gratitude Practice',
        ActivityConfig.gratitude(
          prompts: ['What are you grateful for today?'],
          numberOfEntries: 3,
        ),
      ),
    ];

    for (final activity in defaultActivities) {
      await database.createActivity(
        type: activity.$1,
        name: activity.$2,
        config: activity.$3.toJsonString(),
      );
    }
  }
}
