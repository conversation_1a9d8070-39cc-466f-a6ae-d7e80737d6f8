import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/providers/palette_provider.dart';

class DiscoverButtonGrid extends StatelessWidget {
  const DiscoverButtonGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildButton(
                  context,
                  'Focus',
                  Icons.timer_outlined,
                  () => context.pushNamed(RouteNames.timer),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: _buildButton(
                  context,
                  'Breathwork',
                  Icons.air_outlined,
                  () => context.pushNamed(RouteNames.breathwork),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: _buildButton(
                  context,
                  'Quotes',
                  Icons.format_quote_outlined,
                  () => context.pushNamed(RouteNames.quote),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              Expanded(
                child: _buildButton(
                  context,
                  'Affirmations',
                  Icons.format_quote_outlined,
                  () => context.push('/category/affirmations'),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: _buildButton(
                  context,
                  'Meditation',
                  Icons.self_improvement_outlined,
                  () => context.push('/category/meditation'),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: _buildButton(
                  context,
                  'Videos',
                  Icons.self_improvement_outlined,
                  () => context.pushNamed(RouteNames.video),
                ),
              ),
            ],
          ),
          // Row(
          //   children: [
          //     Expanded(
          //       child: _buildButton(
          //         context,
          //         'Chat with MimiAI',
          //         Icons.chat_bubble_outline,
          //         () => context.pushNamed(RouteNames.chat),
          //       ),
          //     ),
          //     const SizedBox(width: 4),
          //     Expanded(
          //       child: _buildButton(
          //         context,
          //         'Talk to MimiAI',
          //         Icons.mic,
          //         () => context.pushNamed(RouteNames.conversation),
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }

  Widget _buildButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 8,
      color: Theme.of(context).cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DiscoverHomeButtonGrid extends ConsumerWidget {
  const DiscoverHomeButtonGrid({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch palette changes to ensure widget rebuilds when palette changes
    ref.watch(paletteNotifierProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildButton(
                  context,
                  'Chat with MimiAI',
                  Icons.chat_bubble_outline,
                  () => context.pushNamed(RouteNames.chat),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: _buildButton(
                  context,
                  'Talk to MimiAI',
                  Icons.mic,
                  () => context.pushNamed(RouteNames.conversation),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 8,
      color: Theme.of(context).cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
