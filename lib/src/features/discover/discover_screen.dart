import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/discover/widgets/discover_button_grid.dart';
import 'package:mimi_app/src/features/home/<USER>/home_video_section.dart';
import 'package:mimi_app/src/features/home/<USER>/horizontal_track_section.dart';
import 'package:mimi_app/src/features/home/<USER>/wellness_card_grid.dart';
import 'package:mimi_app/src/features/home/<USER>/home_header.dart';
import 'package:mimi_app/src/features/home/<USER>/home_intention_section.dart';

class DiscoverScreen extends ConsumerWidget {
  const DiscoverScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      // showMiniPlayer: true,
      appBar: AppBar(
        title: const Text('Discover'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.zero,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const DiscoverButtonGrid(),
            const SizedBox(height: 24),
            // Meditations Section
            HorizontalTrackSection(
              title: 'Meditations',
              categoryId: 'meditation',
              onViewAllPressed: () => context.push('/category/meditation'),
            ),
            const SizedBox(height: 16),
            // Affirmations Section
            HorizontalTrackSection(
              title: 'Affirmations',
              categoryId: 'affirmations',
              onViewAllPressed: () => context.push('/category/affirmations'),
            ),
            const SizedBox(height: 24),
            HomeVideoSection(),
            const SizedBox(height: 60),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title,
      {Color? color}) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontSize: 26,
            fontWeight: FontWeight.bold,
            color: color,
          ),
    );
  }
}

/*!SECTION
Bottom Nav Bar

1. Today
2. Chat
3. Tools
4. Stats
5. Profile


Meditation
Breathwork
Affirmations
Quotes
Focus Timer
Talk to MimiAI
Videos







*/
