import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
// import 'package:mimi_app/src/features/tabs_test/mood_tracker.dart';

// // Enum for time period selection
// enum TimePeriod { week, month }

// final selectedPeriodProvider =
//     StateProvider<TimePeriod>((ref) => TimePeriod.week);

// class MoodHistoryScreen extends ConsumerWidget {
//   const MoodHistoryScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final selectedPeriod = ref.watch(selectedPeriodProvider);

//     return Scaffold(
//       backgroundColor: const Color(0xFF1A1B1E), // Dark background from image
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.all(20.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   const Text(
//                     'Track Your Daily Mood',
//                     style: TextStyle(
//                       fontSize: 20,
//                       fontWeight: FontWeight.bold,
//                       color: Colors.black,
//                     ),
//                   ),
//                   // Period selector
//                   Container(
//                     decoration: BoxDecoration(
//                       color: Colors.grey[900],
//                       borderRadius: BorderRadius.circular(20),
//                     ),
//                     child: Row(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         _buildPeriodButton(ref, TimePeriod.week, 'This week'),
//                         _buildPeriodButton(ref, TimePeriod.month, 'Month'),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 40),
//               SizedBox(
//                 height: 300,
//                 child: MoodChart(period: selectedPeriod),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildPeriodButton(WidgetRef ref, TimePeriod period, String label) {
//     final isSelected = ref.watch(selectedPeriodProvider) == period;

//     return GestureDetector(
//       onTap: () => ref.read(selectedPeriodProvider.notifier).state = period,
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//         decoration: BoxDecoration(
//           color:
//               isSelected ? Colors.white.withOpacity(0.1) : Colors.transparent,
//           borderRadius: BorderRadius.circular(20),
//         ),
//         child: Text(
//           label,
//           style: TextStyle(
//             color: isSelected ? Colors.white : Colors.grey,
//             fontSize: 14,
//           ),
//         ),
//       ),
//     );
//   }
// }

// class MoodChart extends ConsumerWidget {
//   final TimePeriod period;

//   const MoodChart({Key? key, required this.period}) : super(key: key);

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     // We'll use the mood entries stream from our repository
//     final moodEntriesAsync = ref.watch(moodEntriesProvider);

//     return moodEntriesAsync.when(
//       loading: () => const Center(child: CircularProgressIndicator()),
//       error: (error, stack) => Center(child: Text('Error: $error')),
//       data: (entries) {
//         final points = _processEntries(entries, period);
//         return LineChart(
//           LineChartData(
//             gridData: FlGridData(
//               show: true,
//               drawVerticalLine: true,
//               horizontalInterval: 1,
//               verticalInterval: 1,
//               getDrawingHorizontalLine: (value) {
//                 return FlLine(
//                   color: Colors.grey.withOpacity(0.1),
//                   strokeWidth: 1,
//                 );
//               },
//               getDrawingVerticalLine: (value) {
//                 return FlLine(
//                   color: Colors.grey.withOpacity(0.1),
//                   strokeWidth: 1,
//                 );
//               },
//             ),
//             titlesData: FlTitlesData(
//               bottomTitles: AxisTitles(
//                 sideTitles: SideTitles(
//                   showTitles: true,
//                   getTitlesWidget: (value, meta) {
//                     // Return day labels (Mon, Tue, etc.)
//                     final days = [
//                       'Mon',
//                       'Tue',
//                       'Wed',
//                       'Thu',
//                       'Fri',
//                       'Sat',
//                       'Sun'
//                     ];
//                     if (value >= 0 && value < days.length) {
//                       return Text(
//                         days[value.toInt()],
//                         style: const TextStyle(
//                           color: Colors.grey,
//                           fontSize: 12,
//                         ),
//                       );
//                     }
//                     return const Text('');
//                   },
//                 ),
//               ),
//               rightTitles:
//                   AxisTitles(sideTitles: SideTitles(showTitles: false)),
//               topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
//               leftTitles: AxisTitles(
//                 sideTitles: SideTitles(
//                   showTitles: true,
//                   interval: 1,
//                   getTitlesWidget: (value, meta) {
//                     // Show mood indicators
//                     if (value >= 0 && value < Mood.values.length) {
//                       return Container(
//                         margin: const EdgeInsets.only(right: 8),
//                         child: Container(
//                           width: 8,
//                           height: 8,
//                           decoration: BoxDecoration(
//                             shape: BoxShape.circle,
//                             color: Mood.values[value.toInt()].color,
//                           ),
//                         ),
//                       );
//                     }
//                     return const Text('');
//                   },
//                 ),
//               ),
//             ),
//             borderData: FlBorderData(show: false),
//             lineBarsData: [
//               LineChartBarData(
//                 spots: points,
//                 isCurved: true,
//                 color: Colors.white,
//                 barWidth: 2,
//                 //  isStropped: false,
//                 dotData: FlDotData(
//                   show: true,
//                   getDotPainter: (spot, percent, barData, index) {
//                     return FlDotCirclePainter(
//                       radius: 4,
//                       color: Colors.yellow,
//                       strokeWidth: 0,
//                     );
//                   },
//                 ),
//                 belowBarData: BarAreaData(
//                   show: true,
//                   color: Colors.white.withOpacity(0.1),
//                 ),
//               ),
//             ],
//             minX: 0,
//             maxX: period == TimePeriod.week ? 6 : 30,
//             minY: 0,
//             maxY: 4,
//           ),
//         );
//       },
//     );
//   }

//   List<FlSpot> _processEntries(List<MoodEntry> entries, TimePeriod period) {
//     // Process entries based on selected period
//     // This is a simplified example - you'll need to properly process your data
//     final now = DateTime.now();
//     final filteredEntries = entries.where((entry) {
//       final difference = now.difference(entry.timestamp);
//       return period == TimePeriod.week
//           ? difference.inDays <= 7
//           : difference.inDays <= 30;
//     }).toList();

//     // Convert entries to chart points
//     return filteredEntries.map((entry) {
//       final dayOfWeek = entry.timestamp.weekday - 1;
//       final moodValue = Mood.values.indexOf(entry.mood).toDouble();
//       return FlSpot(dayOfWeek.toDouble(), moodValue);
//     }).toList();
//   }
// }

enum TimePeriod { week, month }

final selectedPeriodProvider =
    StateProvider<TimePeriod>((ref) => TimePeriod.week);

class MoodHistoryScreen extends ConsumerWidget {
  const MoodHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPeriod = ref.watch(selectedPeriodProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Wrap the title in Expanded to prevent overflow
                  Expanded(
                    child: Text(
                      'Your Daily Mood',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Period selector
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize:
                          MainAxisSize.min, // Important to prevent overflow
                      children: [
                        _buildPeriodButton(
                            ref, TimePeriod.week, 'Week'), // Shortened text
                        _buildPeriodButton(ref, TimePeriod.month, 'Month'),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 250,
                child: Expanded(
                  // Wrap chart in Expanded
                  child: MoodChart(period: selectedPeriod),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodButton(WidgetRef ref, TimePeriod period, String label) {
    final isSelected = ref.watch(selectedPeriodProvider) == period;

    return GestureDetector(
      onTap: () => ref.read(selectedPeriodProvider.notifier).state = period,
      child: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: 12, vertical: 8), // Reduced padding
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? AppColors.textPrimary : AppColors.textSecondary,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}

class MoodIndicator {
  final String emoji;
  final Color color;

  const MoodIndicator({required this.emoji, required this.color});
}

final moodIndicators = [
  MoodIndicator(emoji: '😢', color: AppColors.error), // Unhappy
  MoodIndicator(emoji: '😕', color: AppColors.accent1), // Sad
  MoodIndicator(emoji: '😐', color: AppColors.secondary), // Normal
  MoodIndicator(emoji: '🙂', color: AppColors.accent2), // Good
  MoodIndicator(emoji: '😊', color: AppColors.accent4), // Happy
];

class MoodChart extends ConsumerWidget {
  final TimePeriod period;

  const MoodChart({super.key, required this.period});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(right: 16, left: 8, top: 8, bottom: 8),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: 1,
            verticalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: AppColors.border.withValues(alpha: 0.1),
                strokeWidth: 1,
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: AppColors.border.withValues(alpha: 0.1),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  const days = [
                    'Mon',
                    'Tue',
                    'Wed',
                    'Thu',
                    'Fri',
                    'Sat',
                    'Sun'
                  ];
                  if (value >= 0 && value < days.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        days[value.toInt()],
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: 1,
                reservedSize: 50,
                getTitlesWidget: (value, meta) {
                  if (value >= 0 && value < moodIndicators.length) {
                    final mood = moodIndicators[value.toInt()];
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: mood.color,
                          ),
                          child: Center(
                            child: Text(
                              mood.emoji,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                        // Text(
                        //   mood.emoji,
                        //   style: const TextStyle(fontSize: 16),
                        // ),
                      ],
                    );
                  }
                  return const Text('');
                },
              ),
            ),
          ),
          borderData: FlBorderData(show: false),
          lineBarsData: [
            LineChartBarData(
              spots: _getMockData(),
              isCurved: true,
              color: AppColors.textPrimary.withValues(alpha: 0.5),
              barWidth: 2,
              //  isStropped: false,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  // Get the mood color based on the y-value
                  final moodIndex = spot.y.toInt();
                  final moodColor = moodIndicators[moodIndex].color;

                  return FlDotCirclePainter(
                    radius: 6,
                    color: moodColor,
                    strokeWidth: 2,
                    strokeColor: AppColors.textPrimary,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: AppColors.textPrimary.withValues(alpha: 0.1),
              ),
            ),
          ],
          minX: 0,
          maxX: 6,
          minY: 0,
          maxY: 4,
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              //  tooltipBgColor: Colors.grey[800]!,
              getTooltipItems: (touchedSpots) {
                return touchedSpots.map((spot) {
                  final moodIndex = spot.y.toInt();
                  return LineTooltipItem(
                    moodIndicators[moodIndex].emoji,
                    TextStyle(color: AppColors.textPrimary, fontSize: 16),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
  }

  List<FlSpot> _getMockData() {
    // Mock data for demonstration
    return [
      const FlSpot(0, 2), // Monday - Normal
      const FlSpot(1, 3), // Tuesday - Good
      const FlSpot(2, 1), // Wednesday - Sad
      const FlSpot(3, 4), // Thursday - Happy
      const FlSpot(4, 2), // Friday - Normal
      const FlSpot(5, 3), // Saturday - Good
      const FlSpot(6, 4), // Sunday - Happy
    ];
  }
}
