// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';

// // lib/models/pomodoro_session.dart
// class PomodoroSession {
//   final String id;
//   final DateTime startTime;
//   final DateTime endTime;
//   final int workDurationMinutes;
//   final int restDurationMinutes;
//   final int completedCycles;

//   PomodoroSession({
//     required this.id,
//     required this.startTime,
//     required this.endTime,
//     required this.workDurationMinutes,
//     required this.restDurationMinutes,
//     required this.completedCycles,
//   });

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'startTime': startTime.toIso8601String(),
//       'endTime': endTime.toIso8601String(),
//       'workDurationMinutes': workDurationMinutes,
//       'restDurationMinutes': restDurationMinutes,
//       'completedCycles': completedCycles,
//     };
//   }

//   factory PomodoroSession.fromJson(Map<String, dynamic> json) {
//     return PomodoroSession(
//       id: json['id'],
//       startTime: DateTime.parse(json['startTime']),
//       endTime: DateTime.parse(json['endTime']),
//       workDurationMinutes: json['workDurationMinutes'],
//       restDurationMinutes: json['restDurationMinutes'],
//       completedCycles: json['completedCycles'],
//     );
//   }
// }

// // lib/providers/pomodoro_provider.dart

// final pomodoroProvider =
//     StateNotifierProvider<PomodoroNotifier, PomodoroState>((ref) {
//   return PomodoroNotifier();
// });

// class PomodoroState {
//   final bool isRunning;
//   final bool isWorkTime;
//   final int remainingSeconds;
//   final int workDurationMinutes;
//   final int restDurationMinutes;
//   final int completedCycles;

//   PomodoroState({
//     required this.isRunning,
//     required this.isWorkTime,
//     required this.remainingSeconds,
//     required this.workDurationMinutes,
//     required this.restDurationMinutes,
//     required this.completedCycles,
//   });

//   PomodoroState copyWith({
//     bool? isRunning,
//     bool? isWorkTime,
//     int? remainingSeconds,
//     int? workDurationMinutes,
//     int? restDurationMinutes,
//     int? completedCycles,
//   }) {
//     return PomodoroState(
//       isRunning: isRunning ?? this.isRunning,
//       isWorkTime: isWorkTime ?? this.isWorkTime,
//       remainingSeconds: remainingSeconds ?? this.remainingSeconds,
//       workDurationMinutes: workDurationMinutes ?? this.workDurationMinutes,
//       restDurationMinutes: restDurationMinutes ?? this.restDurationMinutes,
//       completedCycles: completedCycles ?? this.completedCycles,
//     );
//   }
// }

// class PomodoroNotifier extends StateNotifier<PomodoroState> {
//   Timer? _timer;
//   final _firestore = FirebaseFirestore.instance;
//   DateTime? _sessionStartTime;

//   PomodoroNotifier()
//       : super(PomodoroState(
//           isRunning: false,
//           isWorkTime: true,
//           remainingSeconds: 50 * 60, // Default 50 minutes
//           workDurationMinutes: 50,
//           restDurationMinutes: 10,
//           completedCycles: 0,
//         ));

//   void setDurations({required int workMinutes, required int restMinutes}) {
//     state = state.copyWith(
//       workDurationMinutes: workMinutes,
//       restDurationMinutes: restMinutes,
//       remainingSeconds: workMinutes * 60,
//     );
//   }

//   void startTimer() {
//     if (_timer != null) return;

//     _sessionStartTime = DateTime.now();
//     state = state.copyWith(isRunning: true);

//     _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
//       if (state.remainingSeconds > 0) {
//         state = state.copyWith(remainingSeconds: state.remainingSeconds - 1);
//       } else {
//         _handlePeriodComplete();
//       }
//     });
//   }

//   void pauseTimer() {
//     _timer?.cancel();
//     _timer = null;
//     state = state.copyWith(isRunning: false);
//   }

//   void resetTimer() {
//     _timer?.cancel();
//     _timer = null;
//     state = state.copyWith(
//       isRunning: false,
//       remainingSeconds: state.workDurationMinutes * 60,
//       isWorkTime: true,
//     );
//   }

//   void _handlePeriodComplete() {
//     if (state.isWorkTime) {
//       // Work period completed
//       state = state.copyWith(
//         completedCycles: state.completedCycles + 1,
//         isWorkTime: false,
//         remainingSeconds: state.restDurationMinutes * 60,
//       );
//       _saveSession();
//     } else {
//       // Rest period completed
//       state = state.copyWith(
//         isWorkTime: true,
//         remainingSeconds: state.workDurationMinutes * 60,
//       );
//     }
//   }

//   Future<void> _saveSession() async {
//     if (_sessionStartTime == null) return;

//     final session = PomodoroSession(
//       id: DateTime.now().toIso8601String(),
//       startTime: _sessionStartTime!,
//       endTime: DateTime.now(),
//       workDurationMinutes: state.workDurationMinutes,
//       restDurationMinutes: state.restDurationMinutes,
//       completedCycles: state.completedCycles,
//     );

//     await _firestore.collection('pomodoro_sessions').add(session.toJson());
//   }

//   @override
//   void dispose() {
//     _timer?.cancel();
//     super.dispose();
//   }
// }

// // lib/screens/pomodoro_screen.dart
// class PomodoroScreen extends ConsumerWidget {
//   const PomodoroScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final pomodoroState = ref.watch(pomodoroProvider);

//     return Scaffold(
//       appBar: AppBar(title: const Text('Pomodoro Timer')),
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Text(
//               pomodoroState.isWorkTime ? 'Work Time' : 'Rest Time',
//               style: Theme.of(context).textTheme.headlineMedium,
//             ),
//             const SizedBox(height: 20),
//             Text(
//               '${(pomodoroState.remainingSeconds ~/ 60).toString().padLeft(2, '0')}:${(pomodoroState.remainingSeconds % 60).toString().padLeft(2, '0')}',
//               style: Theme.of(context).textTheme.displayLarge,
//             ),
//             const SizedBox(height: 30),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 ElevatedButton(
//                   onPressed: () {
//                     pomodoroState.isRunning
//                         ? ref.read(pomodoroProvider.notifier).pauseTimer()
//                         : ref.read(pomodoroProvider.notifier).startTimer();
//                   },
//                   child: Text(pomodoroState.isRunning ? 'Pause' : 'Start'),
//                 ),
//                 const SizedBox(width: 20),
//                 ElevatedButton(
//                   onPressed: () =>
//                       ref.read(pomodoroProvider.notifier).resetTimer(),
//                   child: const Text('Reset'),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 20),
//             Text(
//               'Completed Cycles: ${pomodoroState.completedCycles}',
//               style: Theme.of(context).textTheme.titleLarge,
//             ),
//           ],
//         ),
//       ),
//       floatingActionButton: FloatingActionButton(
//         onPressed: () => _showSettingsDialog(context, ref),
//         child: const Icon(Icons.settings),
//       ),
//     );
//   }

//   void _showSettingsDialog(BuildContext context, WidgetRef ref) {
//     showDialog(
//       context: context,
//       builder: (context) => SettingsDialog(),
//     );
//   }
// }

// // lib/widgets/settings_dialog.dart
// class SettingsDialog extends ConsumerStatefulWidget {
//   const SettingsDialog({super.key});

//   @override
//   _SettingsDialogState createState() => _SettingsDialogState();
// }

// class _SettingsDialogState extends ConsumerState<SettingsDialog> {
//   late TextEditingController _workController;
//   late TextEditingController _restController;

//   @override
//   void initState() {
//     super.initState();
//     final state = ref.read(pomodoroProvider);
//     _workController = TextEditingController(
//       text: state.workDurationMinutes.toString(),
//     );
//     _restController = TextEditingController(
//       text: state.restDurationMinutes.toString(),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//       title: const Text('Timer Settings'),
//       content: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           TextField(
//             controller: _workController,
//             decoration: const InputDecoration(
//               labelText: 'Work Duration (minutes)',
//             ),
//             keyboardType: TextInputType.number,
//           ),
//           TextField(
//             controller: _restController,
//             decoration: const InputDecoration(
//               labelText: 'Rest Duration (minutes)',
//             ),
//             keyboardType: TextInputType.number,
//           ),
//         ],
//       ),
//       actions: [
//         TextButton(
//           onPressed: () => Navigator.pop(context),
//           child: const Text('Cancel'),
//         ),
//         TextButton(
//           onPressed: () {
//             ref.read(pomodoroProvider.notifier).setDurations(
//                   workMinutes: int.parse(_workController.text),
//                   restMinutes: int.parse(_restController.text),
//                 );
//             Navigator.pop(context);
//           },
//           child: const Text('Save'),
//         ),
//       ],
//     );
//   }

//   @override
//   void dispose() {
//     _workController.dispose();
//     _restController.dispose();
//     super.dispose();
//   }
// }

// // lib/screens/stats_screen.dart
// class StatsScreen extends ConsumerWidget {
//   const StatsScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('Statistics')),
//       body: StreamBuilder<QuerySnapshot>(
//         stream: FirebaseFirestore.instance
//             .collection('pomodoro_sessions')
//             .orderBy('startTime', descending: true)
//             .snapshots(),
//         builder: (context, snapshot) {
//           if (snapshot.hasError) {
//             return const Center(child: Text('Something went wrong'));
//           }

//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(child: CircularProgressIndicator());
//           }

//           final sessions = snapshot.data!.docs
//               .map((doc) =>
//                   PomodoroSession.fromJson(doc.data() as Map<String, dynamic>))
//               .toList();

//           return SingleChildScrollView(
//             child: Column(
//               children: [
//                 // WeeklyChart(sessions: sessions),
//                 // SessionsList(sessions: sessions),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
