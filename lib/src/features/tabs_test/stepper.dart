import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';

class MeditationStep {
  final String title;
  final String subtitle;
  final String duration;
  final IconData icon;
  bool isCompleted;

  MeditationStep({
    required this.title,
    required this.subtitle,
    required this.duration,
    required this.icon,
    this.isCompleted = false,
  });
}

class MeditationStepper extends StatefulWidget {
  final double height;
  const MeditationStepper({super.key, this.height = 300});

  @override
  State<MeditationStepper> createState() => _MeditationStepperState();
}

class _MeditationStepperState extends State<MeditationStepper> {
  final List<MeditationStep> steps = [
    MeditationStep(
      title: 'Managing Stress',
      subtitle: 'Video',
      duration: '2 min',
      icon: Icons.play_circle,
    ),
    MeditationStep(
      title: 'Feeling Overwhelmed',
      subtitle: 'Meditation',
      duration: '3-4 min',
      icon: Icons.headphones,
    ),
    MeditationStep(
      title: 'Understanding Meditation',
      subtitle: "Today's Meditation",
      duration: '5-20 min',
      icon: Icons.self_improvement,
    ),
  ];

  void _navigateToLesson(BuildContext context, int index) async {
    final bool? completed = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LessonScreen(step: steps[index]),
      ),
    );

    if (completed == true) {
      setState(() {
        steps[index].isCompleted = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: steps.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final step = steps[index];
        return Stack(
          children: [
            // Connecting Line
            if (index < steps.length - 1)
              Positioned(
                left: 19,
                top: 20,
                bottom: 0,
                child: Container(
                  width: 4,
                  color:
                      step.isCompleted ? AppColors.success : AppColors.border,
                ),
              ),

            // Content Row
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Circle
                  Container(
                    width: 20,
                    height: 20,
                    margin: const EdgeInsets.only(left: 10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: step.isCompleted
                          ? AppColors.success
                          : AppColors.surface,
                    ),
                    // child: Icon(
                    //   step.isCompleted ? Icons.check : Icons.circle_outlined,
                    //   color: step.isCompleted ? Colors.white : Colors.grey,
                    // ),
                  ),

                  // Card
                  Expanded(
                    child: Transform.translate(
                      offset: const Offset(0, -20),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 10),
                        child: InkWell(
                          onTap: () => _navigateToLesson(context, index),
                          child: Card(
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    step.title,
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Icon(step.icon, size: 16),
                                      const SizedBox(width: 4),
                                      Text(step.subtitle),
                                      const SizedBox(width: 8),
                                      Text(step.duration),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

class LessonScreen extends StatelessWidget {
  final MeditationStep step;

  const LessonScreen({super.key, required this.step});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(step.title)),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.pop(context, true);
          },
          child: const Text('Mark as Complete'),
        ),
      ),
    );
  }
}
