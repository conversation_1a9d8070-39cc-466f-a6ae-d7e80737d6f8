import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';

// mood.dart
enum Mood { unhappy, sad, normal, good, happy }

class MoodEntry {
  final String id;
  final Mood mood;
  final DateTime timestamp;
  final String? note;

  MoodEntry({
    required this.id,
    required this.mood,
    required this.timestamp,
    this.note,
  });

  Map<String, dynamic> toJson() {
    return {
      'mood': mood.toString(),
      'timestamp': timestamp.toIso8601String(),
      'note': note,
    };
  }

  factory MoodEntry.fromJson(String id, Map<String, dynamic> json) {
    return MoodEntry(
      id: id,
      mood: Mood.values.firstWhere(
        (e) => e.toString() == json['mood'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
      note: json['note'],
    );
  }
}

// mood_repository.dart

class MoodRepository {
  final FirebaseFirestore _firestore;
  final String userId;

  MoodRepository({required this.userId})
      : _firestore = FirebaseFirestore.instance;

  Future<void> addMoodEntry(MoodEntry entry) async {
    await _firestore
        .collection('users')
        .doc(userId)
        .collection('moods')
        .add(entry.toJson());
  }

  Stream<List<MoodEntry>> getMoodEntries() {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('moods')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MoodEntry.fromJson(doc.id, doc.data()))
            .toList());
  }
}

// mood_provider.dart

final moodRepositoryProvider = Provider<MoodRepository>((ref) {
  final user = ref.watch(userNotifierProvider).valueOrNull;

  // Throw an error if trying to access repository while not authenticated
  if (user == null) {
    throw Exception('Must be authenticated to access MoodRepository');
  }
  return MoodRepository(userId: user.auth.uid);
});

final moodEntriesProvider = StreamProvider<List<MoodEntry>>((ref) {
  final repository = ref.watch(moodRepositoryProvider);
  return repository.getMoodEntries();
});

// mood.dart

extension MoodExtension on Mood {
  Color get color {
    switch (this) {
      case Mood.unhappy:
        return const Color(0xFFFF5252); // Red
      case Mood.sad:
        return const Color(0xFF64B5F6); // Blue
      case Mood.normal:
        return const Color(0xFF9575CD); // Purple
      case Mood.good:
        return const Color(0xFF81C784); // Green
      case Mood.happy:
        return const Color(0xFFFFD54F); // Yellow
    }
  }

  String get emoji {
    switch (this) {
      case Mood.unhappy:
        return '😢';
      case Mood.sad:
        return '😕';
      case Mood.normal:
        return '😐';
      case Mood.good:
        return '🙂';
      case Mood.happy:
        return '😊';
    }
  }

  String get label {
    return name[0].toUpperCase() + name.substring(1);
  }
}

// mood_tracker_screen.dart

final selectedMoodProvider = StateProvider<Mood?>((ref) => null);

class MoodTrackerScreen extends ConsumerWidget {
  const MoodTrackerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMood = ref.watch(selectedMoodProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SizedBox(height: 40),
              Text(
                'How Do You Feel Today?',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 40),
              // Circular mood display
              Container(
                width: 280,
                height: 280,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: selectedMood?.color
                      .withOpacity(0.3), // Deep purple background
                  boxShadow: [
                    BoxShadow(
                      color: (selectedMood?.color.withOpacity(0.1) ??
                          AppColors.accent4),
                      blurRadius: 15,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Center(
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: selectedMood?.color.withOpacity(0.3) ??
                          AppColors.accent4,
                      // Deep purple background
                      boxShadow: [
                        BoxShadow(
                          color: (selectedMood?.color.withOpacity(0.3) ??
                              AppColors.accent4),
                          blurRadius: 15,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: selectedMood?.color ?? AppColors.accent4,
                          boxShadow: [
                            BoxShadow(
                              color: (selectedMood?.color ?? AppColors.accent4),
                              blurRadius: 15,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            selectedMood?.emoji ?? '😊',
                            style: const TextStyle(
                              fontSize: 60,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 60),
              // Mood selection row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: Mood.values.map((mood) {
                  return GestureDetector(
                    onTap: () =>
                        ref.read(selectedMoodProvider.notifier).state = mood,
                    child: Column(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: mood.color,
                            border: Border.all(
                              color: selectedMood == mood
                                  ? AppColors.textOnPrimary
                                  : Colors.transparent,
                              width: 3,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              mood.emoji,
                              style: const TextStyle(fontSize: 24),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          mood.label,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 30),
              // Note Mood button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (selectedMood != null) {
                      _saveMoodEntry(context, ref, selectedMood);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Note Mood',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.textOnPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveMoodEntry(BuildContext context, WidgetRef ref, Mood mood) async {
    final noteController = TextEditingController();
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add a note'),
        content: TextField(
          controller: noteController,
          decoration: const InputDecoration(
            hintText: 'How are you feeling?',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result == true) {
      // Save to Firebase
      final repository = ref.read(moodRepositoryProvider);
      await repository.addMoodEntry(
        MoodEntry(
          id: DateTime.now().toString(),
          mood: mood,
          timestamp: DateTime.now(),
          note: noteController.text,
        ),
      );
      ref.read(selectedMoodProvider.notifier).state = null;
    }
  }
}
