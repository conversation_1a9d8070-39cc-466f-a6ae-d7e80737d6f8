import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'dart:async';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';

enum BreathState { breatheIn, holdIn, breatheOut, holdOut }

class BreathingPattern {
  final String name;
  final String description;
  final int breatheInSeconds;
  final int holdInSeconds;
  final int breatheOutSeconds;
  final int holdOutSeconds;
  final String audioUrl;

  BreathingPattern({
    required this.name,
    required this.description,
    required this.breatheInSeconds,
    required this.holdInSeconds,
    required this.breatheOutSeconds,
    required this.holdOutSeconds,
    required this.audioUrl,
  });
}

class BreathingExercise extends StatefulWidget {
  final int totalDurationMinutes;
  final BreathingPattern pattern;

  const BreathingExercise({
    super.key,
    required this.totalDurationMinutes,
    required this.pattern,
  });

  @override
  _BreathingExerciseState createState() => _BreathingExerciseState();
}

class _BreathingExerciseState extends State<BreathingExercise>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Timer _timer;
  late AudioPlayer _audioPlayer;
  int _totalSeconds = 0;
  int _currentCycleSeconds = 0;
  bool _isPlaying = false;
  BreathState _currentState = BreathState.breatheIn;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    _audioPlayer = AudioPlayer();
    _initAudio();
  }

  Future<void> _initAudio() async {
    await _audioPlayer.setUrl(widget.pattern.audioUrl);
    await _audioPlayer.setLoopMode(LoopMode.one);
  }

  @override
  void dispose() {
    _controller.dispose();
    if (_isPlaying) _timer.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  void _updateBreathState() {
    int totalCycleSeconds = widget.pattern.breatheInSeconds +
        widget.pattern.holdInSeconds +
        widget.pattern.breatheOutSeconds +
        widget.pattern.holdOutSeconds;

    _currentCycleSeconds = _totalSeconds % totalCycleSeconds;

    if (_currentCycleSeconds < widget.pattern.breatheInSeconds) {
      _updateState(BreathState.breatheIn);
    } else if (_currentCycleSeconds <
        widget.pattern.breatheInSeconds + widget.pattern.holdInSeconds) {
      _updateState(BreathState.holdIn);
    } else if (_currentCycleSeconds <
        widget.pattern.breatheInSeconds +
            widget.pattern.holdInSeconds +
            widget.pattern.breatheOutSeconds) {
      _updateState(BreathState.breatheOut);
    } else {
      _updateState(BreathState.holdOut);
    }
  }

  void _updateState(BreathState newState) {
    if (_currentState != newState) {
      setState(() {
        _currentState = newState;
        _updateAnimation();
      });
    }
  }

  void _updateAnimation() {
    switch (_currentState) {
      case BreathState.breatheIn:
        _controller.duration =
            Duration(seconds: widget.pattern.breatheInSeconds);
        _controller.forward(from: 0);
        break;
      case BreathState.breatheOut:
        _controller.duration =
            Duration(seconds: widget.pattern.breatheOutSeconds);
        _controller.reverse(from: 1);
        break;
      case BreathState.holdIn:
      case BreathState.holdOut:
        _controller.stop();
        break;
    }
  }

  void _toggleTimer() {
    setState(() {
      _isPlaying = !_isPlaying;
      if (_isPlaying) {
        _startTimer();
        _audioPlayer.play();
      } else {
        _timer.cancel();
        _audioPlayer.pause();
      }
    });
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_totalSeconds >= widget.totalDurationMinutes * 60 - 1) {
        _timer.cancel();
        _audioPlayer.stop();
        setState(() {
          _isPlaying = false;
          _totalSeconds = 0;
        });
        return;
      }

      setState(() {
        _totalSeconds++;
        _updateBreathState();
      });
    });
  }

  String _getInstructionText() {
    switch (_currentState) {
      case BreathState.breatheIn:
        return 'breathe in';
      case BreathState.holdIn:
        return 'hold';
      case BreathState.breatheOut:
        return 'breathe out';
      case BreathState.holdOut:
        return 'hold';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(widget.pattern.name,
            style: TextStyle(color: AppColors.textPrimary)),
        actions: [
          IconButton(
            icon: Icon(Icons.close, color: AppColors.textPrimary),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Center(
              child: AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Container(
                    width: 300,
                    height: 300,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          AppColors.primary,
                          AppColors.secondary,
                        ],
                        stops: [0, _controller.value],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withValues(alpha: 0.5),
                          blurRadius: 30 * _controller.value,
                          spreadRadius: 10 * _controller.value,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '${(widget.totalDurationMinutes * 60 - _totalSeconds) ~/ 60}:${((widget.totalDurationMinutes * 60 - _totalSeconds) % 60).toString().padLeft(2, '0')}',
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: 48,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                          Text(
                            _getInstructionText(),
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: 20,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 40),
            child: IconButton(
              icon: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: AppColors.textPrimary,
                size: 40,
              ),
              onPressed: _toggleTimer,
            ),
          ),
        ],
      ),
    );
  }
}

// Example usage:
class BreathingPatterns {
  static final box = BreathingPattern(
    name: '4-4-4-4 Box Breathing',
    description: 'Equal duration for breath in, hold, breath out, and hold',
    breatheInSeconds: 4,
    holdInSeconds: 4,
    breatheOutSeconds: 4,
    holdOutSeconds: 4,
    audioUrl: 'assets/audio/calm.mp3',
  );

  static final relaxing = BreathingPattern(
    name: '4-7-8 Relaxing Breath',
    description: 'Longer hold and exhale for relaxation',
    breatheInSeconds: 4,
    holdInSeconds: 7,
    breatheOutSeconds: 8,
    holdOutSeconds: 0,
    audioUrl: 'assets/audio/relax.mp3',
  );
}
