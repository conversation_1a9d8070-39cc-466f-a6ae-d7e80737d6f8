// lib/features/journal/presentation/pages/about_journal_page.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/intentions/journal_constants.dart';
import 'package:mimi_app/src/features/intentions/journal_section_model.dart';
import 'package:mimi_app/src/features/intentions/journal_widgets.dart';

// lib/features/journal/presentation/pages/about_journal_page.dart

class AboutJournalPage extends StatelessWidget {
  const AboutJournalPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Create example data
    final List<JournalExample> morningExamples = [
      JournalExample(
        number: '01',
        content: 'Journaling',
      ),
      JournalExample(
        number: '02',
        content: 'Meditation',
      ),
      JournalExample(
        number: '03',
        content: 'Affirmations',
      ),
      JournalExample(
        number: '04',
        content: 'Mood Tracking',
      ),
    ];

    final List<JournalExample> noonExamples = [
      JournalExample(
        number: '01',
        content: 'Breathwork to release tension',
      ),
      JournalExample(
        number: '02',
        content: 'Affirmations to stay aligned',
      ),
    ];

    final List<JournalExample> eveningExamples = [
      JournalExample(
        number: '01',
        content: 'Meditation for peaceful transition',
      ),
      JournalExample(
        number: '02',
        content: 'Gratitude Affirmations',
      ),
      JournalExample(
        number: '03',
        content: 'Mood Tracking',
      ),
    ];

    final List<JournalExample> intentionExamples = [
      JournalExample(
        number: '01',
        content:
            'I intend to nurture deep, authentic relationships that reflect my highest self.',
      ),
      JournalExample(
        number: '02',
        content:
            'I intend to strengthen my family bonds through presence, understanding, and unconditional love.',
      ),
      JournalExample(
        number: '03',
        content:
            'I intend to open myself to receiving and sharing abundance in all areas of my life',
      ),
    ];

    final List<JournalExample> nightExamples = [
      JournalExample(
        number: '01',
        content: 'Calming Breathwork',
      ),
      JournalExample(
        number: '02',
        content: 'Bedtime Meditation',
      ),
      JournalExample(
        number: '03',
        content: 'Final Journal Reflection',
      ),
    ];
    final List<JournalExample> aIExamples = [
      JournalExample(
        number: '01',
        content: 'Provide supportive conversation',
      ),
      JournalExample(
        number: '02',
        content: 'Offer mindful suggestions',
      ),
      JournalExample(
        number: '03',
        content: 'Help you process challenges',
      ),
    ];
    return Scaffold(
      //showMiniPlayer: false,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back,
              color: Theme.of(context).brightness == Brightness.light
                  ? AppColors.greyLight100
                  : AppColors.greyDark100),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'About Your Journey',
          style: JournalTextStyles.pageTitle(context),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(JournalSpacing.md),
              child: Text(
                'Your path to self-mastery is unique and personal. This space is designed to help you stay connected with your inner self, maintain mindful awareness, and achieve your intentions through consistent daily practices guided by Mimi Bland, your Self-Mastery and Spiritual Mindset mentor.',
                style: JournalTextStyles.bodyText(context),
              ),
            ),
            const JournalQuoteBox(
              quote:
                  'The journey of self-discovery begins with single moment of awareness',
              author: 'Mimi Bland',
            ),
            JournalSection(
              title: 'Setting Your Intentions',
              examples: intentionExamples,
              description:
                  ' Begin by setting clear intentions for your journey. What do you wish to manifest in your life? What areas of growth are calling to you? Your intentions will guide your daily practices and help you stay focused on your path to self-mastery. \n Here are some examples:',
            ),
            JournalSection(
              title: 'Daily Checkins',
              description:
                  'Your journey is supported by fully customizable daily check-ins. Create as many check-ins as you need throughout your day, each with its own name and schedule. Every check-in can include any combination of powerful activities to support your well-being journey.',
            ),
            JournalSection(
              title: 'Meditation',
              description:
                  'Center yourself and find inner peace through guided or silent meditation.',
            ),
            JournalSection(
              title: 'Breathwork',
              description:
                  'Connect with your life force and regulate your nervous system through intentional breathing practices.',
            ),
            JournalSection(
              title: 'Affirmations',
              description:
                  'Strengthen your mindset and manifest your intentions through positive declaration',
            ),
            JournalSection(
              title: 'Journaling',
              description:
                  'Process your thoughts, feelings and experiences through guided writing prompts',
            ),
            JournalSection(
              title: 'Mood Tracking',
              description:
                  'Build emotional awareness by tracking and understanding your emotional patterns \n A sample example Checkin Routine',
            ),
            JournalSection(
              title: 'Morning Checkin',
              icon: Icons.wb_cloudy_outlined,
              description:
                  'Start your morning by expressing gratitude Journaling. Continue with Meditations, Affirmations and Mood Tracking',
              examples: morningExamples,
            ),
            JournalSection(
              title: 'Mid-Day Reset',
              icon: Icons.wb_sunny_outlined,
              description:
                  'Start your morning by expressing gratitude Journaling. Continue with Meditations, Affirmations and Mood Tracking',
              examples: noonExamples,
            ),
            JournalSection(
              title: 'Evening Reflection',
              icon: Icons.wb_twilight_outlined,
              description:
                  'Start your morning by expressing gratitude Journaling. Continue with Meditations, Affirmations and Mood Tracking',
              examples: eveningExamples,
            ),
            JournalSection(
              title: 'Night Integration',
              icon: Icons.nightlight_outlined,
              description:
                  'Reflect on and journal about the experience of fulfilling your morning intention of uplifting others and improving the environment.',
              examples: nightExamples,
            ),
            const SizedBox(height: JournalSpacing.xl),
            const JournalQuoteBox(
              quote:
                  'In the quiet moments of self-reflection, we find the answers we have been seeking outside ourselves."',
              author: 'Mimi Bland',
            ),
            JournalSection(
              title: 'AI Support',
              description:
                  'Whenever you need guidance or support, MIMI AI is here to assist you. Think of her as your companion on this journey, ready to:',
              examples: aIExamples,
            ),
            Padding(
              padding: const EdgeInsets.all(JournalSpacing.md),
              child: Text(
                'Remember: Every moment of mindful awareness is a step toward self-mastery. Your commitment to these practices is a gift to yourself and your growth.',
                style: JournalTextStyles.bodyText(context),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(JournalSpacing.md),
              child: Text(
                'Begin your journey today. Set your intentions, create your Check-In schedule, and let each mindful moment guide you closer to your highest self.',
                style: JournalTextStyles.sectionTitle(context),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
