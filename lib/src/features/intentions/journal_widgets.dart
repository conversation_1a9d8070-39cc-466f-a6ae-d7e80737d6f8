// lib/features/journal/presentation/widgets/journal_quote_box.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/intentions/journal_constants.dart';
import 'package:mimi_app/src/features/intentions/journal_section_model.dart';

class JournalQuoteBox extends StatelessWidget {
  final String quote;
  final String author;

  const JournalQuoteBox({
    super.key,
    required this.quote,
    required this.author,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        vertical: JournalSpacing.md,
        horizontal: JournalSpacing.sm,
      ),
      padding: const EdgeInsets.all(JournalSpacing.md),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: JournalColors.divider(context)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            '"$quote"',
            style: JournalTextStyles.quote(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: JournalSpacing.sm),
          Text(
            '~ $author',
            style: JournalTextStyles.quote(context).copyWith(
              fontWeight: AppTypography.medium,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// lib/features/journal/presentation/widgets/journal_example_item.dart
class JournalExampleItem extends StatelessWidget {
  final String number;
  final String content;

  const JournalExampleItem({
    super.key,
    required this.number,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: JournalSpacing.sm,
        horizontal: JournalSpacing.md,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            number,
            style: JournalTextStyles.bodyText(context).copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: JournalSpacing.md),
          Expanded(
            child: Text(
              content,
              style: JournalTextStyles.bodyText(context),
            ),
          ),
        ],
      ),
    );
  }
}

// lib/features/journal/presentation/widgets/journal_section.dart
class JournalSection extends StatelessWidget {
  final String title;
  final String? description;
  final List<JournalExample> examples;
  final IconData? icon;

  const JournalSection({
    super.key,
    required this.title,
    this.description,
    this.examples = const [],
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(JournalSpacing.md),
          child: Row(
            children: [
              if (icon != null) ...[
                Icon(icon, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: JournalSpacing.sm),
              ],
              Text(
                title,
                style: JournalTextStyles.sectionTitle(context),
              ),
            ],
          ),
        ),
        if (description != null)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: JournalSpacing.md,
              vertical: JournalSpacing.sm,
            ),
            child: Text(
              description!,
              style: JournalTextStyles.bodyText(context),
            ),
          ),
        ...examples.map((example) => JournalExampleItem(
              number: example.number,
              content: example.content,
            )),
      ],
    );
  }
}
