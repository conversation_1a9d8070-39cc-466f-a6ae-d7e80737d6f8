import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/features/intentions/providers/intention_selection_provider.dart';

class IntentionsListScreen extends ConsumerWidget {
  const IntentionsListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final intentionsAsync = ref.watch(allIntentionsProvider);
    final selectedIntentionIdAsync =
        ref.watch(selectedIntentionNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Intentions'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.goNamed(RouteNames.home),
        ),

        // TODO: Add actions like info or settings if needed
      ),
      body: intentionsAsync.when(
        data: (intentions) {
          if (intentions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('No intentions set yet.',
                      style: TextStyle(fontSize: 18)),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.pushNamed(RouteNames.newIntention);
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                    child: const Text('Set Your First Intention',
                        style: TextStyle(fontSize: 16)),
                  ),
                ],
              ),
            );
          }
          return selectedIntentionIdAsync.when(
            data: (selectedIntentionId) {
              // Auto-select first intention if none selected
              final effectiveSelectedId = selectedIntentionId ??
                  (intentions.isNotEmpty ? intentions.first.id : null);

              // If we auto-selected and it's different from stored selection, update it
              if (selectedIntentionId == null && effectiveSelectedId != null) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  ref
                      .read(selectedIntentionNotifierProvider.notifier)
                      .selectIntention(effectiveSelectedId);
                });
              }

              return ListView.builder(
                padding: const EdgeInsets.all(4.0), // Reduced padding
                itemCount: intentions.length,
                itemBuilder: (context, index) {
                  final intention = intentions[index];
                  final routinesAsync =
                      ref.watch(routinesForIntentionProvider(intention.id));

                  final isSelected = effectiveSelectedId == intention.id;

                  return Container(
                    margin: const EdgeInsets.symmetric(
                        vertical: 4.0), // Reduced margin
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: isSelected
                          ? Border.all(
                              color: Theme.of(context).colorScheme.primary,
                              width: 2,
                            )
                          : Border.all(
                              color: Theme.of(context)
                                  .colorScheme
                                  .outline
                                  .withValues(alpha: 0.2),
                              width: 1,
                            ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: InkWell(
                      onTap: () {
                        // Select this intention and navigate back to home
                        ref
                            .read(selectedIntentionNotifierProvider.notifier)
                            .selectIntention(intention.id);
                        context.goNamed(RouteNames.home);
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0), // Reduced padding
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Stack(
                              children: [
                                // Main content
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 4.0), // Space for checkmark
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          intention.name,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium // Changed from titleLarge
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: isSelected
                                                    ? Theme.of(context)
                                                        .colorScheme
                                                        .primary
                                                    : Theme.of(context)
                                                        .colorScheme
                                                        .onSurface,
                                              ),
                                        ),
                                      ),
                                      PopupMenuButton<String>(
                                        icon: const Icon(Icons.more_vert),
                                        onSelected: (value) {
                                          if (value == 'edit') {
                                            context.pushNamed(
                                                RouteNames.editIntention,
                                                pathParameters: {
                                                  'intentionId':
                                                      intention.id.toString()
                                                });
                                          } else if (value == 'delete') {
                                            _showDeleteIntentionDialog(
                                                context, ref, intention);
                                          }
                                        },
                                        itemBuilder: (context) => [
                                          const PopupMenuItem(
                                              value: 'edit',
                                              child: Row(children: [
                                                Icon(Icons.edit, size: 20),
                                                SizedBox(width: 8),
                                                Text('Edit')
                                              ])),
                                          const PopupMenuItem(
                                              value: 'delete',
                                              child: Row(children: [
                                                Icon(Icons.delete, size: 20),
                                                SizedBox(width: 8),
                                                Text('Delete')
                                              ]))
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                // Checkmark icon in upper left corner
                                if (isSelected)
                                  Positioned(
                                    top: 0,
                                    left: 0,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.check,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimary,
                                        size: 16,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            // Reduced spacing
                            Text(
                              'Routines:',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleSmall, // Smaller text
                            ),
                            // Reduced spacing
                            routinesAsync.when(
                              data: (routines) {
                                if (routines.isEmpty) {
                                  return const Padding(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 2.0),
                                    child: Text(
                                        'No routines for this intention yet.'),
                                  );
                                }
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: routines.map((routine) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 2.0), // Reduced padding
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              routine.name,
                                              style: const TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontSize: 16, // Smaller font
                                              ),
                                            ),
                                          ),
                                          PopupMenuButton<String>(
                                            icon: const Icon(Icons.more_vert,
                                                size: 18), // Smaller icon
                                            onSelected: (value) {
                                              if (value == 'edit') {
                                                context.pushNamed(
                                                    RouteNames.editRoutineName,
                                                    pathParameters: {
                                                      'intentionId': intention
                                                          .id
                                                          .toString(),
                                                      'routineId':
                                                          routine.id.toString(),
                                                    });
                                              } else if (value == 'delete') {
                                                _showDeleteRoutineDialog(
                                                    context, ref, routine);
                                              }
                                            },
                                            itemBuilder: (context) => [
                                              const PopupMenuItem(
                                                  value: 'edit',
                                                  child: Row(children: [
                                                    Icon(Icons.edit, size: 20),
                                                    SizedBox(width: 8),
                                                    Text('Edit')
                                                  ])),
                                              const PopupMenuItem(
                                                  value: 'delete',
                                                  child: Row(children: [
                                                    Icon(Icons.delete,
                                                        size: 20),
                                                    SizedBox(width: 8),
                                                    Text('Delete')
                                                  ]))
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  }).toList(),
                                );
                              },
                              loading: () => const Center(
                                  child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )),
                              error: (err, stack) =>
                                  Text('Error loading routines: $err'),
                            ),
                            const SizedBox(height: 4), // Reduced spacing
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton.icon(
                                icon: const Icon(Icons.add,
                                    size: 16), // Smaller icon
                                label: const Text('Add Routine',
                                    style: TextStyle(
                                        fontSize: 12)), // Smaller text
                                onPressed: () {
                                  context.pushNamed(RouteNames.nameNewRoutine,
                                      pathParameters: {
                                        'intentionId': intention.id.toString()
                                      });
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (err, stack) =>
                Center(child: Text('Error loading selected intention: $err')),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) =>
            Center(child: Text('Error loading intentions: $err')),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Navigate to the screen for creating a new intention
          context.pushNamed(RouteNames.newIntention);
        },
        label: const Text('New Intention'),
      ),
    );
  }

  void _showDeleteIntentionDialog(
      BuildContext context, WidgetRef ref, Intention intention) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Intention?'),
        content: Text(
            'Are you sure you want to delete the intention "${intention.name}" and all its associated routines? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref
                    .read(intentionCrudNotifierProvider.notifier)
                    .deleteIntention(intention.id);
                // if (context.mounted) {
                //   ScaffoldMessenger.of(context).showSnackBar(
                //     const SnackBar(
                //         content: Text('Intention deleted successfully')),
                //   );
                // }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error deleting intention: $e')),
                  );
                }
              }
            },
            child: Text('Delete',
                style: TextStyle(color: Theme.of(context).colorScheme.error)),
          ),
        ],
      ),
    );
  }

  void _showDeleteRoutineDialog(
      BuildContext context, WidgetRef ref, CheckInRoutine routine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Routine?'),
        content: Text(
            'Are you sure you want to delete the routine "${routine.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel')),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                // Use the RoutineCrudNotifier to delete the routine
                await ref
                    .read(routineCrudNotifierProvider.notifier)
                    .deleteRoutine(routine.id);

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Routine deleted successfully')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error deleting routine: $e')),
                  );
                }
              }
            },
            child: Text('Delete',
                style: TextStyle(color: Theme.of(context).colorScheme.error)),
          ),
        ],
      ),
    );
  }
}
