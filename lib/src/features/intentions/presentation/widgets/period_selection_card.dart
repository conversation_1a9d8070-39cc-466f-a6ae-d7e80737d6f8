// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
// import '../set_routine_reminder_screen.dart';

// class PeriodSelectionCard extends StatelessWidget {
//   // final RoutinePeriod period;
//   // final PeriodDetails details;
//   final bool isSelected;
//   final VoidCallback onTap;

//   const PeriodSelectionCard({
//     super.key,
//     // required this.period,
//     // required this.details,
//     required this.isSelected,
//     required this.onTap,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Container(
//         padding: const EdgeInsets.all(20),
//         decoration: BoxDecoration(
//           color: AppColors.surface,
//           borderRadius: BorderRadius.circular(16),
//           border: Border.all(
//             color: isSelected ? AppColors.primary : AppColors.border,
//             width: isSelected ? 2 : 1,
//           ),
//           boxShadow: isSelected
//               ? [
//                   BoxShadow(
//                     color: AppColors.primary.withValues(alpha: 0.2),
//                     blurRadius: 8,
//                     offset: const Offset(0, 2),
//                   ),
//                 ]
//               : null,
//         ),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             // SVG Icon
//             SvgPicture.asset(
//               details.svgAssetPath,
//               width: 48,
//               height: 48,
//               colorFilter: ColorFilter.mode(
//                 isSelected ? AppColors.primary : AppColors.textSecondary,
//                 BlendMode.srcIn,
//               ),
//             ),
//             const SizedBox(height: 12),
//             // Period Name
//             Text(
//               details.displayName,
//               style: TextStyle(
//                 fontSize: 14,
//                 fontWeight: FontWeight.w600,
//                 color: isSelected ? AppColors.primary : AppColors.textSecondary,
//                 letterSpacing: 0.5,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
