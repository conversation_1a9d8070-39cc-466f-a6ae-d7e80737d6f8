import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';

class CustomTimePicker extends StatefulWidget {
  final TimeOfDay initialTime;
  final Function(TimeOfDay) onTimeChanged;
  final VoidCallback? onCancel;

  const CustomTimePicker({
    super.key,
    required this.initialTime,
    required this.onTimeChanged,
    this.onCancel,
  });

  @override
  State<CustomTimePicker> createState() => _CustomTimePickerState();
}

class _CustomTimePickerState extends State<CustomTimePicker> {
  late FixedExtentScrollController _hourController;
  late FixedExtentScrollController _minuteController;
  late FixedExtentScrollController _periodController;

  late int _selectedHour;
  late int _selectedMinute;
  late int _selectedPeriod; // 0 for AM, 1 for PM

  @override
  void initState() {
    super.initState();

    // Convert 24-hour format to 12-hour format
    _selectedHour = widget.initialTime.hourOfPeriod == 0
        ? 12
        : widget.initialTime.hourOfPeriod;
    _selectedMinute = widget.initialTime.minute;
    _selectedPeriod = widget.initialTime.period == DayPeriod.am ? 0 : 1;

    _hourController =
        FixedExtentScrollController(initialItem: _selectedHour - 1);
    _minuteController =
        FixedExtentScrollController(initialItem: _selectedMinute);
    _periodController =
        FixedExtentScrollController(initialItem: _selectedPeriod);
  }

  @override
  void dispose() {
    _hourController.dispose();
    _minuteController.dispose();
    _periodController.dispose();
    super.dispose();
  }

  TimeOfDay _getCurrentTime() {
    // Convert back to 24-hour format
    int hour24 = _selectedHour;
    if (_selectedPeriod == 0) {
      // AM
      if (hour24 == 12) hour24 = 0;
    } else {
      // PM
      if (hour24 != 12) hour24 += 12;
    }

    return TimeOfDay(hour: hour24, minute: _selectedMinute);
  }

  void _confirmTime() {
    final newTime = _getCurrentTime();
    widget.onTimeChanged(newTime);
  }

  Widget _buildScrollWheel({
    required List<String> items,
    required FixedExtentScrollController controller,
    required Function(int) onSelectedItemChanged,
    required int selectedIndex,
  }) {
    return SizedBox(
      height: 200,
      width: 80,
      child: ListWheelScrollView.useDelegate(
        controller: controller,
        itemExtent: 50,
        perspective: 0.003,
        diameterRatio: 1.5,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: onSelectedItemChanged,
        childDelegate: ListWheelChildBuilderDelegate(
          childCount: items.length,
          builder: (context, index) {
            final isSelected = index == selectedIndex;
            return Container(
              alignment: Alignment.center,
              child: Text(
                items[index],
                style: TextStyle(
                  fontSize: isSelected ? 28 : 20,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w400,
                  color: isSelected
                      ? Theme.of(context).colorScheme.onSurface
                      : Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.7),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final hours =
        List.generate(12, (index) => (index + 1).toString().padLeft(2, '0'));
    final minutes =
        List.generate(60, (index) => index.toString().padLeft(2, '0'));
    const periods = ['AM', 'PM'];

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        boxShadow: [
          BoxShadow(
            color:
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'SET A TIME',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
              letterSpacing: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          // Show current selected time
          Text(
            '${_selectedHour.toString().padLeft(2, '0')}:${_selectedMinute.toString().padLeft(2, '0')} ${_selectedPeriod == 0 ? 'AM' : 'PM'}',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Hours
              _buildScrollWheel(
                items: hours,
                controller: _hourController,
                selectedIndex: _selectedHour - 1,
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedHour = index + 1;
                  });
                },
              ),

              // Separator
              Container(
                height: 50,
                alignment: Alignment.center,
                child: Text(
                  ':',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),

              // Minutes
              _buildScrollWheel(
                items: minutes,
                controller: _minuteController,
                selectedIndex: _selectedMinute,
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedMinute = index;
                  });
                },
              ),

              const SizedBox(width: 20),

              // AM/PM
              _buildScrollWheel(
                items: periods,
                controller: _periodController,
                selectedIndex: _selectedPeriod,
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedPeriod = index;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Confirmation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (widget.onCancel != null) ...[
                SizedBox(
                  height: 44,
                  child: TextButton(
                    onPressed: widget.onCancel,
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
              ],
              SizedBox(
                height: 44,
                width: 100,
                child: ElevatedButton(
                  onPressed: _confirmTime,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.textOnPrimary,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Set Time',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
