import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/confirmation_dialog.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';

class NameRoutineScreen extends ConsumerStatefulWidget {
  final int intentionId;
  final int? routineId;

  const NameRoutineScreen(
      {super.key, required this.intentionId, this.routineId});

  @override
  ConsumerState<NameRoutineScreen> createState() => _NameRoutineScreenState();
}

class _NameRoutineScreenState extends ConsumerState<NameRoutineScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late FocusNode _focusNode;
  bool _isLoading = false;
  CheckInRoutine? _existingRoutine;
  DateTime _scheduledTime = DateTime(DateTime.now().year, DateTime.now().month,
      DateTime.now().day, 8, 0); // Default to 8:00 AM
  bool _isDataInitialized = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _focusNode = FocusNode();
    // Data loading and field population for editing will happen in the build method

    // Auto-focus the text field after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _saveRoutine() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      final notifier = ref.read(routineCrudNotifierProvider.notifier);
      final routineName = _nameController.text.trim();

      // _scheduledTime is already correctly set from initState (default) or build (from existing routine)
      // It will be explicitly updated in the SetReminderScreen.
      // The 'type' will be set in SetRoutineReminderScreen.

      try {
        int currentRoutineId;
        if (_existingRoutine != null) {
          // Update existing routine
          await notifier.updateRoutine(
            routineId: _existingRoutine!.id,
            name: routineName,
            type: _existingRoutine!.type, // Preserve existing type
            scheduledTime: _scheduledTime, // Pass the preserved scheduled time
            activities:
                _existingRoutine!.activities, // Preserve existing activities
          );
          currentRoutineId = _existingRoutine!.id;
          // Routine name updated successfully - no toast needed
        } else {
          // Add new routine
          currentRoutineId = await notifier.addRoutine(
            intentionId: widget.intentionId,
            name: routineName,
            type: "Morning", // Default placeholder, will be set in next screen
            scheduledTime:
                _scheduledTime, // Use default or preserved time for new routine (will be set in next screen)
          );
          // Routine named successfully - no toast needed
        }
        if (mounted) {
          // For both new and existing routines, continue with the flow
          // This allows editing activities for existing routines
          context.pushNamed(
            RouteNames.setRoutineReminder,
            pathParameters: {
              'intentionId': widget.intentionId.toString(),
              'routineId': currentRoutineId.toString(),
            },
          );
        }
      } catch (e) {
        // Error saving routine - silently handle
        debugPrint('Error saving routine: $e');
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.routineId != null && !_isDataInitialized) {
      final routinesAsyncValue =
          ref.watch(routinesForIntentionProvider(widget.intentionId));
      return routinesAsyncValue.when(
        data: (routines) {
          final index = routines.indexWhere((r) => r.id == widget.routineId);
          if (index != -1) {
            _existingRoutine = routines[index];
            _nameController.text = _existingRoutine!.name;
            _scheduledTime = _existingRoutine!.scheduledTime;
            _isDataInitialized = true;

            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) setState(() {}); // Rebuild with populated data
            });
            // Show a temporary loading indicator while the rebuild is scheduled
            return const Scaffold(
                body: Center(child: CircularProgressIndicator()));
          } else {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Routine not found.')),
                );
                context.pop();
              }
            });
            return const Scaffold(
                body: Center(child: Text('Routine not found. Redirecting...')));
          }
        },
        loading: () =>
            const Scaffold(body: Center(child: CircularProgressIndicator())),
        error: (err, stack) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error loading routine: $err')),
              );
              context.pop();
            }
          });
          return const Scaffold(
              body: Center(child: Text('Error loading. Redirecting...')));
        },
      );
    }
    return _buildScaffold(context);
  }

  Future<bool> _onWillPop() async {
    // If this is a new routine being created and user tries to go back,
    // we should delete the incomplete routine
    if (_existingRoutine == null) {
      // This is a new routine creation flow - no cleanup needed yet as routine hasn't been created
      return true;
    }
    return true;
  }

  Future<void> _showExitConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmationDialog(
        title: 'End Setup?',
        message:
            'Are you sure you want to end the routine setup? Your progress will be lost.',
        confirmText: 'End Setup',
        cancelText: 'Continue',
        isDestructive: true,
      ),
    );

    if (confirmed == true && mounted) {
      // Only delete the intention if this is a new creation flow (routineId is null)
      // For editing existing routines, just discard changes without deletion
      if (widget.routineId == null) {
        // This is a new creation flow - delete the incomplete intention
        try {
          final notifier = ref.read(intentionCrudNotifierProvider.notifier);
          await notifier.deleteIntention(widget.intentionId);
          debugPrint('Deleted incomplete intention ${widget.intentionId}');
        } catch (e) {
          debugPrint('Error deleting incomplete intention: $e');
        }
      }
      if (mounted) {
        context.go('/intentions');
      }
    }
  }

  Widget _buildScaffold(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await _onWillPop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: _showExitConfirmation,
          ),
          actions: [
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: CircularProgressIndicator(),
                ),
              )
            else
              TextButton(
                onPressed: _saveRoutine,
                child: const Text('Next'),
              ),
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            // padding: const EdgeInsets.fromLTRB(16.0, 48.0, 16.0, 16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  const FlowHeaderWidget.withTopPadding(
                    title: 'Give your check-in a name.',
                  ),
                  CustomTextField(
                    controller: _nameController,
                    focusNode: _focusNode,
                    hintText: 'e.g., Morning Reflection, End of Day Review',
                    style: CustomTextFieldStyle.withGradient,
                    maxLines: 1,
                    isFormField: true,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a name for your check-in.';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
