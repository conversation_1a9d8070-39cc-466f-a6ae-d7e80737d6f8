import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/confirmation_dialog.dart';

import 'package:mimi_app/src/features/database/providers/database_provider.dart';

class EditIntentionScreen extends ConsumerStatefulWidget {
  final int? intentionId;

  const EditIntentionScreen({super.key, this.intentionId});

  @override
  ConsumerState<EditIntentionScreen> createState() =>
      _EditIntentionScreenState();
}

class _EditIntentionScreenState extends ConsumerState<EditIntentionScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late FocusNode _focusNode;
  bool _isLoading = false;
  Intention? _existingIntention;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _focusNode = FocusNode();

    if (widget.intentionId != null) {
      // If editing, load the existing intention data
      // A more direct fetch (e.g., a dedicated `intentionByIdProvider`) would be better.
      final intentionsAsyncValue = ref.read(allIntentionsProvider);
      if (intentionsAsyncValue is AsyncData<List<Intention>>) {
        final intentions = intentionsAsyncValue.value;
        final index = intentions.indexWhere((i) => i.id == widget.intentionId);
        if (index != -1) {
          _existingIntention = intentions[index];
          _nameController.text = _existingIntention!.name;
        } else {
          // Intention with given ID not found, handle appropriately (e.g., show error, pop)
          // This case is also handled in the build method for robustness
        }
      }
      // If intentionsAsyncValue is AsyncLoading or AsyncError, it will be handled by a loading/error state
      // in the build method, or _existingIntention will remain null and handled there.
    }

    // Auto-focus the text field after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _saveIntention() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      final notifier = ref.read(intentionCrudNotifierProvider.notifier);
      final intentionName = _nameController.text.trim();

      try {
        if (_existingIntention != null) {
          // Update existing intention
          await notifier.updateIntention(_existingIntention!.id, intentionName);
          if (mounted) {
            // ScaffoldMessenger.of(context).showSnackBar(
            //   const SnackBar(content: Text('Intention updated successfully')),
            // );
            context.pop(); // Go back to the intentions list
          }
        } else {
          // Add new intention
          final newIntentionId = await notifier.addIntention(intentionName);
          // Since addIntention now throws on error and returns int, newIntentionId will always be non-null on success.
          if (mounted) {
            // Check mounted before using context
            // ScaffoldMessenger.of(context).showSnackBar(
            //   const SnackBar(content: Text('Intention added successfully')),
            // );
            // Navigate to Name Routine screen, passing the new intentionId
            final String path = GoRouter.of(context).namedLocation(
              RouteNames.nameNewRoutine,
              pathParameters: {'intentionId': newIntentionId.toString()},
              // queryParameters: {} // No query params needed here
            );
            context.push(path);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving intention: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  Future<void> _showExitConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmationDialog(
        title: 'End Setup?',
        message:
            'Are you sure you want to end the intention setup? Your progress will be lost.',
        confirmText: 'End Setup',
        cancelText: 'Continue',
        isDestructive: true,
      ),
    );

    if (confirmed == true && mounted) {
      // Navigate to intentions list screen
      context.go('/intentions');
    }
  }

  @override
  Widget build(BuildContext context) {
    // If editing and the intention wasn't found (e.g., bad ID), show error or redirect.
    if (widget.intentionId != null &&
        _existingIntention == null &&
        !_isLoading) {
      // This check might be better handled by a dedicated intentionProvider(id).when
      // For now, a simple error message and pop.
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // ScaffoldMessenger.of(context).showSnackBar(
        //   const SnackBar(content: Text('Intention not found.')),
        // );
        context.pop();
      });
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: widget.intentionId == null
              ? _showExitConfirmation
              : () => context.go('/intentions'),
        ),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: CircularProgressIndicator(),
              ),
            )
          else
            TextButton(
              onPressed: _saveIntention,
              child: Text(widget.intentionId == null ? 'Next' : 'Save'),
            ),
        ],
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                FlowHeaderWidget(
                    title: widget.intentionId == null
                        ? 'What do you want to focus on or cultivate?'
                        : 'Update your intention:',
                    subtitle: 'To be present and mindful'),
                // const SizedBox(height: 24),
                CustomTextField(
                  controller: _nameController,
                  focusNode: _focusNode,
                  hintText: 'e.g., To be present and mindful',
                  style: CustomTextFieldStyle.withGradient,
                  isFormField: true,
                  maxLines: 4,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter your intention.';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
