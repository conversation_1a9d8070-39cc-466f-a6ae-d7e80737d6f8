import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/confirmation_dialog.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/core/services/notification_service.dart';
import 'package:mimi_app/src/features/user/providers/notification_state_provider.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'widgets/custom_time_picker.dart';
import 'package:permission_handler/permission_handler.dart';

// Removed period selection - now only using time selection

class SetRoutineReminderScreen extends ConsumerStatefulWidget {
  final int intentionId;
  final int routineId;

  const SetRoutineReminderScreen({
    super.key,
    required this.intentionId,
    required this.routineId,
  });

  @override
  ConsumerState<SetRoutineReminderScreen> createState() =>
      _SetRoutineReminderScreenState();
}

class _SetRoutineReminderScreenState
    extends ConsumerState<SetRoutineReminderScreen> {
  TimeOfDay? _selectedTime;
  bool _notificationsEnabled = false;
  CheckInRoutine? _existingRoutine;
  bool _isLoading = false;
  bool _isDataInitialized = false;
  final _notificationService = NotificationService();

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    await _notificationService.initialize();
  }

  Future<void> _requestNotificationPermission() async {
    final status = await Permission.notification.status;
    if (status.isDenied) {
      final granted = await _notificationService.requestPermissions();
      if (!granted && mounted) {
        showWarningToast(
          context,
          title: 'Notification Permission',
          description: 'Notification permission is required for reminders.',
        );
        setState(() => _notificationsEnabled = false);
      }
    } else if (status.isPermanentlyDenied) {
      if (mounted) {
        showWarningToast(
          context,
          title: 'Notification Permission',
          description: 'Please enable notifications in your device settings.',
        );
        setState(() => _notificationsEnabled = false);
      }
    }
  }

  void _showTimePickerDialog() {
    final initialTime = _selectedTime ?? const TimeOfDay(hour: 8, minute: 0);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: CustomTimePicker(
            initialTime: initialTime,
            onTimeChanged: (TimeOfDay newTime) {
              setState(() {
                _selectedTime = newTime;
              });
              Navigator.of(context).pop();
            },
            onCancel: () {
              Navigator.of(context).pop();
            },
          ),
        );
      },
    );
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  Future<void> _saveReminderSettings() async {
    if (_selectedTime == null) {
      showWarningToast(
        context,
        title: 'Time Required',
        description: 'Please select a time for your reminder.',
      );
      return;
    }

    setState(() => _isLoading = true);
    final notifier = ref.read(routineCrudNotifierProvider.notifier);

    try {
      final notificationsEnabled = ref.read(notificationStateProvider);

      // If notifications are enabled, schedule the reminder
      if (notificationsEnabled) {
        final scheduledTime = DateTime(
          DateTime.now().year,
          DateTime.now().month,
          DateTime.now().day,
          _selectedTime!.hour,
          _selectedTime!.minute,
        );

        await _notificationService.scheduleRoutineReminder(
          id: widget.routineId,
          title: 'Routine Reminder',
          body:
              'Time for your "${_existingRoutine?.name ?? 'routine'}" check-in!',
          scheduledTime: scheduledTime,
        );
      } else {
        // If notifications are disabled, cancel any existing reminder
        await _notificationService.cancelRoutineReminder(widget.routineId);
      }

      await notifier.updateRoutine(
        routineId: widget.routineId,
        scheduledTime: _selectedTime != null
            ? DateTime(DateTime.now().year, DateTime.now().month,
                DateTime.now().day, _selectedTime!.hour, _selectedTime!.minute)
            : null,
        notificationEnabled: notificationsEnabled,
        name: null,
        type: 'Daily',
        activities: null,
      );

      if (mounted) {
        context.pushNamed(
          RouteNames.addActivitiesToRoutine,
          pathParameters: {
            'intentionId': widget.intentionId.toString(),
            'routineId': widget.routineId.toString(),
          },
        );
      }
    } catch (e) {
      debugPrint('Error saving reminder: $e');
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Failed to save reminder settings. Please try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<bool> _onWillPop() async {
    // Only delete the routine if it's truly incomplete (no activities configured)
    // This prevents deletion of existing routines that are being edited
    if (_existingRoutine != null && _isRoutineIncomplete(_existingRoutine!)) {
      try {
        final notifier = ref.read(routineCrudNotifierProvider.notifier);
        await notifier.deleteRoutine(_existingRoutine!.id);
        debugPrint('Deleted incomplete routine ${_existingRoutine!.id}');
      } catch (e) {
        debugPrint('Error deleting incomplete routine: $e');
      }
    }
    return true;
  }

  bool _isRoutineIncomplete(CheckInRoutine routine) {
    // Consider a routine incomplete if it has no activities configured
    try {
      final activities = jsonDecode(routine.activities) as List;
      return activities.isEmpty;
    } catch (e) {
      // If we can't parse activities, assume it's incomplete
      return true;
    }
  }

  Future<void> _showExitConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmationDialog(
        title: 'End Setup?',
        message:
            'Are you sure you want to end the routine setup? Your progress will be lost.',
        confirmText: 'End Setup',
        cancelText: 'Continue',
        isDestructive: true,
      ),
    );

    if (confirmed == true && mounted) {
      // Only delete if this is truly an incomplete creation flow
      if (_existingRoutine != null && _isRoutineIncomplete(_existingRoutine!)) {
        // Delete the incomplete routine
        try {
          final notifier = ref.read(routineCrudNotifierProvider.notifier);
          await notifier.deleteRoutine(_existingRoutine!.id);
          debugPrint('Deleted incomplete routine ${_existingRoutine!.id}');
        } catch (e) {
          debugPrint('Error deleting incomplete routine: $e');
        }

        // Also delete the intention if it was created in this session
        // Only delete intention if the routine was truly incomplete
        try {
          final intentionNotifier =
              ref.read(intentionCrudNotifierProvider.notifier);
          await intentionNotifier.deleteIntention(widget.intentionId);
          debugPrint('Deleted incomplete intention ${widget.intentionId}');
        } catch (e) {
          debugPrint('Error deleting incomplete intention: $e');
        }
      }

      if (mounted) {
        context.go('/intentions');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isDataInitialized) {
      final routinesAsyncValue =
          ref.watch(routinesForIntentionProvider(widget.intentionId));
      return routinesAsyncValue.when(
        data: (routines) {
          final index = routines.indexWhere((r) => r.id == widget.routineId);
          if (index != -1) {
            _existingRoutine = routines[index];
            // Initialize time from existing routine
            _selectedTime =
                TimeOfDay.fromDateTime(_existingRoutine!.scheduledTime);
            _notificationsEnabled = _existingRoutine!.notificationEnabled;
            _isDataInitialized = true;

            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) setState(() {}); // Rebuild with populated data
            });
            return const Scaffold(
                body: Center(child: CircularProgressIndicator()));
          } else {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                showWarningToast(
                  context,
                  title: 'Error',
                  description: 'Routine not found.',
                );
                context.pop();
              }
            });
            return const Scaffold(
                body: Center(child: Text('Routine not found. Redirecting...')));
          }
        },
        loading: () =>
            const Scaffold(body: Center(child: CircularProgressIndicator())),
        error: (err, stack) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              showFailureToast(
                context,
                title: 'Error',
                description: 'Error loading routine: $err',
              );
              context.pop();
            }
          });
          return const Scaffold(
              body: Center(child: Text('Error loading. Redirecting...')));
        },
      );
    }

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await _onWillPop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: _showExitConfirmation,
          ),
          actions: [
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: CircularProgressIndicator(),
                ),
              )
            else
              TextButton(
                onPressed: _saveReminderSettings,
                child: const Text('Next'),
              ),
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                FlowHeaderWidget.centered(
                  title:
                      'When do you want to check-in for "${_existingRoutine?.name ?? 'this routine'}"?',
                  titleStyle: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 24),

                // Time Selection - Always visible
                GestureDetector(
                  onTap: _showTimePickerDialog,
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: AppColors.border),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.access_time,
                          color: AppColors.primary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          _selectedTime != null
                              ? _formatTime(_selectedTime!)
                              : 'Select Time',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: AppColors.primary,
                          size: 24,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Notification Settings - Always visible
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppColors.border),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.notifications_active_outlined,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'Enable Notifications',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ),
                      Switch(
                        value: ref.watch(notificationStateProvider),
                        onChanged: (bool value) async {
                          final success = await ref
                              .read(notificationStateProvider.notifier)
                              .toggleNotifications(value);
                          if (!success && value) {
                            // Show toast if permission was denied
                            if (mounted) {
                              showWarningToast(
                                context,
                                title: 'Notifications',
                                description:
                                    'Please enable notifications in your device settings.',
                              );
                            }
                          }
                        },
                        activeColor: AppColors.primary,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
