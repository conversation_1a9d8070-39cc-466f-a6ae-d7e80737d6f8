import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/widgets/flow_header_widget.dart';
import 'package:mimi_app/src/features/user/presentation/widgets/confirmation_dialog.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/breathwork/data/breathwork_patterns.dart';

// Predefined list of activities and their route names for settings
class ActivityDetails {
  final String name;
  final String settingsRouteName; // Assuming you have route names for these
  final String iconPath; // SVG icon path

  ActivityDetails(
      {required this.name,
      required this.settingsRouteName,
      required this.iconPath});
}

final List<ActivityDetails> _availableActivities = [
  ActivityDetails(
      name: 'Breathwork',
      settingsRouteName: RouteNames.breathworkSettings,
      iconPath: 'assets/icons/breathwork_fill.svg'),
  ActivityDetails(
      name: 'Meditation',
      settingsRouteName: RouteNames.meditationSettings,
      iconPath: 'assets/icons/meditation_fill.svg'),
  ActivityDetails(
      name: 'Journal',
      settingsRouteName: RouteNames.journalingSettings,
      iconPath: 'assets/icons/journal_fill.svg'),
  ActivityDetails(
      name: 'Affirmations',
      settingsRouteName: RouteNames.affirmationsSettings,
      iconPath: 'assets/icons/affirmation_fill.svg'),
  ActivityDetails(
      name: 'Mood Tracking',
      settingsRouteName: RouteNames.moodTrackingSettings,
      iconPath: 'assets/icons/mood_fill.svg'),
  ActivityDetails(
      name: 'Gratitude',
      settingsRouteName: RouteNames.gratitudeSettings,
      iconPath: 'assets/icons/gratitude_fill.svg'),
];

// Helper to get ActivityDetails by name
ActivityDetails? getActivityDetailsByName(String name) {
  try {
    return _availableActivities.firstWhere((activity) => activity.name == name);
  } catch (e) {
    return null;
  }
}

// Data class for navigation parameters to activity settings screens
class ActivitySettingsNavArgs {
  final int intentionId;
  final int routineId;
  final List<String> selectedActivities;
  final int currentActivityIndex;

  ActivitySettingsNavArgs({
    required this.intentionId,
    required this.routineId,
    required this.selectedActivities,
    required this.currentActivityIndex,
  });
}

class AddActivitiesToRoutineScreen extends ConsumerStatefulWidget {
  final int intentionId;
  final int routineId;

  const AddActivitiesToRoutineScreen({
    super.key,
    required this.intentionId,
    required this.routineId,
  });

  @override
  ConsumerState<AddActivitiesToRoutineScreen> createState() =>
      _AddActivitiesToRoutineScreenState();
}

class _AddActivitiesToRoutineScreenState
    extends ConsumerState<AddActivitiesToRoutineScreen> {
  CheckInRoutine? _existingRoutine;
  final Set<String> _selectedActivities = {};
  bool _isLoading = false;
  bool _isDataInitialized = false;

  Future<void> _loadSelectedActivities(String activitiesJson) async {
    if (activitiesJson.isEmpty || activitiesJson == '[]') {
      _selectedActivities.clear();
      setState(() {});
      return;
    }
    try {
      final List<dynamic> decodedActivities = jsonDecode(activitiesJson);
      _selectedActivities.clear();

      final db = ref.read(journalDatabaseProvider);

      for (final item in decodedActivities) {
        if (item is int || (item is String && int.tryParse(item) != null)) {
          // It's an activity ID - load the activity and get its type
          final id = item is int ? item : int.parse(item);
          final activity = await db.getActivityById(id);
          if (activity != null) {
            // Map activity type to display name
            ActivityDetails? activityDetail;

            // Create a mapping from database activity types to display names
            final typeToDisplayNameMap = <String, String>{
              'breathwork': 'Breathwork',
              'meditation': 'Meditation',
              'journaling':
                  'Journal', // Database type 'Journaling' -> Display name 'Journal'
              'affirmations': 'Affirmations',
              'moodtracking':
                  'Mood Tracking', // Database type 'MoodTracking' -> Display name 'Mood Tracking'
              'gratitude': 'Gratitude',
            };

            // Get the correct display name for this activity type
            final displayName =
                typeToDisplayNameMap[activity.type.toLowerCase()];

            if (displayName != null) {
              // Find the activity detail by display name
              try {
                activityDetail = _availableActivities.firstWhere(
                  (act) => act.name == displayName,
                );
                debugPrint(
                    'Mapped activity type "${activity.type}" to display name "$displayName"');
              } catch (e) {
                debugPrint(
                    'Warning: Display name "$displayName" not found in available activities');
              }
            }

            // If we still don't have a match, try direct name matching as fallback
            if (activityDetail == null) {
              try {
                activityDetail = _availableActivities.firstWhere(
                  (act) =>
                      act.name.toLowerCase() == activity.type.toLowerCase(),
                );
                debugPrint(
                    'Found direct match for activity type "${activity.type}"');
              } catch (e) {
                // Try partial match as last resort
                try {
                  activityDetail = _availableActivities.firstWhere(
                    (act) => act.name
                        .toLowerCase()
                        .contains(activity.type.toLowerCase()),
                  );
                  debugPrint(
                      'Found partial match for activity type "${activity.type}" -> "${activityDetail.name}"');
                } catch (e) {
                  debugPrint(
                      'No match found for activity type "${activity.type}", creating fallback');
                }
              }
            }

            // Create fallback if no match found
            if (activityDetail == null) {
              String iconPath = 'assets/icons/info.svg'; // default fallback
              String fallbackDisplayName = displayName ?? activity.type;

              switch (activity.type.toLowerCase()) {
                case 'breathwork':
                  iconPath = 'assets/icons/breathwork_fill.svg';
                  fallbackDisplayName = 'Breathwork';
                  break;
                case 'meditation':
                  iconPath = 'assets/icons/meditation_fill.svg';
                  fallbackDisplayName = 'Meditation';
                  break;
                case 'journal':
                case 'journaling':
                  iconPath = 'assets/icons/journal_fill.svg';
                  fallbackDisplayName = 'Journal';
                  break;
                case 'affirmations':
                  iconPath = 'assets/icons/affirmation_fill.svg';
                  fallbackDisplayName = 'Affirmations';
                  break;
                case 'mood tracking':
                case 'mood_tracking':
                case 'moodtracking':
                  iconPath = 'assets/icons/mood_fill.svg';
                  fallbackDisplayName = 'Mood Tracking';
                  break;
                case 'gratitude':
                  iconPath = 'assets/icons/gratitude_fill.svg';
                  fallbackDisplayName = 'Gratitude';
                  break;
              }

              activityDetail = ActivityDetails(
                name: fallbackDisplayName,
                settingsRouteName: '',
                iconPath: iconPath,
              );
              debugPrint(
                  'Created fallback ActivityDetails for "${activity.type}" -> "$fallbackDisplayName"');
            }

            _selectedActivities.add(activityDetail.name);
            debugPrint(
                'Loaded activity by ID: ${activity.id} (${activity.type}) -> ${activityDetail.name}');
          }
        } else if (item is String) {
          // It's an activity name (legacy format)
          if (_availableActivities.any((act) => act.name == item)) {
            _selectedActivities.add(item);
            debugPrint('Loaded activity by name: $item');
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading selected activities: $e');
      _selectedActivities.clear();
    }

    // Trigger rebuild to show loaded activities
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _saveActivities() async {
    setState(() => _isLoading = true);

    try {
      // Create or find activity IDs for the selected activity names
      final db = ref.read(journalDatabaseProvider);
      final activityIds = <int>[];

      for (final activityName in _selectedActivities) {
        final activityId = await _createOrFindActivityForName(activityName, db);
        if (activityId != null) {
          activityIds.add(activityId);
        }
      }

      // Save activity IDs (new format) instead of activity names
      final activitiesJson = jsonEncode(activityIds);

      final notifier = ref.read(routineCrudNotifierProvider.notifier);
      await notifier.updateRoutine(
        routineId: widget.routineId,
        activities: activitiesJson,
        name: null, // These are not updated here
        type: null,
        scheduledTime: null,
        notificationEnabled: null,
      );

      debugPrint(
          '✅ Successfully saved ${activityIds.length} activity IDs to routine ${widget.routineId}');

      // Add a small delay to ensure database operations are committed
      await Future.delayed(const Duration(milliseconds: 100));

      if (mounted) {
        final List<String> selectedActivityNames = _selectedActivities.toList();
        if (selectedActivityNames.isNotEmpty) {
          // Create Activity objects from the IDs we just saved
          final db = ref.read(journalDatabaseProvider);
          final loadedActivities = <Activity>[];

          for (final activityId in activityIds) {
            try {
              final activity = await db.getActivityById(activityId);
              if (activity != null) {
                // Load routine-specific configuration if it exists
                final routineConfig = await db.getRoutineActivityConfig(
                  widget.routineId,
                  activityId,
                );

                // Use routine-specific config if available, otherwise use base activity config
                final finalConfig = routineConfig ?? activity.config;
                final activityWithConfig =
                    activity.copyWith(config: finalConfig);
                loadedActivities.add(activityWithConfig);

                debugPrint(
                    '✅ Loaded activity $activityId with ${routineConfig != null ? 'routine-specific' : 'default'} config');
              }
            } catch (e) {
              debugPrint('❌ Error loading activity $activityId: $e');
            }
          }

          debugPrint(
              '✅ Loaded ${loadedActivities.length} activities for wizard');

          // Navigate to the wizard for sequential configuration
          if (mounted) {
            debugPrint('🚀 Navigating to activity wizard with:');
            debugPrint('   - intentionId: ${widget.intentionId}');
            debugPrint('   - routineId: ${widget.routineId}');
            debugPrint('   - selectedActivities: $selectedActivityNames');
            debugPrint('   - activities count: ${loadedActivities.length}');

            try {
              // Convert Activity objects to JSON for navigation
              final activitiesJson = loadedActivities.isNotEmpty
                  ? loadedActivities.map((a) => a.toJson()).toList()
                  : null;

              context.pushNamed(
                RouteNames.activityWizard,
                pathParameters: {
                  'intentionId': widget.intentionId.toString(),
                  'routineId': widget.routineId.toString(),
                },
                extra: {
                  'selectedActivities': selectedActivityNames,
                  'currentIndex': 0,
                  'activities': activitiesJson,
                },
              );
              debugPrint(
                  '✅ Navigation to activity wizard initiated successfully');
            } catch (e) {
              debugPrint('❌ Error navigating to activity wizard: $e');
              // Fallback navigation
              context.goNamed(RouteNames.intentionsList);
            }
          }
        } else {
          // No activities selected, go back to intentions list
          context.goNamed(RouteNames.intentionsList);
        }
      }
    } catch (e) {
      // Error saving activities - silently handle
      debugPrint('Error saving activities: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<bool> _onWillPop() async {
    // Only delete the routine if it's truly incomplete (no activities configured)
    // This prevents deletion of existing routines that are being edited
    if (_existingRoutine != null && _isRoutineIncomplete(_existingRoutine!)) {
      try {
        final notifier = ref.read(routineCrudNotifierProvider.notifier);
        await notifier.deleteRoutine(_existingRoutine!.id);
        debugPrint('Deleted incomplete routine ${_existingRoutine!.id}');
      } catch (e) {
        debugPrint('Error deleting incomplete routine: $e');
      }
    }
    return true;
  }

  bool _isRoutineIncomplete(CheckInRoutine routine) {
    // Consider a routine incomplete if it has no activities configured
    try {
      final activities = jsonDecode(routine.activities) as List;
      return activities.isEmpty;
    } catch (e) {
      // If we can't parse activities, assume it's incomplete
      return true;
    }
  }

  /// Create or find activity ID for a given activity name
  Future<int?> _createOrFindActivityForName(
      String activityName, JournalDatabase db) async {
    try {
      // Normalize activity name and determine type
      final normalizedName = activityName.toLowerCase().trim();
      String activityType;
      String displayName;

      // Map activity names to types and display names
      switch (normalizedName) {
        case 'breathwork':
          activityType = 'breathwork';
          displayName = 'Breathwork';
          break;
        case 'meditation':
          activityType = 'meditation';
          displayName = 'Meditation';
          break;
        case 'journal':
        case 'journaling':
          activityType = 'journaling';
          displayName = 'Journal';
          break;
        case 'affirmations':
          activityType = 'affirmations';
          displayName = 'Affirmations';
          break;
        case 'mood tracking':
        case 'mood_tracking':
        case 'moodtracking':
          activityType = 'moodTracking';
          displayName = 'Mood Tracking';
          break;
        case 'gratitude':
          activityType = 'gratitude';
          displayName = 'Gratitude';
          break;
        default:
          debugPrint('⚠️ Unknown activity name: "$activityName"');
          return null;
      }

      // Check if activity already exists (get the first one if multiple exist)
      final existingActivities = await (db.select(db.checkInActivities)
            ..where((a) => a.type.equals(activityType))
            ..limit(1))
          .get();

      if (existingActivities.isNotEmpty) {
        return existingActivities.first.id;
      }

      // Create new activity with default configuration
      final defaultConfig = _getDefaultConfigForActivityType(activityType);

      final activityId = await db.into(db.checkInActivities).insert(
            CheckInActivitiesCompanion.insert(
              type: activityType,
              name: displayName,
              config: jsonEncode(defaultConfig.toJson()),
              lastUpdated: DateTime.now(),
            ),
          );

      debugPrint(
          '✅ Created new activity: ID $activityId, type "$activityType", name "$displayName"');
      return activityId;
    } catch (e) {
      debugPrint('❌ Error creating/finding activity for "$activityName": $e');
      return null;
    }
  }

  /// Get default configuration for activity type
  ActivityConfig _getDefaultConfigForActivityType(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'breathwork':
        return ActivityConfig.breathwork(
          selectedPatternId: defaultBreathworkPatterns.first.id,
          cycles: 3,
          availablePatterns: defaultBreathworkPatterns,
        );
      case 'meditation':
        return const ActivityConfig.meditation();
      case 'journaling':
        return const ActivityConfig.journaling();
      case 'affirmations':
        return const ActivityConfig.affirmations();
      case 'moodtracking':
        return const ActivityConfig.moodTracking();
      case 'gratitude':
        return const ActivityConfig.gratitude();
      default:
        return const ActivityConfig.journaling(); // fallback
    }
  }

  Future<void> _showExitConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmationDialog(
        title: 'End Setup?',
        message:
            'Are you sure you want to end the routine setup? Your progress will be lost.',
        confirmText: 'End Setup',
        cancelText: 'Continue',
        isDestructive: true,
      ),
    );

    if (confirmed == true && mounted) {
      // Only delete if this is truly an incomplete creation flow
      if (_existingRoutine != null && _isRoutineIncomplete(_existingRoutine!)) {
        // Delete the incomplete routine
        try {
          final notifier = ref.read(routineCrudNotifierProvider.notifier);
          await notifier.deleteRoutine(_existingRoutine!.id);
          debugPrint('Deleted incomplete routine ${_existingRoutine!.id}');
        } catch (e) {
          debugPrint('Error deleting incomplete routine: $e');
        }

        // Also delete the intention if it was created in this session
        // Only delete intention if the routine was truly incomplete
        try {
          final intentionNotifier =
              ref.read(intentionCrudNotifierProvider.notifier);
          await intentionNotifier.deleteIntention(widget.intentionId);
          debugPrint('Deleted incomplete intention ${widget.intentionId}');
        } catch (e) {
          debugPrint('Error deleting incomplete intention: $e');
        }
      }

      if (mounted) {
        context.go('/intentions');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isDataInitialized) {
      final routinesAsyncValue =
          ref.watch(routinesForIntentionProvider(widget.intentionId));
      return routinesAsyncValue.when(
        data: (routines) {
          final index = routines.indexWhere((r) => r.id == widget.routineId);
          if (index != -1) {
            _existingRoutine = routines[index];

            // Load selected activities asynchronously
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              await _loadSelectedActivities(_existingRoutine!.activities);
              if (mounted) {
                setState(() {
                  _isDataInitialized = true;
                });
              }
            });

            return const Scaffold(
                body: Center(child: CircularProgressIndicator()));
          } else {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Routine not found.')),
                );
                context.pop(); // Pop back if routine is not found
              }
            });
            return const Scaffold(
                body: Center(child: Text('Routine not found. Redirecting...')));
          }
        },
        loading: () =>
            const Scaffold(body: Center(child: CircularProgressIndicator())),
        error: (err, stack) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error loading routine: $err')),
              );
              context.pop(); // Pop back on error
            }
          });
          return const Scaffold(
              body: Center(child: Text('Error loading. Redirecting...')));
        },
      );
    }

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await _onWillPop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: _showExitConfirmation,
          ),
          actions: [
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: CircularProgressIndicator(),
                ),
              )
            else
              TextButton(
                onPressed: _saveActivities,
                child: const Text('Next'),
              ),
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                FlowHeaderWidget.centered(
                  title: _existingRoutine?.name ?? 'Your Routine',
                  titleStyle: Theme.of(context).textTheme.headlineSmall,
                  subtitleStyle: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                const Divider(),
                const SizedBox(height: 16),
                Text(
                  'Select activities for this check-in:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 16), // Increased spacing
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _availableActivities.length,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2, // 2 columns
                    childAspectRatio: 1.6, // Square format
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemBuilder: (context, index) {
                    final activity = _availableActivities[index];
                    final bool isSelected =
                        _selectedActivities.contains(activity.name);

                    // Get the order number for this activity if selected
                    final selectedList = _selectedActivities.toList();
                    final orderNumber = isSelected
                        ? selectedList.indexOf(activity.name) + 1
                        : null;

                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            _selectedActivities.remove(activity.name);
                          } else {
                            _selectedActivities.add(activity.name);
                          }
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context)
                                  .colorScheme
                                  .primaryContainer
                                  .withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(12),
                          border: isSelected
                              ? Border.all(
                                  color: Theme.of(context).colorScheme.primary,
                                  width: 2,
                                )
                              : null,
                        ),
                        child: Stack(
                          children: [
                            // Main content - perfectly centered
                            Center(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      activity.iconPath,
                                      width: 32,
                                      height: 32,
                                      colorFilter: ColorFilter.mode(
                                        isSelected
                                            ? Theme.of(context)
                                                .colorScheme
                                                .onPrimary
                                            : Theme.of(context)
                                                .colorScheme
                                                .primary,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      activity.name,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            color: isSelected
                                                ? Theme.of(context)
                                                    .colorScheme
                                                    .onPrimary
                                                : Theme.of(context)
                                                    .colorScheme
                                                    .primary,
                                            fontWeight: FontWeight.w600,
                                          ),
                                      textAlign: TextAlign.center,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            // Order number badge
                            if (orderNumber != null)
                              Positioned(
                                top: 8,
                                right: 8,
                                child: Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.onPrimary,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      width: 1,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      orderNumber.toString(),
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
