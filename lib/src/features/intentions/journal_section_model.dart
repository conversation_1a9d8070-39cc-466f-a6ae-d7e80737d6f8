// lib/features/journal/domain/models/journal_section_model.dart
import 'package:flutter/material.dart';

class JournalExample {
  final String number;
  final String content;

  JournalExample({
    required this.number,
    required this.content,
  });
}

class JournalSectionModel {
  final String title;
  final String? description;
  final String? quote;
  final String? quoteAuthor;
  final List<JournalExample> examples;
  final IconData? icon;

  JournalSectionModel({
    required this.title,
    this.description,
    this.quote,
    this.quoteAuthor,
    this.examples = const [],
    this.icon,
  });
}
