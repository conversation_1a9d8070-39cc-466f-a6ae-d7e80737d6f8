// lib/features/journal/core/constants/journal_colors.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';

sealed class JournalColors {
  // Note: These methods now require BuildContext to access theme colors
  static Color background(BuildContext context) =>
      Theme.of(context).scaffoldBackgroundColor;
  static Color primary(BuildContext context) =>
      Theme.of(context).colorScheme.onSurface;
  static Color secondary(BuildContext context) =>
      Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7);
  static Color accent(BuildContext context) =>
      Theme.of(context).colorScheme.secondary;
  static Color divider(BuildContext context) =>
      Theme.of(context).colorScheme.outline;

  static TextStyle headingStyle(BuildContext context) => TextStyle(
        fontSize: AppTypography.headlineSmall,
        fontWeight: AppTypography.bold,
        color: Theme.of(context).colorScheme.onSurface,
      );

  static TextStyle subheadingStyle(BuildContext context) => TextStyle(
        fontSize: AppTypography.bodyLarge,
        fontWeight: AppTypography.semiBold,
        color: Theme.of(context).colorScheme.onSurface,
      );

  static TextStyle bodyStyle(BuildContext context) => TextStyle(
        fontSize: AppTypography.bodySmall,
        color: Theme.of(context).colorScheme.onSurface,
      );

  static TextStyle captionStyle(BuildContext context) => TextStyle(
        fontSize: AppTypography.titleSmall,
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
      );
}

class JournalTextStyles {
  static TextStyle pageTitle(BuildContext context) => TextStyle(
        fontSize: AppTypography.headlineSmall,
        fontWeight: AppTypography.semiBold,
        color: Theme.of(context).colorScheme.onSurface,
      );

  static TextStyle sectionTitle(BuildContext context) => TextStyle(
        fontSize: AppTypography.bodyLarge,
        fontWeight: AppTypography.semiBold,
        color: Theme.of(context).colorScheme.onSurface,
      );

  static TextStyle bodyText(BuildContext context) => TextStyle(
        fontSize: AppTypography.bodySmall,
        color: Theme.of(context).colorScheme.onSurface,
        height: AppTypography.heightLarge,
      );

  static TextStyle quote(BuildContext context) => TextStyle(
        fontSize: AppTypography.bodySmall,
        fontStyle: FontStyle.italic,
        color: Theme.of(context).colorScheme.onSurface,
        height: AppTypography.heightLarge,
      );
}

// lib/features/journal/core/constants/journal_spacing.dart
class JournalSpacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
}
