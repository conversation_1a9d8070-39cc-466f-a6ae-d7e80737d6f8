import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../database/providers/database_provider.dart';

part 'intention_selection_provider.g.dart';

// Provider for managing the currently selected intention ID
@riverpod
class SelectedIntentionNotifier extends _$SelectedIntentionNotifier {
  static const String _selectedIntentionKey = 'selected_intention_id';

  @override
  Future<int?> build() async {
    // Load the selected intention from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final selectedIntentionId = prefs.getInt(_selectedIntentionKey);
    return selectedIntentionId;
  }

  Future<void> selectIntention(int intentionId) async {
    // Save to SharedPreferences and update state
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_selectedIntentionKey, intentionId);
    state = AsyncValue.data(intentionId);
  }

  Future<void> clearSelection() async {
    // Remove from SharedPreferences and clear state
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_selectedIntentionKey);
    state = const AsyncValue.data(null);
  }
}

// Provider that returns routines for the currently selected intention
@riverpod
Stream<List<CheckInRoutine>> selectedIntentionRoutines(Ref ref) {
  final selectedIntentionIdAsync = ref.watch(selectedIntentionNotifierProvider);

  return selectedIntentionIdAsync.when(
    data: (selectedIntentionId) {
      if (selectedIntentionId == null) {
        // If no intention is selected, return all routines
        final db = ref.watch(journalDatabaseProvider);
        return db.watchAllRoutines();
      } else {
        // Return routines for the selected intention
        final db = ref.watch(journalDatabaseProvider);
        return db.watchRoutinesForIntention(selectedIntentionId);
      }
    },
    loading: () {
      // While loading, return all routines
      final db = ref.watch(journalDatabaseProvider);
      return db.watchAllRoutines();
    },
    error: (_, __) {
      // On error, return all routines
      final db = ref.watch(journalDatabaseProvider);
      return db.watchAllRoutines();
    },
  );
}

// Provider that returns the currently selected intention details
@riverpod
Stream<Intention?> selectedIntention(Ref ref) {
  final selectedIntentionIdAsync = ref.watch(selectedIntentionNotifierProvider);

  return selectedIntentionIdAsync.when(
    data: (selectedIntentionId) {
      if (selectedIntentionId == null) {
        return Stream.value(null);
      }

      final allIntentions = ref.watch(allIntentionsProvider);
      return allIntentions.when(
        data: (intentions) {
          final filteredIntentions =
              intentions.where((i) => i.id == selectedIntentionId).toList();
          final intention =
              filteredIntentions.isNotEmpty ? filteredIntentions.first : null;
          return Stream.value(intention);
        },
        loading: () => Stream.value(null),
        error: (_, __) => Stream.value(null),
      );
    },
    loading: () => Stream.value(null),
    error: (_, __) => Stream.value(null),
  );
}
