// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'intention_selection_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectedIntentionRoutinesHash() =>
    r'4095ea9fa1223dfd805465b22c955a120a23c14f';

/// See also [selectedIntentionRoutines].
@ProviderFor(selectedIntentionRoutines)
final selectedIntentionRoutinesProvider =
    AutoDisposeStreamProvider<List<CheckInRoutine>>.internal(
  selectedIntentionRoutines,
  name: r'selectedIntentionRoutinesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedIntentionRoutinesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SelectedIntentionRoutinesRef
    = AutoDisposeStreamProviderRef<List<CheckInRoutine>>;
String _$selectedIntentionHash() => r'bf8f090086bb05fd4a4386ef899e709fc0d935be';

/// See also [selectedIntention].
@ProviderFor(selectedIntention)
final selectedIntentionProvider =
    AutoDisposeStreamProvider<Intention?>.internal(
  selectedIntention,
  name: r'selectedIntentionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedIntentionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SelectedIntentionRef = AutoDisposeStreamProviderRef<Intention?>;
String _$selectedIntentionNotifierHash() =>
    r'b1ee5cb81d4abc275f1e9db4f7a6a95b17cf8fc4';

/// See also [SelectedIntentionNotifier].
@ProviderFor(SelectedIntentionNotifier)
final selectedIntentionNotifierProvider =
    AutoDisposeAsyncNotifierProvider<SelectedIntentionNotifier, int?>.internal(
  SelectedIntentionNotifier.new,
  name: r'selectedIntentionNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedIntentionNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedIntentionNotifier = AutoDisposeAsyncNotifier<int?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
