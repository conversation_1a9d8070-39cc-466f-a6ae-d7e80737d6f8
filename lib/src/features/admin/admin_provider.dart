import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/auth/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'providers/firebase_admin_provider.dart';

part 'admin_provider.g.dart';

@riverpod
Future<String> adminEmail(Ref ref) async {
  final firebaseAdminEmail = await ref.watch(firebaseAdminEmailProvider.future);
  return firebaseAdminEmail ??
      '<EMAIL>'; // Fallback to current email if not set in Firebase
}

@riverpod
Future<bool> isAdmin(Ref ref) async {
  final authState = ref.watch(authStateNotifierProvider);
  final adminEmail = await ref.watch(adminEmailProvider.future);

  return authState.when(
    initial: () => false,
    loading: () => false,
    authenticated: (authUser) => authUser.email == adminEmail,
    unauthenticated: () => false,
    error: (_) => false,
  );
}
