// lib/src/features/admin/widgets/audio_management_tab.dart
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../audio_player/data/models/audio_category.dart';
import '../../audio_player/data/models/audio_track.dart';
import '../../audio_player/data/providers/firebase_audio_provider.dart';

class AudioManagementTab extends ConsumerStatefulWidget {
  const AudioManagementTab({super.key});

  @override
  AudioManagementTabState createState() => AudioManagementTabState();
}

class AudioManagementTabState extends ConsumerState<AudioManagementTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Track form controllers
  final _titleController = TextEditingController();
  final _artistController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Category form controllers
  final _categoryNameController = TextEditingController();
  final _categoryDescriptionController = TextEditingController();

  String _selectedCategoryId = '';
  File? _selectedAudioFile;
  File? _selectedArtworkFile;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _artistController.dispose();
    _descriptionController.dispose();
    _categoryNameController.dispose();
    _categoryDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Add Track'),
            Tab(text: 'Manage Categories'),
            Tab(text: 'All Tracks'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAddTrackTab(),
              _buildManageCategoriesTab(),
              _buildAllTracksTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAddTrackTab() {
    final categoriesAsync = ref.watch(audioCategoriesProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Audio Track',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 24),

              // Title field
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Track Title',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Artist field
              TextFormField(
                controller: _artistController,
                decoration: const InputDecoration(
                  labelText: 'Artist (Optional)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Description field
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // Category dropdown
              categoriesAsync.when(
                data: (categories) {
                  if (categories.isEmpty) {
                    return const Text(
                        'No categories available. Please create a category first.');
                  }

                  if (_selectedCategoryId.isEmpty && categories.isNotEmpty) {
                    _selectedCategoryId = categories.first.id;
                  }

                  return DropdownButtonFormField<String>(
                    value: _selectedCategoryId.isEmpty
                        ? null
                        : _selectedCategoryId,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                    ),
                    items: categories.map((category) {
                      return DropdownMenuItem(
                        value: category.id,
                        child: Text(category.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategoryId = value ?? '';
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a category';
                      }
                      return null;
                    },
                  );
                },
                loading: () => const CircularProgressIndicator(),
                error: (error, stack) =>
                    Text('Error loading categories: $error'),
              ),
              const SizedBox(height: 24),

              // Audio file picker
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ElevatedButton.icon(
                    onPressed: _pickAudioFile,
                    icon: const Icon(Icons.audiotrack),
                    label: const Text('Select Audio File'),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedAudioFile?.path.split('/').last ??
                        'No file selected',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Artwork file picker
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ElevatedButton.icon(
                    onPressed: _pickArtworkFile,
                    icon: const Icon(Icons.image),
                    label: const Text('Select Artwork'),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedArtworkFile?.path.split('/').last ??
                        'No file selected',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Upload button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isUploading ? null : _uploadTrack,
                  child: _isUploading
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 8),
                            Text('Uploading...'),
                          ],
                        )
                      : const Text('Upload Track'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildManageCategoriesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manage Categories',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),

          // Add category form
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Add New Category',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _categoryNameController,
                    decoration: const InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _categoryDescriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _createCategory,
                    child: const Text('Create Category'),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Existing categories list
          Text(
            'Existing Categories',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),

          // Use a fixed height container for the categories list
          SizedBox(
            height: 400, // Fixed height to prevent overflow
            child: Consumer(
              builder: (context, ref, child) {
                final categoriesAsync = ref.watch(audioCategoriesProvider);

                return categoriesAsync.when(
                  data: (categories) {
                    if (categories.isEmpty) {
                      return const Center(child: Text('No categories found'));
                    }

                    return ListView.builder(
                      itemCount: categories.length,
                      itemBuilder: (context, index) {
                        final category = categories[index];
                        return Card(
                          child: ListTile(
                            title: Text(category.name),
                            subtitle: Text(category.description ?? ''),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text('${category.trackCount} tracks'),
                                IconButton(
                                  icon: const Icon(Icons.delete,
                                      color: Colors.red),
                                  onPressed: () => _deleteCategory(category.id),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(child: Text('Error: $error')),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllTracksTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'All Tracks',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                final tracksAsync = ref.watch(allTracksProvider);

                return tracksAsync.when(
                  data: (tracks) {
                    if (tracks.isEmpty) {
                      return const Center(child: Text('No tracks found'));
                    }

                    return ListView.builder(
                      itemCount: tracks.length,
                      itemBuilder: (context, index) {
                        final track = tracks[index];
                        return Card(
                          child: ListTile(
                            leading: track.artworkUrl != null
                                ? Image.network(
                                    track.artworkUrl!,
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            const Icon(Icons.music_note),
                                  )
                                : const Icon(Icons.music_note),
                            title: Text(track.title),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (track.artist != null)
                                  Text('Artist: ${track.artist}'),
                                Text('Category: ${track.categoryId}'),
                                if (track.description.isNotEmpty)
                                  Text(
                                    track.description,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                              ],
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _deleteTrack(track.id),
                            ),
                          ),
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(child: Text('Error: $error')),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickAudioFile() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.audio,
      allowMultiple: false,
    );

    if (result != null && result.files.isNotEmpty) {
      setState(() {
        _selectedAudioFile = File(result.files.first.path!);
      });
    }
  }

  Future<void> _pickArtworkFile() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: false,
    );

    if (result != null && result.files.isNotEmpty) {
      setState(() {
        _selectedArtworkFile = File(result.files.first.path!);
      });
    }
  }

  Future<void> _uploadTrack() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedAudioFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an audio file')),
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      final repository = ref.read(firebaseAudioProvider);

      // Upload audio file
      final audioFileName =
          '${DateTime.now().millisecondsSinceEpoch}_${_selectedAudioFile!.path.split('/').last}';
      final audioUrl =
          await repository.uploadAudioFile(_selectedAudioFile!, audioFileName);

      // Upload artwork file if selected
      String? artworkUrl;
      if (_selectedArtworkFile != null) {
        final artworkFileName =
            '${DateTime.now().millisecondsSinceEpoch}_${_selectedArtworkFile!.path.split('/').last}';
        artworkUrl = await repository.uploadArtworkFile(
            _selectedArtworkFile!, artworkFileName);
      }

      // Create track
      final track = AudioTrack(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: _titleController.text,
        audioUrl: audioUrl,
        categoryId: _selectedCategoryId,
        artworkUrl: artworkUrl,
        description: _descriptionController.text,
        artist: _artistController.text.isEmpty ? null : _artistController.text,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await repository.createTrack(track);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Track uploaded successfully')),
        );
        _clearTrackForm();
        // Refresh the providers
        ref.invalidate(allTracksProvider);
        ref.invalidate(audioCategoriesProvider);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error uploading track: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _createCategory() async {
    if (_categoryNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a category name')),
      );
      return;
    }

    try {
      final repository = ref.read(firebaseAudioProvider);
      final category = AudioCategory(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _categoryNameController.text,
        iconPath: '', // You can add icon selection later
        trackIds: [],
        description: _categoryDescriptionController.text.isEmpty
            ? null
            : _categoryDescriptionController.text,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await repository.createCategory(category);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Category created successfully')),
        );
        _clearCategoryForm();
        // Refresh the categories provider
        ref.invalidate(audioCategoriesProvider);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating category: $e')),
        );
      }
    }
  }

  Future<void> _deleteCategory(String categoryId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: const Text(
            'Are you sure you want to delete this category? This will also delete all tracks in this category.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final repository = ref.read(firebaseAudioProvider);
        await repository.deleteCategory(categoryId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Category deleted successfully')),
          );
          // Refresh providers
          ref.invalidate(audioCategoriesProvider);
          ref.invalidate(allTracksProvider);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting category: $e')),
          );
        }
      }
    }
  }

  Future<void> _deleteTrack(String trackId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Track'),
        content: const Text('Are you sure you want to delete this track?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final repository = ref.read(firebaseAudioProvider);
        await repository.deleteTrack(trackId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Track deleted successfully')),
          );
          // Refresh providers
          ref.invalidate(allTracksProvider);
          ref.invalidate(audioCategoriesProvider);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting track: $e')),
          );
        }
      }
    }
  }

  void _clearTrackForm() {
    _titleController.clear();
    _artistController.clear();
    _descriptionController.clear();
    setState(() {
      _selectedAudioFile = null;
      _selectedArtworkFile = null;
    });
  }

  void _clearCategoryForm() {
    _categoryNameController.clear();
    _categoryDescriptionController.clear();
  }
}
