// lib/src/features/admin/widgets/video_management_tab.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../video_player/data/models/video_category.dart';
import '../../video_player/data/providers/video_category_provider.dart';
import '../../video_player/data/repositories/video_category_repository.dart';
import '../../video_player/providers/video_providers.dart';
import '../../video_player/model/video_model.dart';
import '../../../core/utils/toasts.dart';

class VideoManagementTab extends ConsumerStatefulWidget {
  const VideoManagementTab({super.key});

  @override
  VideoManagementTabState createState() => VideoManagementTabState();
}

class VideoManagementTabState extends ConsumerState<VideoManagementTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Video form controllers
  final _titleController = TextEditingController();
  final _urlController = TextEditingController();
  final _thumbnailUrlController = TextEditingController();

  // Category form controllers
  final _categoryNameController = TextEditingController();
  final _categoryDescriptionController = TextEditingController();

  String _selectedCategoryId = '';
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _urlController.dispose();
    _thumbnailUrlController.dispose();
    _categoryNameController.dispose();
    _categoryDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Add Video'),
            Tab(text: 'Manage Categories'),
            Tab(text: 'All Videos'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAddVideoTab(),
              _buildManageCategoriesTab(),
              _buildAllVideosTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAddVideoTab() {
    final categoriesAsync = ref.watch(videoCategoriesProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add New Video',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 24),

            // Title field
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Video Title',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // URL field
            TextFormField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'Video URL',
                hintText: 'YouTube URL or direct video link',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a URL';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Thumbnail URL field
            TextFormField(
              controller: _thumbnailUrlController,
              decoration: const InputDecoration(
                labelText: 'Thumbnail URL (Optional)',
                hintText: 'Custom thumbnail image URL',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // Category dropdown
            categoriesAsync.when(
              data: (categories) {
                if (categories.isEmpty) {
                  return const Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                          'No categories available. Create a category first.'),
                    ),
                  );
                }

                if (_selectedCategoryId.isEmpty && categories.isNotEmpty) {
                  _selectedCategoryId = categories.first.id;
                }

                return DropdownButtonFormField<String>(
                  value:
                      _selectedCategoryId.isEmpty ? null : _selectedCategoryId,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                  ),
                  items: categories.map((category) {
                    return DropdownMenuItem(
                      value: category.id,
                      child: Text(category.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategoryId = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a category';
                    }
                    return null;
                  },
                );
              },
              loading: () => const CircularProgressIndicator(),
              error: (error, stack) => Text('Error loading categories: $error'),
            ),
            const SizedBox(height: 32),

            // Upload button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isUploading ? null : _uploadVideo,
                child: _isUploading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Uploading...'),
                        ],
                      )
                    : const Text('Add Video'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManageCategoriesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manage Video Categories',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),

          // Add Category Form
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Add New Category',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _categoryNameController,
                    decoration: const InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _categoryDescriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _createCategory,
                    child: const Text('Create Category'),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Existing Categories List
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Existing Categories',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Wrap(
                    spacing: 8,
                    children: [
                      SizedBox(
                        height: 32,
                        child: ElevatedButton.icon(
                          onPressed: _refreshAllCategoryCounts,
                          icon: const Icon(Icons.refresh, size: 14),
                          label: const Text('Refresh',
                              style: TextStyle(fontSize: 12)),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            minimumSize: Size.zero,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 32,
                        child: ElevatedButton.icon(
                          onPressed: _debugVideoCategories,
                          icon: const Icon(Icons.bug_report, size: 14),
                          label: const Text('Debug',
                              style: TextStyle(fontSize: 12)),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            minimumSize: Size.zero,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Consumer(
            builder: (context, ref, child) {
              final categoriesAsync = ref.watch(videoCategoriesProvider);
              return categoriesAsync.when(
                data: (categories) => ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    return Card(
                      child: ListTile(
                        title: Text(category.name),
                        subtitle:
                            Text(category.description ?? 'No description'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text('${category.videoCount} videos'),
                            const SizedBox(width: 8),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () => _deleteCategory(category.id),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
                loading: () => const CircularProgressIndicator(),
                error: (error, stack) => Text('Error: $error'),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAllVideosTab() {
    return Consumer(
      builder: (context, ref, child) {
        final allVideosAsync = ref.watch(allVideosProvider);
        final categoriesAsync = ref.watch(videoCategoriesProvider);

        return allVideosAsync.when(
          data: (videos) => videos.isEmpty
              ? const Center(
                  child: Text('No videos found'),
                )
              : categoriesAsync.when(
                  data: (categories) {
                    // Create a map for quick category lookup
                    final categoryMap = {
                      for (var category in categories)
                        category.id: category.name
                    };

                    return ListView.builder(
                      padding: const EdgeInsets.all(16.0),
                      itemCount: videos.length,
                      itemBuilder: (context, index) {
                        final video = videos[index];
                        final categoryName =
                            categoryMap[video.categoryId] ?? 'Unknown Category';

                        return Card(
                          child: ListTile(
                            leading: video.thumbnailUrl.isNotEmpty
                                ? Image.network(
                                    video.thumbnailUrl,
                                    width: 80,
                                    height: 60,
                                    fit: BoxFit.cover,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            const Icon(Icons.video_library),
                                  )
                                : const Icon(Icons.video_library),
                            title: Text(video.title),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Category: $categoryName'),
                                Text('Type: ${video.type.name}'),
                                Text('URL: ${video.url}'),
                              ],
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () =>
                                  _deleteVideo(video.id, video.categoryId),
                            ),
                            onTap: () => _showVideoDetails(video),
                          ),
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) =>
                      Center(child: Text('Error loading categories: $error')),
                ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Error: $error')),
        );
      },
    );
  }

  Future<void> _uploadVideo() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isUploading = true;
    });

    try {
      final url = _urlController.text;
      String videoId = DateTime.now().millisecondsSinceEpoch.toString();
      String thumbnailUrl = _thumbnailUrlController.text;

      // Handle YouTube URLs - extract video ID and thumbnail
      if (url.contains('youtube.com') || url.contains('youtu.be')) {
        // Simple YouTube ID extraction
        RegExp regExp =
            RegExp(r'(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)');
        final match = regExp.firstMatch(url);
        if (match != null) {
          videoId = match.group(1)!;
          if (thumbnailUrl.isEmpty) {
            thumbnailUrl =
                'https://img.youtube.com/vi/$videoId/maxresdefault.jpg';
          }
        }
      }

      await FirebaseFirestore.instance.collection('videos').doc(videoId).set({
        'id': videoId,
        'title': _titleController.text,
        'url': url,
        'thumbnailUrl': thumbnailUrl,
        'categoryId': _selectedCategoryId,
        'createdAt': FieldValue.serverTimestamp(),
      });

      // Update video count in category
      if (_selectedCategoryId.isNotEmpty) {
        await _updateCategoryVideoCount(_selectedCategoryId);
      }

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: 'Video added successfully',
        );
        _clearVideoForm();
        // Refresh providers
        ref.invalidate(allVideosProvider);
        ref.invalidate(videoCategoriesProvider);
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Error adding video: $e',
        );
      }
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _createCategory() async {
    if (_categoryNameController.text.isEmpty) {
      showWarningToast(
        context,
        title: 'Validation Error',
        description: 'Please enter a category name',
      );
      return;
    }

    try {
      final repository = ref.read(videoCategoryRepositoryProvider);
      final category = VideoCategory(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _categoryNameController.text,
        description: _categoryDescriptionController.text.isEmpty
            ? null
            : _categoryDescriptionController.text,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await repository.createCategory(category);

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: 'Category created successfully',
        );
        _clearCategoryForm();
        // Refresh the categories provider
        ref.invalidate(videoCategoriesProvider);
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Error creating category: $e',
        );
      }
    }
  }

  Future<void> _deleteCategory(String categoryId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: const Text('Are you sure you want to delete this category?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final repository = ref.read(videoCategoryRepositoryProvider);
        await repository.deleteCategory(categoryId);

        if (mounted) {
          showSuccessToast(
            context,
            title: 'Success',
            description: 'Category deleted successfully',
          );
          // Refresh providers
          ref.invalidate(videoCategoriesProvider);
        }
      } catch (e) {
        if (mounted) {
          showFailureToast(
            context,
            title: 'Error',
            description: 'Error deleting category: $e',
          );
        }
      }
    }
  }

  void _clearVideoForm() {
    _titleController.clear();
    _urlController.clear();
    _thumbnailUrlController.clear();
  }

  void _clearCategoryForm() {
    _categoryNameController.clear();
    _categoryDescriptionController.clear();
  }

  Future<void> _deleteVideo(String videoId, String categoryId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Video'),
        content: const Text('Are you sure you want to delete this video?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        debugPrint('Deleting video with ID: $videoId, categoryId: $categoryId');

        // First check if the video document exists
        final videoDoc = await FirebaseFirestore.instance
            .collection('videos')
            .doc(videoId)
            .get();

        if (!videoDoc.exists) {
          debugPrint(
              'Video document does not exist, may have been already deleted');
          if (mounted) {
            showWarningToast(
              context,
              title: 'Warning',
              description: 'Video was already deleted or does not exist',
            );
            // Still refresh the UI to remove it from the list
            ref.invalidate(allVideosProvider);
            ref.invalidate(videoCategoriesProvider);
          }
          return;
        }

        await FirebaseFirestore.instance
            .collection('videos')
            .doc(videoId)
            .delete();

        debugPrint('Video deleted successfully. Updating category count...');

        // Update video count in category (only if category exists)
        await _updateCategoryVideoCount(categoryId);

        if (mounted) {
          showSuccessToast(
            context,
            title: 'Success',
            description: 'Video deleted successfully',
          );
          // Refresh providers
          ref.invalidate(allVideosProvider);
          ref.invalidate(videoCategoriesProvider);
        }
      } catch (e) {
        debugPrint('Error deleting video: $e');
        if (mounted) {
          showFailureToast(
            context,
            title: 'Error',
            description: 'Error deleting video: $e',
          );
        }
      }
    }
  }

  void _showVideoDetails(Video video) {
    showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          final categoriesAsync = ref.watch(videoCategoriesProvider);

          return AlertDialog(
            title: Text(video.title),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (video.thumbnailUrl.isNotEmpty)
                  Image.network(
                    video.thumbnailUrl,
                    height: 200,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        const Icon(Icons.video_library, size: 100),
                  ),
                const SizedBox(height: 16),
                Text('URL: ${video.url}'),
                categoriesAsync.when(
                  data: (categories) {
                    final category = categories.firstWhere(
                      (cat) => cat.id == video.categoryId,
                      orElse: () =>
                          const VideoCategory(id: '', name: 'Unknown Category'),
                    );
                    return Text('Category: ${category.name}');
                  },
                  loading: () => Text('Category ID: ${video.categoryId}'),
                  error: (error, stack) =>
                      Text('Category ID: ${video.categoryId}'),
                ),
                Text('Type: ${video.type.name}'),
                Text('ID: ${video.id}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _updateCategoryVideoCount(String categoryId) async {
    try {
      // First check if the category exists
      final repository = ref.read(videoCategoryRepositoryProvider);
      final category = await repository.getCategoryById(categoryId);

      if (category == null) {
        debugPrint(
            'Category with ID $categoryId not found. Skipping video count update.');
        return;
      }

      final videosSnapshot = await FirebaseFirestore.instance
          .collection('videos')
          .where('categoryId', isEqualTo: categoryId)
          .get();

      final videoCount = videosSnapshot.docs.length;
      await repository.updateVideoCount(categoryId, videoCount);
      debugPrint(
          'Updated video count for category ${category.name}: $videoCount videos');
    } catch (e) {
      debugPrint(
          'Error updating category video count for categoryId $categoryId: $e');
    }
  }

  Future<void> _refreshAllCategoryCounts() async {
    try {
      final repository = ref.read(videoCategoryRepositoryProvider);
      final categories = await repository.getCategories();

      for (final category in categories) {
        await _updateCategoryVideoCount(category.id);
      }

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: 'All category counts refreshed successfully',
        );
        // Refresh the categories provider to show updated counts
        ref.invalidate(videoCategoriesProvider);
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Error refreshing counts: $e',
        );
      }
    }
  }

  Future<void> _debugVideoCategories() async {
    try {
      debugPrint('=== DEBUG: Video Categories Analysis ===');

      // Get all videos
      final videosSnapshot =
          await FirebaseFirestore.instance.collection('videos').get();
      final videos =
          videosSnapshot.docs.map((doc) => Video.fromMap(doc.data())).toList();

      // Get all categories
      final repository = ref.read(videoCategoryRepositoryProvider);
      final categories = await repository.getCategories();

      debugPrint('Total videos: ${videos.length}');
      debugPrint('Total categories: ${categories.length}');

      // Create a set of valid category IDs
      final validCategoryIds = categories.map((c) => c.id).toSet();
      debugPrint('Valid category IDs: $validCategoryIds');

      // Check for orphaned videos
      final orphanedVideos = videos
          .where((video) => !validCategoryIds.contains(video.categoryId))
          .toList();

      if (orphanedVideos.isNotEmpty) {
        debugPrint('=== ORPHANED VIDEOS (${orphanedVideos.length}) ===');
        for (final video in orphanedVideos) {
          debugPrint(
              'Video: ${video.title} (ID: ${video.id}) -> Invalid Category ID: ${video.categoryId}');
        }
      } else {
        debugPrint('No orphaned videos found.');
      }

      // Show category breakdown
      debugPrint('=== CATEGORY BREAKDOWN ===');
      for (final category in categories) {
        final categoryVideos =
            videos.where((v) => v.categoryId == category.id).length;
        debugPrint(
            '${category.name} (${category.id}): $categoryVideos videos (stored count: ${category.videoCount})');
      }

      if (mounted) {
        showInfoToast(
          context,
          title: 'Debug Complete',
          description:
              'Check console for detailed analysis. Found ${orphanedVideos.length} orphaned videos.',
        );
      }
    } catch (e) {
      debugPrint('Error during debug analysis: $e');
      if (mounted) {
        showFailureToast(
          context,
          title: 'Debug Error',
          description: 'Error during analysis: $e',
        );
      }
    }
  }
}
