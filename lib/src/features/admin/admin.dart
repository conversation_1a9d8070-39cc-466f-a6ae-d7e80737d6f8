// admin_auth_provider.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/admin/admin_provider.dart';
import 'package:mimi_app/src/features/admin/widgets/audio_management_tab.dart';
import 'package:mimi_app/src/features/admin/widgets/video_management_tab.dart';
import 'package:mimi_app/src/features/admin/widgets/header_image_management_tab.dart';
import 'package:mimi_app/src/features/admin/widgets/background_image_management_tab.dart';
import 'package:mimi_app/src/features/daily_quotes/quote_model.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';

// admin_screen.dart
class AdminScreen extends ConsumerWidget {
  const AdminScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAdminAsync = ref.watch(isAdminProvider);

    return isAdminAsync.when(
      data: (isAdmin) {
        if (!isAdmin) {
          return Scaffold(
            body: Center(
              child: Text('Access Denied: Admin privileges required'),
            ),
          );
        }

        return DefaultTabController(
          length: 7,
          child: Scaffold(
            appBar: AppBar(
              title: Text('Admin Panel'),
              bottom: TabBar(
                isScrollable: true,
                tabs: [
                  Tab(text: 'Videos'),
                  Tab(text: 'Quotes'),
                  Tab(text: 'Journal Prompts'),
                  Tab(text: 'Audio'),
                  Tab(text: 'Home Headers'),
                  Tab(text: 'Backgrounds'),
                  Tab(text: 'Database'),
                ],
              ),
            ),
            body: TabBarView(
              children: [
                VideoManagementTab(),
                QuoteManagementTab(),
                JournalPromptsManagementTab(),
                AudioManagementTab(),
                HeaderImageManagementTab(),
                BackgroundImageManagementTab(),
                DatabaseManagementTab(),
              ],
            ),
          ),
        );
      },
      error: (e, _) => Scaffold(
        body: Center(
          child: Text('Error: $e'),
        ),
      ),
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }
}

// quote_management_tab.dart
class QuoteManagementTab extends ConsumerStatefulWidget {
  const QuoteManagementTab({super.key});

  @override
  _QuoteManagementTabState createState() => _QuoteManagementTabState();
}

class _QuoteManagementTabState extends ConsumerState<QuoteManagementTab> {
  final _formKey = GlobalKey<FormState>();
  final _textController = TextEditingController();
  final _authorController = TextEditingController();
  String _selectedCategory = 'Motivation';

  final List<String> _categories = [
    'Motivation',
    'Success',
    'Life',
    'Wisdom',
    'Leadership',
    // Add more categories as needed
  ];

  Future<void> _uploadQuote() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      await FirebaseFirestore.instance.collection('quotes').add({
        'text': _textController.text,
        'author': _authorController.text,
        'category': _selectedCategory,
        'createdAt': FieldValue.serverTimestamp(),
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Quote uploaded successfully')),
      );

      _clearForm();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error uploading quote: $e')),
      );
    }
  }

  void _clearForm() {
    _textController.clear();
    _authorController.clear();
    setState(() {
      _selectedCategory = _categories.first;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextFormField(
              controller: _textController,
              decoration: InputDecoration(labelText: 'Quote Text'),
              maxLines: 3,
              validator: (value) =>
                  value?.isEmpty ?? true ? 'Please enter the quote text' : null,
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _authorController,
              decoration: InputDecoration(labelText: 'Author'),
              validator: (value) =>
                  value?.isEmpty ?? true ? 'Please enter the author' : null,
            ),
            SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: InputDecoration(labelText: 'Category'),
              items: _categories.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _uploadQuote,
              child: Text('Upload Quote'),
            ),
            SizedBox(height: 32),
            Text(
              'Recent Quotes',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance
                  .collection('quotes')
                  .orderBy('createdAt', descending: true)
                  .limit(10)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return Text('Error: ${snapshot.error}');
                }

                if (!snapshot.hasData) {
                  return Center(child: CircularProgressIndicator());
                }

                final quotes = snapshot.data!.docs.map((doc) {
                  return Quote.fromFirestore(doc);
                }).toList();

                return ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: quotes.length,
                  itemBuilder: (context, index) {
                    final quote = quotes[index];
                    return Card(
                      margin: EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        title: Text(quote.text),
                        subtitle: Text('- ${quote.author}'),
                        trailing: IconButton(
                          icon: Icon(Icons.delete),
                          onPressed: () async {
                            try {
                              await FirebaseFirestore.instance
                                  .collection('quotes')
                                  .doc(quote.id)
                                  .delete();
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Quote deleted')),
                              );
                            } catch (e) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Error deleting quote: $e'),
                                ),
                              );
                            }
                          },
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    _authorController.dispose();
    super.dispose();
  }
}

// database_management_tab.dart
class DatabaseManagementTab extends ConsumerWidget {
  const DatabaseManagementTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Database Management',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Danger Zone',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'This will permanently delete ALL app data including:\n'
                    '• All journal entries\n'
                    '• All activity records (mood, meditation, etc.)\n'
                    '• All routines and configurations\n'
                    '• All intentions\n'
                    '• All user progress and statistics',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _showClearDataDialog(context, ref),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        'Clear All App Data',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⚠️ Clear All Data'),
        content: const Text(
          'Are you absolutely sure you want to delete ALL app data?\n\n'
          'This action cannot be undone and will permanently remove:\n'
          '• All user progress\n'
          '• All journal entries\n'
          '• All activity records\n'
          '• All routines and configurations\n\n'
          'Type "DELETE" to confirm:',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => _showConfirmationDialog(context, ref),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _showConfirmationDialog(BuildContext context, WidgetRef ref) {
    final TextEditingController confirmController = TextEditingController();

    Navigator.pop(context); // Close first dialog

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Final Confirmation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Type "DELETE" to confirm:'),
            const SizedBox(height: 16),
            TextField(
              controller: confirmController,
              decoration: const InputDecoration(
                hintText: 'Type DELETE here',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (confirmController.text.trim() == 'DELETE') {
                Navigator.pop(context);
                await _clearAllData(context, ref);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please type "DELETE" to confirm'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('DELETE ALL DATA'),
          ),
        ],
      ),
    );
  }

  Future<void> _clearAllData(BuildContext context, WidgetRef ref) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Clearing all data...'),
            ],
          ),
        ),
      );

      // Clear the database
      final db = ref.read(journalDatabaseProvider);
      await db.clearAllData();

      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ All app data has been cleared successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error clearing data: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }
}

// journal_prompts_management_tab.dart
class JournalPromptsManagementTab extends ConsumerStatefulWidget {
  const JournalPromptsManagementTab({super.key});

  @override
  ConsumerState<JournalPromptsManagementTab> createState() =>
      _JournalPromptsManagementTabState();
}

class _JournalPromptsManagementTabState
    extends ConsumerState<JournalPromptsManagementTab> {
  final _formKey = GlobalKey<FormState>();
  final _promptController = TextEditingController();

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Add Prompt Form
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Add New Journal Prompt',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _promptController,
                      decoration: const InputDecoration(
                        labelText: 'Prompt Text',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a prompt';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _uploadPrompt,
                      child: const Text('Add Prompt'),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          // Existing Prompts List
          Text(
            'Existing Prompts',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance
                  .collection('journal_prompts')
                  .orderBy('createdAt', descending: true)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }

                final prompts = snapshot.data?.docs ?? [];

                if (prompts.isEmpty) {
                  return const Center(child: Text('No prompts found'));
                }

                return ListView.builder(
                  itemCount: prompts.length,
                  itemBuilder: (context, index) {
                    final prompt = prompts[index];
                    final data = prompt.data() as Map<String, dynamic>;

                    return Card(
                      child: ListTile(
                        title: Text(data['text'] ?? ''),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _deletePrompt(prompt.id),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _uploadPrompt() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      await FirebaseFirestore.instance.collection('journal_prompts').add({
        'text': _promptController.text,
        'createdAt': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Prompt added successfully')),
        );
      }

      _clearForm();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding prompt: $e')),
        );
      }
    }
  }

  Future<void> _deletePrompt(String promptId) async {
    try {
      await FirebaseFirestore.instance
          .collection('journal_prompts')
          .doc(promptId)
          .delete();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Prompt deleted')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting prompt: $e')),
        );
      }
    }
  }

  void _clearForm() {
    _promptController.clear();
  }
}
