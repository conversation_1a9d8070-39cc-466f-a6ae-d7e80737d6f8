import 'package:flutter/foundation.dart';
import '../models/firebase_header_theme.dart';
import '../models/firebase_background_image.dart';
import '../repositories/firebase_header_repository.dart';
import '../repositories/firebase_background_repository.dart';

class MigrationHelper {
  static const List<Map<String, dynamic>> _defaultHeaderThemes = [
    {
      'id': 'mountain',
      'name': 'Mountain',
      'description': 'Serene mountain landscapes for different times of day',
      'imageUrls': {
        'morning': 'assets/images/headers/mountain_morning.png',
        'noon': 'assets/images/headers/mountain_noon.png',
        'evening': 'assets/images/headers/mountain_evening.png',
        'night': 'assets/images/headers/mountain_night.png',
      },
      'order': 0,
    },
    {
      'id': 'sea',
      'name': 'Sea',
      'description': 'Peaceful ocean views throughout the day',
      'imageUrls': {
        'morning': 'assets/images/headers/sea_morning.png',
        'noon': 'assets/images/headers/sea_noon.png',
        'evening': 'assets/images/headers/sea_evening.png',
        'night': 'assets/images/headers/sea_night.png',
      },
      'order': 1,
    },
    {
      'id': 'city',
      'name': 'City',
      'description': 'Urban skylines from dawn to dusk',
      'imageUrls': {
        'morning': 'assets/images/headers/city_morning.png',
        'noon': 'assets/images/headers/city_noon.png',
        'evening': 'assets/images/headers/city_evening.png',
        'night': 'assets/images/headers/city_night.png',
      },
      'order': 2,
    },
    {
      'id': 'forest',
      'name': 'Forest',
      'description': 'Tranquil forest scenes through the day',
      'imageUrls': {
        'morning': 'assets/images/headers/forest_morning.png',
        'noon': 'assets/images/headers/forest_noon.png',
        'evening': 'assets/images/headers/forest_evening.png',
        'night': 'assets/images/headers/forest_night.png',
      },
      'order': 3,
    },
  ];

  static const List<Map<String, dynamic>> _defaultBackgroundImages = [
    {
      'id': 'ocean',
      'name': 'Ocean Waves',
      'description': 'Calming ocean waves for meditation and quotes',
      'imageUrl': 'assets/images/breathwork/ocean.png',
      'tags': ['nature', 'water', 'peaceful', 'blue'],
      'order': 0,
    },
    {
      'id': 'forest',
      'name': 'Forest',
      'description': 'Lush green forest for grounding and focus',
      'imageUrl': 'assets/images/breathwork/forest.png',
      'tags': ['nature', 'green', 'trees', 'peaceful'],
      'order': 1,
    },
    {
      'id': 'mountain',
      'name': 'Mountain View',
      'description': 'Majestic mountain peaks for inspiration',
      'imageUrl': 'assets/images/breathwork/mountain.png',
      'tags': ['nature', 'mountains', 'inspiring', 'peaceful'],
      'order': 2,
    },
    {
      'id': 'space',
      'name': 'Space',
      'description': 'Cosmic views for deep contemplation',
      'imageUrl': 'assets/images/breathwork/space.png',
      'tags': ['cosmic', 'stars', 'universe', 'contemplative'],
      'order': 3,
    },
  ];

  /// Migrates default header themes to Firebase
  /// This should be called once during app initialization or from admin panel
  static Future<void> migrateDefaultHeaderThemes() async {
    try {
      final repository = FirebaseHeaderRepository();

      // Check if themes already exist
      final existingThemes = await repository.getHeaderThemes();
      if (existingThemes.isNotEmpty) {
        debugPrint(
            'Header themes already exist in Firebase, skipping migration');
        return;
      }

      debugPrint('Migrating default header themes to Firebase...');

      for (final themeData in _defaultHeaderThemes) {
        final theme = FirebaseHeaderTheme(
          id: themeData['id'],
          name: themeData['name'],
          description: themeData['description'],
          imageUrls: Map<String, String>.from(themeData['imageUrls']),
          order: themeData['order'],
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await repository.createHeaderTheme(theme);
        debugPrint('Created header theme: ${theme.name}');
      }

      debugPrint('Header themes migration completed successfully');
    } catch (e) {
      debugPrint('Error migrating header themes: $e');
      rethrow;
    }
  }

  /// Migrates default background images to Firebase
  /// This should be called once during app initialization or from admin panel
  static Future<void> migrateDefaultBackgroundImages() async {
    try {
      final repository = FirebaseBackgroundRepository();

      // Check if images already exist
      final existingImages = await repository.getBackgroundImages();
      if (existingImages.isNotEmpty) {
        debugPrint(
            'Background images already exist in Firebase, skipping migration');
        return;
      }

      debugPrint('Migrating default background images to Firebase...');

      for (final imageData in _defaultBackgroundImages) {
        final image = FirebaseBackgroundImage(
          id: imageData['id'],
          name: imageData['name'],
          description: imageData['description'],
          imageUrl: imageData['imageUrl'],
          tags: List<String>.from(imageData['tags']),
          order: imageData['order'],
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await repository.createBackgroundImage(image);
        debugPrint('Created background image: ${image.name}');
      }

      debugPrint('Background images migration completed successfully');
    } catch (e) {
      debugPrint('Error migrating background images: $e');
      rethrow;
    }
  }

  /// Migrates all default content to Firebase
  static Future<void> migrateAllDefaults() async {
    await migrateDefaultHeaderThemes();
    await migrateDefaultBackgroundImages();
  }

  /// Clears all Firebase content (use with caution!)
  static Future<void> clearAllFirebaseContent() async {
    try {
      debugPrint('Clearing all Firebase content...');

      final headerRepo = FirebaseHeaderRepository();
      final backgroundRepo = FirebaseBackgroundRepository();

      // Get all themes and images
      final themes = await headerRepo.getHeaderThemes();
      final images = await backgroundRepo.getBackgroundImages();

      // Delete all themes
      for (final theme in themes) {
        await headerRepo.deleteHeaderTheme(theme.id);
        debugPrint('Deleted header theme: ${theme.name}');
      }

      // Delete all images
      for (final image in images) {
        await backgroundRepo.deleteBackgroundImage(image.id);
        debugPrint('Deleted background image: ${image.name}');
      }

      debugPrint('Firebase content cleared successfully');
    } catch (e) {
      debugPrint('Error clearing Firebase content: $e');
      rethrow;
    }
  }
}
