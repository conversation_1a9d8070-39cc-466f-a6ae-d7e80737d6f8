import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/firebase_background_image.dart';
import '../repositories/firebase_background_repository.dart';

part 'firebase_background_provider.g.dart';

// Provider for Firebase background repository
@riverpod
FirebaseBackgroundRepository firebaseBackgroundRepository(Ref ref) {
  return FirebaseBackgroundRepository();
}

// Provider for background images from Firebase
@riverpod
Future<List<FirebaseBackgroundImage>> firebaseBackgroundImages(Ref ref) async {
  final repository = ref.watch(firebaseBackgroundRepositoryProvider);
  return repository.getBackgroundImages();
}

// Stream provider for real-time background images
@riverpod
Stream<List<FirebaseBackgroundImage>> firebaseBackgroundImagesStream(Ref ref) {
  final repository = ref.watch(firebaseBackgroundRepositoryProvider);
  return repository.watchBackgroundImages();
}

// Provider for a specific background image by ID
@riverpod
Future<FirebaseBackgroundImage?> firebaseBackgroundImageById(
  Ref ref,
  String id,
) async {
  final repository = ref.watch(firebaseBackgroundRepositoryProvider);
  return repository.getBackgroundImageById(id);
}
