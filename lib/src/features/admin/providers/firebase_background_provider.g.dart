// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_background_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$firebaseBackgroundRepositoryHash() =>
    r'ab6539e67af732679f578714d515d5936daabd0a';

/// See also [firebaseBackgroundRepository].
@ProviderFor(firebaseBackgroundRepository)
final firebaseBackgroundRepositoryProvider =
    AutoDisposeProvider<FirebaseBackgroundRepository>.internal(
  firebaseBackgroundRepository,
  name: r'firebaseBackgroundRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseBackgroundRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseBackgroundRepositoryRef
    = AutoDisposeProviderRef<FirebaseBackgroundRepository>;
String _$firebaseBackgroundImagesHash() =>
    r'c45d13ca2511eda161668cbbd8d62b3d6dd81e06';

/// See also [firebaseBackgroundImages].
@ProviderFor(firebaseBackgroundImages)
final firebaseBackgroundImagesProvider =
    AutoDisposeFutureProvider<List<FirebaseBackgroundImage>>.internal(
  firebaseBackgroundImages,
  name: r'firebaseBackgroundImagesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseBackgroundImagesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseBackgroundImagesRef
    = AutoDisposeFutureProviderRef<List<FirebaseBackgroundImage>>;
String _$firebaseBackgroundImagesStreamHash() =>
    r'65cf717b434269ffb566b559c25365e8ddde0242';

/// See also [firebaseBackgroundImagesStream].
@ProviderFor(firebaseBackgroundImagesStream)
final firebaseBackgroundImagesStreamProvider =
    AutoDisposeStreamProvider<List<FirebaseBackgroundImage>>.internal(
  firebaseBackgroundImagesStream,
  name: r'firebaseBackgroundImagesStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseBackgroundImagesStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseBackgroundImagesStreamRef
    = AutoDisposeStreamProviderRef<List<FirebaseBackgroundImage>>;
String _$firebaseBackgroundImageByIdHash() =>
    r'769dd06e4b885e4267ce2df02eb0d77ee790c787';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [firebaseBackgroundImageById].
@ProviderFor(firebaseBackgroundImageById)
const firebaseBackgroundImageByIdProvider = FirebaseBackgroundImageByIdFamily();

/// See also [firebaseBackgroundImageById].
class FirebaseBackgroundImageByIdFamily
    extends Family<AsyncValue<FirebaseBackgroundImage?>> {
  /// See also [firebaseBackgroundImageById].
  const FirebaseBackgroundImageByIdFamily();

  /// See also [firebaseBackgroundImageById].
  FirebaseBackgroundImageByIdProvider call(
    String id,
  ) {
    return FirebaseBackgroundImageByIdProvider(
      id,
    );
  }

  @override
  FirebaseBackgroundImageByIdProvider getProviderOverride(
    covariant FirebaseBackgroundImageByIdProvider provider,
  ) {
    return call(
      provider.id,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'firebaseBackgroundImageByIdProvider';
}

/// See also [firebaseBackgroundImageById].
class FirebaseBackgroundImageByIdProvider
    extends AutoDisposeFutureProvider<FirebaseBackgroundImage?> {
  /// See also [firebaseBackgroundImageById].
  FirebaseBackgroundImageByIdProvider(
    String id,
  ) : this._internal(
          (ref) => firebaseBackgroundImageById(
            ref as FirebaseBackgroundImageByIdRef,
            id,
          ),
          from: firebaseBackgroundImageByIdProvider,
          name: r'firebaseBackgroundImageByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$firebaseBackgroundImageByIdHash,
          dependencies: FirebaseBackgroundImageByIdFamily._dependencies,
          allTransitiveDependencies:
              FirebaseBackgroundImageByIdFamily._allTransitiveDependencies,
          id: id,
        );

  FirebaseBackgroundImageByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<FirebaseBackgroundImage?> Function(
            FirebaseBackgroundImageByIdRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FirebaseBackgroundImageByIdProvider._internal(
        (ref) => create(ref as FirebaseBackgroundImageByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FirebaseBackgroundImage?> createElement() {
    return _FirebaseBackgroundImageByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FirebaseBackgroundImageByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FirebaseBackgroundImageByIdRef
    on AutoDisposeFutureProviderRef<FirebaseBackgroundImage?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _FirebaseBackgroundImageByIdProviderElement
    extends AutoDisposeFutureProviderElement<FirebaseBackgroundImage?>
    with FirebaseBackgroundImageByIdRef {
  _FirebaseBackgroundImageByIdProviderElement(super.provider);

  @override
  String get id => (origin as FirebaseBackgroundImageByIdProvider).id;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
