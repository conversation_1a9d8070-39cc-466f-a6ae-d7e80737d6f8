// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_admin_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$firebaseAdminRepositoryHash() =>
    r'b8958b43083c6a831dcacde7e9331281d0a9b26b';

/// See also [firebaseAdminRepository].
@ProviderFor(firebaseAdminRepository)
final firebaseAdminRepositoryProvider =
    AutoDisposeProvider<FirebaseAdminRepository>.internal(
  firebaseAdminRepository,
  name: r'firebaseAdminRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseAdminRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseAdminRepositoryRef
    = AutoDisposeProviderRef<FirebaseAdminRepository>;
String _$firebaseAdminEmailHash() =>
    r'af5fad33b0036f079be0548ca41a056a2ed5c4a9';

/// See also [firebaseAdminEmail].
@ProviderFor(firebaseAdminEmail)
final firebaseAdminEmailProvider = AutoDisposeFutureProvider<String?>.internal(
  firebaseAdminEmail,
  name: r'firebaseAdminEmailProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseAdminEmailHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseAdminEmailRef = AutoDisposeFutureProviderRef<String?>;
String _$firebaseAdminEmailStreamHash() =>
    r'c3c8da1918ebf5065cc3bafa450afe64abce11b7';

/// See also [firebaseAdminEmailStream].
@ProviderFor(firebaseAdminEmailStream)
final firebaseAdminEmailStreamProvider =
    AutoDisposeStreamProvider<String?>.internal(
  firebaseAdminEmailStream,
  name: r'firebaseAdminEmailStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseAdminEmailStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseAdminEmailStreamRef = AutoDisposeStreamProviderRef<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
