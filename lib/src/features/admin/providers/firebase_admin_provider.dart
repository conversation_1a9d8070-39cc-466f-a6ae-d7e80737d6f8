import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../repositories/firebase_admin_repository.dart';

part 'firebase_admin_provider.g.dart';

// Provider for Firebase admin repository
@riverpod
FirebaseAdminRepository firebaseAdminRepository(Ref ref) {
  return FirebaseAdminRepository();
}

// Provider for admin email from Firebase
@riverpod
Future<String?> firebaseAdminEmail(Ref ref) async {
  final repository = ref.watch(firebaseAdminRepositoryProvider);
  return repository.getAdminEmail();
}

// Stream provider for real-time admin email updates
@riverpod
Stream<String?> firebaseAdminEmailStream(Ref ref) {
  final repository = ref.watch(firebaseAdminRepositoryProvider);
  return repository.watchAdminEmail();
}
