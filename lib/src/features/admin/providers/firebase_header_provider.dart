import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/firebase_header_theme.dart';
import '../repositories/firebase_header_repository.dart';

part 'firebase_header_provider.g.dart';

// Provider for Firebase header repository
@riverpod
FirebaseHeaderRepository firebaseHeaderRepository(Ref ref) {
  return FirebaseHeaderRepository();
}

// Provider for header themes from Firebase
@riverpod
Future<List<FirebaseHeaderTheme>> firebaseHeaderThemes(Ref ref) async {
  final repository = ref.watch(firebaseHeaderRepositoryProvider);
  return repository.getHeaderThemes();
}

// Stream provider for real-time header themes
@riverpod
Stream<List<FirebaseHeaderTheme>> firebaseHeaderThemesStream(Ref ref) {
  final repository = ref.watch(firebaseHeaderRepositoryProvider);
  return repository.watchHeaderThemes();
}

// Provider for a specific header theme by ID
@riverpod
Future<FirebaseHeaderTheme?> firebaseHeaderThemeById(
  Ref ref,
  String id,
) async {
  final repository = ref.watch(firebaseHeaderRepositoryProvider);
  return repository.getHeaderThemeById(id);
}
