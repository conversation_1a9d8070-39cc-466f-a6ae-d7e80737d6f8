// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_header_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$firebaseHeaderRepositoryHash() =>
    r'8cb78498218f8bd03c26734ec7a88de52e1769fd';

/// See also [firebaseHeaderRepository].
@ProviderFor(firebaseHeaderRepository)
final firebaseHeaderRepositoryProvider =
    AutoDisposeProvider<FirebaseHeaderRepository>.internal(
  firebaseHeaderRepository,
  name: r'firebaseHeaderRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseHeaderRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseHeaderRepositoryRef
    = AutoDisposeProviderRef<FirebaseHeaderRepository>;
String _$firebaseHeaderThemesHash() =>
    r'5619b68b142d946e456426a7086c8f08bacb37e5';

/// See also [firebaseHeaderThemes].
@ProviderFor(firebaseHeaderThemes)
final firebaseHeaderThemesProvider =
    AutoDisposeFutureProvider<List<FirebaseHeaderTheme>>.internal(
  firebaseHeaderThemes,
  name: r'firebaseHeaderThemesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseHeaderThemesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseHeaderThemesRef
    = AutoDisposeFutureProviderRef<List<FirebaseHeaderTheme>>;
String _$firebaseHeaderThemesStreamHash() =>
    r'2d584abf2d93df8b990932d0d00a06d1cb835483';

/// See also [firebaseHeaderThemesStream].
@ProviderFor(firebaseHeaderThemesStream)
final firebaseHeaderThemesStreamProvider =
    AutoDisposeStreamProvider<List<FirebaseHeaderTheme>>.internal(
  firebaseHeaderThemesStream,
  name: r'firebaseHeaderThemesStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseHeaderThemesStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseHeaderThemesStreamRef
    = AutoDisposeStreamProviderRef<List<FirebaseHeaderTheme>>;
String _$firebaseHeaderThemeByIdHash() =>
    r'98cbeb3aada0311aaa6e2bb09717e53d9a26f5b4';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [firebaseHeaderThemeById].
@ProviderFor(firebaseHeaderThemeById)
const firebaseHeaderThemeByIdProvider = FirebaseHeaderThemeByIdFamily();

/// See also [firebaseHeaderThemeById].
class FirebaseHeaderThemeByIdFamily
    extends Family<AsyncValue<FirebaseHeaderTheme?>> {
  /// See also [firebaseHeaderThemeById].
  const FirebaseHeaderThemeByIdFamily();

  /// See also [firebaseHeaderThemeById].
  FirebaseHeaderThemeByIdProvider call(
    String id,
  ) {
    return FirebaseHeaderThemeByIdProvider(
      id,
    );
  }

  @override
  FirebaseHeaderThemeByIdProvider getProviderOverride(
    covariant FirebaseHeaderThemeByIdProvider provider,
  ) {
    return call(
      provider.id,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'firebaseHeaderThemeByIdProvider';
}

/// See also [firebaseHeaderThemeById].
class FirebaseHeaderThemeByIdProvider
    extends AutoDisposeFutureProvider<FirebaseHeaderTheme?> {
  /// See also [firebaseHeaderThemeById].
  FirebaseHeaderThemeByIdProvider(
    String id,
  ) : this._internal(
          (ref) => firebaseHeaderThemeById(
            ref as FirebaseHeaderThemeByIdRef,
            id,
          ),
          from: firebaseHeaderThemeByIdProvider,
          name: r'firebaseHeaderThemeByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$firebaseHeaderThemeByIdHash,
          dependencies: FirebaseHeaderThemeByIdFamily._dependencies,
          allTransitiveDependencies:
              FirebaseHeaderThemeByIdFamily._allTransitiveDependencies,
          id: id,
        );

  FirebaseHeaderThemeByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<FirebaseHeaderTheme?> Function(FirebaseHeaderThemeByIdRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FirebaseHeaderThemeByIdProvider._internal(
        (ref) => create(ref as FirebaseHeaderThemeByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FirebaseHeaderTheme?> createElement() {
    return _FirebaseHeaderThemeByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FirebaseHeaderThemeByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FirebaseHeaderThemeByIdRef
    on AutoDisposeFutureProviderRef<FirebaseHeaderTheme?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _FirebaseHeaderThemeByIdProviderElement
    extends AutoDisposeFutureProviderElement<FirebaseHeaderTheme?>
    with FirebaseHeaderThemeByIdRef {
  _FirebaseHeaderThemeByIdProviderElement(super.provider);

  @override
  String get id => (origin as FirebaseHeaderThemeByIdProvider).id;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
