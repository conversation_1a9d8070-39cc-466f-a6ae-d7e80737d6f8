import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import '../models/firebase_header_theme.dart';

class FirebaseHeaderRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  static const String _collection = 'home_header_themes';

  // Get all header themes
  Future<List<FirebaseHeaderTheme>> getHeaderThemes() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => FirebaseHeaderTheme.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error fetching header themes: $e');
      return [];
    }
  }

  // Stream of header themes for real-time updates
  Stream<List<FirebaseHeaderTheme>> watchHeaderThemes() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => FirebaseHeaderTheme.fromFirestore(doc))
            .toList());
  }

  // Get header theme by ID
  Future<FirebaseHeaderTheme?> getHeaderThemeById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (!doc.exists) return null;
      return FirebaseHeaderTheme.fromFirestore(doc);
    } catch (e) {
      debugPrint('Error fetching header theme $id: $e');
      return null;
    }
  }

  // Upload image file to Firebase Storage
  Future<String> uploadHeaderImage(
    File imageFile,
    String themeId,
    String timePeriod,
  ) async {
    try {
      final fileName =
          '${themeId}_${timePeriod}_${DateTime.now().millisecondsSinceEpoch}.png';
      final ref = _storage.ref().child('home_headers/$fileName');
      final uploadTask = await ref.putFile(imageFile);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Error uploading header image: $e');
      rethrow;
    }
  }

  // Create new header theme
  Future<void> createHeaderTheme(FirebaseHeaderTheme theme) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(theme.id)
          .set(theme.toFirestore());
    } catch (e) {
      debugPrint('Error creating header theme: $e');
      rethrow;
    }
  }

  // Update header theme
  Future<void> updateHeaderTheme(FirebaseHeaderTheme theme) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(theme.id)
          .update(theme.copyWith(updatedAt: DateTime.now()).toFirestore());
    } catch (e) {
      debugPrint('Error updating header theme: $e');
      rethrow;
    }
  }

  // Delete header theme (soft delete by setting isActive to false)
  Future<void> deleteHeaderTheme(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'isActive': false,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      debugPrint('Error deleting header theme: $e');
      rethrow;
    }
  }

  // Update theme order
  Future<void> updateThemeOrder(String id, int newOrder) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'order': newOrder,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      debugPrint('Error updating theme order: $e');
      rethrow;
    }
  }

  // Delete image from Firebase Storage
  Future<void> deleteImage(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      debugPrint('Error deleting image: $e');
      // Don't rethrow as this is not critical
    }
  }
}
