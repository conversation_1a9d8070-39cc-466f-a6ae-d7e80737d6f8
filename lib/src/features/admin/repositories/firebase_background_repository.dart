import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import '../models/firebase_background_image.dart';

class FirebaseBackgroundRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  static const String _collection = 'background_images';

  // Get all background images
  Future<List<FirebaseBackgroundImage>> getBackgroundImages() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => FirebaseBackgroundImage.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error fetching background images: $e');
      return [];
    }
  }

  // Stream of background images for real-time updates
  Stream<List<FirebaseBackgroundImage>> watchBackgroundImages() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => FirebaseBackgroundImage.fromFirestore(doc))
            .toList());
  }

  // Get background image by ID
  Future<FirebaseBackgroundImage?> getBackgroundImageById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (!doc.exists) return null;
      return FirebaseBackgroundImage.fromFirestore(doc);
    } catch (e) {
      debugPrint('Error fetching background image $id: $e');
      return null;
    }
  }

  // Upload image file to Firebase Storage
  Future<String> uploadBackgroundImage(File imageFile, String imageId) async {
    try {
      final fileName =
          '${imageId}_${DateTime.now().millisecondsSinceEpoch}.png';
      final ref = _storage.ref().child('background_images/$fileName');
      final uploadTask = await ref.putFile(imageFile);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Error uploading background image: $e');
      rethrow;
    }
  }

  // Create new background image
  Future<void> createBackgroundImage(FirebaseBackgroundImage image) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(image.id)
          .set(image.toFirestore());
    } catch (e) {
      debugPrint('Error creating background image: $e');
      rethrow;
    }
  }

  // Update background image
  Future<void> updateBackgroundImage(FirebaseBackgroundImage image) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(image.id)
          .update(image.copyWith(updatedAt: DateTime.now()).toFirestore());
    } catch (e) {
      debugPrint('Error updating background image: $e');
      rethrow;
    }
  }

  // Delete background image (soft delete by setting isActive to false)
  Future<void> deleteBackgroundImage(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'isActive': false,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      debugPrint('Error deleting background image: $e');
      rethrow;
    }
  }

  // Update image order
  Future<void> updateImageOrder(String id, int newOrder) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'order': newOrder,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      debugPrint('Error updating image order: $e');
      rethrow;
    }
  }

  // Delete image from Firebase Storage
  Future<void> deleteImage(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      debugPrint('Error deleting image: $e');
      // Don't rethrow as this is not critical
    }
  }
}
