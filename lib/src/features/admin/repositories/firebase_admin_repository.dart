import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class FirebaseAdminRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'admin_settings';
  static const String _adminEmailDoc = 'admin_email';

  // Get admin email from Firestore
  Future<String?> getAdminEmail() async {
    try {
      final doc =
          await _firestore.collection(_collection).doc(_adminEmailDoc).get();
      if (!doc.exists) return null;
      return doc.data()?['email'] as String?;
    } catch (e) {
      debugPrint('Error fetching admin email: $e');
      return null;
    }
  }

  // Stream of admin email for real-time updates
  Stream<String?> watchAdminEmail() {
    return _firestore
        .collection(_collection)
        .doc(_adminEmailDoc)
        .snapshots()
        .map((doc) => doc.data()?['email'] as String?);
  }

  // Set admin email in Firestore
  Future<void> setAdminEmail(String email) async {
    try {
      await _firestore.collection(_collection).doc(_adminEmailDoc).set({
        'email': email,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error setting admin email: $e');
      rethrow;
    }
  }
}
