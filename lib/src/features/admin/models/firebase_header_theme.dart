import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseHeaderTheme {
  final String id;
  final String name;
  final String description;
  final Map<String, String> imageUrls; // time period -> Firebase Storage URL
  final int order;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FirebaseHeaderTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrls,
    required this.order,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FirebaseHeaderTheme.fromFirestore(
    DocumentSnapshot doc,
  ) {
    final data = doc.data() as Map<String, dynamic>;
    return FirebaseHeaderTheme(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      imageUrls: Map<String, String>.from(data['imageUrls'] ?? {}),
      order: data['order'] ?? 0,
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'imageUrls': imageUrls,
      'order': order,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  String getImageUrlForTimeOfDay() {
    final hour = DateTime.now().hour;
    String timePeriod;
    if (hour >= 5 && hour < 12) {
      timePeriod = 'morning';
    } else if (hour >= 12 && hour < 16) {
      timePeriod = 'noon';
    } else if (hour >= 16 && hour < 20) {
      timePeriod = 'evening';
    } else {
      timePeriod = 'night';
    }

    return imageUrls[timePeriod] ?? imageUrls['morning'] ?? '';
  }

  String getPreviewImageUrl() {
    return imageUrls['morning'] ?? imageUrls.values.first;
  }

  FirebaseHeaderTheme copyWith({
    String? id,
    String? name,
    String? description,
    Map<String, String>? imageUrls,
    int? order,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FirebaseHeaderTheme(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrls: imageUrls ?? this.imageUrls,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
