import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:mimi_app/src/features/video_player/providers/video_providers.dart';

import 'package:mimi_app/src/features/video_player/data/providers/video_category_provider.dart';
import 'package:mimi_app/src/features/video_player/data/models/video_category.dart';

class HomeVideoSection extends ConsumerStatefulWidget {
  const HomeVideoSection({super.key});

  @override
  ConsumerState<HomeVideoSection> createState() => _HomeVideoSectionState();
}

class _HomeVideoSectionState extends ConsumerState<HomeVideoSection> {
  int selectedTab = 0;

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(videoCategoriesProvider);

    return categoriesAsync.when(
      data: (categories) {
        if (categories.isEmpty) return const SizedBox.shrink();

        // Add "All" category at the beginning
        final allCategories = [
          const VideoCategory(id: 'all', name: 'All'),
          ...categories,
        ];

        final selectedCategory = allCategories[selectedTab];
        final isAll = selectedCategory.id == 'all';
        final videosAsync = isAll
            ? ref.watch(allVideosProvider)
            : ref.watch(videosProvider(selectedCategory.id));

        return Padding(
          padding: const EdgeInsets.only(
            left: 20,
            bottom: 20,
          ),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            padding: const EdgeInsets.only(left: 20, top: 20, bottom: 20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                bottomLeft: Radius.circular(32),
              ),
            ),
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Videos',
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: Icon(Icons.arrow_outward,
                          color: AppColors.textPrimary),
                      onPressed: () {
                        context.push(RouteNames.video);
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Text Tabs
                SizedBox(
                  height: 36,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: allCategories.length,
                    separatorBuilder: (_, __) => const SizedBox(width: 18),
                    itemBuilder: (context, idx) {
                      final isSelected = idx == selectedTab;
                      return GestureDetector(
                        onTap: () => setState(() => selectedTab = idx),
                        child: Text(
                          allCategories[idx].name,
                          style: TextStyle(
                            color: isSelected
                                ? AppColors.primary
                                : AppColors.textPrimary,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                            fontSize: 16,
                            letterSpacing: 0.2,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 18),
                // Videos Horizontal List
                SizedBox(
                  height: 160,
                  child: videosAsync.when(
                    data: (videos) {
                      final showVideos = videos.take(3).toList();
                      return ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.only(right: 20),
                        itemCount: showVideos.length + 1, // +1 for See All
                        itemBuilder: (context, idx) {
                          if (idx < showVideos.length) {
                            final video = showVideos[idx];
                            return _VideoCard(video: video);
                          } else {
                            // See All Card - Match video thumbnail dimensions
                            return GestureDetector(
                              onTap: () {
                                if (isAll) {
                                  context.push('/video');
                                } else {
                                  context.push(
                                      '/video/category/${selectedCategory.id}');
                                }
                              },
                              child: Container(
                                width: 100,
                                // Match video card width
                                margin: const EdgeInsets.only(right: 12),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 112,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.grey[900],
                                          borderRadius: BorderRadius.circular(
                                              12), // Match video thumbnail radius
                                        ),
                                        child: const Center(
                                          child: Text(
                                            'See All',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }
                        },
                      );
                    },
                    loading: () =>
                        const Center(child: CircularProgressIndicator()),
                    error: (e, _) => Center(
                        child: Text('Error: $e',
                            style: TextStyle(color: Colors.white))),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error loading categories: $error',
            style: const TextStyle(color: Colors.white)),
      ),
    );
  }
}

class _VideoCard extends StatelessWidget {
  final Video video;
  const _VideoCard({required this.video});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.pushNamed(
          RouteNames.videoPlayer,
          extra: video,
        );
      },
      child: Container(
        width: 200, // Slightly wider for landscape videos
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Video Thumbnail with 16:9 aspect ratio
            AspectRatio(
              aspectRatio: 16 / 9,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    // Thumbnail image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: video.thumbnailUrl.isNotEmpty
                          ? Image.network(
                              video.thumbnailUrl,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.grey[200],
                                ),
                                child: Icon(
                                  Icons.video_library,
                                  color: Colors.grey[400],
                                  size: 40,
                                ),
                              ),
                            )
                          : Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: Colors.grey[200],
                              ),
                              child: Icon(
                                Icons.video_library,
                                color: Colors.grey[400],
                                size: 40,
                              ),
                            ),
                    ),
                    // Play button overlay
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            // Video Title
            Text(
              video.title,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
