// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'header_image_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$headerImageThemesHash() => r'80d4cbda449e01b567025f94127391f6f70d9ede';

/// See also [headerImageThemes].
@ProviderFor(headerImageThemes)
final headerImageThemesProvider =
    AutoDisposeFutureProvider<List<HeaderImageTheme>>.internal(
  headerImageThemes,
  name: r'headerImageThemesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$headerImageThemesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HeaderImageThemesRef
    = AutoDisposeFutureProviderRef<List<HeaderImageTheme>>;
String _$currentTimePeriodHash() => r'671aad43cf6bbf5dc37b45ccf5722d988be2f0b5';

/// See also [currentTimePeriod].
@ProviderFor(currentTimePeriod)
final currentTimePeriodProvider = AutoDisposeProvider<String>.internal(
  currentTimePeriod,
  name: r'currentTimePeriodProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentTimePeriodHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentTimePeriodRef = AutoDisposeProviderRef<String>;
String _$headerImageNotifierHash() =>
    r'37ee1c19418740726a78ba288d29d2c48d1e4d23';

/// See also [HeaderImageNotifier].
@ProviderFor(HeaderImageNotifier)
final headerImageNotifierProvider =
    AutoDisposeAsyncNotifierProvider<HeaderImageNotifier, String>.internal(
  HeaderImageNotifier.new,
  name: r'headerImageNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$headerImageNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HeaderImageNotifier = AutoDisposeAsyncNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
