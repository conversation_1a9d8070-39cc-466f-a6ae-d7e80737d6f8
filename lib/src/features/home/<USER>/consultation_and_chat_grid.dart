import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/navigation/providers/navigation_provider.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/utils/url_launcher_utils.dart';
import 'package:go_router/go_router.dart';

class ConsultationAndChatGrid extends ConsumerWidget {
  const ConsultationAndChatGrid({super.key});

  static Color _containerColor(BuildContext context) =>
      Theme.of(context).colorScheme.primary; // Dark purple
  static TextStyle _buttonTextStyle(BuildContext context) => TextStyle(
        color: Theme.of(context).colorScheme.onPrimary,
        fontSize: AppTypography.bodyMedium,
        fontWeight: AppTypography.bold,
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        decoration: BoxDecoration(
          color: _containerColor(context),
          borderRadius: BorderRadius.circular(40),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 32),
              Text(
                'Consultation & Chat',
                style: _buttonTextStyle(context)
                    .copyWith(color: Colors.white, fontSize: 28),
              ),
              const SizedBox(height: 32),
              _ConsultationChatButton(
                label: 'Book One-on-One Consultation',
                icon: Icons.calendar_today_rounded,
                onTap: () async {
                  // Replace with your actual booking URL
                  const url = 'https://your-booking-url.com';
                  await UrlLauncherUtils.launchURL(context, url);
                },
              ),
              const SizedBox(height: 16),
              _ConsultationChatButton(
                label: 'Talk to MIMIAI',
                icon: Icons.chat_bubble_outline_rounded,
                onTap: () {
                  final navItems = ref.read(navigationItemsProvider);
                  final chatIndex = navItems
                      .indexWhere((item) => item.path == RouteNames.chat);
                  if (chatIndex != -1) {
                    ref
                        .read(selectedNavIndexProvider.notifier)
                        .setIndex(chatIndex);
                  }
                  context.goNamed(RouteNames.chat);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ConsultationChatButton extends StatelessWidget {
  final String label;
  final IconData icon;
  final VoidCallback onTap;

  const _ConsultationChatButton({
    required this.label,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.amber,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 8),
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.all(12),
                child: Icon(icon, color: Colors.white, size: 28),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Text(
                  label,
                  style: ConsultationAndChatGrid._buttonTextStyle(context),
                ),
              ),
              const Icon(Icons.arrow_forward_ios_rounded,
                  color: Colors.white, size: 20),
            ],
          ),
        ),
      ),
    );
  }
}
