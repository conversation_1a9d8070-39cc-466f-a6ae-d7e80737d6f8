import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/core/theme/providers/palette_provider.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/intentions/providers/intention_selection_provider.dart';
import 'package:mimi_app/src/features/journal/providers/providers.dart';

enum DotState { completed, current, upcoming }

class RoutinesStepperWidget extends ConsumerWidget {
  const RoutinesStepperWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch palette changes to ensure widget rebuilds when palette changes
    ref.watch(paletteNotifierProvider);

    final routinesAsync = ref.watch(selectedIntentionRoutinesProvider);
    final todayEntries = ref.watch(todayEntriesProvider);

    return routinesAsync.when(
      data: (routines) {
        if (routines.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                'Start Your Day',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontSize: AppTypography.headlineMedium,
                      fontWeight: AppTypography.bold,
                    ),
              ),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(vertical: 8),
              physics: const NeverScrollableScrollPhysics(),
              itemCount: routines.length,
              itemBuilder: (context, index) {
                final routine = routines[index];
                final isLast = index == routines.length - 1;

                // Find the current routine index
                final currentIndex =
                    _getCurrentRoutineIndex(routines, todayEntries);
                final dotState =
                    _getDotState(index, currentIndex, routine, todayEntries);

                return Stack(
                  children: [
                    if (!isLast)
                      Positioned(
                        left: 10,
                        top: 24,
                        bottom: 0,
                        child: Container(
                          width: 4,
                          color: Colors.grey.withValues(alpha: 0.2),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildTimelineDot(context, dotState),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildRoutineCard(
                              context,
                              routine,
                              isLast,
                              todayEntries,
                              ref,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  int _getCurrentRoutineIndex(
    List<CheckInRoutine> routines,
    AsyncValue<List<JournalEntry>> todayEntries,
  ) {
    return todayEntries.when(
      data: (entries) {
        // Find the first incomplete routine
        for (int i = 0; i < routines.length; i++) {
          final isCompleted = entries.any(
              (e) => e.routineId == routines[i].id && e.status == 'completed');
          if (!isCompleted) return i;
        }
        return routines.length - 1; // All completed, return last routine
      },
      loading: () => 0,
      error: (_, __) => 0,
    );
  }

  DotState _getDotState(
    int index,
    int currentIndex,
    CheckInRoutine routine,
    AsyncValue<List<JournalEntry>> todayEntries,
  ) {
    return todayEntries.when(
      data: (entries) {
        final isCompleted = entries
            .any((e) => e.routineId == routine.id && e.status == 'completed');

        if (isCompleted) return DotState.completed;
        if (index == currentIndex) return DotState.current;
        return DotState.upcoming;
      },
      loading: () => DotState.upcoming,
      error: (_, __) => DotState.upcoming,
    );
  }

  Widget _buildTimelineDot(BuildContext context, DotState state) {
    return SizedBox(
      width: 24,
      height: 24,
      child: Center(
        child: Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: switch (state) {
              DotState.completed =>
                AppColors.primary, // Primary color for completed
              DotState.current => AppColors.primary
                  .withValues(alpha: 0.8), // Primary color for current
              DotState.upcoming =>
                Colors.grey.withValues(alpha: 0.3), // Grey for upcoming
            },
            border: state == DotState.current
                ? Border.all(
                    color: AppColors.secondary.withValues(alpha: 0.3),
                    width: 2,
                  )
                : null,
          ),
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              border: state == DotState.current
                  ? Border.all(
                      color: AppColors.secondary.withValues(alpha: 0.3),
                      width: 3,
                    )
                  : null,
            ),
            child: Center(
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: switch (state) {
                    DotState.completed =>
                      AppColors.primary, // Primary for completed
                    DotState.current =>
                      AppColors.secondary, // Secondary for current
                    DotState.upcoming =>
                      Colors.grey.withValues(alpha: 0.3), // Grey for upcoming
                  },
                  border: state == DotState.current
                      ? Border.all(
                          color: AppColors.secondary.withValues(alpha: 0.3),
                          width: 2,
                        )
                      : null,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRoutineCard(
    BuildContext context,
    CheckInRoutine routine,
    bool isLast,
    AsyncValue<List<JournalEntry>> todayEntries,
    WidgetRef ref,
  ) {
    return todayEntries.when(
      data: (entries) {
        final isCompleted = entries
            .any((e) => e.routineId == routine.id && e.status == 'completed');

        return InkWell(
          onTap: isCompleted
              ? null
              : () => _startRoutine(context, routine, entries),
          child: Opacity(
            opacity: isCompleted ? 0.7 : 1.0,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).canvasColor,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? Colors.grey.withValues(alpha: 0.6)
                            : AppColors.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        isCompleted
                            ? Icons.check_circle
                            : _getTypeIcon(routine.type),
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            routine.name,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isCompleted
                                      ? Theme.of(context).colorScheme.onSurface
                                      : null,
                                ),
                          ),
                          if (isCompleted)
                            Text(
                              'Completed',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                        ],
                      ),
                    ),
                    _buildStatusIcon(routine, todayEntries)
                  ],
                ),
              ),
            ),
          ),
        );
      },
      loading: () => InkWell(
        onTap: () => _startRoutine(context, routine, []),
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).canvasColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getTypeIcon(routine.type),
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        routine.name,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),
                _buildStatusIcon(routine, todayEntries)
              ],
            ),
          ),
        ),
      ),
      error: (_, __) => InkWell(
        onTap: () => _startRoutine(context, routine, []),
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).canvasColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getTypeIcon(routine.type),
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        routine.name,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),
                _buildStatusIcon(routine, todayEntries)
              ],
            ),
          ),
        ),
      ),
    );
  }

  // ... rest of the existing methods remain the same ...
  // ... rest of the methods remain the same ...

  Widget _buildStatusIcon(
    CheckInRoutine routine,
    AsyncValue<List<JournalEntry>> todayEntries,
  ) {
    return todayEntries.when(
      data: (entries) {
        final isCompleted = entries
            .any((e) => e.routineId == routine.id && e.status == 'completed');
        if (isCompleted) {
          return Builder(
            builder: (context) => Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
            ),
          );
        } else {
          return Icon(Icons.arrow_forward_ios_rounded, size: 20);
        }

        // return const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  IconData _getTypeIcon(String type) {
    return Icons.check_circle_outline;
  }

  void _startRoutine(BuildContext context, CheckInRoutine routine,
      [List<JournalEntry>? entries]) {
    // Double-check that the routine is not completed before starting
    if (entries != null) {
      final isCompleted = entries
          .any((e) => e.routineId == routine.id && e.status == 'completed');
      if (isCompleted) {
        // Routine is already completed, don't allow starting it again
        return;
      }
    }

    context.push('/checkinflow/${routine.id}');
    // Navigator.push(
    //   context,
    //   MaterialPageRoute(
    //     builder: (context) => CheckinFlowScreen(routineId: routine.id),
    //   ),
    // );
  }
}
