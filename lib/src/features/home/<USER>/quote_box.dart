import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';

import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';

import 'package:mimi_app/src/features/daily_quotes/quote_model.dart';
import 'package:mimi_app/src/features/daily_quotes/quotes_provider.dart';

class QuoteBox extends ConsumerWidget {
  final Quote quote;
  final VoidCallback? onTap;
  const QuoteBox({super.key, required this.quote, this.onTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final backgroundId = ref.watch(quoteBackgroundIdProvider);
    final backgroundsAsync = ref.watch(quoteBackgroundsProvider);

    return backgroundsAsync.when(
      loading: () => Container(
        width: double.infinity,
        height: 220,
        margin: const EdgeInsets.symmetric(
            horizontal: AppSizing.spaceM, vertical: AppSizing.spaceL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizing.radiusXL),
          color: Colors.grey[300],
        ),
        child: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Container(
        width: double.infinity,
        height: 220,
        margin: const EdgeInsets.symmetric(
            horizontal: AppSizing.spaceM, vertical: AppSizing.spaceL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizing.radiusXL),
          color: Colors.grey[300],
        ),
        child: const Center(child: Text('Error loading background')),
      ),
      data: (backgrounds) {
        final background = backgrounds.firstWhere(
          (bg) => bg['id'] == backgroundId,
          orElse: () => backgrounds.first,
        );

        return GestureDetector(
            onTap: onTap ??
                () {
                  context.push(RouteNames.quote);
                },
            child: Container(
              width: double.infinity,
              height: 220, // Fixed height for consistent look
              margin: const EdgeInsets.symmetric(
                  horizontal: AppSizing.spaceM, vertical: AppSizing.spaceL),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSizing.radiusXL),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppSizing.radiusXL),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Background image
                    background['assetPath']!.startsWith('http')
                        ? Image.network(
                            background['assetPath']!,
                            fit: BoxFit.cover,
                          )
                        : Image.asset(
                            background['assetPath']!,
                            fit: BoxFit.cover,
                          ),
                    // Dark overlay
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.3),
                            Colors.black.withOpacity(0.4),
                          ],
                        ),
                      ),
                    ),
                    // Content
                    Padding(
                      padding: const EdgeInsets.all(AppSizing.spaceXL),
                      child: Stack(
                        children: [
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Icon(
                                Icons.format_quote,
                                size: AppSizing.iconL,
                                color: Colors.white70,
                              ),
                              const SizedBox(height: AppSizing.spaceM),
                              Text(
                                quote.text,
                                style: TextStyle(
                                  fontSize: AppTypography.bodyMedium,
                                  fontWeight: AppTypography.semiBold,
                                  color: Colors.white,
                                  height: AppTypography.heightMedium,
                                ),
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (quote.author.isNotEmpty) ...[
                                const SizedBox(height: AppSizing.spaceM),
                                Text(
                                  '- ${quote.author}',
                                  style: TextStyle(
                                    fontSize: AppTypography.titleSmall,
                                    fontStyle: FontStyle.italic,
                                    color: Colors.white70,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                borderRadius:
                                    BorderRadius.circular(AppSizing.radiusL),
                              ),
                              child: const Icon(
                                Icons.open_in_full,
                                size: AppSizing.iconL,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ));
      },
    );
  }
}
