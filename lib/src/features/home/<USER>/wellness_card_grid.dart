import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/navigation/providers/navigation_provider.dart';

class WellnessCardGrid1 extends ConsumerWidget {
  const WellnessCardGrid1({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // First Row - Two InfoCards
            SizedBox(
              height: 120, // Adjust this height based on your needs
              child: Row(
                children: [
                  Flexible(
                    flex: 1,
                    child: InfoCard(
                      title: 'Focus Timer',
                      subtitle: 'Stay productive',
                      icon: Icons.timer_outlined,
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      iconColor: Colors.white,
                      textColor: Colors.white,
                      onTap: () {
                        context.pushNamed(RouteNames.timer);
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    flex: 1,
                    child: InfoCard(
                      title: 'Quotes',
                      subtitle: 'Audio Sessions',
                      icon: Icons.self_improvement_rounded,
                      backgroundColor: AppColors.primary,
                      iconColor: AppColors.accent4,
                      textColor: AppColors.accent4,
                      onTap: () {
                        context.push('/quote');
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    flex: 1,
                    child: InfoCard(
                      title: 'Breathwork',
                      subtitle: 'Calm and Focused',
                      icon: Icons.air_rounded,
                      backgroundColor: AppColors.primary,
                      iconColor: AppColors.accent4,
                      textColor: AppColors.accent4,
                      onTap: () {
                        context.push(RouteNames.breathwork);
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Middle Row - Custom Full Width Card
            SizedBox(
              height: 120, // Adjust this height based on your needs
              child: CustomWideCard(
                title: 'Talk to MimiAI',
                subtitle:
                    'Something on your mind? Have a conversation with MimiAI',
                icon: Icons.trending_up_rounded,
                backgroundColor: AppColors.accent4,
                iconColor: AppColors.primary,
                textColor: AppColors.primary,
                onTap: () {
                  final navItems = ref.read(navigationItemsProvider);
                  final chatIndex = navItems
                      .indexWhere((item) => item.path == RouteNames.chat);
                  if (chatIndex != -1) {
                    ref
                        .read(selectedNavIndexProvider.notifier)
                        .setIndex(chatIndex);
                  }
                  context.goNamed(RouteNames.chat);
                },
              ),
            ),
            const SizedBox(height: 16),
            // Last Row - Two InfoCards
            SizedBox(
              height: 120, // Adjust this height based on your needs
              child: Row(
                children: [
                  Flexible(
                    flex: 1,
                    child: InfoCard(
                      title: 'Affirmation',
                      subtitle: 'Audio Sessions',
                      icon: Icons.favorite_rounded,
                      backgroundColor: AppColors.primary,
                      iconColor: AppColors.accent4,
                      textColor: AppColors.accent4,
                      onTap: () {
                        context.push('/category/affirmations');
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    flex: 1,
                    child: InfoCard(
                      title: 'Mediation',
                      subtitle: 'Calm and Focused',
                      icon: Icons.air_rounded,
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      iconColor: Colors.white,
                      textColor: Colors.white,
                      onTap: () {
                        context.push('/category/meditation');
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    flex: 1,
                    child: InfoCard(
                      title: 'Breathwork',
                      subtitle: 'Calm and Focused',
                      icon: Icons.air_rounded,
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      iconColor: Theme.of(context).colorScheme.secondary,
                      textColor: Theme.of(context).colorScheme.secondary,
                      onTap: () {
                        context.push(RouteNames.breathwork);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomWideCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final Color textColor;
  final VoidCallback onTap;

  const CustomWideCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.textColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: backgroundColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: backgroundColor.withOpacity(0.5),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 36,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: textColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class InfoCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final Color textColor;
  final VoidCallback onTap;

  const InfoCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.textColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: backgroundColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: backgroundColor.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 32,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 4),
          ],
        ),
      ),
    );
  }
}
