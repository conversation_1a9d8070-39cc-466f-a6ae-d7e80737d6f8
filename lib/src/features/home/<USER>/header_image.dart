class HeaderImageTheme {
  final String id;
  final String name;
  final String description;
  final Map<String, String> assets;

  const HeaderImageTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.assets,
  });

  String getAssetForTimeOfDay() {
    final hour = DateTime.now().hour;
    if (hour >= 4 && hour < 12) {
      return assets['morning']!;
    } else if (hour >= 12 && hour < 16) {
      return assets['noon']!;
    } else if (hour >= 16 && hour < 20) {
      return assets['evening']!;
    } else {
      return assets['night']!;
    }
  }

  String getPreviewAsset() {
    return assets['morning']!; // Use morning as preview
  }
}

const List<HeaderImageTheme> defaultHeaderThemes = [
  HeaderImageTheme(
    id: 'mountain',
    name: 'Mountain',
    description: 'Peaceful mountain landscapes',
    assets: {
      'morning': 'assets/images/headers/mountain_morning.png',
      'noon': 'assets/images/headers/mountain_noon.png',
      'evening': 'assets/images/headers/mountain_evening.png',
      'night': 'assets/images/headers/mountain_night.png',
    },
  ),
  HeaderImageTheme(
    id: 'sea',
    name: 'Sea',
    description: 'Calming ocean views',
    assets: {
      'morning': 'assets/images/headers/sea_morning.png',
      'noon': 'assets/images/headers/sea_noon.png',
      'evening': 'assets/images/headers/sea_evening.png',
      'night': 'assets/images/headers/sea_night.png',
    },
  ),
  HeaderImageTheme(
    id: 'city',
    name: 'City',
    description: 'Urban skyline scenes',
    assets: {
      'morning': 'assets/images/headers/city_morning.png',
      'noon': 'assets/images/headers/city_noon.png',
      'evening': 'assets/images/headers/city_evening.png',
      'night': 'assets/images/headers/city_night.png',
    },
  ),
  HeaderImageTheme(
    id: 'forest',
    name: 'Forest',
    description: 'Serene forest environments',
    assets: {
      'morning': 'assets/images/headers/forest_morning.png',
      'noon': 'assets/images/headers/forest_noon.png',
      'evening': 'assets/images/headers/forest_evening.png',
      'night': 'assets/images/headers/forest_night.png',
    },
  ),
];
