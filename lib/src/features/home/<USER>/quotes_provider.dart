import 'dart:math';

import 'package:mimi_app/src/features/home/<USER>/quotes_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'quotes_provider.g.dart';

@riverpod
class DailyQuoteController extends _$DailyQuoteController {
  final List<DataQuote> _quotes = [
    const DataQuote(
        text:
            "The only way to do great work is to love what you do.The only way to do great work is to love what you do.The only way to do great work is to love what you do.",
        author: "<PERSON>"),
    const <PERSON>Quote(
        text:
            "The future belongs to those who believe in the beauty of their dreams.",
        author: "<PERSON>"),
    const DataQuote(
        text:
            "Success is not final, failure is not fatal: it is the courage to continue that counts.",
        author: "<PERSON>"),
    const DataQuote(
        text: "Believe you can and you're halfway there.",
        author: "<PERSON>"),
    const DataQuote(
        text: "Everything you've ever wanted is on the other side of fear.",
        author: "<PERSON>"),
  ];

  @override
  FutureOr<DataQuote> build() {
    // Simply return initial random quote
    return _getRandomQuote();
  }

  DataQuote _getRandomQuote() {
    final random = Random();
    DataQuote newQuote;

    if (!state.hasValue) {
      return _quotes[random.nextInt(_quotes.length)];
    }

    do {
      newQuote = _quotes[random.nextInt(_quotes.length)];
    } while (state.hasValue && state.value == newQuote && _quotes.length > 1);

    return newQuote;
  }

  void shuffleQuote() {
    state = AsyncData(_getRandomQuote());
  }
}
