import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/features/database/providers/database_provider.dart';
import 'package:mimi_app/src/features/intentions/providers/intention_selection_provider.dart';
import 'package:mimi_app/src/features/home/<USER>/set_intention_card.dart';

class HomeIntentionSection extends ConsumerWidget {
  const HomeIntentionSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allIntentionsAsync = ref.watch(allIntentionsProvider);
    final selectedIntentionAsync = ref.watch(selectedIntentionProvider);

    return allIntentionsAsync.when(
      data: (allIntentions) {
        if (allIntentions.isEmpty) {
          // No intentions created yet
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Card(
              child: SetIntentionCard(
                title: 'Set Your Intention',
                subtitle: 'Start your journey by setting an intention.',
                actionText: 'Set Intention',
                onTap: () {
                  // Navigate to the new intentions list screen to add an intention
                  context.pushNamed(RouteNames.intentionsList);
                },
              ),
            ),
          );
        }

        // Intentions exist, show selected intention or prompt to select
        return selectedIntentionAsync.when(
          data: (selectedIntention) {
            if (selectedIntention == null) {
              // Intentions exist but none selected
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: Card(
                  child: SetIntentionCard(
                    title: 'Select Your Intention',
                    subtitle: 'Choose which intention to focus on today.',
                    actionText: 'Select Intention',
                    onTap: () {
                      context.pushNamed(RouteNames.intentionsList);
                    },
                  ),
                ),
              );
            }

            // Selected intention exists
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Card(
                child: SetIntentionCard(
                  title: 'My Intention',
                  subtitle: selectedIntention.name,
                  actionText: 'Change Intention',
                  onTap: () {
                    context.pushNamed(RouteNames.intentionsList);
                  },
                ),
              ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Error: $error')),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => Center(
        child: Text('Error: $error'),
      ),
    );
  }
}
