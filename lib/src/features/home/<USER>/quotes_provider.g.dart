// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quotes_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dailyQuoteControllerHash() =>
    r'afd6ac4b0088c374f9c614aa05132ca872fbdf0a';

/// See also [DailyQuoteController].
@ProviderFor(DailyQuoteController)
final dailyQuoteControllerProvider =
    AutoDisposeAsyncNotifierProvider<DailyQuoteController, DataQuote>.internal(
  DailyQuoteController.new,
  name: r'dailyQuoteControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dailyQuoteControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DailyQuoteController = AutoDisposeAsyncNotifier<DataQuote>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
