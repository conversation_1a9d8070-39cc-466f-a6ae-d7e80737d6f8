import 'package:flutter/material.dart';

class CustomButtonRow extends StatelessWidget {
  final List<CustomButtonData> buttons;
  final double borderRadius;
  final Color borderColor;
  final Color backgroundColor;
  final Color iconColor;
  final TextStyle? textStyle;

  const CustomButtonRow({
    super.key,
    required this.buttons,
    this.borderRadius = 20.0,
    this.borderColor = const Color(0xFFDFBFFF),
    this.backgroundColor = const Color(0xFF181A2A),
    this.iconColor = Colors.white,
    this.textStyle,
  }) : assert(buttons.length == 3, 'Must provide exactly 3 buttons');

  @override
  Widget build(BuildContext context) {
    return Row(
      children: buttons.map((button) {
        return Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _GlowingButton(
                icon: button.icon,
                borderRadius: borderRadius,
                borderColor: borderColor,
                backgroundColor: backgroundColor,
                iconColor: iconColor,
              ),
              const SizedBox(height: 12),
              Text(
                button.label,
                textAlign: TextAlign.center,
                style: textStyle ??
                    Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

class CustomButtonData {
  final IconData icon;
  final String label;
  final VoidCallback? onTap; // For future use

  CustomButtonData({
    required this.icon,
    required this.label,
    this.onTap,
  });
}

class _GlowingButton extends StatelessWidget {
  final IconData icon;
  final double borderRadius;
  final Color borderColor;
  final Color backgroundColor;
  final Color iconColor;

  const _GlowingButton({
    required this.icon,
    required this.borderRadius,
    required this.borderColor,
    required this.backgroundColor,
    required this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(color: borderColor, width: 2),
        boxShadow: [
          BoxShadow(
            color: borderColor.withOpacity(0.5),
            blurRadius: 12,
            spreadRadius: 1,
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 8),
      child: Icon(icon, size: 38, color: iconColor),
    );
  }
}
