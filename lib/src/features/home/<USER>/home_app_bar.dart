import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';

import 'package:mimi_app/src/features/user/providers/user_provider.dart';

class HomeAppBar extends ConsumerWidget {
  const HomeAppBar({super.key});

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 4) {
      return 'Good Night';
    } else if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else if (hour < 21) {
      return 'Good Evening';
    } else {
      return 'Good Night';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userNotifierProvider);
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppSizing.spaceXS),
      child: userAsync.when(
        data: (user) => Column(
          children: [
            Row(
              children: [
                // UserCircularAvatar(
                //   photoUrl: user?.auth.photoUrl,
                //   firstName: user?.firstName ?? '',
                //   lastName: user?.lastName ?? '',
                //   onTap: () {
                //     // Update the navigation index for profile
                //     final navItems = ref.read(navigationItemsProvider);
                //     final profileIndex = navItems
                //         .indexWhere((item) => item.path == RouteNames.profile);
                //     if (profileIndex != -1) {
                //       ref
                //           .read(selectedNavIndexProvider.notifier)
                //           .setIndex(profileIndex);
                //     }
                //     context.go(RouteNames.profile);
                //   },
                // ),
                SizedBox(width: AppSizing.spaceM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_getGreeting()}, ${user?.firstName} ',
                        style: theme.textTheme.headlineLarge
                            ?.copyWith(fontWeight: AppTypography.semiBold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // SizedBox(height: AppSizing.spaceL),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
              child: Text(
                AppStrings.greeting,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                softWrap: true,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: AppTypography.regular,
                ),
              ),
            ),
          ],
        ),
        loading: () => const SizedBox(
          height: 56,
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        error: (error, stack) => const SizedBox(height: 56),
      ),
    );
  }
}
