import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import '../providers/header_image_provider.dart';
import '../widgets/header_image_selector.dart';

class HomeHeader extends ConsumerStatefulWidget {
  const HomeHeader({super.key});

  @override
  ConsumerState<HomeHeader> createState() => _HomeHeaderState();
}

class _HomeHeaderState extends ConsumerState<HomeHeader> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // Set up a timer to refresh every minute to catch time period changes
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      // Force a rebuild to update the time period
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String _getTimeOfDayAsset(String themeId, String timePeriod, WidgetRef ref) {
    final themesAsync = ref.watch(headerImageThemesProvider);
    return themesAsync.when(
      data: (themes) {
        final currentTheme = themes.firstWhere(
          (theme) => theme.id == themeId,
          orElse: () => themes.first,
        );
        // Use the time period to get the correct asset
        return currentTheme.assets[timePeriod] ??
            currentTheme.getAssetForTimeOfDay();
      },
      loading: () =>
          'assets/images/headers/mountain_morning.png', // Default fallback
      error: (_, __) =>
          'assets/images/headers/mountain_morning.png', // Default fallback
    );
  }

  Color _getTimeOfDayColor(BuildContext context) {
    final hour = DateTime.now().hour;
    if (hour >= 4 && hour < 12) {
      return AppColors.textPrimary;
    } else if (hour >= 12 && hour < 16) {
      return AppColors.textPrimary;
    } else if (hour >= 16 && hour < 20) {
      return AppColors.textPrimary;
    } else {
      return AppColors.textPrimary;
    }
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour >= 4 && hour < 12) {
      return 'Good Morning, ';
    } else if (hour >= 12 && hour < 16) {
      return 'Good Afternoon, ';
    } else if (hour >= 16 && hour < 20) {
      return 'Good Evening, ';
    } else {
      return 'Good Night, ';
    }
  }

  void _showThemePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: const ThemePickerBottomSheet(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final headerThemeAsync = ref.watch(headerImageNotifierProvider);
    final timePeriod = ref.watch(currentTimePeriodProvider);
    final greeting = _getGreeting();
    final userAsync = ref.watch(userNotifierProvider);

    return headerThemeAsync.when(
      data: (themeId) {
        final asset = _getTimeOfDayAsset(themeId, timePeriod, ref);
        return userAsync.when(
          data: (user) {
            final firstName = user?.firstName ?? '';

            return Stack(
              children: [
                Container(
                  height: 350,
                  width: double.infinity,
                  margin: EdgeInsets.zero,
                  padding: EdgeInsets.zero,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: asset.startsWith('http')
                          ? NetworkImage(asset) as ImageProvider
                          : AssetImage(asset),
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                ),
                // Gradient overlay for seamless blending
                Container(
                  height: 350,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.transparent,
                        AppColors.gradientStart.withValues(alpha: 0.3),
                        AppColors.gradientStart.withValues(alpha: 0.7),
                        AppColors.gradientStart,
                      ],
                      stops: [0.0, 0.4, 0.8, 0.9, 1.0],
                    ),
                  ),
                ),
                Positioned(
                  top: 310,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 2, horizontal: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          greeting,
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(
                                color: _getTimeOfDayColor(context),
                              ),
                        ),
                        Text(
                          firstName,
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                color: _getTimeOfDayColor(context),
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Image settings icon in top right corner
                Positioned(
                  top: 50,
                  right: 20,
                  child: IconButton(
                    onPressed: () => _showThemePicker(context),
                    icon: SvgPicture.asset(
                      'assets/icons/image.svg',
                      width: 24,
                      height: 24,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.black.withValues(alpha: 0.3),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                ),
              ],
            );
          },
          loading: () => const SizedBox(
            height: 120,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stack) => const SizedBox(height: 120),
        );
      },
      loading: () => const SizedBox(
        height: 120,
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => const SizedBox(height: 120),
    );
  }
}
