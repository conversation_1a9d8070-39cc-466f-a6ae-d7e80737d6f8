import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/core/theme/providers/palette_provider.dart';
import 'package:mimi_app/src/features/daily_quotes/quotes_provider.dart';
import 'package:mimi_app/src/features/discover/widgets/discover_button_grid.dart';
import 'package:mimi_app/src/features/home/<USER>/home_header.dart';
import 'package:mimi_app/src/features/home/<USER>/home_intention_section.dart';
import 'package:mimi_app/src/features/home/<USER>/quote_box.dart';
import 'package:mimi_app/src/features/home/<USER>/routine_stepper.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch palette changes to ensure widget rebuilds when palette changes
    ref.watch(paletteNotifierProvider);

    final quotesAsync = ref.watch(quotesProvider);
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
              colors: [
                AppColors.gradientStart,
                AppColors.gradientStart,
                AppColors.gradientEnd,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomRight,
              stops: [0.0, 0.5, 1.0]),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HomeHeader(),
              HomeIntentionSection(),
              // Stack for header and intention section only, with fixed height
              // SizedBox(
              //   height:
              //       350 + 180, // header height + space for intention section
              //   child: Stack(
              //     clipBehavior: Clip.none,
              //     children: [
              //       const HomeHeader(),
              //       Padding(
              //         padding: const EdgeInsets.symmetric(
              //           horizontal: 16.0,
              //         ),
              //         child: const HomeIntentionSection(),
              //       ),
              //     ],
              //   ),
              // ),
              // Reduced space after stack since intention section is now contained within
              // const SizedBox(height: 16),
              // 2. Routines Stepper
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: RoutinesStepperWidget(),
              ),
              const DiscoverHomeButtonGrid(),
              quotesAsync.when(
                data: (quotes) {
                  if (quotes.isEmpty) return const SizedBox.shrink();
                  final quote = quotes[ref.watch(currentQuoteIndexProvider)];
                  return QuoteBox(
                    quote: quote,
                    onTap: () {
                      // Set the current quote index and navigate
                      final index = quotes.indexOf(quote);
                      ref.read(currentQuoteIndexProvider.notifier).state =
                          index;
                      context.pushNamed(RouteNames.quote);
                    },
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (e, _) => const SizedBox.shrink(),
              ),
              const SizedBox(height: 60), // Bottom spacing for content
            ],
          ),
        ),
      ),
    );
  }
}

/*!SECTION
Bottom Nav Bar

1. Today
2. Chat
3. Tools
4. Stats
5. Profile


Meditation
Breathwork
Affirmations
Quotes
Focus Timer
Talk to MimiAI
Videos







*/
