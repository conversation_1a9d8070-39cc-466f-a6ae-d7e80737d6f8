// // lib/features/audio_player/presentation/mixins/audio_focus_handler_mixin.dart
// import 'package:flutter/widgets.dart';
// import 'package:mimi_app/src/features/audio_player/service/audio_session_service.dart';

// import 'package:flutter/widgets.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart'; // Add this import
// import 'package:mimi_app/src/features/audio_player/service/audio_session_service.dart';

// mixin AudioFocusHandlerMixin<T extends ConsumerStatefulWidget>
//     on ConsumerState<T> {
//   // Change to ConsumerState
//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     _handleAudioFocus();
//   }

//   @override
//   void dispose() {
//     _releaseAudioFocus();
//     super.dispose();
//   }

//   void _handleAudioFocus() {
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       ref.read(audioSessionServiceProvider.notifier).activate();
//     });
//   }

//   void _releaseAudioFocus() {
//     ref.read(audioSessionServiceProvider.notifier).deactivate();
//   }
// }
