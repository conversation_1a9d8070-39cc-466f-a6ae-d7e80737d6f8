import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/audio_player/presentation/widgets/shared_track_list_item.dart';

class HorizontalTrackSection extends ConsumerWidget {
  final String title;
  final String categoryId;
  final VoidCallback? onViewAllPressed;
  final int tracksPerColumn;

  const HorizontalTrackSection({
    super.key,
    required this.title,
    required this.categoryId,
    this.onViewAllPressed,
    this.tracksPerColumn = 3,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoryTracksAsync = ref.watch(categoryTracksProvider(categoryId));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              TextButton(
                onPressed: onViewAllPressed,
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Row(
                  children: [
                    Text(
                      'All',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      color: AppColors.primary,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 330, // This height will contain 3 tracks with some spacing
          child: categoryTracksAsync.when(
            data: (tracks) {
              if (tracks.isEmpty) {
                return const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Center(
                    child: Text('No tracks available'),
                  ),
                );
              }

              // We'll show tracks in groups of 3 (columns)
              final columns = _createTrackColumns(tracks, tracksPerColumn);

              return ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(left: 4.0, right: 4.0),
                itemCount: columns.length,
                itemBuilder: (context, index) {
                  return TrackColumn(
                    tracks: columns[index],
                    isFirst: index == 0,
                  );
                },
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stack) => Center(
              child: Text('Error: $error'),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to group tracks into columns of [tracksPerColumn]
  List<List<AudioTrack>> _createTrackColumns(
      List<AudioTrack> tracks, int tracksPerColumn) {
    final result = <List<AudioTrack>>[];

    for (var i = 0; i < tracks.length; i += tracksPerColumn) {
      final end = (i + tracksPerColumn <= tracks.length)
          ? i + tracksPerColumn
          : tracks.length;
      result.add(tracks.sublist(i, end));

      // If we don't have enough tracks to fill the column, pad with null
      if (end - i < tracksPerColumn) {
        // We don't need to pad with nulls as we'll handle partial columns differently
      }
    }

    // Ensure we have at least 2 columns
    if (result.length < 2 && tracks.isNotEmpty) {
      // If we only have 1 column, duplicate the first one
      result.add(tracks.sublist(0,
          tracks.length > tracksPerColumn ? tracksPerColumn : tracks.length));
    }

    return result;
  }
}

class TrackColumn extends StatelessWidget {
  final List<AudioTrack> tracks;
  final bool isFirst;

  const TrackColumn({
    super.key,
    required this.tracks,
    this.isFirst = false,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate width based on whether this is the first column or not
    // First column takes ~90% of screen width, others are smaller
    final screenWidth = MediaQuery.of(context).size.width;
    final width = screenWidth * 0.85; // All columns have the same width

    return Container(
      width: width,
      margin: EdgeInsets.only(right: 16.0),
      child: Column(
        children: tracks.map((track) => TrackListItem(track: track)).toList(),
      ),
    );
  }
}

class TrackListItem extends StatelessWidget {
  final AudioTrack track;

  const TrackListItem({
    super.key,
    required this.track,
  });

  @override
  Widget build(BuildContext context) {
    return SharedTrackListItem(
      track: track,
      isSelectable: false,
    );
  }
}
