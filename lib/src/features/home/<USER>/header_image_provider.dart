import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/header_image.dart';
import '../../admin/providers/firebase_header_provider.dart';

part 'header_image_provider.g.dart';

@riverpod
class HeaderImageNotifier extends _$HeaderImageNotifier {
  static const String _headerImageKey = 'selected_header_image_theme';

  @override
  Future<String> build() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_headerImageKey) ??
        'mountain'; // Default to mountain
  }

  Future<void> setHeaderTheme(String themeId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_headerImageKey, themeId);
      return themeId;
    });
  }

  String getCurrentThemeId() {
    return state.valueOrNull ?? 'mountain';
  }

  HeaderImageTheme getCurrentTheme() {
    final themeId = getCurrentThemeId();
    return defaultHeaderThemes.firstWhere(
      (theme) => theme.id == themeId,
      orElse: () => defaultHeaderThemes.first,
    );
  }
}

@riverpod
Future<List<HeaderImageTheme>> headerImageThemes(Ref ref) async {
  try {
    // Try to get Firebase themes first
    final firebaseThemes = await ref.watch(firebaseHeaderThemesProvider.future);

    if (firebaseThemes.isNotEmpty) {
      // Convert Firebase themes to local HeaderImageTheme format
      return firebaseThemes.map((firebaseTheme) {
        return HeaderImageTheme(
          id: firebaseTheme.id,
          name: firebaseTheme.name,
          description: firebaseTheme.description,
          assets: firebaseTheme.imageUrls,
        );
      }).toList();
    }
  } catch (e) {
    // If Firebase fails, fall back to local assets
    // Using debugPrint instead of print for production
  }

  // Fallback to local assets
  return defaultHeaderThemes;
}

// Simple provider that returns current time period - will be refreshed by periodic timer
@riverpod
String currentTimePeriod(Ref ref) {
  final hour = DateTime.now().hour;
  if (hour >= 4 && hour < 12) {
    return 'morning';
  } else if (hour >= 12 && hour < 16) {
    return 'noon';
  } else if (hour >= 16 && hour < 20) {
    return 'evening';
  } else {
    return 'night';
  }
}
