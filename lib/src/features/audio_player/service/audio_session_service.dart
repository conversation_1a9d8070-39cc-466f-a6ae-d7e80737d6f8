// lib/features/audio_player/services/audio_session_service.dart
import 'package:audio_session/audio_session.dart';
// import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'audio_session_service.g.dart';

@riverpod
class AudioSessionService extends _$AudioSessionService {
  AudioSession? _session;

  @override
  Future<void> build() async {
    await _initializeSession();
  }

  Future<void> _initializeSession() async {
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.playback,
      avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
      avAudioSessionMode: AVAudioSessionMode.spokenAudio,
      androidAudioAttributes: AndroidAudioAttributes(
        contentType: AndroidAudioContentType.music,
        usage: AndroidAudioUsage.media,
      ),
    ));

    await session.setActive(true);
  }

  // Future<void> _initializeSession() async {
  //   _session = await AudioSession.instance;
  //   await _session?.configure(const AudioSessionConfiguration(
  //     avAudioSessionCategory: AVAudioSessionCategory.playback,
  //     avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
  //     avAudioSessionMode: AVAudioSessionMode.defaultMode,
  //     avAudioSessionRouteSharingPolicy:
  //         AVAudioSessionRouteSharingPolicy.defaultPolicy,
  //     avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
  //     androidAudioAttributes: AndroidAudioAttributes(
  //       contentType: AndroidAudioContentType.music,
  //       flags: AndroidAudioFlags.none,
  //       usage: AndroidAudioUsage.media,
  //     ),
  //     androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
  //     androidWillPauseWhenDucked: true,
  //   ));

  //   // Handle interruptions
  //   _session?.interruptionEventStream.listen(_handleInterruption);
  //   //  _session?.becomingNoisyEventStream.listen(_handleBecomingNoisy);
  // }

  // Future<void> _handleInterruption(AudioInterruptionEvent event) async {
  //   if (event.begin) {
  //     switch (event.type) {
  //       case AudioInterruptionType.duck:
  //         // Lower the volume temporarily
  //         await ref.read(audioPlayerControllerProvider.notifier).setVolume(0.5);
  //         break;
  //       case AudioInterruptionType.pause:
  //       case AudioInterruptionType.unknown:
  //         // Pause playback
  //         await ref.read(audioPlayerControllerProvider.notifier).pause();
  //         break;
  //     }
  //   } else {
  //     switch (event.type) {
  //       case AudioInterruptionType.duck:
  //         // Restore the volume
  //         await ref.read(audioPlayerControllerProvider.notifier).setVolume(1.0);
  //         break;
  //       case AudioInterruptionType.pause:
  //         // We can optionally resume playback here
  //         break;
  //       case AudioInterruptionType.unknown:
  //         // Handle unknown interruption end
  //         break;
  //     }
  //   }
  // }

  // Future<void> _handleBecomingNoisy() async {
  //   // Pause playback when headphones are unplugged, etc.
  //   await ref.read(audioPlayerControllerProvider.notifier).pause();
  // }

  Future<void> activate() async {
    await _session?.setActive(true);
  }

  Future<void> deactivate() async {
    await _session?.setActive(false);
  }
}
