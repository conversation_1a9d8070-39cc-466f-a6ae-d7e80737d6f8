// // lib/features/audio_player/services/audio_handler.dart
// import 'package:audioplayers/audioplayers.dart';
// import 'package:flutter/material.dart';

// class AudioHandler {
//   final AudioPlayer player;

//   AudioHandler(this.player);

//   Future<void> initAudio(String assetPath) async {
//     try {
//       // First, try to stop any current playback
//       await player.stop();

//       debugPrint('Initializing audio with asset: $assetPath');

//       // Create the audio source
//       final duration = await player.setAsset(assetPath);

//       if (duration == null) {
//         throw Exception('Failed to load audio duration');
//       }

//       debugPrint('Audio initialized successfully with duration: $duration');
//     } catch (e) {
//       debugPrint('Error initializing audio: $e');
//       rethrow;
//     }
//   }

//   Future<void> play() async {
//     try {
//       await player.play();
//     } catch (e) {
//       debugPrint('Error playing audio: $e');
//       rethrow;
//     }
//   }

//   Future<void> pause() async {
//     try {
//       await player.pause();
//     } catch (e) {
//       debugPrint('Error pausing audio: $e');
//       rethrow;
//     }
//   }
// }
