// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_session_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$audioSessionServiceHash() =>
    r'6fb50464ecd34ad275d50c3b0bc0169c19a7eb32';

/// See also [AudioSessionService].
@ProviderFor(AudioSessionService)
final audioSessionServiceProvider =
    AutoDisposeAsyncNotifierProvider<AudioSessionService, void>.internal(
  AudioSessionService.new,
  name: r'audioSessionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$audioSessionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AudioSessionService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
