import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/track_duration_provider.dart';

class SharedTrackListItem extends ConsumerWidget {
  final AudioTrack track;
  final VoidCallback? onSelect;
  final bool isSelectable;
  final bool showDownloadButton;

  const SharedTrackListItem({
    super.key,
    required this.track,
    this.onSelect,
    this.isSelectable = false,
    this.showDownloadButton = false,
  });

  void _playTrack(BuildContext context, WidgetRef ref) {
    ref.read(audioPlayerControllerProvider.notifier).playTracks([track]);
    context.pushNamed(RouteNames.player);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final durationAsync = ref.watch(trackDurationProvider(track));

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: InkWell(
        onTap: isSelectable ? onSelect : () => _playTrack(context, ref),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: isSelectable && track.isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Track Artwork
                Container(
                  height: 60,
                  width: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: track.artworkUrl != null
                        ? DecorationImage(
                            image: AssetImage(track.artworkUrl!),
                            fit: BoxFit.cover,
                          )
                        : null,
                    color: AppColors.surface,
                  ),
                  child: track.artworkUrl == null
                      ? Icon(
                          Icons.music_note,
                          color: AppColors.textSecondary,
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                // Track Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        track.title,
                        style: TextStyle(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      // Duration display
                      durationAsync.when(
                        data: (duration) => Text(
                          _formatDuration(duration),
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 13,
                          ),
                        ),
                        loading: () => SizedBox(
                          width: 35,
                          height: 2,
                          child: LinearProgressIndicator(
                            backgroundColor: Theme.of(context)
                                .colorScheme
                                .secondary
                                .withOpacity(0.1),
                          ),
                        ),
                        error: (_, __) => Text(
                          '--:--',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.error,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Play button
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary,
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.play_arrow_rounded,
                      color: AppColors.textOnPrimary,
                      size: 24,
                    ),
                    onPressed: () => _playTrack(context, ref),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
