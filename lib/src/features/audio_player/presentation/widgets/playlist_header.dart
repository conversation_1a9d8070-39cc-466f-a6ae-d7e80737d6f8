import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';

class PlaylistHeader extends ConsumerWidget {
  final String categoryId;
  const PlaylistHeader({
    super.key,
    required this.categoryId,
  });

  String _getAsset(String categoryName) {
    if (categoryName.toLowerCase() == 'meditation') {
      return 'assets/images/home/<USER>';
    } else if (categoryName.toLowerCase() == 'afternoon') {
      return 'assets/images/home/<USER>';
    } else if (categoryName.toLowerCase() == 'affirmations') {
      return 'assets/images/home/<USER>';
    } else {
      return 'assets/images/home/<USER>/m.png';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoryAsync = ref.watch(categoryProvider(categoryId));
    final asset = _getAsset(categoryAsync.value?.name ?? '');

    return Stack(
      children: [
        Container(
          height: 300,
          width: double.infinity,
          margin: EdgeInsets.zero,
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(40),
            ),
            image: DecorationImage(
              image: AssetImage(asset),
              fit: BoxFit.fill,
            ),
          ),
        ),
        Positioned(
          top: 40,
          left: 16,
          child: IconButton(
            icon:
                const Icon(Icons.arrow_back_ios, color: Colors.white, size: 28),
            onPressed: () => Navigator.of(context).maybePop(),
            tooltip: 'Back',
          ),
        ),
        Positioned(
          bottom: 40,
          left: 0,
          right: 0,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: categoryAsync.when(
              data: (category) => Text(
                category.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 28,
                ),
              ),
              loading: () => const SizedBox(
                width: 120,
                height: 32,
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => Text(
                'Error loading category',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                      fontSize: 24,
                    ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _ReverseCurveHeaderClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    const double curveDepth = 60.0; // Adjust for how deep the inward curve is
    final path = Path();
    // Start at top left
    path.moveTo(0, 0);
    // Top edge
    path.lineTo(size.width, 0);
    // Right edge down
    path.lineTo(size.width, size.height);
    // Bottom edge to just before bottom left
    path.lineTo(curveDepth, size.height);
    // Bottom left concave curve (reverse/inward)
    path.quadraticBezierTo(
      0, size.height, // control point at the corner
      0, size.height - curveDepth, // end point higher up the left edge
    );
    // Left edge up to top
    path.lineTo(0, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class BottomLeftConvexClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    const double curveRadius = 80.0; // Adjust for bulge size

    Path path = Path();
    path.moveTo(0, 0); // Top left
    path.lineTo(size.width, 0); // Top right
    path.lineTo(size.width, size.height); // Bottom right
    path.lineTo(curveRadius, size.height); // Move to where curve starts

    // Bottom left convex (outward) curve
    path.quadraticBezierTo(
      0, size.height, // Control point (bulge outward)
      0, size.height - curveRadius, // End point higher up the left edge
    );

    path.lineTo(0, 0); // Back to top left
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class MyClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height);
    path.quadraticBezierTo(
        size.width / 3, size.height / 3, size.width, size.height / 5);
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}

class CombinedBottomClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    // Parameters for the curves
    final double rightCurveHeight = 30; // much smaller than MyClipperB
    final double rightCurveDepth = 20; // controls how deep the right curve is

    Path path = Path();
    // Start at top left
    path.moveTo(0, 0);
    // Left edge down
    path.lineTo(0, size.height);
    // Bottom left large curve (MyClipperA style)
    path.quadraticBezierTo(
        size.width / 3, size.height / 3, size.width, size.height / 5);
    // Now, instead of going straight to top right, we want to add a small right curve
    // So, from the end of the left curve, go to (size.width, size.height - rightCurveHeight)
    path.lineTo(size.width, size.height - rightCurveHeight);
    // Small bottom right curve (MyClipperB style, but much smaller)
    path.quadraticBezierTo(size.width - rightCurveDepth, size.height,
        size.width - rightCurveDepth * 2, size.height - rightCurveHeight);
    // Up right edge to top right
    path.lineTo(size.width, 0);
    // Close the path
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class WavyHeaderClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final double leftCurveHeight =
        size.height * 0.35; // how high the left wave rises
    final double rightCurveDepth =
        size.height * 0.10; // how deep the right wave dips

    Path path = Path();
    path.moveTo(0, 0); // Top left
    path.lineTo(0, size.height - leftCurveHeight);

    // Large upward curve on the left, then a smooth dip on the right
    path.quadraticBezierTo(
        size.width * 0.25,
        size.height, // control point for left wave
        size.width * 0.6,
        size.height - rightCurveDepth // end point before right dip
        );
    path.quadraticBezierTo(
        size.width * 0.85,
        size.height - rightCurveDepth * 2, // control for right dip
        size.width,
        size.height - rightCurveDepth // end at bottom right
        );

    path.lineTo(size.width, 0); // Top right
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
