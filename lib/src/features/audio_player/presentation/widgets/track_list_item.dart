// lib/features/audio_player/presentation/widgets/track_list_item.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/presentation/widgets/shared_track_list_item.dart';

class TrackListItem extends StatelessWidget {
  final AudioTrack track;
  final VoidCallback onSelect;
  final bool showDownloadButton;

  const TrackListItem({
    super.key,
    required this.track,
    required this.onSelect,
    this.showDownloadButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return SharedTrackListItem(
      track: track,
      onSelect: onSelect,
      isSelectable: true,
      showDownloadButton: showDownloadButton,
    );
  }
}

// class _DownloadButton extends ConsumerWidget {
//   final AudioTrack track;

//   const _DownloadButton({required this.track});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final downloadStatus = ref.watch(downloadProvider(track.id));

//     return IconButton(
//       icon: Icon(
//         track.isDownloaded
//             ? Icons.download_done
//             : downloadStatus.isLoading
//                 ? Icons.downloading
//                 : Icons.download_outlined,
//         size: AppSizing.iconM,
//         color: track.isDownloaded
//             ? Theme.of(context).colorScheme.success
//             : downloadStatus.isLoading
//                 ? Theme.of(context).colorScheme.primary
//                 : Theme.of(context).colorScheme.textSecondary,
//       ),
//       tooltip: track.isDownloaded
//           ? AppStrings.downloadComplete
//           : downloadStatus.isLoading
//               ? AppStrings.downloading
//               : AppStrings.download,
//       onPressed: track.isDownloaded
//           ? null
//           : () => ref.read(downloadProvider(track.id).notifier).downloadTrack(),
//     );
//   }
// }








/**------------------------------------------------------------------------
 * !                              WARNING


class TrackListItem extends ConsumerWidget {
  final AudioTrack track;

  const TrackListItem({
    super.key,
    required this.track,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: InkWell(
        onTap: () {
          // Play the selected track
          ref.read(audioPlayerControllerProvider.notifier).playTracks([track]);

          // Navigate to player screen
          context.pushNamed(RouteNames.player);
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          // decoration: BoxDecoration(
          //   color: Colors.white,
          //   borderRadius: BorderRadius.circular(16),
          //   boxShadow: [
          //     BoxShadow(
          //       color: Colors.black.withOpacity(0.05),
          //       blurRadius: 10,
          //       offset: const Offset(0, 2),
          //     ),
          //   ],
          // ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Track Artwork
                Container(
                  height: 60,
                  width: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: track.artworkUrl != null
                        ? DecorationImage(
                            image: AssetImage(track.artworkUrl!),
                            fit: BoxFit.cover,
                          )
                        : null,
                    color: AppColors.surface,
                  ),
                  child: track.artworkUrl == null
                      ? Icon(
                          Icons.music_note,
                          color: AppColors.textSecondary,
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                // Track Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        track.title,
                        style: TextStyle(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      // You can add categories, tags, or duration here
                      // Text(
                      //   _getTrackDescription(track),
                      //   style: TextStyle(
                      //     color: Colors.grey.shade600,
                      //     fontSize: 13,
                      //   ),
                      // ),
                      Text(
                        track.description,
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
                // Play button
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primaryLight,
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.play_arrow_rounded,
                      color: AppColors.greyDark100,
                      size: 24,
                    ),
                    onPressed: () {
                      ref
                          .read(audioPlayerControllerProvider.notifier)
                          .playTracks([track]);
                      context.pushNamed(RouteNames.player);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}



 *------------------------------------------------------------------------**/