// lib/features/audio_player/presentation/screens/category_tracks_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/category_tracks_provider.dart';
import 'package:mimi_app/src/features/audio_player/presentation/widgets/playlist_header.dart';
import 'package:mimi_app/src/features/audio_player/presentation/widgets/track_list_item.dart';

class CategoryTracksScreen extends ConsumerStatefulWidget {
  final String categoryId;

  const CategoryTracksScreen({
    super.key,
    required this.categoryId,
  });

  @override
  ConsumerState<CategoryTracksScreen> createState() =>
      _CategoryTracksScreenState();
}

class _CategoryTracksScreenState extends ConsumerState<CategoryTracksScreen> {
  bool _isInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (!_isInitialized) {
      final tracks =
          ref.read(categoryTracksProvider(widget.categoryId)).valueOrNull;
      if (tracks != null) {
        ref
            .read(audioPlayerControllerProvider.notifier)
            .preFetchDurations(tracks);
      }
      _isInitialized = true;
    }
  }

  void _playSelectedTracks(List<AudioTrack> tracks) async {
    debugPrint('Starting playSelectedTracks with ${tracks.length} tracks');

    try {
      // Start playing tracks
      await ref.read(audioPlayerControllerProvider.notifier).playTracks(tracks);

      if (context.mounted) {
        // Navigate after successful playback start
        debugPrint('Navigating to player screen');
        context.pushNamed(RouteNames.player);
      }
    } catch (e) {
      debugPrint('Error playing tracks: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error playing tracks: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final tracksAsync = ref.watch(categoryTracksProvider(widget.categoryId));
    final selectedTracks =
        tracksAsync.valueOrNull?.where((track) => track.isSelected).toList();
    final hasSelectedTracks = selectedTracks?.isNotEmpty ?? false;
    final selectedCount = selectedTracks?.length ?? 0;

    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                PlaylistHeader(categoryId: widget.categoryId),
                tracksAsync.when(
                  data: (tracks) => _TracksList(tracks: tracks),
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error, stackTrace) => Center(
                    child: Padding(
                      padding: EdgeInsets.all(AppSizing.spaceM),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: AppSizing.iconXL,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          SizedBox(height: AppSizing.spaceM),
                          Text(
                            AppStrings.tracksLoadError,
                            style: Theme.of(context)
                                .textTheme
                                .bodyLarge
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.error,
                                ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // Add extra padding at bottom for the floating button
                SizedBox(height: hasSelectedTracks ? 80 : 0),
              ],
            ),
          ),
          if (hasSelectedTracks)
            Positioned(
              left: 16,
              right: 16,
              bottom: 16,
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(32),
                color: AppColors.primaryLight,
                child: InkWell(
                  onTap: () => _playSelectedTracks(selectedTracks!),
                  borderRadius: BorderRadius.circular(32),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.play_arrow_rounded,
                          color: Colors.white,
                          size: 32,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Play $selectedCount Selected',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _TracksList extends ConsumerWidget {
  final List<AudioTrack> tracks;

  const _TracksList({required this.tracks});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (tracks.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppSizing.spaceM),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.queue_music,
                size: AppSizing.iconXL,
                color: Theme.of(context).colorScheme.secondary.withOpacity(0.5),
              ),
              SizedBox(height: AppSizing.spaceM),
              Text(
                AppStrings.noTracksAvailable,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.all(AppSizing.spaceM),
      itemCount: tracks.length,
      separatorBuilder: (_, __) => SizedBox(height: AppSizing.spaceS),
      itemBuilder: (context, index) {
        final track = tracks[index];
        return TrackListItem(
          track: track,
          onSelect: () {
            debugPrint('Track selected: ${track.title}');
            debugPrint('Track URL: ${track.audioUrl}');
            ref
                .read(categoryTracksProvider(track.categoryId).notifier)
                .toggleTrackSelection(track.id);
          },
        );
      },
    );
  }
}
