// lib/features/audio_player/presentation/widgets/category_button.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_category.dart';

class CategoryButton extends ConsumerWidget {
  final AudioCategory category;

  const CategoryButton({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () => context.push('/category/${category.id}'),
      child: Container(
        padding: const EdgeInsets.all(AppSizing.componentSpacingS),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(AppSizing.radiusS),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              category.iconPath,
              width: AppSizing.iconS,
              height: AppSizing.iconS,
            ),
            const SizedBox(height: AppSizing.componentSpacingS),
            Text(
              category.name,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      ),
    );
  }
}
