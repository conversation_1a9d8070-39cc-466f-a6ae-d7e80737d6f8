// lib/features/audio_player/presentation/screens/audio_player_screen.dart

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/stream_providers.dart';

class AudioPlayerScreen extends ConsumerWidget {
  const AudioPlayerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch both the controller and its state
    final audioState = ref.watch(audioPlayerControllerProvider);
    final controller = ref.watch(audioPlayerControllerProvider.notifier);

    return audioState.when(
      data: (track) {
        final currentTrack = controller.currentTrack;
        debugPrint(
            'Current track in AudioPlayerScreen: ${currentTrack?.title}');

        if (currentTrack == null) {
          return const Scaffold(
            body: Center(
              child: Text(AppStrings.noTrackSelected),
            ),
          );
        }

        return Scaffold(
          body: Stack(
            fit: StackFit.expand,
            children: [
              // Blurred background image from track artwork
              if (currentTrack.artworkUrl != null)
                Image.asset(
                  currentTrack.artworkUrl!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  errorBuilder: (_, __, ___) => Container(
                    color: Theme.of(context).colorScheme.surface,
                  ),
                )
              else
                Container(
                  color: Theme.of(context).colorScheme.surface,
                ),
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  color: Colors.white.withOpacity(0.1),
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
              // Content
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: _buildAppBar(context, currentTrack),
                body: SafeArea(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSizing.spaceL,
                      vertical: AppSizing.spaceM,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Spacer(),
                        _TrackArtwork(track: currentTrack),
                        SizedBox(height: AppSizing.spaceXL),
                        _TrackInfo(track: currentTrack),
                        SizedBox(height: AppSizing.spaceL),
                        _ProgressBar(),
                        SizedBox(height: AppSizing.spaceL),
                        Consumer(
                          builder: (context, ref, child) {
                            final playerState = ref.watch(playerStateProvider);
                            return playerState.when(
                              data: (state) => _PlaybackControls(
                                isPlaying: controller.isPlaying,
                                hasNext: controller.hasNext,
                                hasPrevious: controller.hasPrevious,
                              ),
                              loading: () => const CircularProgressIndicator(),
                              error: (_, __) => const Icon(Icons.error),
                            );
                          },
                        ),
                        const Spacer(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      BuildContext context, AudioTrack currentTrack) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          size: AppSizing.iconM,
          color: Theme.of(context).colorScheme.primary,
        ),
        onPressed: () => context.goNamed(RouteNames.home),
      ),
      actions: [
        //  _FavoriteButton(trackId: currentTrack.id),  ac
      ],
    );
  }
}

class _TrackArtwork extends StatelessWidget {
  final AudioTrack track;

  const _TrackArtwork({required this.track});
  final String heroTag = 'mini-player-artwork';
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Hero(
        tag: heroTag,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.width * 0.8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizing.radiusL),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppSizing.radiusS),
            child: track.artworkUrl != null
                ? Image.asset(
                    track.artworkUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => _buildPlaceholder(context),
                  )
                : _buildPlaceholder(context),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: Icon(
        Icons.music_note,
        size: AppSizing.iconXL * 2,
        color: Theme.of(context).colorScheme.secondary.withOpacity(0.5),
      ),
    );
  }
}

class _TrackInfo extends StatelessWidget {
  final AudioTrack track;

  const _TrackInfo({required this.track});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          track.title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: AppSizing.spaceXS),
        Text(
          track.categoryId, // You might want to fetch category name instead
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.secondary,
              ),
        ),
      ],
    );
  }
}

class _ProgressBar extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Create a StreamBuilder for position
    return StreamBuilder<Duration>(
      stream: ref.watch(audioPlayerControllerProvider.notifier).positionStream,
      builder: (context, positionSnapshot) {
        return StreamBuilder<Duration>(
          stream:
              ref.watch(audioPlayerControllerProvider.notifier).durationStream,
          builder: (context, durationSnapshot) {
            final position = positionSnapshot.data ?? Duration.zero;
            final duration = durationSnapshot.data ?? Duration.zero;

            debugPrint('Position: $position, Duration: $duration');

            final progress = duration.inMilliseconds > 0
                ? position.inMilliseconds / duration.inMilliseconds
                : 0.0;

            return Column(
              children: [
                SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 4,
                    thumbShape:
                        const RoundSliderThumbShape(enabledThumbRadius: 6),
                    overlayShape:
                        const RoundSliderOverlayShape(overlayRadius: 14),
                    activeTrackColor: Theme.of(context).colorScheme.primary,
                    inactiveTrackColor:
                        Theme.of(context).colorScheme.primary.withOpacity(0.2),
                    thumbColor: Theme.of(context).colorScheme.primary,
                    overlayColor:
                        Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  ),
                  child: Slider(
                    value: progress.clamp(0.0, 1.0),
                    onChanged: (value) {
                      if (duration.inMilliseconds > 0) {
                        final newPosition = Duration(
                          milliseconds:
                              (value * duration.inMilliseconds).round(),
                        );
                        ref
                            .read(audioPlayerControllerProvider.notifier)
                            .seek(newPosition);
                      }
                    },
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSizing.spaceXS),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _formatDuration(position),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                      ),
                      Text(
                        _formatDuration(duration),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}

class _PlaybackControls extends ConsumerWidget {
  final bool isPlaying;
  final bool hasNext;
  final bool hasPrevious;
  final bool showQueue;
  final bool showSpeed;

  const _PlaybackControls({
    required this.isPlaying,
    required this.hasNext,
    required this.hasPrevious,
    this.showQueue = true,
    this.showSpeed = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showQueue)
          IconButton(
            icon: Icon(
              Icons.queue_music,
              size: AppSizing.iconL,
              color: Theme.of(context).colorScheme.primary,
            ),
            onPressed: () => _showQueueBottomSheet(context),
          ),
        IconButton(
          icon: Icon(
            Icons.skip_previous,
            size: AppSizing.iconL,
            color: hasPrevious
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.primary.withOpacity(0.3),
          ),
          onPressed: hasPrevious
              ? () => ref
                  .read(audioPlayerControllerProvider.notifier)
                  .playPrevious()
              : null,
        ),
        SizedBox(width: AppSizing.spaceL),
        _AnimatedPlayButton(
          isPlaying: isPlaying,
          onPressed: () {
            final controller = ref.read(audioPlayerControllerProvider.notifier);
            isPlaying ? controller.pause() : controller.play();
          },
        ),
        SizedBox(width: AppSizing.spaceL),
        IconButton(
          icon: Icon(
            Icons.skip_next,
            size: AppSizing.iconL,
            color: hasNext
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.primary.withOpacity(0.3),
          ),
          onPressed: hasNext
              ? () =>
                  ref.read(audioPlayerControllerProvider.notifier).playNext()
              : null,
        ),
        if (showSpeed)
          IconButton(
            icon: Icon(
              Icons.speed,
              size: AppSizing.iconL,
              color: Theme.of(context).colorScheme.primary,
            ),
            onPressed: () => _showSpeedBottomSheet(context, ref),
          ),
      ],
    );
  }

  void _showQueueBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => QueueBottomSheet(),
    );
  }

  void _showSpeedBottomSheet(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _SpeedBottomSheet(),
    );
  }
}

class _SpeedBottomSheet extends ConsumerWidget {
  final List<double> speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(audioPlayerControllerProvider.notifier);
    final currentSpeed = controller.playbackSpeed;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: AppSizing.spaceS),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSizing.spaceM,
                  vertical: AppSizing.spaceS,
                ),
                child: Row(
                  children: [
                    Text(
                      'Playback Speed',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 300, // Fixed height for the list
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const BouncingScrollPhysics(),
                  itemCount: speeds.length,
                  itemBuilder: (context, index) {
                    final speed = speeds[index];
                    final isSelected = speed == currentSpeed;

                    return ListTile(
                      title: Text(
                        '${speed}x',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : null,
                              fontWeight: isSelected ? FontWeight.bold : null,
                            ),
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                      onTap: () {
                        controller.setPlaybackSpeed(speed);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AnimatedPlayButton extends StatelessWidget {
  final bool isPlaying;
  final VoidCallback onPressed;

  const _AnimatedPlayButton({
    required this.isPlaying,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        iconSize: AppSizing.iconL,
        icon: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          transitionBuilder: (child, animation) {
            return RotationTransition(
              turns: animation,
              child: ScaleTransition(
                scale: animation,
                child: child,
              ),
            );
          },
          child: Icon(
            isPlaying ? Icons.pause : Icons.play_arrow,
            key: ValueKey<bool>(isPlaying),
            color: Colors.white,
          ),
        ),
        onPressed: onPressed,
      ),
    );
  }
}

class QueueBottomSheet extends ConsumerWidget {
  const QueueBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final playerController = ref.watch(audioPlayerControllerProvider.notifier);
    final playlist = playerController.playlist;
    final currentTrackId = playerController.currentTrack?.id;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.all(AppSizing.spaceM),
            child: Row(
              children: [
                Text(
                  AppStrings.playlist,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: playlist.length,
              itemBuilder: (context, index) {
                final track = playlist[index];
                final isCurrentTrack = track.id == currentTrackId;

                return ListTile(
                  leading: Container(
                    width: AppSizing.iconXL,
                    height: AppSizing.iconXL,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppSizing.radiusS),
                      image: track.artworkUrl != null
                          ? DecorationImage(
                              image: NetworkImage(track.artworkUrl!),
                              fit: BoxFit.cover,
                            )
                          : null,
                    ),
                    child: track.artworkUrl == null
                        ? Icon(
                            Icons.music_note,
                            color: Theme.of(context).colorScheme.secondary,
                          )
                        : null,
                  ),
                  title: Text(
                    track.title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: isCurrentTrack
                              ? Theme.of(context).colorScheme.primary
                              : null,
                          fontWeight: isCurrentTrack
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                  ),
                  // subtitle: Text(
                  //   _formatDuration(track.duration),
                  //   style: Theme.of(context).textTheme.bodySmall,
                  // ),
                  trailing: isCurrentTrack
                      ? Icon(
                          Icons.equalizer,
                          color: Theme.of(context).colorScheme.primary,
                        )
                      : null,
                  onTap: () {
                    playerController.playTracks(
                      playlist.sublist(index),
                    );
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // String _formatDuration(Duration duration) {
  //   final minutes = duration.inMinutes;
  //   final seconds = duration.inSeconds % 60;
  //   return '$minutes:${seconds.toString().padLeft(2, '0')}';
  // }
}

class CustomAudioPlayerScreen extends ConsumerWidget {
  final bool showQueue;
  final bool showSpeed;
  final VoidCallback? onComplete;

  const CustomAudioPlayerScreen({
    super.key,
    this.showQueue = true,
    this.showSpeed = true,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerControllerProvider);
    final controller = ref.watch(audioPlayerControllerProvider.notifier);

    // Listen to track completion
    ref.listen<AsyncValue<AudioTrack?>>(
      audioPlayerControllerProvider,
      (previous, next) {
        next.whenData((track) {
          if (track == null && onComplete != null) {
            onComplete!();
          }
        });
      },
    );

    return audioState.when(
      data: (track) {
        final currentTrack = controller.currentTrack;

        if (currentTrack == null) {
          return const Scaffold(
            body: Center(
              child: Text('No track selected'),
            ),
          );
        }

        return Scaffold(
          body: Stack(
            fit: StackFit.expand,
            children: [
              // Blurred background image from track artwork
              if (currentTrack.artworkUrl != null)
                Image.asset(
                  currentTrack.artworkUrl!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  errorBuilder: (_, __, ___) => Container(
                    color: Theme.of(context).colorScheme.surface,
                  ),
                )
              else
                Container(
                  color: Theme.of(context).colorScheme.surface,
                ),
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  color: Colors.white.withOpacity(0.1),
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
              // Content
              Scaffold(
                backgroundColor: Colors.transparent,
                body: SafeArea(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSizing.spaceM,
                      vertical: AppSizing.spaceS,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Spacer(),
                        _TrackArtwork(track: currentTrack),
                        SizedBox(height: AppSizing.spaceS),
                        _TrackInfo(track: currentTrack),
                        SizedBox(height: AppSizing.spaceM),
                        _ProgressBar(),
                        SizedBox(height: AppSizing.spaceM),
                        Consumer(
                          builder: (context, ref, child) {
                            final playerState = ref.watch(playerStateProvider);
                            return playerState.when(
                              data: (state) => _PlaybackControls(
                                isPlaying: controller.isPlaying,
                                hasNext: controller.hasNext,
                                hasPrevious: controller.hasPrevious,
                                showQueue: showQueue,
                                showSpeed: showSpeed,
                              ),
                              loading: () => const CircularProgressIndicator(),
                              error: (_, __) => const Icon(Icons.error),
                            );
                          },
                        ),
                        const Spacer(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }
}
