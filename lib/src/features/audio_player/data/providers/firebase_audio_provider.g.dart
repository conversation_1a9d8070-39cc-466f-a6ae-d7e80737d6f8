// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_audio_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$firebaseAudioHash() => r'fcac611babf8278df2f1534f780ff01af50f8a2e';

/// See also [firebaseAudio].
@ProviderFor(firebaseAudio)
final firebaseAudioProvider =
    AutoDisposeProvider<FirebaseAudioRepository>.internal(
  firebaseAudio,
  name: r'firebaseAudioProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseAudioHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseAudioRef = AutoDisposeProviderRef<FirebaseAudioRepository>;
String _$audioCategoriesHash() => r'1960700609fa3b4616c15e01667cca5b073fc115';

/// See also [audioCategories].
@ProviderFor(audioCategories)
final audioCategoriesProvider =
    AutoDisposeFutureProvider<List<AudioCategory>>.internal(
  audioCategories,
  name: r'audioCategoriesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$audioCategoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AudioCategoriesRef = AutoDisposeFutureProviderRef<List<AudioCategory>>;
String _$tracksByCategoryHash() => r'4b3dd121c356e3f41ba38f06b3f1a39fadefa339';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [tracksByCategory].
@ProviderFor(tracksByCategory)
const tracksByCategoryProvider = TracksByCategoryFamily();

/// See also [tracksByCategory].
class TracksByCategoryFamily extends Family<AsyncValue<List<AudioTrack>>> {
  /// See also [tracksByCategory].
  const TracksByCategoryFamily();

  /// See also [tracksByCategory].
  TracksByCategoryProvider call(
    String categoryId,
  ) {
    return TracksByCategoryProvider(
      categoryId,
    );
  }

  @override
  TracksByCategoryProvider getProviderOverride(
    covariant TracksByCategoryProvider provider,
  ) {
    return call(
      provider.categoryId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tracksByCategoryProvider';
}

/// See also [tracksByCategory].
class TracksByCategoryProvider
    extends AutoDisposeFutureProvider<List<AudioTrack>> {
  /// See also [tracksByCategory].
  TracksByCategoryProvider(
    String categoryId,
  ) : this._internal(
          (ref) => tracksByCategory(
            ref as TracksByCategoryRef,
            categoryId,
          ),
          from: tracksByCategoryProvider,
          name: r'tracksByCategoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$tracksByCategoryHash,
          dependencies: TracksByCategoryFamily._dependencies,
          allTransitiveDependencies:
              TracksByCategoryFamily._allTransitiveDependencies,
          categoryId: categoryId,
        );

  TracksByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<List<AudioTrack>> Function(TracksByCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TracksByCategoryProvider._internal(
        (ref) => create(ref as TracksByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<AudioTrack>> createElement() {
    return _TracksByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TracksByCategoryProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TracksByCategoryRef on AutoDisposeFutureProviderRef<List<AudioTrack>> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _TracksByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<AudioTrack>>
    with TracksByCategoryRef {
  _TracksByCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as TracksByCategoryProvider).categoryId;
}

String _$allTracksHash() => r'29ebb0c6fc81d1e4f374fab3c1f03d1b767ccf2a';

/// See also [allTracks].
@ProviderFor(allTracks)
final allTracksProvider = AutoDisposeFutureProvider<List<AudioTrack>>.internal(
  allTracks,
  name: r'allTracksProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$allTracksHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllTracksRef = AutoDisposeFutureProviderRef<List<AudioTrack>>;
String _$trackByIdHash() => r'573ff0eeac185b092ebf8a9e114351489603b110';

/// See also [trackById].
@ProviderFor(trackById)
const trackByIdProvider = TrackByIdFamily();

/// See also [trackById].
class TrackByIdFamily extends Family<AsyncValue<AudioTrack>> {
  /// See also [trackById].
  const TrackByIdFamily();

  /// See also [trackById].
  TrackByIdProvider call(
    String trackId,
  ) {
    return TrackByIdProvider(
      trackId,
    );
  }

  @override
  TrackByIdProvider getProviderOverride(
    covariant TrackByIdProvider provider,
  ) {
    return call(
      provider.trackId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'trackByIdProvider';
}

/// See also [trackById].
class TrackByIdProvider extends AutoDisposeFutureProvider<AudioTrack> {
  /// See also [trackById].
  TrackByIdProvider(
    String trackId,
  ) : this._internal(
          (ref) => trackById(
            ref as TrackByIdRef,
            trackId,
          ),
          from: trackByIdProvider,
          name: r'trackByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$trackByIdHash,
          dependencies: TrackByIdFamily._dependencies,
          allTransitiveDependencies: TrackByIdFamily._allTransitiveDependencies,
          trackId: trackId,
        );

  TrackByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.trackId,
  }) : super.internal();

  final String trackId;

  @override
  Override overrideWith(
    FutureOr<AudioTrack> Function(TrackByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TrackByIdProvider._internal(
        (ref) => create(ref as TrackByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        trackId: trackId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AudioTrack> createElement() {
    return _TrackByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TrackByIdProvider && other.trackId == trackId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, trackId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TrackByIdRef on AutoDisposeFutureProviderRef<AudioTrack> {
  /// The parameter `trackId` of this provider.
  String get trackId;
}

class _TrackByIdProviderElement
    extends AutoDisposeFutureProviderElement<AudioTrack> with TrackByIdRef {
  _TrackByIdProviderElement(super.provider);

  @override
  String get trackId => (origin as TrackByIdProvider).trackId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
