// lib/features/audio_player/data/providers/category_tracks_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_category.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_repository_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'category_tracks_provider.g.dart';

@riverpod
class CategoryTracks extends _$CategoryTracks {
  @override
  FutureOr<List<AudioTrack>> build(String categoryId) async {
    final repository = ref.watch(audioRepositoryProvider);
    return repository.getTracksByCategory(categoryId);
  }

  Future<void> toggleTrackSelection(String trackId) async {
    state = await AsyncValue.guard(() async {
      final currentTracks = state.valueOrNull ?? [];
      return currentTracks.map((track) {
        if (track.id == trackId) {
          return track.copyWith(isSelected: !track.isSelected);
        }
        return track;
      }).toList();
    });
  }

  List<AudioTrack> getSelectedTracks() {
    return state.valueOrNull?.where((track) => track.isSelected).toList() ?? [];
  }

  // Add method to clear selections
  void clearSelections() {
    if (state.hasValue) {
      state = AsyncValue.data(state.value!
          .map(
            (track) => track.copyWith(isSelected: false),
          )
          .toList());
    }
  }
}

@riverpod
class CurrentAudioTrack extends _$CurrentAudioTrack {
  @override
  AudioTrack? build() {
    final playerState = ref.watch(audioPlayerControllerProvider);
    return playerState.when(
      data: (track) => track,
      loading: () => null,
      error: (_, __) => null,
    );
  }
}

@riverpod
Future<List<AudioCategory>> categories(Ref ref) {
  final repository = ref.watch(audioRepositoryProvider);
  return repository.getCategories();
}

@riverpod
Future<AudioCategory> category(Ref ref, String categoryId) {
  final repository = ref.watch(audioRepositoryProvider);
  return repository.getCategoryById(categoryId);
}
