// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'track_duration_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$trackDurationHash() => r'4cb9e8af9a75cde00ca79057b8a61caf95396658';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$TrackDuration
    extends BuildlessAutoDisposeAsyncNotifier<Duration> {
  late final AudioTrack track;

  FutureOr<Duration> build(
    AudioTrack track,
  );
}

/// See also [TrackDuration].
@ProviderFor(TrackDuration)
const trackDurationProvider = TrackDurationFamily();

/// See also [TrackDuration].
class TrackDurationFamily extends Family<AsyncValue<Duration>> {
  /// See also [TrackDuration].
  const TrackDurationFamily();

  /// See also [TrackDuration].
  TrackDurationProvider call(
    AudioTrack track,
  ) {
    return TrackDurationProvider(
      track,
    );
  }

  @override
  TrackDurationProvider getProviderOverride(
    covariant TrackDurationProvider provider,
  ) {
    return call(
      provider.track,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'trackDurationProvider';
}

/// See also [TrackDuration].
class TrackDurationProvider
    extends AutoDisposeAsyncNotifierProviderImpl<TrackDuration, Duration> {
  /// See also [TrackDuration].
  TrackDurationProvider(
    AudioTrack track,
  ) : this._internal(
          () => TrackDuration()..track = track,
          from: trackDurationProvider,
          name: r'trackDurationProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$trackDurationHash,
          dependencies: TrackDurationFamily._dependencies,
          allTransitiveDependencies:
              TrackDurationFamily._allTransitiveDependencies,
          track: track,
        );

  TrackDurationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.track,
  }) : super.internal();

  final AudioTrack track;

  @override
  FutureOr<Duration> runNotifierBuild(
    covariant TrackDuration notifier,
  ) {
    return notifier.build(
      track,
    );
  }

  @override
  Override overrideWith(TrackDuration Function() create) {
    return ProviderOverride(
      origin: this,
      override: TrackDurationProvider._internal(
        () => create()..track = track,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        track: track,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<TrackDuration, Duration>
      createElement() {
    return _TrackDurationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TrackDurationProvider && other.track == track;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, track.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TrackDurationRef on AutoDisposeAsyncNotifierProviderRef<Duration> {
  /// The parameter `track` of this provider.
  AudioTrack get track;
}

class _TrackDurationProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<TrackDuration, Duration>
    with TrackDurationRef {
  _TrackDurationProviderElement(super.provider);

  @override
  AudioTrack get track => (origin as TrackDurationProvider).track;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
