// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_tracks_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$categoriesHash() => r'36b69d51a8933433330a5dc1549dbc0fe9ac9b31';

/// See also [categories].
@ProviderFor(categories)
final categoriesProvider =
    AutoDisposeFutureProvider<List<AudioCategory>>.internal(
  categories,
  name: r'categoriesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$categoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CategoriesRef = AutoDisposeFutureProviderRef<List<AudioCategory>>;
String _$categoryHash() => r'40e41a11e9847488e46f8f54e14aec3dc3d69e17';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [category].
@ProviderFor(category)
const categoryProvider = CategoryFamily();

/// See also [category].
class CategoryFamily extends Family<AsyncValue<AudioCategory>> {
  /// See also [category].
  const CategoryFamily();

  /// See also [category].
  CategoryProvider call(
    String categoryId,
  ) {
    return CategoryProvider(
      categoryId,
    );
  }

  @override
  CategoryProvider getProviderOverride(
    covariant CategoryProvider provider,
  ) {
    return call(
      provider.categoryId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryProvider';
}

/// See also [category].
class CategoryProvider extends AutoDisposeFutureProvider<AudioCategory> {
  /// See also [category].
  CategoryProvider(
    String categoryId,
  ) : this._internal(
          (ref) => category(
            ref as CategoryRef,
            categoryId,
          ),
          from: categoryProvider,
          name: r'categoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$categoryHash,
          dependencies: CategoryFamily._dependencies,
          allTransitiveDependencies: CategoryFamily._allTransitiveDependencies,
          categoryId: categoryId,
        );

  CategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<AudioCategory> Function(CategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryProvider._internal(
        (ref) => create(ref as CategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AudioCategory> createElement() {
    return _CategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryRef on AutoDisposeFutureProviderRef<AudioCategory> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _CategoryProviderElement
    extends AutoDisposeFutureProviderElement<AudioCategory> with CategoryRef {
  _CategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as CategoryProvider).categoryId;
}

String _$categoryTracksHash() => r'f86b3d9c69a0d8903892f75b54ba0661661348d7';

abstract class _$CategoryTracks
    extends BuildlessAutoDisposeAsyncNotifier<List<AudioTrack>> {
  late final String categoryId;

  FutureOr<List<AudioTrack>> build(
    String categoryId,
  );
}

/// See also [CategoryTracks].
@ProviderFor(CategoryTracks)
const categoryTracksProvider = CategoryTracksFamily();

/// See also [CategoryTracks].
class CategoryTracksFamily extends Family<AsyncValue<List<AudioTrack>>> {
  /// See also [CategoryTracks].
  const CategoryTracksFamily();

  /// See also [CategoryTracks].
  CategoryTracksProvider call(
    String categoryId,
  ) {
    return CategoryTracksProvider(
      categoryId,
    );
  }

  @override
  CategoryTracksProvider getProviderOverride(
    covariant CategoryTracksProvider provider,
  ) {
    return call(
      provider.categoryId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryTracksProvider';
}

/// See also [CategoryTracks].
class CategoryTracksProvider extends AutoDisposeAsyncNotifierProviderImpl<
    CategoryTracks, List<AudioTrack>> {
  /// See also [CategoryTracks].
  CategoryTracksProvider(
    String categoryId,
  ) : this._internal(
          () => CategoryTracks()..categoryId = categoryId,
          from: categoryTracksProvider,
          name: r'categoryTracksProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$categoryTracksHash,
          dependencies: CategoryTracksFamily._dependencies,
          allTransitiveDependencies:
              CategoryTracksFamily._allTransitiveDependencies,
          categoryId: categoryId,
        );

  CategoryTracksProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  FutureOr<List<AudioTrack>> runNotifierBuild(
    covariant CategoryTracks notifier,
  ) {
    return notifier.build(
      categoryId,
    );
  }

  @override
  Override overrideWith(CategoryTracks Function() create) {
    return ProviderOverride(
      origin: this,
      override: CategoryTracksProvider._internal(
        () => create()..categoryId = categoryId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<CategoryTracks, List<AudioTrack>>
      createElement() {
    return _CategoryTracksProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryTracksProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryTracksRef
    on AutoDisposeAsyncNotifierProviderRef<List<AudioTrack>> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _CategoryTracksProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<CategoryTracks,
        List<AudioTrack>> with CategoryTracksRef {
  _CategoryTracksProviderElement(super.provider);

  @override
  String get categoryId => (origin as CategoryTracksProvider).categoryId;
}

String _$currentAudioTrackHash() => r'e8c808bf1c9ebd94507be7fe412524e305c41910';

/// See also [CurrentAudioTrack].
@ProviderFor(CurrentAudioTrack)
final currentAudioTrackProvider =
    AutoDisposeNotifierProvider<CurrentAudioTrack, AudioTrack?>.internal(
  CurrentAudioTrack.new,
  name: r'currentAudioTrackProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAudioTrackHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentAudioTrack = AutoDisposeNotifier<AudioTrack?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
