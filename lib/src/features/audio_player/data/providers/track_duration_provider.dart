import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/audio_track.dart';
import 'audio_player_provider.dart';

part 'track_duration_provider.g.dart';

@riverpod
class TrackDuration extends _$TrackDuration {
  @override
  Future<Duration> build(AudioTrack track) async {
    // Keep the provider alive for a while to prevent unnecessary recalculations
    ref.keepAlive();
    return ref
        .read(audioPlayerControllerProvider.notifier)
        .getTrackDuration(track);
  }
}
