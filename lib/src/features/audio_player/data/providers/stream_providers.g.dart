// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stream_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$playerStateHash() => r'612bb073e4791000c1778803502b985e03203dff';

/// See also [playerState].
@ProviderFor(playerState)
final playerStateProvider = AutoDisposeStreamProvider<PlayerState>.internal(
  playerState,
  name: r'playerStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$playerStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PlayerStateRef = AutoDisposeStreamProviderRef<PlayerState>;
String _$positionHash() => r'7102deb36e68ee50c50932bd70cd26150bf6b75b';

/// See also [position].
@ProviderFor(position)
final positionProvider = AutoDisposeStreamProvider<Duration>.internal(
  position,
  name: r'positionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$positionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PositionRef = AutoDisposeStreamProviderRef<Duration>;
String _$durationHash() => r'6f87480559fc7ea07beb626549a41d14bf7f601d';

/// See also [duration].
@ProviderFor(duration)
final durationProvider = AutoDisposeStreamProvider<Duration>.internal(
  duration,
  name: r'durationProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$durationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DurationRef = AutoDisposeStreamProviderRef<Duration>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
