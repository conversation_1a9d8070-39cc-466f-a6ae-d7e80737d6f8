import 'package:audioplayers/audioplayers.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'stream_providers.g.dart';

@riverpod
Stream<PlayerState> playerState(Ref ref) {
  final player = ref.watch(audioPlayerControllerProvider.notifier);
  // Keep the provider alive as long as the controller exists
  final link = ref.keepAlive();
  ref.onDispose(() {
    link.close();
  });
  return player.playerStateStream;
}

@riverpod
Stream<Duration> position(Ref ref) {
  final player = ref.watch(audioPlayerControllerProvider.notifier);
  // Keep the provider alive as long as the controller exists
  final link = ref.keepAlive();
  ref.onDispose(() {
    link.close();
  });
  return player.positionStream;
}

@riverpod
Stream<Duration> duration(Ref ref) {
  final player = ref.watch(audioPlayerControllerProvider.notifier);
  // Keep the provider alive as long as the controller exists
  final link = ref.keepAlive();
  ref.onDispose(() {
    link.close();
  });
  return player.durationStream;
}
