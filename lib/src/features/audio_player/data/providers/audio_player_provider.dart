import 'dart:async';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'audio_player_provider.g.dart';

@riverpod
class AudioPlayerController extends _$AudioPlayerController {
  AudioPlayer? _player;
  List<AudioTrack> _playlist = [];
  double _volume = 1.0;
  double get volume => _volume;
  int _currentIndex = 0;
  PlayerState _currentState = PlayerState.stopped;
  bool _isInitialized = false;
  final Map<String, Duration> _trackDurations = {};
  double _playbackSpeed = 1.0;
  double get playbackSpeed => _playbackSpeed;

  AudioPlayer? _durationCalculator;

  @override
  Future<AudioTrack?> build() async {
    await _initializePlayer();

    return _playlist.isEmpty ? null : _playlist[_currentIndex];
  }

  Future<void> setVolume(double volume) async {
    try {
      final newVolume = volume.clamp(0.0, 1.0);
      await _player?.setVolume(newVolume);
      _volume = newVolume;
      state = AsyncValue.data(currentTrack);
    } catch (e) {
      debugPrint('Error setting volume: $e');
    }
  }

  Future<void> _initializePlayer() async {
    if (_isInitialized) return;
    // ignore: unused_local_variable
    final link = ref.keepAlive(); // used to keep alive the provider
    _player = AudioPlayer();
    await _player?.setReleaseMode(ReleaseMode.stop);

    // Listen to state changes
    _player?.onPlayerStateChanged.listen((playerState) {
      _currentState = playerState;
      if (playerState == PlayerState.completed) {
        // If there's a next track, play it
        if (hasNext) {
          playNext();
        } else {
          // If no next track, update state to show play button
          _currentState = PlayerState.stopped;
          // Notify listeners by updating the state
          state = AsyncValue.data(currentTrack);
        }
      }
    });

    // Listen for completion separately to ensure we catch it
    _player?.onPlayerComplete.listen((_) {
      if (!hasNext) {
        _currentState = PlayerState.stopped;
        // Notify listeners by updating the state
        state = AsyncValue.data(currentTrack);
      }
    });

    _isInitialized = true;
    debugPrint('Player initialized');
  }

  Future<void> setPlaybackSpeed(double speed) async {
    try {
      await _player?.setPlaybackRate(speed);
      _playbackSpeed = speed;
      state = AsyncData(currentTrack!);
    } catch (e) {
      debugPrint('Error setting playback speed: $e');
    }
  }

  Future<Duration> getTrackDuration(AudioTrack track) async {
    try {
      // Return cached duration if available
      if (_trackDurations.containsKey(track.id)) {
        return _trackDurations[track.id]!;
      }

      // Create a new player instance for this calculation
      final durationPlayer = AudioPlayer();

      try {
        // Set up duration listener
        final completer = Completer<Duration>();
        StreamSubscription? subscription;

        subscription = durationPlayer.onDurationChanged.listen(
          (duration) {
            if (!completer.isCompleted) {
              _trackDurations[track.id] = duration;
              completer.complete(duration);
            }
            subscription?.cancel();
          },
          onError: (error) {
            if (!completer.isCompleted) {
              completer.completeError(error);
            }
            subscription?.cancel();
          },
        );

        // Set source and start loading
        await durationPlayer.setSource(AssetSource(track.audioUrl));

        // Wait for duration with timeout
        final duration = await completer.future.timeout(
          const Duration(seconds: 3),
          onTimeout: () {
            subscription?.cancel();
            return Duration.zero;
          },
        );

        return duration;
      } finally {
        // Clean up the temporary player
        await durationPlayer.dispose();
      }
    } catch (e) {
      debugPrint('Error calculating duration for track ${track.id}: $e');
      return Duration.zero;
    }
  }

  Future<void> preFetchDurations(List<AudioTrack> tracks) async {
    for (final track in tracks) {
      if (!_trackDurations.containsKey(track.id)) {
        try {
          final duration = await getTrackDuration(track);
          debugPrint('Pre-fetched duration for ${track.id}: $duration');
        } catch (e) {
          debugPrint('Failed to pre-fetch duration for ${track.id}: $e');
          // Continue with next track even if one fails
          continue;
        }
      }
    }
  }

  Future<void> playTracks(List<AudioTrack> tracks) async {
    if (tracks.isEmpty) return;
    try {
      debugPrint('Starting playTracks with ${tracks.length} tracks');
      _playlist = List.from(tracks);
      _currentIndex = 0;
      final currentTrack = _playlist[_currentIndex];
      debugPrint('Playing track: ${currentTrack.title}');

      await _player?.stop();

      // Check if it's a Firebase URL or local asset
      if (currentTrack.audioUrl.startsWith('http://') ||
          currentTrack.audioUrl.startsWith('https://')) {
        // Firebase URL - use UrlSource
        await _player?.play(UrlSource(currentTrack.audioUrl));
      } else {
        // Local asset - use AssetSource
        await _player?.play(AssetSource(currentTrack.audioUrl));
      }

      // Get and cache the duration after starting playback
      await getTrackDuration(currentTrack);

      state = AsyncValue.data(currentTrack);
    } catch (e, stack) {
      debugPrint('Error in playTracks: $e');
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> play() async {
    if (_player == null) return;
    await _player?.resume();
    state = AsyncValue.data(currentTrack);
  }

  Future<void> pause() async {
    if (_player == null) return;
    await _player?.pause();
    state = AsyncValue.data(currentTrack);
  }

  Future<void> playNext() async {
    if (_currentIndex < _playlist.length - 1) {
      _currentIndex++;
      final nextTrack = _playlist[_currentIndex];
      await _player?.stop();
      await _player?.play(AssetSource(nextTrack.audioUrl));
      state = AsyncValue.data(nextTrack);
    }
  }

  Future<void> playPrevious() async {
    if (_currentIndex > 0) {
      _currentIndex--;
      final previousTrack = _playlist[_currentIndex];
      await _player?.stop();
      await _player?.play(AssetSource(previousTrack.audioUrl));
      state = AsyncValue.data(previousTrack);
    }
  }

  Future<void> stopAndClear() async {
    if (_player == null) return;

    try {
      // Stop the player
      await _player?.stop();

      // Clear the playlist and reset index
      _playlist = [];
      _currentIndex = 0;
      _currentState = PlayerState.stopped;

      // Update the state to null since no track is playing
      state = const AsyncValue.data(null);

      debugPrint('Player stopped and cleared');
    } catch (e, stack) {
      debugPrint('Error in stopAndClear: $e');
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> seek(Duration position) async {
    await _player?.seek(position);
  }

  // Getters
  AudioTrack? get currentTrack =>
      _playlist.isEmpty ? null : _playlist[_currentIndex];

  List<AudioTrack> get playlist => List.unmodifiable(_playlist);
  bool get isPlaying => _currentState == PlayerState.playing;
  bool get hasNext => _currentIndex < _playlist.length - 1;
  bool get hasPrevious => _currentIndex > 0;

  Stream<Duration> get positionStream =>
      _player?.onPositionChanged ?? Stream.value(Duration.zero);

  Stream<Duration> get durationStream async* {
    if (_player != null && currentTrack != null) {
      // First try to get cached duration
      if (_trackDurations.containsKey(currentTrack!.id)) {
        yield _trackDurations[currentTrack!.id]!;
      }

      // Then listen to duration changes
      await for (final duration in _player!.onDurationChanged) {
        _trackDurations[currentTrack!.id] = duration; // Cache the duration
        yield duration;
      }
    } else {
      yield Duration.zero;
    }
  }

  // Add a method to get current track duration
  Future<Duration> getCurrentTrackDuration() async {
    if (currentTrack == null) return Duration.zero;
    return getTrackDuration(currentTrack!);
  }

  Stream<PlayerState> get playerStateStream => Stream.value(_currentState);

  void clearDurationCache() {
    _trackDurations.clear();
  }

  @override
  // ignore: override_on_non_overriding_member
  void dispose() {
    _player?.dispose();
    _durationCalculator?.dispose();
  }
}
