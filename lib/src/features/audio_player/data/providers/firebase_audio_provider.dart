// lib/src/features/audio_player/data/providers/firebase_audio_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/audio_category.dart';
import '../models/audio_track.dart';
import '../repositories/firebase_audio_repository.dart';

part 'firebase_audio_provider.g.dart';

// Provider for Firebase audio repository
@riverpod
FirebaseAudioRepository firebaseAudio(Ref ref) {
  return FirebaseAudioRepository();
}

// Provider for audio categories from Firebase
@riverpod
Future<List<AudioCategory>> audioCategories(Ref ref) async {
  final repository = ref.watch(firebaseAudioProvider);
  return repository.getCategories();
}

// Provider for tracks by category from Firebase
@riverpod
Future<List<AudioTrack>> tracksByCategory(Ref ref, String categoryId) async {
  final repository = ref.watch(firebaseAudioProvider);
  return repository.getTracksByCategory(categoryId);
}

// Provider for all tracks from Firebase
@riverpod
Future<List<AudioTrack>> allTracks(Ref ref) async {
  final repository = ref.watch(firebaseAudioProvider);
  return repository.getAllTracks();
}

// Provider for a specific track by ID
@riverpod
Future<AudioTrack> trackById(Ref ref, String trackId) async {
  final repository = ref.watch(firebaseAudioProvider);
  return repository.getTrackById(trackId);
}
