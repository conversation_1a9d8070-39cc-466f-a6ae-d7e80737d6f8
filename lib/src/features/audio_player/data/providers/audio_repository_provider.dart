// lib/features/audio_player/data/providers/audio_repository_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/audio_player/data/repositories/audio_repository.dart';
import 'package:mimi_app/src/features/timer/sound_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'audio_repository_provider.g.dart';

@riverpod
AudioRepository audioRepository(Ref ref) {
  final prefs = ref.watch(sharedPreferencesProvider);

  return AudioRepository(prefs);
}
