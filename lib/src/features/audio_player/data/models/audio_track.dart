// lib/features/audio_player/data/models/audio_track.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'audio_track.freezed.dart';
part 'audio_track.g.dart';

@freezed
class AudioTrack with _$AudioTrack {
  const factory AudioTrack({
    required String id,
    required String title,
    required String audioUrl,
    required String categoryId,
    // required Duration duration,
    String? artworkUrl,
    @Default(false) bool isDownloaded,
    String? localPath,
    @Default(false) bool isSelected, // Added this field
    @Default('') String description, // Added description field
    String? artist,
    int? durationInSeconds,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default(false)
    bool isAsset, // To distinguish between Firebase URLs and local assets
  }) = _AudioTrack;

  const AudioTrack._();

  factory AudioTrack.fromJson(Map<String, dynamic> json) =>
      _$AudioTrackFromJson(json);

  // Helper method to convert Firestore data
  factory AudioTrack.fromFirestore(Map<String, dynamic> data, String id) {
    return AudioTrack(
      id: id,
      title: data['title'] ?? '',
      audioUrl: data['audioUrl'] ?? '',
      categoryId: data['categoryId'] ?? '',
      artworkUrl: data['artworkUrl'],
      description: data['description'] ?? '',
      artist: data['artist'],
      durationInSeconds: data['durationInSeconds'],
      createdAt: data['createdAt']?.toDate(),
      updatedAt: data['updatedAt']?.toDate(),
      isAsset: false, // Firebase tracks are not assets
    );
  }

  // Helper method to convert to Firestore data
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'audioUrl': audioUrl,
      'categoryId': categoryId,
      'artworkUrl': artworkUrl,
      'description': description,
      'artist': artist,
      'durationInSeconds': durationInSeconds,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}
