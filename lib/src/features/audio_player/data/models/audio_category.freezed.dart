// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'audio_category.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AudioCategory _$AudioCategoryFromJson(Map<String, dynamic> json) {
  return _AudioCategory.fromJson(json);
}

/// @nodoc
mixin _$AudioCategory {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get iconPath => throw _privateConstructorUsedError;
  List<String> get trackIds => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get iconUrl => throw _privateConstructorUsedError;
  int get trackCount => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this AudioCategory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AudioCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AudioCategoryCopyWith<AudioCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AudioCategoryCopyWith<$Res> {
  factory $AudioCategoryCopyWith(
          AudioCategory value, $Res Function(AudioCategory) then) =
      _$AudioCategoryCopyWithImpl<$Res, AudioCategory>;
  @useResult
  $Res call(
      {String id,
      String name,
      String iconPath,
      List<String> trackIds,
      String? description,
      String? iconUrl,
      int trackCount,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$AudioCategoryCopyWithImpl<$Res, $Val extends AudioCategory>
    implements $AudioCategoryCopyWith<$Res> {
  _$AudioCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AudioCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? iconPath = null,
    Object? trackIds = null,
    Object? description = freezed,
    Object? iconUrl = freezed,
    Object? trackCount = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      iconPath: null == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String,
      trackIds: null == trackIds
          ? _value.trackIds
          : trackIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      trackCount: null == trackCount
          ? _value.trackCount
          : trackCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AudioCategoryImplCopyWith<$Res>
    implements $AudioCategoryCopyWith<$Res> {
  factory _$$AudioCategoryImplCopyWith(
          _$AudioCategoryImpl value, $Res Function(_$AudioCategoryImpl) then) =
      __$$AudioCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String iconPath,
      List<String> trackIds,
      String? description,
      String? iconUrl,
      int trackCount,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$AudioCategoryImplCopyWithImpl<$Res>
    extends _$AudioCategoryCopyWithImpl<$Res, _$AudioCategoryImpl>
    implements _$$AudioCategoryImplCopyWith<$Res> {
  __$$AudioCategoryImplCopyWithImpl(
      _$AudioCategoryImpl _value, $Res Function(_$AudioCategoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? iconPath = null,
    Object? trackIds = null,
    Object? description = freezed,
    Object? iconUrl = freezed,
    Object? trackCount = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$AudioCategoryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      iconPath: null == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String,
      trackIds: null == trackIds
          ? _value._trackIds
          : trackIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      trackCount: null == trackCount
          ? _value.trackCount
          : trackCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AudioCategoryImpl extends _AudioCategory {
  const _$AudioCategoryImpl(
      {required this.id,
      required this.name,
      required this.iconPath,
      required final List<String> trackIds,
      this.description,
      this.iconUrl,
      this.trackCount = 0,
      this.createdAt,
      this.updatedAt})
      : _trackIds = trackIds,
        super._();

  factory _$AudioCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$AudioCategoryImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String iconPath;
  final List<String> _trackIds;
  @override
  List<String> get trackIds {
    if (_trackIds is EqualUnmodifiableListView) return _trackIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_trackIds);
  }

  @override
  final String? description;
  @override
  final String? iconUrl;
  @override
  @JsonKey()
  final int trackCount;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'AudioCategory(id: $id, name: $name, iconPath: $iconPath, trackIds: $trackIds, description: $description, iconUrl: $iconUrl, trackCount: $trackCount, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AudioCategoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            const DeepCollectionEquality().equals(other._trackIds, _trackIds) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.trackCount, trackCount) ||
                other.trackCount == trackCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      iconPath,
      const DeepCollectionEquality().hash(_trackIds),
      description,
      iconUrl,
      trackCount,
      createdAt,
      updatedAt);

  /// Create a copy of AudioCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AudioCategoryImplCopyWith<_$AudioCategoryImpl> get copyWith =>
      __$$AudioCategoryImplCopyWithImpl<_$AudioCategoryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AudioCategoryImplToJson(
      this,
    );
  }
}

abstract class _AudioCategory extends AudioCategory {
  const factory _AudioCategory(
      {required final String id,
      required final String name,
      required final String iconPath,
      required final List<String> trackIds,
      final String? description,
      final String? iconUrl,
      final int trackCount,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$AudioCategoryImpl;
  const _AudioCategory._() : super._();

  factory _AudioCategory.fromJson(Map<String, dynamic> json) =
      _$AudioCategoryImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get iconPath;
  @override
  List<String> get trackIds;
  @override
  String? get description;
  @override
  String? get iconUrl;
  @override
  int get trackCount;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of AudioCategory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AudioCategoryImplCopyWith<_$AudioCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
