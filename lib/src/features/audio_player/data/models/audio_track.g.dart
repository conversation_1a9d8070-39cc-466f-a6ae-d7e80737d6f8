// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_track.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AudioTrackImpl _$$AudioTrackImplFromJson(Map<String, dynamic> json) =>
    _$AudioTrackImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      audioUrl: json['audioUrl'] as String,
      categoryId: json['categoryId'] as String,
      artworkUrl: json['artworkUrl'] as String?,
      isDownloaded: json['isDownloaded'] as bool? ?? false,
      localPath: json['localPath'] as String?,
      isSelected: json['isSelected'] as bool? ?? false,
      description: json['description'] as String? ?? '',
      artist: json['artist'] as String?,
      durationInSeconds: (json['durationInSeconds'] as num?)?.toInt(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      isAsset: json['isAsset'] as bool? ?? false,
    );

Map<String, dynamic> _$$AudioTrackImplToJson(_$AudioTrackImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'audioUrl': instance.audioUrl,
      'categoryId': instance.categoryId,
      'artworkUrl': instance.artworkUrl,
      'isDownloaded': instance.isDownloaded,
      'localPath': instance.localPath,
      'isSelected': instance.isSelected,
      'description': instance.description,
      'artist': instance.artist,
      'durationInSeconds': instance.durationInSeconds,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'isAsset': instance.isAsset,
    };
