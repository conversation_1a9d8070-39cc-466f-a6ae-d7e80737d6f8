// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_category.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AudioCategoryImpl _$$AudioCategoryImplFromJson(Map<String, dynamic> json) =>
    _$AudioCategoryImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      iconPath: json['iconPath'] as String,
      trackIds:
          (json['trackIds'] as List<dynamic>).map((e) => e as String).toList(),
      description: json['description'] as String?,
      iconUrl: json['iconUrl'] as String?,
      trackCount: (json['trackCount'] as num?)?.toInt() ?? 0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$AudioCategoryImplToJson(_$AudioCategoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'iconPath': instance.iconPath,
      'trackIds': instance.trackIds,
      'description': instance.description,
      'iconUrl': instance.iconUrl,
      'trackCount': instance.trackCount,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
