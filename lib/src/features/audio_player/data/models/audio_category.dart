// lib/features/audio_player/data/models/audio_category.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'audio_category.freezed.dart';
part 'audio_category.g.dart';

@freezed
class AudioCategory with _$AudioCategory {
  const factory AudioCategory({
    required String id,
    required String name,
    required String iconPath,
    required List<String> trackIds,
    String? description,
    String? iconUrl,
    @Default(0) int trackCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _AudioCategory;

  const AudioCategory._();

  factory AudioCategory.fromJson(Map<String, dynamic> json) =>
      _$AudioCategoryFromJson(json);

  // Helper method to convert Firestore data
  factory AudioCategory.fromFirestore(Map<String, dynamic> data, String id) {
    return AudioCategory(
      id: id,
      name: data['name'] ?? '',
      iconPath: data['iconPath'] ?? '',
      trackIds: List<String>.from(data['trackIds'] ?? []),
      description: data['description'],
      iconUrl: data['iconUrl'],
      trackCount: data['trackCount'] ?? 0,
      createdAt: data['createdAt']?.toDate(),
      updatedAt: data['updatedAt']?.toDate(),
    );
  }

  // Helper method to convert to Firestore data
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'iconPath': iconPath,
      'trackIds': trackIds,
      'description': description,
      'iconUrl': iconUrl,
      'trackCount': trackCount,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}
