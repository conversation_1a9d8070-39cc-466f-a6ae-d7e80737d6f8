// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_audio_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$firebaseAudioRepositoryHash() =>
    r'08e0c50cf44163b45cc0466a5652d0cf33f43ef2';

/// See also [firebaseAudioRepository].
@ProviderFor(firebaseAudioRepository)
final firebaseAudioRepositoryProvider =
    AutoDisposeProvider<FirebaseAudioRepository>.internal(
  firebaseAudioRepository,
  name: r'firebaseAudioRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseAudioRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FirebaseAudioRepositoryRef
    = AutoDisposeProviderRef<FirebaseAudioRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
