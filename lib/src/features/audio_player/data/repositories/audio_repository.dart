// lib/features/audio_player/data/repositories/audio_repository.dart
import 'package:collection/collection.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_category.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';
import 'package:mimi_app/src/features/audio_player/data/repositories/audio_repository_interface.dart';

import 'package:shared_preferences/shared_preferences.dart';

class AudioRepository implements IAudioRepository {
  final SharedPreferences _prefs;

  // Keys for SharedPreferences
  static const String _playHistoryKey = 'play_history';
  static const String _downloadedTracksKey = 'downloaded_tracks';
  static const String _mostPlayedTracksKey = 'most_played_tracks';

  AudioRepository(this._prefs);

  @override
  Future<List<AudioCategory>> getCategories() async {
    // In a real app, this would come from Firebase
    // For now, we'll return mock data
    return [
      AudioCategory(
        id: 'meditation',
        name: 'Meditation',
        iconPath: 'assets/images/cover/image_10.png',
        trackIds: [
          'med1',
          //'med2',
        ],
        description: 'Peaceful meditation tracks',
      ),
      AudioCategory(
        id: 'affirmations',
        name: 'Affirmations',
        iconPath: 'assets/images/cover/image_11.png',
        trackIds: [
          'aff1',
          //'aff2',
        ],
        description: 'Daily Affirmations',
      ),
      AudioCategory(
        id: 'sleep',
        name: 'Sleep',
        iconPath: 'assets/images/cover/image_9.png',
        trackIds: ['sleep1', 'sleep2'],
        description: 'Relaxing sleep sounds',
      ),
      AudioCategory(
        id: 'breathwork',
        name: 'Breathwork Music',
        iconPath: 'assets/images/cover/image_8.png',
        trackIds: ['breath1', 'breath2'],
        description: 'Background music for breathwork sessions',
      ),
      // Add more categories as needed
    ];
  }

  @override
  Future<AudioCategory> getCategoryById(String id) async {
    final categories = await getCategories();
    return categories.firstWhere(
      (category) => category.id == id,
      orElse: () => throw Exception('Category not found'),
    );
  }

  @override
  Future<List<AudioTrack>> getTracksByCategory(String categoryId) async {
    // In a real app, this would be a Firebase query
    // For now, we'll return mock data
    final mockTracks = {
      'meditation': [
        AudioTrack(
          id: 'med1',
          title: "Ho'oponopono Prayer",
          audioUrl: 'assets/audio/meditation/meditationhoponopono.mp3',
          artworkUrl: 'assets/images/cover/image_2.png',
          categoryId: 'meditation',
          description: 'Meditation • Relaxing • Calm',
        ),
        AudioTrack(
          id: 'med2',
          title: "Abundance Meditation",
          audioUrl: 'assets/audio/meditation/meditationabundance.mp3',
          artworkUrl: 'assets/images/cover/image_9.png',
          categoryId: 'meditation',
          description: 'Meditation • Relaxing • Calm',
        ),
        AudioTrack(
          id: 'med3',
          title: "Letting Go Meditation",
          audioUrl: 'assets/audio/meditation/meditationlettinggo.mp3',
          artworkUrl: 'assets/images/cover/image_8.png',
          categoryId: 'meditation',
          description: 'Meditation • Relaxing • Calm',
        ),
        AudioTrack(
          id: 'med4',
          title: "Self-Love Meditation",
          audioUrl: 'assets/audio/meditation/meditationselflove.mp3',
          artworkUrl: 'assets/images/cover/image_13.png',
          categoryId: 'meditation',
          description: 'Meditation • Relaxing • Calm',
        ),
      ],
      'affirmations': [
        AudioTrack(
          id: 'aff1',
          title: 'Complete Daily Affirmations',
          audioUrl: 'assets/audio/affirmations/affirmationcombined.mp3',
          artworkUrl: 'assets/images/cover/image_14.png',
          categoryId: 'affirmations',
          description: 'Positive • Uplifting • Daily',
        ),
        AudioTrack(
          id: 'aff2',
          title: 'Financial Affirmations',
          audioUrl: 'assets/audio/affirmations/affirmationfinancial.mp3',
          artworkUrl: 'assets/images/cover/image_12.png',
          categoryId: 'affirmations',
          description: 'Positive • Uplifting • Daily',
        ),
        AudioTrack(
          id: 'aff3',
          title: 'Relationship Affirmations',
          audioUrl: 'assets/audio/affirmations/affirmationrelationship.mp3',
          artworkUrl: 'assets/images/cover/image_5.png',
          categoryId: 'affirmations',
          description: 'Positive • Uplifting • Daily',
        ),
        AudioTrack(
          id: 'aff4',
          title: 'Career Growth Affirmations',
          audioUrl: 'assets/audio/affirmations/affirmationcareergrowth.mp3',
          artworkUrl: 'assets/images/cover/image_2.png',
          categoryId: 'affirmations',
          description: 'Positive • Uplifting • Daily',
        ),
      ],
      'breathwork': [
        AudioTrack(
          id: 'breath1',
          title: 'Flow and Rhythm',
          audioUrl: 'assets/audio/breathwork/flowandrhythm.mp3',
          artworkUrl: 'assets/images/cover/image_3.png',
          categoryId: 'breathwork',
          description: 'Ambient • Peaceful • Calm',
        ),
        AudioTrack(
          id: 'breath2',
          title: 'Gentle Awakening',
          audioUrl: 'assets/audio/breathwork/gentleawakening.mp3',
          artworkUrl: 'assets/images/cover/image_10.png',
          categoryId: 'breathwork',
          description: 'Meditation • Calm • Focus',
        ),
        AudioTrack(
          id: 'breath3',
          title: 'Midnight Forest',
          audioUrl: 'assets/audio/breathwork/midnightforest.mp3',
          artworkUrl: 'assets/images/cover/image_3.png',
          categoryId: 'breathwork',
          description: 'Ambient • Peaceful • Calm',
        ),
        AudioTrack(
          id: 'breath4',
          title: 'Piano Serenity',
          audioUrl: 'assets/audio/breathwork/pianoserenity.mp3',
          artworkUrl: 'assets/images/cover/image_10.png',
          categoryId: 'breathwork',
          description: 'Meditation • Calm • Focus',
        ),
        AudioTrack(
          id: 'breath5',
          title: 'Silent Waves',
          audioUrl: 'assets/audio/breathwork/silentwaves.mp3',
          artworkUrl: 'assets/images/cover/image_3.png',
          categoryId: 'breathwork',
          description: 'Ambient • Peaceful • Calm',
        ),
      ],
      'sleep': [
        AudioTrack(
          id: 'sleep1',
          title: 'Rain Sounds',
          audioUrl: 'assets/audio/meditation/meditationselflove.mp3',
          artworkUrl: 'assets/images/cover/image_11.png',
          categoryId: 'sleep',
          description: 'Sleep • Relaxing • Nature',
        ),
        // Add more tracks
      ],
    };

    return mockTracks[categoryId] ?? [];
  }

  @override
  Future<AudioTrack> getTrackById(String id) async {
    // In a real app, this would be a Firebase query
    final allCategories = await getCategories();
    for (final category in allCategories) {
      final tracks = await getTracksByCategory(category.id);
      final track = tracks.firstWhere(
        (track) => track.id == id,
        orElse: () => throw Exception('Track not found'),
      );
      return track;
    }
    throw Exception('Track not found');
  }

  @override
  Future<List<AudioTrack>> getAllTracks() async {
    final allTracks = <AudioTrack>[];
    final categories = await getCategories();

    for (final category in categories) {
      final tracks = await getTracksByCategory(category.id);
      allTracks.addAll(tracks);
    }

    return allTracks;
  }

  @override
  Future<void> updateTrackDownloadStatus(
    String trackId,
    bool isDownloaded, {
    String? localPath,
  }) async {
    final downloadedTracks = _prefs.getStringList(_downloadedTracksKey) ?? [];

    if (isDownloaded) {
      if (!downloadedTracks.contains(trackId)) {
        downloadedTracks.add(trackId);
        await _prefs.setString('track_path_$trackId', localPath ?? '');
      }
    } else {
      downloadedTracks.remove(trackId);
      await _prefs.remove('track_path_$trackId');
    }

    await _prefs.setStringList(_downloadedTracksKey, downloadedTracks);
  }

  @override
  Future<void> savePlayHistory(String trackId, Duration playedDuration) async {
    final history = _prefs.getStringList(_playHistoryKey) ?? [];
    final timestamp = DateTime.now().toIso8601String();

    // Save in format: trackId|timestamp|duration
    history.add('$trackId|$timestamp|${playedDuration.inSeconds}');

    // Keep only last 100 entries
    if (history.length > 100) {
      history.removeRange(0, history.length - 100);
    }

    await _prefs.setStringList(_playHistoryKey, history);
    await _updateMostPlayedTracks(trackId);
  }

  Future<void> _updateMostPlayedTracks(String trackId) async {
    final mostPlayed = Map<String, int>.from(
      _prefs.getStringList(_mostPlayedTracksKey)?.asMap() ?? {},
    );

    mostPlayed[trackId] = (mostPlayed[trackId] ?? 0) + 1;

    // Sort by play count and keep top 20
    final sortedTracks = mostPlayed.entries
        .sorted((a, b) => b.value.compareTo(a.value))
        .take(20)
        .map((e) => e.key)
        .toList();

    await _prefs.setStringList(_mostPlayedTracksKey, sortedTracks);
  }

  @override
  Future<List<String>> getMostPlayedTrackIds() async {
    return _prefs.getStringList(_mostPlayedTracksKey) ?? [];
  }
}
