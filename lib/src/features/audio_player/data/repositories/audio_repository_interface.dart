// lib/features/audio_player/domain/repositories/audio_repository_interface.dart
import 'package:mimi_app/src/features/audio_player/data/models/audio_category.dart';
import 'package:mimi_app/src/features/audio_player/data/models/audio_track.dart';

abstract class IAudioRepository {
  Future<List<AudioCategory>> getCategories();
  Future<AudioCategory> getCategoryById(String id);
  Future<List<AudioTrack>> getTracksByCategory(String categoryId);
  Future<AudioTrack> getTrackById(String id);
  Future<List<AudioTrack>> getAllTracks();
  Future<void> updateTrackDownloadStatus(String trackId, bool isDownloaded,
      {String? localPath});
  Future<void> savePlayHistory(String trackId, Duration playedDuration);
  Future<List<String>> getMostPlayedTrackIds();
}

// New abstract class for Firebase operations
abstract class AudioRepository {
  Future<List<AudioCategory>> getCategories();
  Future<List<AudioTrack>> getTracksByCategory(String categoryId);
  Future<AudioTrack> getTrackById(String id);
  Future<List<AudioTrack>> getAllTracks();
}
