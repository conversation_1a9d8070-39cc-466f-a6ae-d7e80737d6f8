// lib/src/features/audio_player/data/repositories/firebase_audio_repository.dart
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/audio_category.dart';
import '../models/audio_track.dart';
import 'audio_repository.dart';

part 'firebase_audio_repository.g.dart';

@riverpod
FirebaseAudioRepository firebaseAudioRepository(Ref ref) {
  return FirebaseAudioRepository();
}

class FirebaseAudioRepository implements AudioRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collection names
  static const String _categoriesCollection = 'audio_categories';
  static const String _tracksCollection = 'audio_tracks';

  @override
  Future<List<AudioCategory>> getCategories() async {
    try {
      final snapshot = await _firestore
          .collection(_categoriesCollection)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => AudioCategory.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error fetching categories: $e');
      return [];
    }
  }

  @override
  Future<List<AudioTrack>> getTracksByCategory(String categoryId) async {
    try {
      final snapshot = await _firestore
          .collection(_tracksCollection)
          .where('categoryId', isEqualTo: categoryId)
          .orderBy('title')
          .get();

      return snapshot.docs
          .map((doc) => AudioTrack.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error fetching tracks for category $categoryId: $e');
      return [];
    }
  }

  @override
  Future<AudioTrack> getTrackById(String id) async {
    try {
      final doc = await _firestore.collection(_tracksCollection).doc(id).get();

      if (!doc.exists) {
        throw Exception('Track not found');
      }

      return AudioTrack.fromFirestore(doc.data()!, doc.id);
    } catch (e) {
      debugPrint('Error fetching track $id: $e');
      rethrow;
    }
  }

  @override
  Future<List<AudioTrack>> getAllTracks() async {
    try {
      final snapshot =
          await _firestore.collection(_tracksCollection).orderBy('title').get();

      return snapshot.docs
          .map((doc) => AudioTrack.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error fetching all tracks: $e');
      return [];
    }
  }

  // Missing interface methods implementation
  @override
  Future<AudioCategory> getCategoryById(String id) async {
    try {
      final doc =
          await _firestore.collection(_categoriesCollection).doc(id).get();

      if (!doc.exists) {
        throw Exception('Category not found');
      }

      return AudioCategory.fromFirestore(doc.data()!, doc.id);
    } catch (e) {
      debugPrint('Error fetching category $id: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateTrackDownloadStatus(String trackId, bool isDownloaded,
      {String? localPath}) async {
    // For Firebase implementation, we might not need to track download status
    // But we can implement it if needed for offline functionality
    debugPrint(
        'updateTrackDownloadStatus called for track $trackId: $isDownloaded');
  }

  @override
  Future<void> savePlayHistory(String trackId, Duration playedDuration) async {
    // For Firebase implementation, we could save play history to Firestore
    // For now, just log it
    debugPrint(
        'savePlayHistory called for track $trackId: ${playedDuration.inSeconds}s');
  }

  @override
  Future<List<String>> getMostPlayedTrackIds() async {
    // For Firebase implementation, we could query play history
    // For now, return empty list
    return [];
  }

  // Admin methods for managing audio content
  Future<String> uploadAudioFile(File audioFile, String fileName) async {
    try {
      final ref = _storage.ref().child('audio/$fileName');
      final uploadTask = await ref.putFile(audioFile);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Error uploading audio file: $e');
      rethrow;
    }
  }

  Future<String> uploadArtworkFile(File artworkFile, String fileName) async {
    try {
      final ref = _storage.ref().child('artwork/$fileName');
      final uploadTask = await ref.putFile(artworkFile);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Error uploading artwork file: $e');
      rethrow;
    }
  }

  Future<void> createCategory(AudioCategory category) async {
    try {
      await _firestore
          .collection(_categoriesCollection)
          .doc(category.id)
          .set(category.toFirestore());
    } catch (e) {
      debugPrint('Error creating category: $e');
      rethrow;
    }
  }

  Future<void> updateCategory(AudioCategory category) async {
    try {
      await _firestore
          .collection(_categoriesCollection)
          .doc(category.id)
          .update(category.toFirestore());
    } catch (e) {
      debugPrint('Error updating category: $e');
      rethrow;
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    try {
      // First delete all tracks in this category
      final tracks = await getTracksByCategory(categoryId);
      for (final track in tracks) {
        await deleteTrack(track.id);
      }

      // Then delete the category
      await _firestore
          .collection(_categoriesCollection)
          .doc(categoryId)
          .delete();
    } catch (e) {
      debugPrint('Error deleting category: $e');
      rethrow;
    }
  }

  Future<void> createTrack(AudioTrack track) async {
    try {
      await _firestore
          .collection(_tracksCollection)
          .doc(track.id)
          .set(track.toFirestore());

      // Update category track count
      await _updateCategoryTrackCount(track.categoryId);
    } catch (e) {
      debugPrint('Error creating track: $e');
      rethrow;
    }
  }

  Future<void> updateTrack(AudioTrack track) async {
    try {
      await _firestore
          .collection(_tracksCollection)
          .doc(track.id)
          .update(track.toFirestore());
    } catch (e) {
      debugPrint('Error updating track: $e');
      rethrow;
    }
  }

  Future<void> deleteTrack(String trackId) async {
    try {
      final track = await getTrackById(trackId);

      // Delete the track document
      await _firestore.collection(_tracksCollection).doc(trackId).delete();

      // Update category track count
      await _updateCategoryTrackCount(track.categoryId);

      // Optionally delete the audio and artwork files from storage
      // This is commented out to prevent accidental deletion
      // await _deleteStorageFiles(track);
    } catch (e) {
      debugPrint('Error deleting track: $e');
      rethrow;
    }
  }

  Future<void> _updateCategoryTrackCount(String categoryId) async {
    try {
      final tracks = await getTracksByCategory(categoryId);
      await _firestore
          .collection(_categoriesCollection)
          .doc(categoryId)
          .update({'trackCount': tracks.length});
    } catch (e) {
      debugPrint('Error updating category track count: $e');
    }
  }
}
