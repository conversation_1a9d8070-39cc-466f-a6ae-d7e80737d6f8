// lib/src/services/sound_service.dart
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/timer/timer_sound_config.dart';
import 'package:shared_preferences/shared_preferences.dart';

final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('sharedPreferencesProvider not initialized');
});

// final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
//   throw UnimplementedError('Provider was not initialized');
// });

final soundServiceProvider = Provider<SoundService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return SoundService(prefs);
});

class SoundService {
  final SharedPreferences _prefs;
  final AudioPlayer _audioPlayer = AudioPlayer();

  SoundService(this._prefs);

  static const String _workSoundKey = 'work_sound_path';
  static const String _breakSoundKey = 'break_sound_path';

  String? get workSoundPath => _prefs.getString(_workSoundKey);
  String? get breakSoundPath => _prefs.getString(_breakSoundKey);

  Future<void> setSound(TimerSoundType type, String? assetPath) async {
    switch (type) {
      case TimerSoundType.workComplete:
        if (assetPath == null) {
          await _prefs.remove(_workSoundKey);
        } else {
          await _prefs.setString(_workSoundKey, assetPath);
        }
        break;
      case TimerSoundType.breakComplete:
        if (assetPath == null) {
          await _prefs.remove(_breakSoundKey);
        } else {
          await _prefs.setString(_breakSoundKey, assetPath);
        }
        break;
    }
  }

  Future<void> playSound(TimerSoundType type) async {
    final soundPath =
        type == TimerSoundType.workComplete ? workSoundPath : breakSoundPath;
    if (soundPath != null) {
      await _audioPlayer.play(AssetSource(soundPath));
    }
  }

  Future<void> previewSound(String? assetPath) async {
    if (assetPath != null) {
      await _audioPlayer.play(AssetSource(assetPath));
    }
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}
