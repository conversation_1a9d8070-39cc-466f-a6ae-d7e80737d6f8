import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/timer/timer.dart';

class WeeklyInsightsWidget extends ConsumerWidget {
  const WeeklyInsightsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return StreamBuilder<QuerySnapshot>(
      stream: _getWeeklySessionsStream(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return const Center(child: Text('Something went wrong'));
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final sessions = snapshot.data!.docs
            .map((doc) =>
                PomodoroSession.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        final taskStats = _calculateTaskStats(sessions);
        final totalHours = _calculateTotalHours(sessions);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Focus Sessions This Week: ${totalHours.toStringAsFixed(1)} hrs',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  'Session Details:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ...taskStats.entries.map((entry) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Text(
                        '${entry.key}: ${entry.value.toStringAsFixed(1)} hrs',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  Stream<QuerySnapshot> _getWeeklySessionsStream() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return Stream.empty();

    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartTimestamp = Timestamp.fromDate(weekStart);

    return FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .collection('pomodoro_sessions')
        .where('startTime', isGreaterThanOrEqualTo: weekStartTimestamp)
        .orderBy('startTime', descending: true)
        .snapshots();
  }

  Map<String, double> _calculateTaskStats(List<PomodoroSession> sessions) {
    final taskStats = <String, double>{};

    for (final session in sessions) {
      final hours =
          session.workDurationMinutes * session.completedCycles / 60.0;
      taskStats[session.taskDescription] =
          (taskStats[session.taskDescription] ?? 0) + hours;
    }

    return taskStats;
  }

  double _calculateTotalHours(List<PomodoroSession> sessions) {
    return sessions.fold(
        0,
        (sum, session) =>
            sum +
            (session.workDurationMinutes * session.completedCycles / 60.0));
  }
}
