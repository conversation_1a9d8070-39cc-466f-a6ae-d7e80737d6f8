import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/timer/pomodoro_notifier.dart';
import 'package:mimi_app/src/features/timer/settings_dialog.dart';
import 'package:mimi_app/src/features/timer/task_input_dialog.dart';

class PomodoroScreen extends ConsumerWidget {
  const PomodoroScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pomodoroState = ref.watch(pomodoroNotifierProvider);
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    //final timerSize = screenSize.width * 0.8;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Focus Timer'),
        leading: const BackButton(),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Center(
                  child: Text(
                    'I am focusing on:',
                    style: theme.textTheme.titleLarge,
                  ),
                ),
                if (pomodoroState.currentTask.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      pomodoroState.currentTask,
                      style: theme.textTheme.headlineSmall,
                      textAlign: TextAlign.center,
                    ),
                  ),
                SizedBox(height: screenSize.height * 0.15),
                Center(
                  child: SizedBox(
                    width: 300,
                    height: 300,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          width: 300,
                          height: 300,
                          child: CircularProgressIndicator(
                            value: pomodoroState.remainingSeconds /
                                (pomodoroState.workDurationMinutes * 60),
                            strokeWidth: 16,
                            strokeCap: StrokeCap.round,
                            backgroundColor:
                                theme.colorScheme.primary.withOpacity(0.2),
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${(pomodoroState.remainingSeconds ~/ 60).toString().padLeft(2, '0')}:${(pomodoroState.remainingSeconds % 60).toString().padLeft(2, '0')}',
                              style: theme.textTheme.displayLarge,
                            ),
                            Text(
                              pomodoroState.isWorkTime
                                  ? 'Work Time'
                                  : 'Break Time',
                              style: theme.textTheme.titleMedium,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: screenSize.height * 0.05),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      onPressed: () => ref
                          .read(pomodoroNotifierProvider.notifier)
                          .resetTimer(),
                      icon: const Icon(Icons.replay),
                      iconSize: 32,
                    ),
                    const SizedBox(width: 20),
                    FloatingActionButton(
                      onPressed: () {
                        if (!pomodoroState.isRunning &&
                            pomodoroState.currentTask.isEmpty) {
                          _showTaskInputDialog(context);
                        } else {
                          pomodoroState.isRunning
                              ? ref
                                  .read(pomodoroNotifierProvider.notifier)
                                  .pauseTimer()
                              : ref
                                  .read(pomodoroNotifierProvider.notifier)
                                  .startTimer();
                        }
                      },
                      child: Icon(
                        pomodoroState.isRunning
                            ? Icons.pause
                            : Icons.play_arrow,
                      ),
                    ),
                    const SizedBox(width: 20),
                    IconButton(
                      onPressed: () => _showSettingsDialog(context, ref),
                      icon: const Icon(Icons.settings),
                      iconSize: 32,
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Text(
                  'Completed Cycles: ${pomodoroState.completedCycles}',
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showTaskInputDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const TaskInputDialog(),
    );
  }

  void _showSettingsDialog(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const SettingsBottomSheet(),
    );
  }
}

// lib/src/features/timer/settings_dialog.dart

// lib/src/features/timer/settings_dialog.dart
