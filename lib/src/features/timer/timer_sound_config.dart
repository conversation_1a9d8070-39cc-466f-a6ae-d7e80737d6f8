// enum TimerSoundType {
//   workComplete,
//   breakComplete;

//   String get displayName {
//     switch (this) {
//       case TimerSoundType.workComplete:
//         return 'Work Complete';
//       case TimerSoundType.breakComplete:
//         return 'Break Complete';
//     }
//   }
// }

// class SoundOption {
//   final String name;
//   final String assetPath;

//   const SoundOption({
//     required this.name,
//     required this.assetPath,
//   });
// }

// class TimerSoundConfig {
//   static const List<SoundOption> availableSounds = [
//     SoundOption(
//       name: 'Bell',
//       assetPath: 'assets/audio/sounds/bell.mp3',
//     ),
//     SoundOption(
//       name: 'Chime',
//       assetPath: 'assets/audio/sounds/chime.mp3',
//     ),
//     SoundOption(
//       name: 'Gentle',
//       assetPath: 'assets/audio/sounds/gentle.mp3',
//     ),
//     SoundOption(
//       name: 'Digital',
//       assetPath: 'assets/audio/sounds/digital.mp3',
//     ),
//   ];
// }

// lib/src/features/timer/models/sound_config.dart
enum TimerSoundType {
  workComplete,
  breakComplete;

  String get displayName {
    switch (this) {
      case TimerSoundType.workComplete:
        return 'Work Complete';
      case TimerSoundType.breakComplete:
        return 'Break Complete';
    }
  }
}

class SoundOption {
  final String name;
  final String? assetPath; // Make assetPath nullable to support "No Sound"

  const SoundOption({
    required this.name,
    this.assetPath,
  });

  bool get isNoSound => assetPath == null;
}

class TimerSoundConfig {
  static const List<SoundOption> availableSounds = [
    SoundOption(
      name: 'No Sound',
      assetPath: null,
    ),
    SoundOption(
      name: 'Bell',
      assetPath: 'assets/sounds/bell.mp3',
    ),
    SoundOption(
      name: 'Chime',
      assetPath: 'assets/sounds/chime.mp3',
    ),
    SoundOption(
      name: 'Gentle',
      assetPath: 'assets/sounds/gentle.mp3',
    ),
    SoundOption(
      name: 'Digital',
      assetPath: 'assets/sounds/digital.mp3',
    ),
  ];
}
