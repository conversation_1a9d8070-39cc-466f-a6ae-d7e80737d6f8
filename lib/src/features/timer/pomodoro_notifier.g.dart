// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pomodoro_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pomodoroNotifierHash() => r'31a957df7eb088a82517d4860ff5a8d373ae7a99';

/// See also [PomodoroNotifier].
@ProviderFor(PomodoroNotifier)
final pomodoroNotifierProvider =
    AutoDisposeNotifierProvider<PomodoroNotifier, PomodoroState>.internal(
  PomodoroNotifier.new,
  name: r'pomodoroNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pomodoroNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PomodoroNotifier = AutoDisposeNotifier<PomodoroState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
