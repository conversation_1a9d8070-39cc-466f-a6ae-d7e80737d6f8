// lib/models/pomodoro_session.dart
class PomodoroSession {
  final String id;
  final String userId;
  final String taskDescription;
  final DateTime startTime;
  final DateTime endTime;
  final int workDurationMinutes;
  final int restDurationMinutes;
  final int completedCycles;

  PomodoroSession({
    required this.id,
    required this.userId,
    required this.taskDescription,
    required this.startTime,
    required this.endTime,
    required this.workDurationMinutes,
    required this.restDurationMinutes,
    required this.completedCycles,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'taskDescription': taskDescription,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'workDurationMinutes': workDurationMinutes,
      'restDurationMinutes': restDurationMinutes,
      'completedCycles': completedCycles,
    };
  }

  factory PomodoroSession.fromJson(Map<String, dynamic> json) {
    return PomodoroSession(
      id: json['id'],
      userId: json['userId'],
      taskDescription: json['taskDescription'],
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      workDurationMinutes: json['workDurationMinutes'],
      restDurationMinutes: json['restDurationMinutes'],
      completedCycles: json['completedCycles'],
    );
  }
}
