import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/timer/pomodoro_notifier.dart';
import 'package:mimi_app/src/features/timer/sound_service.dart';
import 'package:mimi_app/src/features/timer/timer_sound_config.dart';

class SettingsBottomSheet extends ConsumerStatefulWidget {
  const SettingsBottomSheet({super.key});

  @override
  SettingsBottomSheetState createState() => SettingsBottomSheetState();
}

class SettingsBottomSheetState extends ConsumerState<SettingsBottomSheet> {
  int _selectedTabIndex = 0;
  int _workDurationMinutes = 25;
  int _restDurationMinutes = 5;

  @override
  void initState() {
    super.initState();

    final state = ref.read(pomodoroNotifierProvider);
    _workDurationMinutes = state.workDurationMinutes;
    _restDurationMinutes = state.restDurationMinutes;
  }

  Future<void> _playSound(String assetPath) async {
    final player = AudioPlayer();
    await player.play(AssetSource(assetPath));
    await Future.delayed(const Duration(seconds: 1));
    await player.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            decoration: BoxDecoration(
              color: AppColors.border,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Timer Settings',
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),

          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Row(
              children: [
                _buildTab('Timer Settings', 0),
                _buildTab('Sound Settings', 1),
              ],
            ),
          ),

          // Tab Content
          Flexible(
            child: SizedBox(
              height: 275,
              child: IndexedStack(
                index: _selectedTabIndex,
                children: [
                  _buildTimerSettingsTab(),
                  _buildSoundSettingsTab(),
                ],
              ),
            ),
          ),

          // Action Buttons
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _saveSettings,
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(100, 50),
                  ),
                  child: const Text('Save'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTabIndex = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(30),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: AppTypography.medium,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimerSettingsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),

          // Work Duration Selector
          const Text(
            'Work Duration',
            style: TextStyle(
              fontSize: AppTypography.bodySmall,
              fontWeight: AppTypography.medium,
            ),
          ),
          const SizedBox(height: 8),
          _buildDurationSelector(
            currentValue: _workDurationMinutes,
            minValue: 5,
            maxValue: 90,
            onChanged: (value) {
              setState(() {
                _workDurationMinutes = value;
              });
            },
          ),

          const SizedBox(height: 24),

          // Rest Duration Selector
          const Text(
            'Rest Duration',
            style: TextStyle(
              fontSize: AppTypography.bodySmall,
              fontWeight: AppTypography.medium,
            ),
          ),
          const SizedBox(height: 8),
          _buildDurationSelector(
            currentValue: _restDurationMinutes,
            minValue: 5,
            maxValue: 30,
            onChanged: (value) {
              setState(() {
                _restDurationMinutes = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDurationSelector({
    required int currentValue,
    required int minValue,
    required int maxValue,
    required ValueChanged<int> onChanged,
  }) {
    // Generate list of values in multiples of 5
    final values = <int>[];
    for (int i = minValue; i <= maxValue; i += 5) {
      values.add(i);
    }

    final currentIndex = values.indexOf(currentValue);
    final canGoLeft = currentIndex > 0;
    final canGoRight = currentIndex < values.length - 1;

    return SizedBox(
      height: 60,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Left Arrow
          IconButton(
            onPressed:
                canGoLeft ? () => onChanged(values[currentIndex - 1]) : null,
            icon: Icon(
              Icons.chevron_left,
              size: 24,
              color: canGoLeft
                  ? Theme.of(context).colorScheme.onSurface
                  : Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.3),
            ),
          ),

          const SizedBox(width: 16),

          // Duration Display
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '$currentValue',
                    style: TextStyle(
                      fontSize: AppTypography.headlineSmall,
                      fontWeight: AppTypography.bold,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                  Text(
                    'min',
                    style: TextStyle(
                      fontSize: AppTypography.labelSmall,
                      fontWeight: AppTypography.medium,
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Right Arrow
          IconButton(
            onPressed:
                canGoRight ? () => onChanged(values[currentIndex + 1]) : null,
            icon: Icon(
              Icons.chevron_right,
              size: 24,
              color: canGoRight
                  ? Theme.of(context).colorScheme.onSurface
                  : Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSoundSelector({
    required String? currentSoundPath,
    required ValueChanged<String?> onSoundChanged,
    required VoidCallback onPlaySound,
  }) {
    final currentIndex = TimerSoundConfig.availableSounds.indexWhere(
      (sound) => sound.assetPath == currentSoundPath,
    );
    final validIndex = currentIndex >= 0 ? currentIndex : 0;
    final currentSound = TimerSoundConfig.availableSounds[validIndex];

    final canGoLeft = validIndex > 0;
    final canGoRight = validIndex < TimerSoundConfig.availableSounds.length - 1;

    return SizedBox(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Left Arrow
          IconButton(
            onPressed: canGoLeft
                ? () => onSoundChanged(
                    TimerSoundConfig.availableSounds[validIndex - 1].assetPath)
                : null,
            icon: Icon(
              Icons.chevron_left,
              size: 24,
              color: canGoLeft
                  ? Theme.of(context).colorScheme.onSurface
                  : Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.3),
            ),
          ),

          const SizedBox(width: 16),

          // Sound Display
          Expanded(
            child: Container(
              height: 60,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  currentSound.name,
                  style: TextStyle(
                    fontSize: AppTypography.bodySmall,
                    fontWeight: AppTypography.semiBold,
                    color: AppColors.textOnPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Play Button
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: IconButton(
              onPressed: onPlaySound,
              icon: Icon(
                Icons.play_arrow,
                color: AppColors.textOnPrimary,
                size: 24,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Right Arrow
          IconButton(
            onPressed: canGoRight
                ? () => onSoundChanged(
                    TimerSoundConfig.availableSounds[validIndex + 1].assetPath)
                : null,
            icon: Icon(
              Icons.chevron_right,
              size: 24,
              color: canGoRight
                  ? Theme.of(context).colorScheme.onSurface
                  : Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSoundSettingsTab() {
    final soundService = ref.watch(soundServiceProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          ...TimerSoundType.values.map((type) => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    type.displayName,
                    style: TextStyle(
                      fontSize: AppTypography.bodySmall,
                      fontWeight: AppTypography.medium,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildSoundSelector(
                    currentSoundPath: type == TimerSoundType.workComplete
                        ? soundService.workSoundPath
                        : soundService.breakSoundPath,
                    onSoundChanged: (soundPath) {
                      soundService.setSound(type, soundPath);
                      setState(() {});
                    },
                    onPlaySound: () {
                      final soundPath = type == TimerSoundType.workComplete
                          ? soundService.workSoundPath
                          : soundService.breakSoundPath;
                      if (soundPath != null) {
                        _playSound(soundPath);
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                ],
              )),
        ],
      ),
    );
  }

  void _saveSettings() {
    ref.read(pomodoroNotifierProvider.notifier).setDurations(
          workMinutes: _workDurationMinutes,
          restMinutes: _restDurationMinutes,
        );
    Navigator.pop(context);
  }

  @override
  void dispose() {
    super.dispose();
  }
}
