import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mimi_app/src/features/timer/sound_service.dart';
import 'package:mimi_app/src/features/timer/timer.dart';
import 'package:mimi_app/src/features/timer/timer_sound_config.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'pomodoro_notifier.g.dart';

class PomodoroState {
  final bool isRunning;
  final bool isWorkTime;
  final int remainingSeconds;
  final int workDurationMinutes;
  final int restDurationMinutes;
  final int completedCycles;
  final String currentTask;

  PomodoroState({
    required this.isRunning,
    required this.isWorkTime,
    required this.remainingSeconds,
    required this.workDurationMinutes,
    required this.restDurationMinutes,
    required this.completedCycles,
    required this.currentTask,
  });

  PomodoroState copyWith({
    bool? isRunning,
    bool? isWorkTime,
    int? remainingSeconds,
    int? workDurationMinutes,
    int? restDurationMinutes,
    int? completedCycles,
    String? currentTask,
  }) {
    return PomodoroState(
      isRunning: isRunning ?? this.isRunning,
      isWorkTime: isWorkTime ?? this.isWorkTime,
      remainingSeconds: remainingSeconds ?? this.remainingSeconds,
      workDurationMinutes: workDurationMinutes ?? this.workDurationMinutes,
      restDurationMinutes: restDurationMinutes ?? this.restDurationMinutes,
      completedCycles: completedCycles ?? this.completedCycles,
      currentTask: currentTask ?? this.currentTask,
    );
  }
}

@riverpod
class PomodoroNotifier extends _$PomodoroNotifier {
  Timer? _timer;
  final _firestore = FirebaseFirestore.instance;
  DateTime? _sessionStartTime;

  @override
  PomodoroState build() {
    return PomodoroState(
      isRunning: false,
      isWorkTime: true,
      remainingSeconds: 25 * 60,
      workDurationMinutes: 25,
      restDurationMinutes: 5,
      completedCycles: 0,
      currentTask: '',
    );
  }

  void setTask(String task) {
    state = state.copyWith(currentTask: task);
  }

  void startTimer() {
    if (_timer != null || state.currentTask.isEmpty) return;

    _sessionStartTime = DateTime.now();
    state = state.copyWith(isRunning: true);

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.remainingSeconds > 0) {
        state = state.copyWith(remainingSeconds: state.remainingSeconds - 1);
      } else {
        _handlePeriodComplete();
      }
    });
  }

  void pauseTimer() {
    _timer?.cancel();
    _timer = null;
    state = state.copyWith(isRunning: false);
  }

  void resetTimer() {
    _timer?.cancel();
    _timer = null;
    state = state.copyWith(
      isRunning: false,
      remainingSeconds: state.workDurationMinutes * 60,
      isWorkTime: true,
      currentTask: '',
    );
  }

  void setDurations({required int workMinutes, required int restMinutes}) {
    state = state.copyWith(
      workDurationMinutes: workMinutes,
      restDurationMinutes: restMinutes,
      remainingSeconds: workMinutes * 60,
    );
  }

  void _handlePeriodComplete() async {
    if (state.isWorkTime) {
      await ref
          .read(soundServiceProvider)
          .playSound(TimerSoundType.workComplete);
      // Work period completed
      state = state.copyWith(
        completedCycles: state.completedCycles + 1,
        isWorkTime: false,
        remainingSeconds: state.restDurationMinutes * 60,
      );
      _saveSession();
    } else {
      // Rest period completed
      await ref
          .read(soundServiceProvider)
          .playSound(TimerSoundType.breakComplete);
      state = state.copyWith(
        isWorkTime: true,
        remainingSeconds: state.workDurationMinutes * 60,
      );
    }
  }

  Future<void> _saveSession() async {
    if (_sessionStartTime == null) return;

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    final session = PomodoroSession(
      id: DateTime.now().toIso8601String(),
      userId: user.uid,
      taskDescription: state.currentTask,
      startTime: _sessionStartTime!,
      endTime: DateTime.now(),
      workDurationMinutes: state.workDurationMinutes,
      restDurationMinutes: state.restDurationMinutes,
      completedCycles: state.completedCycles,
    );

    await _firestore
        .collection('users')
        .doc(user.uid)
        .collection('pomodoro_sessions')
        .add(session.toJson());
  }
}
