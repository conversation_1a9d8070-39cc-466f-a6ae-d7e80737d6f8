import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/config/url_config.dart';
import 'package:mimi_app/src/core/utils/url_launcher_utils.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';

class MinutesPurchaseBottomSheet extends ConsumerStatefulWidget {
  const MinutesPurchaseBottomSheet({super.key});

  @override
  ConsumerState<MinutesPurchaseBottomSheet> createState() =>
      _MinutesPurchaseBottomSheetState();
}

class _MinutesPurchaseBottomSheetState
    extends ConsumerState<MinutesPurchaseBottomSheet> {
  List<Package> _packages = [];
  bool _isLoading = true;
  bool _isPurchasing = false;
  Package? _selectedPackage;

  @override
  void initState() {
    super.initState();
    _loadPackages();
  }

  Future<void> _loadPackages() async {
    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);

      print('🛒 UI_DEBUG: Starting to load packages...');

      // Try to get minute packages
      final packages = await revenueCatService.getMinutePackages();
      print('🛒 UI_DEBUG: Loaded ${packages.length} minute packages');

      print('🛒 UI_DEBUG: === PACKAGES RECEIVED IN UI ===');
      for (final package in packages) {
        final minutes = _getMinutesFromPackageId(package.identifier);
        print('🛒 UI_DEBUG: Package ${package.identifier}:');
        print(
            '🛒 UI_DEBUG:   - Store Product ID: ${package.storeProduct.identifier}');
        print(
            '🛒 UI_DEBUG:   - Price String: ${package.storeProduct.priceString}');
        print(
            '🛒 UI_DEBUG:   - Currency: ${package.storeProduct.currencyCode}');
        print('🛒 UI_DEBUG:   - Title: ${package.storeProduct.title}');
        print(
            '🛒 UI_DEBUG:   - Description: ${package.storeProduct.description}');
        print('🛒 UI_DEBUG:   - Extracted Minutes: $minutes');
        print('🛒 UI_DEBUG:   ---');
      }

      setState(() {
        _packages = packages;
        _isLoading = false;
      });
    } catch (e) {
      print('🛒 UI_DEBUG: ❌ Error loading packages: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading packages: $e')),
        );
      }
      setState(() => _isLoading = false);
    }
  }

  void _selectPackage(Package package) {
    setState(() {
      _selectedPackage = package;
    });
  }

  Future<void> _purchaseSelectedPackage() async {
    if (_selectedPackage == null) return;

    setState(() => _isPurchasing = true);

    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final success =
          await revenueCatService.purchaseMinutePackage(_selectedPackage!);
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('Purchase successful! Minutes added to your account.'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.pop(context, true); // Return true to indicate success
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Purchase failed. Please try again.'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error during purchase: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isPurchasing = false);
    }
  }

  Future<void> _restorePurchases() async {
    setState(() => _isPurchasing = true);

    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      await revenueCatService.restorePurchases();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchases restored successfully!'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error restoring purchases: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isPurchasing = false);
    }
  }

  Future<void> _handlePrivacyPolicy(BuildContext context) async {
    await UrlLauncherUtils.launchURL(
      context,
      UrlConfig.privacyPolicyUrl,
      mode: LaunchMode.inAppWebView,
    );
  }

  Future<void> _handleTerms(BuildContext context) async {
    await UrlLauncherUtils.launchURL(
      context,
      UrlConfig.termsUrl,
      mode: LaunchMode.inAppWebView,
    );
  }

  Future<void> _handleEula(BuildContext context) async {
    await UrlLauncherUtils.launchURL(
      context,
      UrlConfig.eulaUrl,
      mode: LaunchMode.inAppWebView,
    );
  }

  String _getMinutesFromPackageId(String packageId) {
    final parts = packageId.split('_');
    if (parts.length >= 2) {
      return parts.last;
    }
    return '0';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppSizing.spaceS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textSecondary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppSizing.spaceL),
            child: Column(
              children: [
                Text(
                  'Purchase Minutes',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                ),
                const SizedBox(height: AppSizing.spaceS),
                Text(
                  'Choose a package to add conversation minutes to your account',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Content
          Flexible(
            child: _isLoading
                ? const Padding(
                    padding: EdgeInsets.all(AppSizing.spaceXL),
                    child: Center(child: CircularProgressIndicator()),
                  )
                : _packages.isEmpty
                    ? Padding(
                        padding: const EdgeInsets.all(AppSizing.spaceXL),
                        child: Column(
                          children: [
                            Text(
                              'No minute packages available at the moment.',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: AppSizing.spaceM),
                            Text(
                              'This could be due to:\n'
                              '• RevenueCat configuration issues\n'
                              '• App Store Connect product setup\n'
                              '• Network connectivity\n'
                              '• Missing "minutes" offering in RevenueCat',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizing.spaceL,
                        ),
                        child: Column(
                          children: _packages.map((package) {
                            final minutes =
                                _getMinutesFromPackageId(package.identifier);
                            final index = _packages.indexOf(package);
                            final isSelected = _selectedPackage == package;
                            final isMiddle =
                                index == 1 && _packages.length == 3;

                            return Container(
                              width: double.infinity,
                              margin: EdgeInsets.only(
                                bottom: index < _packages.length - 1
                                    ? AppSizing.spaceM
                                    : 0,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.surface,
                                borderRadius:
                                    BorderRadius.circular(AppSizing.radiusM),
                                border: Border.all(
                                  color: isSelected
                                      ? AppColors.primary
                                      : AppColors.border,
                                  width: isSelected ? 2 : 1,
                                ),
                              ),
                              child: InkWell(
                                onTap: _isPurchasing
                                    ? null
                                    : () => _selectPackage(package),
                                borderRadius:
                                    BorderRadius.circular(AppSizing.radiusM),
                                child: Padding(
                                  padding:
                                      const EdgeInsets.all(AppSizing.spaceM),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 48,
                                        height: 48,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary
                                              .withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                              AppSizing.radiusS),
                                        ),
                                        child: Icon(
                                          Icons.access_time,
                                          color: AppColors.primary,
                                          size: AppSizing.iconM,
                                        ),
                                      ),
                                      const SizedBox(width: AppSizing.spaceM),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  minutes.toString(),
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleLarge
                                                      ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: AppColors
                                                            .textPrimary,
                                                      ),
                                                ),
                                                const SizedBox(
                                                    width: AppSizing.spaceS),
                                                Text(
                                                  'Minutes',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                        color: AppColors
                                                            .textSecondary,
                                                      ),
                                                ),
                                                if (isMiddle) ...[
                                                  const SizedBox(
                                                      width: AppSizing.spaceS),
                                                  Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                      horizontal:
                                                          AppSizing.spaceS,
                                                      vertical: 2,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: AppColors.accent1
                                                          .withValues(
                                                              alpha: 0.1),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              AppSizing
                                                                  .radiusS),
                                                    ),
                                                    child: Text(
                                                      'Best Value',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .labelSmall
                                                          ?.copyWith(
                                                            color: AppColors
                                                                .textPrimary,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      Row(
                                        children: [
                                          Text(
                                            package.storeProduct.priceString
                                                    .isNotEmpty
                                                ? package
                                                    .storeProduct.priceString
                                                : 'Price loading...',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(
                                                  fontWeight: FontWeight.bold,
                                                  color: package
                                                          .storeProduct
                                                          .priceString
                                                          .isNotEmpty
                                                      ? AppColors.primary
                                                      : AppColors.textSecondary,
                                                ),
                                          ),
                                          if (isSelected) ...[
                                            const SizedBox(
                                                width: AppSizing.spaceS),
                                            Container(
                                              width: 24,
                                              height: 24,
                                              decoration: BoxDecoration(
                                                color: AppColors.primary,
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Icon(
                                                Icons.check,
                                                color: Colors.white,
                                                size: 16,
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
          ),

          // Continue Button
          if (!_isLoading && _selectedPackage != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isPurchasing ? null : _purchaseSelectedPackage,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.textOnPrimary,
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSizing.spaceM),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppSizing.radiusM),
                    ),
                  ),
                  child: _isPurchasing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Continue with ${_getMinutesFromPackageId(_selectedPackage!.identifier)} Minutes',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textOnPrimary,
                                  ),
                        ),
                ),
              ),
            ),

          const SizedBox(height: AppSizing.spaceM),

          // Restore Purchases Button
          if (!_isLoading && _packages.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
              child: TextButton(
                onPressed: _isPurchasing ? null : _restorePurchases,
                child: Text(
                  'Restore Purchases',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                        decoration: TextDecoration.underline,
                      ),
                ),
              ),
            ),

          const SizedBox(height: AppSizing.spaceM),

          // Legal Links
          if (!_isLoading && _packages.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () => _handlePrivacyPolicy(context),
                    child: Text(
                      AppStrings.privacyPolicy,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                            decoration: TextDecoration.underline,
                          ),
                    ),
                  ),
                  TextButton(
                    onPressed: () => _handleTerms(context),
                    child: Text(
                      AppStrings.termsOfService,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                            decoration: TextDecoration.underline,
                          ),
                    ),
                  ),
                  TextButton(
                    onPressed: () => _handleEula(context),
                    child: Text(
                      AppStrings.eula,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                            decoration: TextDecoration.underline,
                          ),
                    ),
                  ),
                ],
              ),
            ),

          // Bottom padding
          const SizedBox(height: AppSizing.spaceL),
        ],
      ),
    );
  }
}
