import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:mimi_app/src/features/ai_mimi/conversational/providers/conversation_providers.dart';
import 'package:mimi_app/src/features/purchases/utils/purchase_utils.dart';

class MinutesSettingsBottomSheet extends ConsumerStatefulWidget {
  const MinutesSettingsBottomSheet({super.key});

  @override
  ConsumerState<MinutesSettingsBottomSheet> createState() =>
      _MinutesSettingsBottomSheetState();
}

class _MinutesSettingsBottomSheetState
    extends ConsumerState<MinutesSettingsBottomSheet> {
  List<Map<String, dynamic>> _purchaseHistory = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPurchaseHistory();
  }

  Future<void> _loadPurchaseHistory() async {
    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final history = await revenueCatService.getPurchaseHistory();
      setState(() {
        _purchaseHistory = history;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  String _formatDate(dynamic timestamp) {
    if (timestamp == null) return 'Unknown';

    DateTime date;
    if (timestamp is DateTime) {
      date = timestamp;
    } else {
      // Assume Firestore Timestamp
      date = timestamp.toDate();
    }

    return DateFormat('MMM dd, yyyy').format(date);
  }

  /// Formats remaining minutes to show minutes and seconds
  /// Examples: 8.5 minutes -> "8:30", 10.0 minutes -> "10:00", 0.5 minutes -> "0:30"
  String _formatRemainingMinutes(double totalMinutes) {
    final wholeMinutes = totalMinutes.floor();
    final fractionalMinutes = totalMinutes - wholeMinutes;
    final seconds = (fractionalMinutes * 60).round();

    return '$wholeMinutes:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userNotifierProvider).valueOrNull;
    final limitsState = ref.watch(userConversationLimitsProvider).valueOrNull;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppSizing.spaceS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textSecondary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppSizing.spaceL),
            child: Text(
              'Minutes Statistics',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
            ),
          ),

          // Minutes Summary - 3 Columns
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
            child: Row(
              children: [
                // Total Purchased Minutes
                Expanded(
                  child: _buildCompactSummaryCard(
                    icon: Icons.shopping_cart,
                    title: 'Purchased',
                    value: '${(user?.purchasedMinutes ?? 0).toInt()}',
                    color: AppColors.accent1,
                  ),
                ),
                const SizedBox(width: AppSizing.spaceS),

                // Used Minutes
                Expanded(
                  child: _buildCompactSummaryCard(
                    icon: Icons.access_time_filled,
                    title: 'Used',
                    value: '${(user?.totalConversationMinutes ?? 0).toInt()}',
                    color: AppColors.accent2,
                  ),
                ),
                const SizedBox(width: AppSizing.spaceS),

                // Remaining Minutes
                Expanded(
                  child: _buildCompactSummaryCard(
                    icon: Icons.hourglass_bottom,
                    title: 'Remaining',
                    value: _formatRemainingMinutes(
                        limitsState?.remainingMinutes ?? 0),
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppSizing.spaceL),

          // Purchase More Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.pop(context); // Close current bottom sheet
                  PurchaseUtils.showMinutesPurchaseBottomSheet(context);
                },
                icon: const Icon(Icons.add_shopping_cart),
                label: const Text('Purchase More Minutes'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                  padding:
                      const EdgeInsets.symmetric(vertical: AppSizing.spaceM),
                ),
              ),
            ),
          ),

          const SizedBox(height: AppSizing.spaceL),

          // Purchase History Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceL),
            child: Row(
              children: [
                Text(
                  'Purchase History',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                ),
                const Spacer(),
                Text(
                  '${_purchaseHistory.length} purchases',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppSizing.spaceM),

          // Purchase History List
          Flexible(
            child: _isLoading
                ? const Padding(
                    padding: EdgeInsets.all(AppSizing.spaceXL),
                    child: Center(child: CircularProgressIndicator()),
                  )
                : _purchaseHistory.isEmpty
                    ? Padding(
                        padding: const EdgeInsets.all(AppSizing.spaceXL),
                        child: Column(
                          children: [
                            Icon(
                              Icons.receipt_long,
                              size: 48,
                              color: AppColors.textSecondary,
                            ),
                            const SizedBox(height: AppSizing.spaceM),
                            Text(
                              'No purchases yet',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizing.spaceL,
                        ),
                        itemCount: _purchaseHistory.length,
                        itemBuilder: (context, index) {
                          final purchase = _purchaseHistory[index];
                          final minutes = purchase['minutes']?.toInt() ?? 0;
                          final price = purchase['price'] ?? 'Unknown';
                          final date = _formatDate(purchase['purchase_date']);

                          return Container(
                            margin:
                                const EdgeInsets.only(bottom: AppSizing.spaceS),
                            padding: const EdgeInsets.all(AppSizing.spaceM),
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              borderRadius:
                                  BorderRadius.circular(AppSizing.radiusM),
                              border: Border.all(
                                color: AppColors.border,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: AppColors.primary
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(
                                        AppSizing.radiusS),
                                  ),
                                  child: Icon(
                                    Icons.access_time,
                                    color: AppColors.primary,
                                    size: AppSizing.iconS,
                                  ),
                                ),
                                const SizedBox(width: AppSizing.spaceM),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '$minutes Minutes',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleSmall
                                            ?.copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: AppColors.textPrimary,
                                            ),
                                      ),
                                      Text(
                                        date,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall
                                            ?.copyWith(
                                              color: AppColors.textSecondary,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                                Text(
                                  price,
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleSmall
                                      ?.copyWith(
                                        fontWeight: AppTypography.bold,
                                        color: AppColors.primary,
                                      ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
          ),

          // Bottom padding
          const SizedBox(height: AppSizing.spaceL),
        ],
      ),
    );
  }

  Widget _buildCompactSummaryCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSizing.spaceM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        border: Border.all(
          color: AppColors.border,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSizing.radiusS),
            ),
            child: Icon(
              icon,
              color: color,
              size: AppSizing.iconS,
            ),
          ),
          const SizedBox(height: AppSizing.spaceS),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: AppTypography.bold,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            'min',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: AppColors.textSecondary,
                  fontSize: AppTypography.labelSmall,
                ),
          ),
        ],
      ),
    );
  }
}
