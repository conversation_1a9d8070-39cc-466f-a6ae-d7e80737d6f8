// lib/src/features/purchases/providers/revenuecat_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/purchases/service/revenuecat_service.dart';

// Provider for RevenueCat service initialization
final revenueCatInitializationProvider = FutureProvider<void>((ref) async {
  final service = RevenueCatService();
  await service.initialize();
});

// Provider for RevenueCat service instance
final revenueCatServiceProvider = Provider<RevenueCatService>((ref) {
  return RevenueCatService();
});
