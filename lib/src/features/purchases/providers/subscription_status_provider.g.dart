// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_status_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionStatusNotifierHash() =>
    r'320c03977f8764f28729b7096a58bdd51e959329';

/// See also [SubscriptionStatusNotifier].
@ProviderFor(SubscriptionStatusNotifier)
final subscriptionStatusNotifierProvider =
    AutoDisposeAsyncNotifierProvider<SubscriptionStatusNotifier, bool>.internal(
  SubscriptionStatusNotifier.new,
  name: r'subscriptionStatusNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionStatusNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SubscriptionStatusNotifier = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
