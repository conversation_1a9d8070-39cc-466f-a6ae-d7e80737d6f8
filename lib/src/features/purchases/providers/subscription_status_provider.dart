// lib/src/features/purchases/providers/subscription_status_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'revenuecat_provider.dart';
import '../../auth/providers/auth_state_provider.dart';

part 'subscription_status_provider.g.dart';

@riverpod
class SubscriptionStatusNotifier extends _$SubscriptionStatusNotifier {
  @override
  Future<bool> build() async {
    print('🔐 SUBSCRIPTION_DEBUG: SubscriptionStatusNotifier build() called');
    // Listen to auth state changes
    final authState = ref.watch(authStateNotifierProvider);
    print('🔐 SUBSCRIPTION_DEBUG: Auth state: ${authState.runtimeType}');

    return authState.when(
      initial: () {
        print('🔐 SUBSCRIPTION_DEBUG: Auth state is initial, returning false');
        return false;
      },
      loading: () {
        print('🔐 SUBSCRIPTION_DEBUG: Auth state is loading, returning false');
        return false;
      },
      authenticated: (user) async {
        print(
            '🔐 SUBSCRIPTION_DEBUG: Auth state is authenticated for user: ${user.uid}');
        // User is authenticated, check subscription status
        final revenueCatService = ref.read(revenueCatServiceProvider);
        print('🔐 SUBSCRIPTION_DEBUG: Calling refreshPremiumStatus');
        final result = await revenueCatService.refreshPremiumStatus();
        print('🔐 SUBSCRIPTION_DEBUG: Premium status result: $result');
        return result;
      },
      unauthenticated: () {
        print(
            '🔐 SUBSCRIPTION_DEBUG: Auth state is unauthenticated, returning false');
        return false;
      },
      error: (error) {
        print(
            '🔐 SUBSCRIPTION_DEBUG: Auth state has error: $error, returning false');
        return false;
      },
    );
  }

  /// Manually refresh the subscription status
  Future<void> refresh() async {
    print('🔐 SUBSCRIPTION_DEBUG: refresh() called');
    print('🔐 SUBSCRIPTION_DEBUG: Current state isLoading: ${state.isLoading}');

    // Prevent multiple concurrent refresh attempts
    if (state.isLoading) {
      print('🔐 SUBSCRIPTION_DEBUG: Already loading, returning early');
      return;
    }

    print('🔐 SUBSCRIPTION_DEBUG: Setting state to AsyncLoading');
    state = const AsyncValue.loading();

    final authState = ref.read(authStateNotifierProvider);
    print(
        '🔐 SUBSCRIPTION_DEBUG: Auth state for refresh: ${authState.runtimeType}');

    try {
      final result = await authState.when<Future<bool>>(
        initial: () async {
          print(
              '🔐 SUBSCRIPTION_DEBUG: Auth state is initial in refresh, returning false');
          return false;
        },
        loading: () async {
          print(
              '🔐 SUBSCRIPTION_DEBUG: Auth state is loading in refresh, returning false');
          return false;
        },
        authenticated: (user) async {
          print(
              '🔐 SUBSCRIPTION_DEBUG: Auth state is authenticated in refresh for user: ${user.uid}');
          final revenueCatService = ref.read(revenueCatServiceProvider);
          print(
              '🔐 SUBSCRIPTION_DEBUG: Calling refreshPremiumStatus in refresh');
          final result = await revenueCatService.refreshPremiumStatus();
          print(
              '🔐 SUBSCRIPTION_DEBUG: Premium status result in refresh: $result');
          return result;
        },
        unauthenticated: () async {
          print(
              '🔐 SUBSCRIPTION_DEBUG: Auth state is unauthenticated in refresh, returning false');
          return false;
        },
        error: (error) async {
          print(
              '🔐 SUBSCRIPTION_DEBUG: Auth state has error in refresh: $error, returning false');
          return false;
        },
      );

      print('🔐 SUBSCRIPTION_DEBUG: Setting state to AsyncValue.data($result)');
      state = AsyncValue.data(result);
    } catch (e, stackTrace) {
      print('🔐 SUBSCRIPTION_DEBUG: Error in refresh: $e');
      print('🔐 SUBSCRIPTION_DEBUG: Setting state to AsyncValue.error');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Mark user as premium (called after successful purchase)
  void markAsPremium() {
    state = const AsyncValue.data(true);
  }

  /// Mark user as free (called after logout or subscription cancellation)
  void markAsFree() {
    state = const AsyncValue.data(false);
  }
}
