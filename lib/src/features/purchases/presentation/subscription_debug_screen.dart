// lib/src/features/purchases/presentation/subscription_debug_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/constants/sizing_constants.dart';
import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';
import 'package:mimi_app/src/features/purchases/providers/subscription_status_provider.dart';

class SubscriptionDebugScreen extends ConsumerStatefulWidget {
  const SubscriptionDebugScreen({super.key});

  @override
  ConsumerState<SubscriptionDebugScreen> createState() =>
      _SubscriptionDebugScreenState();
}

class _SubscriptionDebugScreenState
    extends ConsumerState<SubscriptionDebugScreen> {
  Map<String, dynamic>? _debugInfo;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() => _isLoading = true);
    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final debugInfo = await revenueCatService.getDetailedSubscriptionStatus();
      setState(() => _debugInfo = debugInfo);
    } catch (e) {
      setState(() => _debugInfo = {'error': e.toString()});
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleSubscriptionTransfer() async {
    setState(() => _isLoading = true);
    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      final success = await revenueCatService.handleSubscriptionTransfer();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Subscription transfer successful!'
                : 'No subscription found to transfer'),
            backgroundColor: success ? Colors.green : Colors.orange,
          ),
        );

        // Refresh debug info and subscription status
        await _loadDebugInfo();
        ref.read(subscriptionStatusNotifierProvider.notifier).refresh();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Transfer failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _restorePurchases() async {
    setState(() => _isLoading = true);
    try {
      final revenueCatService = ref.read(revenueCatServiceProvider);
      await revenueCatService.restorePurchases();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Purchases restored'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh debug info and subscription status
        await _loadDebugInfo();
        ref.read(subscriptionStatusNotifierProvider.notifier).refresh();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Restore failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final subscriptionStatus = ref.watch(subscriptionStatusNotifierProvider);

    return GradientScaffold(
      appBar: AppBar(
        title: const Text('Subscription Debug'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSizing.spaceM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current subscription status
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppSizing.spaceM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Status',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: AppSizing.spaceS),
                      subscriptionStatus.when(
                        data: (isPremium) => Row(
                          children: [
                            Icon(
                              isPremium ? Icons.check_circle : Icons.cancel,
                              color: isPremium ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: AppSizing.spaceS),
                            Text(isPremium ? 'Premium User' : 'Free User'),
                          ],
                        ),
                        loading: () => const Row(
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: AppSizing.spaceS),
                            Text('Loading...'),
                          ],
                        ),
                        error: (error, _) => Row(
                          children: [
                            const Icon(Icons.error, color: Colors.red),
                            const SizedBox(width: AppSizing.spaceS),
                            Text('Error: $error'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppSizing.spaceM),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed:
                          _isLoading ? null : _handleSubscriptionTransfer,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.textOnPrimary,
                      ),
                      child: const Text('Transfer Subscription'),
                    ),
                  ),
                  const SizedBox(width: AppSizing.spaceS),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _restorePurchases,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.secondary,
                        foregroundColor: AppColors.textSecondary,
                      ),
                      child: const Text('Restore Purchases'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppSizing.spaceS),

              ElevatedButton(
                onPressed: _isLoading ? null : _loadDebugInfo,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                ),
                child: const Text('Refresh Debug Info'),
              ),

              const SizedBox(height: AppSizing.spaceM),

              // Debug information
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppSizing.spaceM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Debug Information',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const Spacer(),
                            if (_debugInfo != null)
                              IconButton(
                                icon: const Icon(Icons.copy),
                                onPressed: () =>
                                    _copyToClipboard(_debugInfo.toString()),
                                tooltip: 'Copy to clipboard',
                              ),
                          ],
                        ),
                        const SizedBox(height: AppSizing.spaceS),
                        Expanded(
                          child: _isLoading
                              ? const Center(child: CircularProgressIndicator())
                              : _debugInfo == null
                                  ? const Center(
                                      child: Text('No debug info available'))
                                  : SingleChildScrollView(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children:
                                            _debugInfo!.entries.map((entry) {
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 4),
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                  width: 120,
                                                  child: Text(
                                                    '${entry.key}:',
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold),
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    entry.value.toString(),
                                                    style: const TextStyle(
                                                        fontFamily:
                                                            'monospace'),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }).toList(),
                                      ),
                                    ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
