// lib/src/features/purchases/services/revenue_cat_service.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mimi_app/src/core/config/app_config.dart';

class RevenueCatService {
  static const String _premiumEntitlementId = 'pro';
  static const String _minutesEntitlementId = 'minutes';

  // Initialize RevenueCat
  Future<void> initialize() async {
    await Purchases.setLogLevel(LogLevel.debug);

    // Get the appropriate API key based on platform
    String apiKey;
    if (kIsWeb) {
      throw UnsupportedError('RevenueCat is not supported on web');
    } else if (Platform.isIOS) {
      apiKey = AppConfig.revenueCatApiKeyIOS;
      // } else if (Platform.isAndroid) {
      //   apiKey = AppConfig.revenueCatApiKeyAndroid;
      // } else if (Platform.isMacOS) {
      //   apiKey = AppConfig.revenueCatApiKeyMacOS;
    } else {
      throw UnsupportedError('Platform not supported for RevenueCat');
    }

    await Purchases.configure(PurchasesConfiguration(apiKey));

    // Log in the current Firebase user to RevenueCat
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      await Purchases.logIn(currentUser.uid);
    }
  }

  // Get current subscription status
  Future<bool> isPremiumUser() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      return customerInfo.entitlements.active
          .containsKey(_premiumEntitlementId);
    } catch (e) {
      print('Error checking premium status: $e');
      return false;
    }
  }

  // Fetch all offerings from RevenueCat (for debugging)
  Future<Offerings> fetchOfferings() async {
    return Purchases.getOfferings();
  }

  // Get subscription packages for paywall
  Future<List<Package>> getSubscriptionPackages() async {
    try {
      final offerings = await Purchases.getOfferings();
      final defaultOffering = offerings.current;

      if (defaultOffering == null) {
        print('No default offering found');
        return [];
      }

      return defaultOffering.availablePackages;
    } catch (e) {
      print('Error getting subscription packages: $e');
      return [];
    }
  }

  // Purchase subscription package
  Future<bool> purchaseSubscription(Package package) async {
    try {
      final customerInfo = await Purchases.purchasePackage(package);

      // Log in the user to RevenueCat if not already done
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        await Purchases.logIn(currentUser.uid);
      }

      return customerInfo.entitlements.active
          .containsKey(_premiumEntitlementId);
    } catch (e) {
      print('Error purchasing subscription: $e');
      return false;
    }
  }

  // Show RevenueCat built-in paywall
  Future<bool> showPaywallIfNeeded(BuildContext context) async {
    try {
      final paywallResult = await RevenueCatUI.presentPaywallIfNeeded(
        _premiumEntitlementId,
        displayCloseButton: true,
      );

      // Return true if user made a purchase or already has entitlement
      return paywallResult == PaywallResult.purchased ||
          paywallResult == PaywallResult.notPresented;
    } catch (e) {
      print('Error showing paywall: $e');
      return false;
    }
  }

  // Get available minute packages
  Future<List<Package>> getMinutePackages() async {
    try {
      final offerings = await Purchases.getOfferings();
      print('🛒 MINUTES_DEBUG: All offerings: ${offerings.all.keys}');
      print(
          '🛒 MINUTES_DEBUG: Current offering: ${offerings.current?.identifier}');

      List<Package> minutePackages = [];

      // Enhanced debugging - show all packages from all offerings
      print('🛒 MINUTES_DEBUG: === ALL AVAILABLE PACKAGES ===');
      for (final offeringEntry in offerings.all.entries) {
        final offeringId = offeringEntry.key;
        final offering = offeringEntry.value;
        print('🛒 MINUTES_DEBUG: Offering "$offeringId":');
        for (final package in offering.availablePackages) {
          print('🛒 MINUTES_DEBUG:   - Package ID: ${package.identifier}');
          print(
              '🛒 MINUTES_DEBUG:   - Product ID: ${package.storeProduct.identifier}');
          print(
              '🛒 MINUTES_DEBUG:   - Price: ${package.storeProduct.priceString}');
          print(
              '🛒 MINUTES_DEBUG:   - Currency: ${package.storeProduct.currencyCode}');
          print('🛒 MINUTES_DEBUG:   - Title: ${package.storeProduct.title}');
          print('🛒 MINUTES_DEBUG:   ---');
        }
      }

      // Define the exact product IDs we're looking for
      const targetProductIds = ['minutes_60', 'minutes_120', 'minutes_180'];

      // First try to get the "minutes" offering specifically for consumable minute packages
      final minutesOffering = offerings.all['minutes'];
      print(
          '🛒 MINUTES_DEBUG: Minutes offering: ${minutesOffering?.identifier}');

      if (minutesOffering != null) {
        print('🛒 MINUTES_DEBUG: Searching in minutes offering...');

        for (final package in minutesOffering.availablePackages) {
          final productId = package.storeProduct.identifier;
          print(
              '🛒 MINUTES_DEBUG: Checking package with product ID: $productId');

          if (targetProductIds.contains(productId)) {
            print(
                '🛒 MINUTES_DEBUG: ✅ Found matching package: $productId - ${package.storeProduct.priceString}');
            minutePackages.add(package);
          }
        }
      }

      // If no minutes offering or no packages found, try the default offering
      if (minutePackages.isEmpty && offerings.current != null) {
        print(
            '🛒 MINUTES_DEBUG: No packages found in minutes offering, trying default offering');

        for (final package in offerings.current!.availablePackages) {
          final productId = package.storeProduct.identifier;
          print(
              '🛒 MINUTES_DEBUG: Checking package with product ID: $productId');

          if (targetProductIds.contains(productId)) {
            print(
                '🛒 MINUTES_DEBUG: ✅ Found matching package: $productId - ${package.storeProduct.priceString}');
            minutePackages.add(package);
          }
        }
      }

      // If still no packages found, search all offerings
      if (minutePackages.isEmpty) {
        print(
            '🛒 MINUTES_DEBUG: No packages found in default offering, searching all offerings...');

        for (final offering in offerings.all.values) {
          for (final package in offering.availablePackages) {
            final productId = package.storeProduct.identifier;

            if (targetProductIds.contains(productId)) {
              print(
                  '🛒 MINUTES_DEBUG: ✅ Found matching package in ${offering.identifier}: $productId - ${package.storeProduct.priceString}');
              minutePackages.add(package);
            }
          }
        }
      }

      // Remove duplicates (in case same package appears in multiple offerings)
      final uniquePackages = <String, Package>{};
      for (final package in minutePackages) {
        uniquePackages[package.storeProduct.identifier] = package;
      }
      minutePackages = uniquePackages.values.toList();

      // Sort packages by minutes (60, 120, 180)
      minutePackages.sort((a, b) {
        final minutesA = _getMinutesFromPackage(a);
        final minutesB = _getMinutesFromPackage(b);
        return minutesA.compareTo(minutesB);
      });

      print('🛒 MINUTES_DEBUG: === FINAL FILTERED PACKAGES ===');
      for (final package in minutePackages) {
        print(
            '🛒 MINUTES_DEBUG: ${package.storeProduct.identifier} - ${package.storeProduct.priceString}');
      }

      if (minutePackages.isEmpty) {
        print('🛒 MINUTES_DEBUG: ❌ WARNING: No minute packages found!');
        print('🛒 MINUTES_DEBUG: Expected product IDs: $targetProductIds');
      }

      return minutePackages;
    } catch (e) {
      print('🛒 MINUTES_DEBUG: ❌ Error getting minute packages: $e');
      return [];
    }
  }

  // Purchase minutes package (consumable)
  Future<bool> purchaseMinutePackage(Package package) async {
    try {
      // This line actually launches the store's purchase flow
      await Purchases.purchasePackage(package);

      // If we get here, the purchase was successful
      // Extract minutes from package identifier and update Firestore
      await _updatePurchasedMinutes(package);
      return true;
    } catch (e) {
      print('Error purchasing package: $e');
      return false;
    }
  }

  // Update purchased minutes in Firestore with purchase history
  Future<void> _updatePurchasedMinutes(Package package) async {
    final userId = await _getCurrentUserId();
    if (userId == null) return;

    // Extract minutes from package identifier
    final minutes = _getMinutesFromPackage(package);

    final batch = FirebaseFirestore.instance.batch();

    // Update user's total purchased minutes
    final userDoc = FirebaseFirestore.instance.collection('users').doc(userId);
    batch.update(userDoc, {
      'purchased_minutes': FieldValue.increment(minutes),
      'last_purchase_date': FieldValue.serverTimestamp(),
    });

    // Add purchase history record
    final purchaseHistoryDoc = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('purchase_history')
        .doc();

    batch.set(purchaseHistoryDoc, {
      'package_id': package.identifier,
      'minutes': minutes,
      'price': package.storeProduct.priceString,
      'currency': package.storeProduct.currencyCode,
      'purchase_date': FieldValue.serverTimestamp(),
      'product_id': package.storeProduct.identifier,
    });

    await batch.commit();
  }

  // Helper to get minutes from package identifier
  double _getMinutesFromPackage(Package package) {
    final productId = package.storeProduct.identifier;

    // Extract minutes from product identifier (e.g., "minutes_60" -> 60.0)
    if (productId.contains('minutes_')) {
      final minutesStr = productId.split('_').last;
      return double.tryParse(minutesStr) ?? 0.0;
    }

    return 0.0;
  }

  // Restore purchases (for consumables, this syncs with RevenueCat)
  Future<void> restorePurchases() async {
    try {
      final customerInfo = await Purchases.restorePurchases();

      // For consumable purchases, we need to sync any non-subscription transactions
      // that haven't been processed yet with our Firestore records
      await _syncConsumablePurchases(customerInfo);
    } catch (e) {
      print('Error restoring purchases: $e');
    }
  }

  // Sync consumable purchases with Firestore
  Future<void> _syncConsumablePurchases(CustomerInfo customerInfo) async {
    final userId = await _getCurrentUserId();
    if (userId == null) return;

    try {
      // Get existing purchase history from Firestore
      final existingPurchases = await getPurchaseHistory();
      final existingProductIds = existingPurchases
          .map((purchase) => purchase['product_id'] as String?)
          .where((id) => id != null)
          .toSet();

      // Check RevenueCat non-subscription transactions
      for (final transaction in customerInfo.nonSubscriptionTransactions) {
        // Only process minute packages
        if (transaction.productIdentifier.contains('minutes_') &&
            !existingProductIds.contains(transaction.productIdentifier)) {
          // Create a mock package for the transaction to extract minutes
          final minutes =
              _getMinutesFromProductId(transaction.productIdentifier);

          // Add to Firestore if not already recorded
          await _recordConsumablePurchase(transaction, minutes);
        }
      }
    } catch (e) {
      print('Error syncing consumable purchases: $e');
    }
  }

  // Record a consumable purchase in Firestore
  Future<void> _recordConsumablePurchase(
      StoreTransaction transaction, double minutes) async {
    final userId = await _getCurrentUserId();
    if (userId == null) return;

    final batch = FirebaseFirestore.instance.batch();

    // Update user's total purchased minutes
    final userDoc = FirebaseFirestore.instance.collection('users').doc(userId);
    batch.update(userDoc, {
      'purchased_minutes': FieldValue.increment(minutes),
      'last_purchase_date': FieldValue.serverTimestamp(),
    });

    // Add purchase history record
    final purchaseHistoryDoc = FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('purchase_history')
        .doc();

    batch.set(purchaseHistoryDoc, {
      'package_id': transaction.productIdentifier,
      'minutes': minutes,
      'price': 'Unknown', // RevenueCat doesn't provide price in transaction
      'currency': 'USD',
      'purchase_date': FieldValue.serverTimestamp(),
      'product_id': transaction.productIdentifier,
      'transaction_id': transaction.transactionIdentifier,
    });

    await batch.commit();
  }

  // Helper to get minutes from product identifier
  double _getMinutesFromProductId(String productId) {
    final minutesStr = productId.split('_').last;
    return double.tryParse(minutesStr) ?? 0.0;
  }

  // Get purchase history for user
  Future<List<Map<String, dynamic>>> getPurchaseHistory() async {
    final userId = await _getCurrentUserId();
    if (userId == null) return [];

    try {
      final querySnapshot = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('purchase_history')
          .orderBy('purchase_date', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
    } catch (e) {
      print('Error getting purchase history: $e');
      return [];
    }
  }

  Future<String?> _getCurrentUserId() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      return user?.uid;
    } catch (e) {
      print('Error getting user ID: $e');
      return null;
    }
  }

  // Log in user to RevenueCat (called after Firebase auth)
  Future<void> loginUser(String userId) async {
    print('🔐 REVENUECAT_DEBUG: loginUser called for userId: $userId');

    try {
      print('🔐 REVENUECAT_DEBUG: Getting current customer info before login');
      // Get current customer info before login to check for existing purchases
      final anonymousCustomerInfo = await Purchases.getCustomerInfo();
      final hadPremiumBeforeLogin = anonymousCustomerInfo.entitlements.active
          .containsKey(_premiumEntitlementId);

      print(
          '🔐 REVENUECAT_DEBUG: Anonymous user had premium: $hadPremiumBeforeLogin');
      print('🔐 REVENUECAT_DEBUG: Logging in user: $userId');

      if (kDebugMode) {
        print('RevenueCat: Anonymous user had premium: $hadPremiumBeforeLogin');
        print('RevenueCat: Logging in user: $userId');
      }

      print('🔐 REVENUECAT_DEBUG: Calling Purchases.logIn');
      // Perform the login - this transfers purchases from anonymous to identified user
      final customerInfo = await Purchases.logIn(userId);
      print('🔐 REVENUECAT_DEBUG: Purchases.logIn completed');

      final hasPremiumAfterLogin = customerInfo.customerInfo.entitlements.active
          .containsKey(_premiumEntitlementId);

      print('🔐 REVENUECAT_DEBUG: User logged in successfully: $userId');
      print(
          '🔐 REVENUECAT_DEBUG: Premium status after login: $hasPremiumAfterLogin');

      if (kDebugMode) {
        print('RevenueCat: User logged in successfully: $userId');
        print('RevenueCat: Premium status after login: $hasPremiumAfterLogin');

        if (hadPremiumBeforeLogin && !hasPremiumAfterLogin) {
          print('WARNING: Premium status lost during login transfer!');
        }
      }

      // If we had premium before but not after, try to restore purchases
      if (hadPremiumBeforeLogin && !hasPremiumAfterLogin) {
        print(
            '🔐 REVENUECAT_DEBUG: Premium status lost, attempting to restore purchases');
        if (kDebugMode) {
          print('RevenueCat: Attempting to restore purchases...');
        }
        await restorePurchases();
        print('🔐 REVENUECAT_DEBUG: Restore purchases completed');
      }

      print('🔐 REVENUECAT_DEBUG: loginUser completed successfully');
    } catch (e, stackTrace) {
      print('🔐 REVENUECAT_DEBUG: Error in loginUser: $e');
      print('🔐 REVENUECAT_DEBUG: Stack trace: $stackTrace');

      if (kDebugMode) {
        print('Error logging in user to RevenueCat: $e');
      }
      rethrow;
    }
  }

  // Log out user from RevenueCat (called during logout)
  Future<void> logoutUser() async {
    try {
      await Purchases.logOut();
      if (kDebugMode) {
        print('RevenueCat: User logged out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error logging out user from RevenueCat: $e');
      }
    }
  }

  // Force refresh customer info (useful after login)
  Future<bool> refreshPremiumStatus() async {
    try {
      // Force a fresh fetch from RevenueCat servers
      final customerInfo = await Purchases.getCustomerInfo();
      final isPremium =
          customerInfo.entitlements.active.containsKey(_premiumEntitlementId);
      if (kDebugMode) {
        print('RevenueCat: Refreshed premium status: $isPremium');
      }
      return isPremium;
    } catch (e) {
      if (kDebugMode) {
        print('Error refreshing premium status: $e');
      }
      return false;
    }
  }

  // Enhanced method to handle subscription transfer issues
  Future<bool> handleSubscriptionTransfer() async {
    print('🔐 REVENUECAT_DEBUG: handleSubscriptionTransfer called');

    try {
      print('🔐 REVENUECAT_DEBUG: Handling subscription transfer...');
      if (kDebugMode) {
        print('RevenueCat: Handling subscription transfer...');
      }

      print('🔐 REVENUECAT_DEBUG: Calling restorePurchases');
      // First try to restore purchases
      await restorePurchases();
      print('🔐 REVENUECAT_DEBUG: restorePurchases completed');

      print('🔐 REVENUECAT_DEBUG: Calling refreshPremiumStatus');
      // Check if we now have premium access
      bool isPremium = await refreshPremiumStatus();
      print('🔐 REVENUECAT_DEBUG: refreshPremiumStatus result: $isPremium');

      if (isPremium) {
        print('🔐 REVENUECAT_DEBUG: Subscription successfully restored');
        if (kDebugMode) {
          print('RevenueCat: Subscription successfully restored');
        }
        return true;
      }

      print('🔐 REVENUECAT_DEBUG: Still no premium, getting customer info');
      // If still no premium, try to sync customer info
      final customerInfo = await Purchases.getCustomerInfo();

      print(
          '🔐 REVENUECAT_DEBUG: Customer ID: ${customerInfo.originalAppUserId}');
      print(
          '🔐 REVENUECAT_DEBUG: Active entitlements: ${customerInfo.entitlements.active.keys}');
      print(
          '🔐 REVENUECAT_DEBUG: All entitlements: ${customerInfo.entitlements.all.keys}');

      if (kDebugMode) {
        print('RevenueCat: Customer ID: ${customerInfo.originalAppUserId}');
        print(
            'RevenueCat: Active entitlements: ${customerInfo.entitlements.active.keys}');
        print(
            'RevenueCat: All entitlements: ${customerInfo.entitlements.all.keys}');
      }

      // Check if there are any entitlements that might be inactive but valid
      final hasAnyPremiumEntitlement =
          customerInfo.entitlements.all.containsKey(_premiumEntitlementId);
      print(
          '🔐 REVENUECAT_DEBUG: Has any premium entitlement: $hasAnyPremiumEntitlement');

      if (hasAnyPremiumEntitlement) {
        final entitlement =
            customerInfo.entitlements.all[_premiumEntitlementId];
        if (kDebugMode) {
          print(
              'RevenueCat: Found premium entitlement - Active: ${entitlement?.isActive}, Will renew: ${entitlement?.willRenew}');
        }
      }

      return customerInfo.entitlements.active
          .containsKey(_premiumEntitlementId);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling subscription transfer: $e');
      }
      return false;
    }
  }

  // Method to check if user is in sandbox environment
  bool get isSandboxEnvironment {
    try {
      // In sandbox, the customer info will have sandbox indicators
      return kDebugMode; // Simple check for now, can be enhanced
    } catch (e) {
      return false;
    }
  }

  // Method to provide detailed subscription status for debugging
  Future<Map<String, dynamic>> getDetailedSubscriptionStatus() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();

      return {
        'customer_id': customerInfo.originalAppUserId,
        'is_premium':
            customerInfo.entitlements.active.containsKey(_premiumEntitlementId),
        'active_entitlements': customerInfo.entitlements.active.keys.toList(),
        'all_entitlements': customerInfo.entitlements.all.keys.toList(),
        'is_sandbox': isSandboxEnvironment,
        'latest_expiration_date': customerInfo.latestExpirationDate,
        'management_url': customerInfo.managementURL,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting detailed subscription status: $e');
      }
      return {
        'error': e.toString(),
        'is_sandbox': isSandboxEnvironment,
      };
    }
  }
}
