import 'package:flutter/material.dart';
import 'package:mimi_app/src/features/purchases/widgets/minutes_purchase_bottom_sheet.dart';
import 'package:mimi_app/src/features/purchases/widgets/minutes_settings_bottom_sheet.dart';

class PurchaseUtils {
  /// Show the minutes purchase bottom sheet
  static Future<bool?> showMinutesPurchaseBottomSheet(BuildContext context) {
    print('DEBUG: showMinutesPurchaseBottomSheet called');
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(
        maxHeight:
            MediaQuery.of(context).size.height * 0.67, // 2/3 of screen height
      ),
      builder: (context) => const MinutesPurchaseBottomSheet(),
    );
  }

  /// Show the minutes settings bottom sheet
  static Future<void> showMinutesSettingsBottomSheet(BuildContext context) {
    print('DEBUG: showMinutesSettingsBottomSheet called');
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(
        maxHeight:
            MediaQuery.of(context).size.height * 0.67, // 2/3 of screen height
      ),
      builder: (context) => const MinutesSettingsBottomSheet(),
    );
  }

  /// Quick access to purchase minutes - shows settings first, then purchase
  static Future<void> showMinutesManagement(BuildContext context) {
    return showMinutesSettingsBottomSheet(context);
  }
}
