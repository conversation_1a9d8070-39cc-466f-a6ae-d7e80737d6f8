// lib/src/features/journal/data/journal_database.dart
import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_data.dart';

import 'package:mimi_app/src/features/database/tables/checkin_activities.dart';
import 'package:mimi_app/src/features/database/tables/activity_records.dart';
import 'package:mimi_app/src/features/database/tables/routine_activity_config.dart';
import 'package:mimi_app/src/features/database/tables/intentions.dart';

import 'package:mimi_app/src/features/journal/models/statistics.dart';
import 'package:mimi_app/src/features/database/tables/checkin_routines.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:drift/native.dart';
import 'dart:io';

import '../tables/journal_entries.dart';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'database_provider.g.dart';

// Singleton database connection to prevent multiple instances
LazyDatabase? _databaseConnection;

LazyDatabase _getDatabaseConnection() {
  return _databaseConnection ??= LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'journal.db'));
    return NativeDatabase(file);
  });
}

@Riverpod(keepAlive: true)
JournalDatabase journalDatabase(JournalDatabaseRef ref) {
  return JournalDatabase._internal(_getDatabaseConnection());
}

@riverpod
Stream<List<Intention>> allIntentions(AllIntentionsRef ref) {
  final db = ref.watch(journalDatabaseProvider);
  return db.watchAllIntentions();
}

@riverpod
Stream<List<CheckInRoutine>> routinesForIntention(
    RoutinesForIntentionRef ref, int intentionId) {
  final db = ref.watch(journalDatabaseProvider);
  return db.watchRoutinesForIntention(intentionId);
}

@riverpod
class IntentionCrudNotifier extends _$IntentionCrudNotifier {
  JournalDatabase get _db => ref.read(journalDatabaseProvider);

  @override
  Future<void> build() async {
    // No initial build state needed for a CUD notifier
  }

  Future<int> addIntention(String name) async {
    try {
      final newId = await _db.into(_db.intentions).insert(
            IntentionsCompanion.insert(name: name),
          );
      return newId;
    } catch (e) {
      rethrow; // Rethrow to let the caller (EditIntentionScreen) handle the error
    }
  }

  Future<void> updateIntention(int id, String newName) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await (_db.update(_db.intentions)..where((tbl) => tbl.id.equals(id)))
          .write(
        IntentionsCompanion(name: Value(newName)),
      );
    });
  }

  Future<void> deleteIntention(int intentionId) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      // Transaction to delete intention and its associated routines
      await _db.transaction(() async {
        // 1. Delete associated routines
        await (_db.delete(_db.checkInRoutines)
              ..where((tbl) => tbl.intentionId.equals(intentionId)))
            .go();
        // 2. Delete the intention itself
        await (_db.delete(_db.intentions)
              ..where((tbl) => tbl.id.equals(intentionId)))
            .go();
      });
    });
  }
}

@riverpod
class RoutineCrudNotifier extends _$RoutineCrudNotifier {
  JournalDatabase get _db => ref.read(journalDatabaseProvider);

  @override
  Future<void> build() async {
    // No initial build state needed
  }

  Future<int> addRoutine({
    required int intentionId,
    required String name,
    required String type,
    required DateTime scheduledTime,
  }) async {
    try {
      final newRoutineId = await _db.into(_db.checkInRoutines).insert(
            CheckInRoutinesCompanion.insert(
              intentionId: intentionId,
              name: name,
              type: type,
              scheduledTime: scheduledTime,
              activities: '[]', // Default to empty list of activities
              lastUpdated: DateTime.now(),
            ),
          );
      return newRoutineId;
    } catch (e) {
      // Log error or handle as needed
      rethrow;
    }
  }

  Future<void> updateRoutine({
    required int routineId,
    String? name,
    String? type,
    DateTime? scheduledTime,
    String? activities, // Can be '[]' or a JSON string of activity IDs
    bool? notificationEnabled,
    // intentionId is not updatable as a routine belongs to one intention
  }) async {
    try {
      await (_db.update(_db.checkInRoutines)
            ..where((tbl) => tbl.id.equals(routineId)))
          .write(
        CheckInRoutinesCompanion(
          name: name != null ? Value(name) : const Value.absent(),
          type: type != null ? Value(type) : const Value.absent(),
          scheduledTime: scheduledTime != null
              ? Value(scheduledTime)
              : const Value.absent(),
          activities:
              activities != null ? Value(activities) : const Value.absent(),
          notificationEnabled: notificationEnabled != null
              ? Value(notificationEnabled)
              : const Value.absent(),
          lastUpdated:
              Value(DateTime.now()), // Always update lastUpdated timestamp
        ),
      );
    } catch (e) {
      // Log error or handle as needed
      rethrow;
    }
  }

  Future<void> deleteRoutine(int routineId) async {
    try {
      await (_db.delete(_db.checkInRoutines)
            ..where((tbl) => tbl.id.equals(routineId)))
          .go();
    } catch (e) {
      // Log error or handle as needed
      rethrow;
    }
  }
}

@DriftDatabase(tables: [
  CheckInRoutines,
  JournalEntries,
  CheckInActivities,
  ActivityRecords,
  RoutineActivityConfigs,
  Intentions,
])
class JournalDatabase extends _$JournalDatabase {
  JournalDatabase._internal(QueryExecutor executor) : super(executor);

  @override
  int get schemaVersion => 3;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from < 2) {
          await m.createTable(intentions);
          await m.addColumn(checkInRoutines, checkInRoutines.intentionId);
        }
        if (from < 3) {
          await _migrateActivitiesFromNamesToIds();
        }
      },
    );
  }

  /// Migration method to convert activity names to activity IDs in routines
  Future<void> _migrateActivitiesFromNamesToIds() async {
    if (kDebugMode) {
      print('🔄 Starting migration: Converting activity names to IDs...');
    }

    try {
      await transaction(() async {
        // Get all routines
        final routines = await select(checkInRoutines).get();

        for (final routine in routines) {
          if (routine.activities.isEmpty || routine.activities == '[]') {
            continue; // Skip empty routines
          }

          try {
            // Parse the activities field
            final activitiesData = jsonDecode(routine.activities) as List;

            if (activitiesData.isEmpty) {
              continue; // Skip empty activity lists
            }

            // Check if this routine is already in the new format (activity IDs)
            if (activitiesData.first is int) {
              if (kDebugMode) {
                print(
                    '✅ Routine ${routine.id} already in new format, skipping');
              }
              continue; // Already migrated
            }

            // Old format: activity names - convert to activity IDs
            if (kDebugMode) {
              print(
                  '🔄 Migrating routine ${routine.id} from old format: $activitiesData');
            }

            final activityNames = activitiesData.cast<String>();
            final newActivityIds = <int>[];

            for (final activityName in activityNames) {
              // Create or find activity record for this activity name
              final activityId =
                  await _createOrFindActivityForMigration(activityName);
              if (activityId != null) {
                newActivityIds.add(activityId);
                if (kDebugMode) {
                  print(
                      '  ✅ Mapped "$activityName" to activity ID $activityId');
                }
              } else {
                if (kDebugMode) {
                  print('  ⚠️ Could not create activity for "$activityName"');
                }
              }
            }

            // Update the routine with new activity IDs
            if (newActivityIds.isNotEmpty) {
              final newActivitiesJson = jsonEncode(newActivityIds);
              await (update(checkInRoutines)
                    ..where((r) => r.id.equals(routine.id)))
                  .write(CheckInRoutinesCompanion(
                activities: Value(newActivitiesJson),
                lastUpdated: Value(DateTime.now()),
              ));

              if (kDebugMode) {
                print(
                    '  ✅ Updated routine ${routine.id} activities: $newActivitiesJson');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('  ❌ Error migrating routine ${routine.id}: $e');
            }
            // Continue with other routines even if one fails
          }
        }

        if (kDebugMode) {
          print('✅ Migration completed: Activity names converted to IDs');
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Migration failed: $e');
      }
      rethrow;
    }
  }

  /// Helper method to create or find activity record for migration
  Future<int?> _createOrFindActivityForMigration(String activityName) async {
    try {
      // Normalize activity name and determine type
      final normalizedName = activityName.toLowerCase().trim();
      String activityType;
      String displayName;

      // Map activity names to types and display names
      switch (normalizedName) {
        case 'breathwork':
          activityType = 'breathwork';
          displayName = 'Breathwork';
          break;
        case 'meditation':
          activityType = 'meditation';
          displayName = 'Meditation';
          break;
        case 'journal':
        case 'journaling':
          activityType = 'journaling';
          displayName = 'Journal';
          break;
        case 'affirmations':
          activityType = 'affirmations';
          displayName = 'Affirmations';
          break;
        case 'mood tracking':
        case 'mood_tracking':
        case 'moodtracking':
          activityType = 'moodTracking';
          displayName = 'Mood Tracking';
          break;
        case 'gratitude':
          activityType = 'gratitude';
          displayName = 'Gratitude';
          break;
        default:
          if (kDebugMode) {
            print('⚠️ Unknown activity name: "$activityName"');
          }
          return null;
      }

      // Check if activity already exists
      final existingActivity = await (select(checkInActivities)
            ..where((a) => a.type.equals(activityType)))
          .getSingleOrNull();

      if (existingActivity != null) {
        return existingActivity.id;
      }

      // Create new activity with default configuration
      final defaultConfig = _getDefaultConfigForActivityType(activityType);

      final activityId = await into(checkInActivities).insert(
        CheckInActivitiesCompanion.insert(
          type: activityType,
          name: displayName,
          config: jsonEncode(defaultConfig.toJson()),
          lastUpdated: DateTime.now(),
        ),
      );

      if (kDebugMode) {
        print(
            '  ✅ Created new activity: ID $activityId, type "$activityType", name "$displayName"');
      }

      return activityId;
    } catch (e) {
      if (kDebugMode) {
        print('  ❌ Error creating activity for "$activityName": $e');
      }
      return null;
    }
  }

  /// Helper method to get default configuration for activity type
  ActivityConfig _getDefaultConfigForActivityType(String activityType) {
    switch (activityType) {
      case 'breathwork':
        return const ActivityConfig.breathwork(
          selectedPatternId: 'box',
          cycles: 3,
          availablePatterns: [],
        );
      case 'meditation':
        return const ActivityConfig.meditation();
      case 'journaling':
        return const ActivityConfig.journaling();
      case 'affirmations':
        return const ActivityConfig.affirmations();
      case 'moodTracking':
        return const ActivityConfig.moodTracking();
      case 'gratitude':
        return const ActivityConfig.gratitude();
      default:
        return const ActivityConfig.journaling(); // Fallback
    }
  }

  Future<void> upsertRoutineActivityConfig({
    required int routineId,
    required int activityId,
    required String config,
  }) async {
    try {
      final updateCount = await (update(routineActivityConfigs)
            ..where((t) =>
                t.routineId.equals(routineId) &
                t.activityId.equals(activityId)))
          .write(
        RoutineActivityConfigsCompanion(
          config: Value(config),
          lastUpdated: Value(DateTime.now()),
        ),
      );

      if (updateCount == 0) {
        await into(routineActivityConfigs).insert(
          RoutineActivityConfigsCompanion.insert(
            routineId: routineId,
            activityId: activityId,
            config: config,
            lastUpdated: DateTime.now(),
          ),
        );
      }
    } catch (e) {
      print('Error in upsertRoutineActivityConfig: $e');
      rethrow;
    }
  }

  Future<ActivityConfig?> getRoutineActivityConfig(
    int routineId,
    int activityId,
  ) async {
    try {
      debugPrint('=== DB: Getting routine activity config ===');
      debugPrint('Routine ID: $routineId, Activity ID: $activityId');

      final result = await (select(routineActivityConfigs)
            ..where((t) =>
                t.routineId.equals(routineId) &
                t.activityId.equals(activityId)))
          .getSingleOrNull();

      debugPrint('DB result: ${result != null ? 'Found' : 'Not found'}');
      if (result != null) {
        debugPrint('Config JSON: ${result.config}');
        debugPrint('Last updated: ${result.lastUpdated}');
      }

      if (result != null && result.config.isNotEmpty) {
        try {
          final config = ActivityConfigX.fromJsonSafe(result.config);
          debugPrint('Successfully parsed config: ${config.runtimeType}');
          return config;
        } catch (e) {
          debugPrint('Error parsing config: $e');
          return null;
        }
      }
      debugPrint('No config found or config is empty');
      return null;
    } catch (e) {
      debugPrint('Error in getRoutineActivityConfig: $e');
      return null;
    }
  }

  Future<bool> deleteRoutineActivityConfig(
    int routineId,
    int activityId,
  ) async {
    try {
      final deletedCount = await (delete(routineActivityConfigs)
            ..where((t) =>
                t.routineId.equals(routineId) &
                t.activityId.equals(activityId)))
          .go();
      return deletedCount > 0;
    } catch (e) {
      return false;
    }
  }

  Future<void> saveRoutineActivityConfig(
    int routineId,
    int activityId,
    ActivityConfig config,
  ) async {
    try {
      final updateCount = await (update(routineActivityConfigs)
            ..where((t) =>
                t.routineId.equals(routineId) &
                t.activityId.equals(activityId)))
          .write(
        RoutineActivityConfigsCompanion(
          config: Value(config.toJsonString()),
          lastUpdated: Value(DateTime.now()),
        ),
      );

      if (updateCount == 0) {
        await into(routineActivityConfigs).insert(
          RoutineActivityConfigsCompanion.insert(
            routineId: routineId,
            activityId: activityId,
            config: config.toJsonString(),
            lastUpdated: DateTime.now(),
          ),
        );
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<List<CheckInRoutine>> getAllRoutines() =>
      select(checkInRoutines).get();

  Future<void> updateRoutineActivities(
      int routineId, List<int> activityIds) async {
    try {
      await (update(checkInRoutines)..where((t) => t.id.equals(routineId)))
          .write(CheckInRoutinesCompanion(
        activities: Value(jsonEncode(activityIds)),
        lastUpdated: Value(DateTime.now()),
      ));
    } catch (e) {
      rethrow;
    }
  }

  Future<List<CheckInRoutine>> getRoutinesForIntention(int intentionId) {
    return (select(checkInRoutines)
          ..where((tbl) => tbl.intentionId.equals(intentionId)))
        .get();
  }

  Stream<List<CheckInRoutine>> watchRoutinesForIntention(int intentionId) {
    return (select(checkInRoutines)
          ..where((tbl) => tbl.intentionId.equals(intentionId)))
        .watch();
  }

  Future<void> deleteRoutine(int id) async {
    await (delete(checkInRoutines)..where((r) => r.id.equals(id))).go();
  }

  Future<void> deleteAllRoutines() async {
    await delete(checkInRoutines).go();
  }

  Future<void> deleteEntriesForRoutine(int routineId) async {
    await transaction(() async {
      await (delete(journalEntries)
            ..where((e) => e.routineId.equals(routineId)))
          .go();

      final routine = await getRoutineById(routineId);
      if (routine != null) {
        final activityIds = List<int>.from(jsonDecode(routine.activities));
        for (final activityId in activityIds) {
          await (delete(activityRecords)
                ..where((r) => r.activityId.equals(activityId)))
              .go();
        }
      }
    });
  }

  Stream<List<CheckInRoutine>> watchAllRoutines() =>
      select(checkInRoutines).watch();

  Future<CheckInRoutine?> getRoutineById(int id) =>
      (select(checkInRoutines)..where((r) => r.id.equals(id)))
          .getSingleOrNull();

  Future<int> createRoutine(CheckInRoutinesCompanion routine) =>
      into(checkInRoutines).insert(routine);

  Future<bool> updateRoutine(CheckInRoutinesCompanion routine) =>
      update(checkInRoutines).replace(routine);

  Future<List<Activity>> getAllActivities() async {
    try {
      final query = select(checkInActivities)
        ..orderBy([(t) => OrderingTerm(expression: t.id)]);

      final results = await query.get();

      return results
          .map((act) => Activity(
                id: act.id,
                type: act.type,
                name: act.name,
                config: ActivityConfigX.fromJsonSafe(act.config),
                enabled: act.enabled,
                lastUpdated: act.lastUpdated,
              ))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  Future<Activity?> getActivityById(int id) async {
    final result = await (select(checkInActivities)
          ..where((a) => a.id.equals(id)))
        .getSingleOrNull();
    if (result == null) return null;

    return Activity(
      id: result.id,
      type: result.type,
      name: result.name,
      config: ActivityConfigX.fromJsonSafe(result.config),
      enabled: result.enabled,
      lastUpdated: result.lastUpdated,
    );
  }

  Future<int> createActivity({
    required String type,
    required String name,
    required String config,
  }) async {
    try {
      ActivityConfigX.fromJsonSafe(config);

      return into(checkInActivities).insert(
        CheckInActivitiesCompanion.insert(
          type: type,
          name: name,
          config: config,
          lastUpdated: DateTime.now(),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> updateActivity({
    required int id,
    required String name,
    required String type,
    required ActivityConfig config,
  }) {
    return update(checkInActivities).replace(
      CheckInActivitiesCompanion(
        id: Value(id),
        type: Value(type),
        name: Value(name),
        config: Value(config.toJsonString()),
        lastUpdated: Value(DateTime.now()),
      ),
    );
  }

  Future<List<ActivityData>> getActivityRecordsForDate(
      int activityId, DateTime date) async {
    final query = select(activityRecords)
      ..where(
          (tbl) => tbl.activityId.equals(activityId) & tbl.date.equals(date));
    final results = await query.get();
    return results
        .map((record) => ActivityData(
              id: record.id,
              activityId: record.activityId,
              date: record.date,
              data: json.decode(record.data) as Map<String, dynamic>,
              status: record.status,
              completedAt: record.completedAt,
            ))
        .toList();
  }

  Future<int> saveActivityRecord(ActivityRecordsCompanion record) {
    return into(activityRecords).insert(record);
  }

  Future<void> updateActivityRecord(ActivityData data) async {
    await update(activityRecords).replace(ActivityRecordsCompanion(
      id: Value(data.id),
      activityId: Value(data.activityId),
      date: Value(data.date),
      data: Value(json.encode(data.data)),
      status: Value(data.status),
      completedAt: Value(data.completedAt),
    ));
  }

  Future<List<ActivityData>> getActivityRecordsForDateRange(
      int activityId, DateTime start, DateTime end) {
    return (select(activityRecords)
          ..where((d) => d.activityId.equals(activityId))
          ..where((d) => d.date.isBetweenValues(start, end)))
        .get()
        .then((records) => records
            .map((record) => ActivityData(
                  id: record.id,
                  activityId: record.activityId,
                  date: record.date,
                  data: json.decode(record.data) as Map<String, dynamic>,
                  status: record.status,
                  completedAt: record.completedAt,
                ))
            .toList());
  }

  Stream<List<ActivityData>> watchActivityRecords(int activityId) {
    return (select(activityRecords)
          ..where((d) => d.activityId.equals(activityId)))
        .watch()
        .map((records) => records
            .map((record) => ActivityData(
                  id: record.id,
                  activityId: record.activityId,
                  date: record.date,
                  data: json.decode(record.data) as Map<String, dynamic>,
                  status: record.status,
                  completedAt: record.completedAt,
                ))
            .toList());
  }

  Future<List<JournalEntry>> getEntriesByDate(DateTime date) {
    return (select(journalEntries)..where((e) => e.date.equals(date))).get();
  }

  Stream<List<JournalEntry>> watchTodayEntries() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return (select(journalEntries)
          ..where((e) => e.date.isBetweenValues(startOfDay, endOfDay)))
        .watch();
  }

  Future<int> createEntry(JournalEntriesCompanion entry) =>
      into(journalEntries).insert(entry);

  Future<List<JournalEntry>> getEntriesForDateRange(
      DateTime start, DateTime end) {
    return (select(journalEntries)
          ..where((e) => e.date.isBetweenValues(start, end)))
        .get();
  }

  Future<List<MoodEntry>> getMoodEntriesForDateRange(
      DateTime start, DateTime end) async {
    final entries = await (select(journalEntries)
          ..where((e) => e.date.isBetweenValues(start, end))
          ..where((e) => e.mood.isNotNull()))
        .get();

    return entries
        .map((e) => MoodEntry(
              date: e.date,
              mood: e.mood!,
            ))
        .toList();
  }

  Future<Map<String, ActivityStats>> getActivityStats(
      DateTime start, DateTime end) async {
    final entries = await getEntriesForDateRange(start, end);
    final Map<String, ActivityStats> stats = {};

    for (final entry in entries) {
      final activities = json.decode(entry.responses) as Map<String, dynamic>;

      activities.forEach((activity, completed) {
        if (!stats.containsKey(activity)) {
          stats[activity] = ActivityStats(name: activity);
        }

        stats[activity]!.totalCount++;
        if (completed == true) {
          stats[activity]!.completedCount++;
        }
      });
    }

    return stats;
  }

  Future<List<MeditationStats>> getMeditationStats(
      DateTime start, DateTime end) async {
    final entries = await (select(journalEntries)
          ..where((e) => e.date.isBetweenValues(start, end))
          ..where((e) => e.meditationMinutes.isNotNull()))
        .get();

    return entries
        .map((e) => MeditationStats(
              date: e.date,
              minutes: e.meditationMinutes!,
            ))
        .toList();
  }

  Future<DailyStreak> getStreak() async {
    final entries = await (select(journalEntries)
          ..orderBy([(e) => OrderingTerm.desc(e.date)]))
        .get();

    if (entries.isEmpty) {
      return DailyStreak(
        currentStreak: 0,
        longestStreak: 0,
        lastCompletedDate: DateTime.now(),
      );
    }

    var currentStreak = 0;
    var longestStreak = 0;
    var currentCount = 0;
    DateTime? lastDate;

    for (final entry in entries) {
      if (lastDate == null) {
        lastDate = entry.date;
        currentCount = 1;
      } else {
        final difference = lastDate.difference(entry.date).inDays;

        if (difference == 1) {
          currentCount++;
        } else {
          if (currentCount > longestStreak) {
            longestStreak = currentCount;
          }
          currentCount = 1;
        }

        lastDate = entry.date;
      }
    }

    currentStreak = currentCount;
    if (currentCount > longestStreak) {
      longestStreak = currentCount;
    }

    return DailyStreak(
      currentStreak: currentStreak,
      longestStreak: longestStreak,
      lastCompletedDate: entries.first.date,
    );
  }

  Future<OverviewStats> getOverviewStats(DateTime start, DateTime end) async {
    // Get total checkins (journal entries)
    final checkinEntries = await (select(this.journalEntries)
          ..where((e) => e.date.isBetweenValues(start, end)))
        .get();

    // Get meditation minutes from journal entries
    final meditationMinutes = checkinEntries
        .where((e) => e.meditationMinutes != null)
        .fold<int>(0, (sum, e) => sum + (e.meditationMinutes ?? 0));

    // Get current streak
    final streak = await getStreak();

    // Count journal and gratitude entries from activity records
    final activityRecordsData = await (select(this.activityRecords)
          ..where((r) => r.date.isBetweenValues(start, end)))
        .get();

    int journalEntriesCount = 0;
    int gratitudeEntriesCount = 0;

    for (final record in activityRecordsData) {
      try {
        final data = json.decode(record.data) as Map<String, dynamic>;
        // Check if it's a journal entry
        if (data.containsKey('responses')) {
          journalEntriesCount++;
        }
        // Check if it's a gratitude entry
        if (data.containsKey('gratitude') ||
            (data.containsKey('mood_label') &&
                data['mood_label'] == 'Grateful')) {
          gratitudeEntriesCount++;
        }
      } catch (e) {
        // Skip invalid JSON
        continue;
      }
    }

    return OverviewStats(
      totalCheckins: checkinEntries.length,
      currentStreak: streak.currentStreak,
      totalMeditationMinutes: meditationMinutes,
      totalFocusSessions: 0, // TODO: Implement focus session tracking
      totalJournalEntries: journalEntriesCount,
      totalGratitudeEntries: gratitudeEntriesCount,
    );
  }

  Future<List<MoodEntryWithTime>> getMoodEntriesWithTimeForDateRange(
      DateTime start, DateTime end) async {
    // Get mood entries from activity records
    final activityRecordsData = await (select(activityRecords)
          ..where((r) => r.date.isBetweenValues(start, end)))
        .get();

    final List<MoodEntryWithTime> moodEntries = [];

    for (final record in activityRecordsData) {
      try {
        final data = json.decode(record.data) as Map<String, dynamic>;
        if (data.containsKey('mood_label') && data.containsKey('mood_emoji')) {
          moodEntries.add(MoodEntryWithTime(
            date: record.date,
            mood: data['mood_label'] as String,
            note: data['note'] as String?,
            emoji: data['mood_emoji'] as String,
            label: data['mood_label'] as String,
          ));
        }
      } catch (e) {
        // Skip invalid JSON
        continue;
      }
    }

    return moodEntries;
  }

  Future<void> updateCheckinStatus(
    int routineId,
    String status,
    DateTime completedAt,
  ) async {
    await into(journalEntries).insert(
      JournalEntriesCompanion.insert(
        routineId: routineId,
        date: DateTime.now(),
        responses: '{}',
        status: status,
        completedAt: Value(completedAt),
      ),
    );

    await (update(checkInRoutines)..where((r) => r.id.equals(routineId))).write(
      CheckInRoutinesCompanion(
        lastUpdated: Value(completedAt),
      ),
    );
  }

  /// Manually trigger the migration from activity names to IDs
  /// This can be called if the automatic migration fails or needs to be re-run
  Future<void> manuallyMigrateActivities() async {
    if (kDebugMode) {
      print('🔄 Manually triggering activity migration...');
    }
    await _migrateActivitiesFromNamesToIds();
  }

  /// Clear all app data - USE WITH CAUTION!
  /// This will delete all journal entries, activity records, routines, etc.
  Future<void> clearAllData() async {
    await transaction(() async {
      // Clear all tables
      await delete(journalEntries).go();
      await delete(activityRecords).go();
      await delete(checkInRoutines).go();
      await delete(checkInActivities).go();
      await delete(routineActivityConfigs).go();
      await delete(intentions).go();

      if (kDebugMode) {
        print('✅ All app data has been cleared');
      }
    });
  }

  // Intention Operations
  Future<List<Intention>> getAllIntentions() => select(intentions).get();

  Future<Intention?> getIntentionById(int id) {
    return (select(intentions)..where((tbl) => tbl.id.equals(id)))
        .getSingleOrNull();
  }

  Future<int> createIntention(IntentionsCompanion entry) {
    return into(intentions).insert(entry);
  }

  Future<bool> updateIntention(IntentionsCompanion entry) {
    return update(intentions).replace(entry);
  }

  Future<int> deleteIntention(int id) {
    return (delete(intentions)..where((tbl) => tbl.id.equals(id))).go();
  }

  Stream<List<Intention>> watchAllIntentions() => select(intentions).watch();
}
