// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database_provider.dart';

// ignore_for_file: type=lint
class $IntentionsTable extends Intentions
    with TableInfo<$IntentionsTable, Intention> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $IntentionsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 255),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, name];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'intentions';
  @override
  VerificationContext validateIntegrity(Insertable<Intention> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Intention map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Intention(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
    );
  }

  @override
  $IntentionsTable createAlias(String alias) {
    return $IntentionsTable(attachedDatabase, alias);
  }
}

class Intention extends DataClass implements Insertable<Intention> {
  final int id;
  final String name;
  const Intention({required this.id, required this.name});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    return map;
  }

  IntentionsCompanion toCompanion(bool nullToAbsent) {
    return IntentionsCompanion(
      id: Value(id),
      name: Value(name),
    );
  }

  factory Intention.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Intention(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
    };
  }

  Intention copyWith({int? id, String? name}) => Intention(
        id: id ?? this.id,
        name: name ?? this.name,
      );
  Intention copyWithCompanion(IntentionsCompanion data) {
    return Intention(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Intention(')
          ..write('id: $id, ')
          ..write('name: $name')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, name);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Intention && other.id == this.id && other.name == this.name);
}

class IntentionsCompanion extends UpdateCompanion<Intention> {
  final Value<int> id;
  final Value<String> name;
  const IntentionsCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
  });
  IntentionsCompanion.insert({
    this.id = const Value.absent(),
    required String name,
  }) : name = Value(name);
  static Insertable<Intention> custom({
    Expression<int>? id,
    Expression<String>? name,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
    });
  }

  IntentionsCompanion copyWith({Value<int>? id, Value<String>? name}) {
    return IntentionsCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('IntentionsCompanion(')
          ..write('id: $id, ')
          ..write('name: $name')
          ..write(')'))
        .toString();
  }
}

class $CheckInRoutinesTable extends CheckInRoutines
    with TableInfo<$CheckInRoutinesTable, CheckInRoutine> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CheckInRoutinesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _intentionIdMeta =
      const VerificationMeta('intentionId');
  @override
  late final GeneratedColumn<int> intentionId = GeneratedColumn<int>(
      'intention_id', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('REFERENCES intentions (id)'));
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _enabledMeta =
      const VerificationMeta('enabled');
  @override
  late final GeneratedColumn<bool> enabled = GeneratedColumn<bool>(
      'enabled', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("enabled" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _activitiesMeta =
      const VerificationMeta('activities');
  @override
  late final GeneratedColumn<String> activities = GeneratedColumn<String>(
      'activities', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _scheduledTimeMeta =
      const VerificationMeta('scheduledTime');
  @override
  late final GeneratedColumn<DateTime> scheduledTime =
      GeneratedColumn<DateTime>('scheduled_time', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _notificationEnabledMeta =
      const VerificationMeta('notificationEnabled');
  @override
  late final GeneratedColumn<bool> notificationEnabled = GeneratedColumn<bool>(
      'notification_enabled', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("notification_enabled" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _lastUpdatedMeta =
      const VerificationMeta('lastUpdated');
  @override
  late final GeneratedColumn<DateTime> lastUpdated = GeneratedColumn<DateTime>(
      'last_updated', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        intentionId,
        type,
        name,
        enabled,
        activities,
        scheduledTime,
        notificationEnabled,
        lastUpdated
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'check_in_routines';
  @override
  VerificationContext validateIntegrity(Insertable<CheckInRoutine> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('intention_id')) {
      context.handle(
          _intentionIdMeta,
          intentionId.isAcceptableOrUnknown(
              data['intention_id']!, _intentionIdMeta));
    } else if (isInserting) {
      context.missing(_intentionIdMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('enabled')) {
      context.handle(_enabledMeta,
          enabled.isAcceptableOrUnknown(data['enabled']!, _enabledMeta));
    }
    if (data.containsKey('activities')) {
      context.handle(
          _activitiesMeta,
          activities.isAcceptableOrUnknown(
              data['activities']!, _activitiesMeta));
    } else if (isInserting) {
      context.missing(_activitiesMeta);
    }
    if (data.containsKey('scheduled_time')) {
      context.handle(
          _scheduledTimeMeta,
          scheduledTime.isAcceptableOrUnknown(
              data['scheduled_time']!, _scheduledTimeMeta));
    } else if (isInserting) {
      context.missing(_scheduledTimeMeta);
    }
    if (data.containsKey('notification_enabled')) {
      context.handle(
          _notificationEnabledMeta,
          notificationEnabled.isAcceptableOrUnknown(
              data['notification_enabled']!, _notificationEnabledMeta));
    }
    if (data.containsKey('last_updated')) {
      context.handle(
          _lastUpdatedMeta,
          lastUpdated.isAcceptableOrUnknown(
              data['last_updated']!, _lastUpdatedMeta));
    } else if (isInserting) {
      context.missing(_lastUpdatedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CheckInRoutine map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CheckInRoutine(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      intentionId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}intention_id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      enabled: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}enabled'])!,
      activities: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}activities'])!,
      scheduledTime: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}scheduled_time'])!,
      notificationEnabled: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}notification_enabled'])!,
      lastUpdated: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_updated'])!,
    );
  }

  @override
  $CheckInRoutinesTable createAlias(String alias) {
    return $CheckInRoutinesTable(attachedDatabase, alias);
  }
}

class CheckInRoutine extends DataClass implements Insertable<CheckInRoutine> {
  final int id;
  final int intentionId;
  final String type;
  final String name;
  final bool enabled;
  final String activities;
  final DateTime scheduledTime;
  final bool notificationEnabled;
  final DateTime lastUpdated;
  const CheckInRoutine(
      {required this.id,
      required this.intentionId,
      required this.type,
      required this.name,
      required this.enabled,
      required this.activities,
      required this.scheduledTime,
      required this.notificationEnabled,
      required this.lastUpdated});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['intention_id'] = Variable<int>(intentionId);
    map['type'] = Variable<String>(type);
    map['name'] = Variable<String>(name);
    map['enabled'] = Variable<bool>(enabled);
    map['activities'] = Variable<String>(activities);
    map['scheduled_time'] = Variable<DateTime>(scheduledTime);
    map['notification_enabled'] = Variable<bool>(notificationEnabled);
    map['last_updated'] = Variable<DateTime>(lastUpdated);
    return map;
  }

  CheckInRoutinesCompanion toCompanion(bool nullToAbsent) {
    return CheckInRoutinesCompanion(
      id: Value(id),
      intentionId: Value(intentionId),
      type: Value(type),
      name: Value(name),
      enabled: Value(enabled),
      activities: Value(activities),
      scheduledTime: Value(scheduledTime),
      notificationEnabled: Value(notificationEnabled),
      lastUpdated: Value(lastUpdated),
    );
  }

  factory CheckInRoutine.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CheckInRoutine(
      id: serializer.fromJson<int>(json['id']),
      intentionId: serializer.fromJson<int>(json['intentionId']),
      type: serializer.fromJson<String>(json['type']),
      name: serializer.fromJson<String>(json['name']),
      enabled: serializer.fromJson<bool>(json['enabled']),
      activities: serializer.fromJson<String>(json['activities']),
      scheduledTime: serializer.fromJson<DateTime>(json['scheduledTime']),
      notificationEnabled:
          serializer.fromJson<bool>(json['notificationEnabled']),
      lastUpdated: serializer.fromJson<DateTime>(json['lastUpdated']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'intentionId': serializer.toJson<int>(intentionId),
      'type': serializer.toJson<String>(type),
      'name': serializer.toJson<String>(name),
      'enabled': serializer.toJson<bool>(enabled),
      'activities': serializer.toJson<String>(activities),
      'scheduledTime': serializer.toJson<DateTime>(scheduledTime),
      'notificationEnabled': serializer.toJson<bool>(notificationEnabled),
      'lastUpdated': serializer.toJson<DateTime>(lastUpdated),
    };
  }

  CheckInRoutine copyWith(
          {int? id,
          int? intentionId,
          String? type,
          String? name,
          bool? enabled,
          String? activities,
          DateTime? scheduledTime,
          bool? notificationEnabled,
          DateTime? lastUpdated}) =>
      CheckInRoutine(
        id: id ?? this.id,
        intentionId: intentionId ?? this.intentionId,
        type: type ?? this.type,
        name: name ?? this.name,
        enabled: enabled ?? this.enabled,
        activities: activities ?? this.activities,
        scheduledTime: scheduledTime ?? this.scheduledTime,
        notificationEnabled: notificationEnabled ?? this.notificationEnabled,
        lastUpdated: lastUpdated ?? this.lastUpdated,
      );
  CheckInRoutine copyWithCompanion(CheckInRoutinesCompanion data) {
    return CheckInRoutine(
      id: data.id.present ? data.id.value : this.id,
      intentionId:
          data.intentionId.present ? data.intentionId.value : this.intentionId,
      type: data.type.present ? data.type.value : this.type,
      name: data.name.present ? data.name.value : this.name,
      enabled: data.enabled.present ? data.enabled.value : this.enabled,
      activities:
          data.activities.present ? data.activities.value : this.activities,
      scheduledTime: data.scheduledTime.present
          ? data.scheduledTime.value
          : this.scheduledTime,
      notificationEnabled: data.notificationEnabled.present
          ? data.notificationEnabled.value
          : this.notificationEnabled,
      lastUpdated:
          data.lastUpdated.present ? data.lastUpdated.value : this.lastUpdated,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CheckInRoutine(')
          ..write('id: $id, ')
          ..write('intentionId: $intentionId, ')
          ..write('type: $type, ')
          ..write('name: $name, ')
          ..write('enabled: $enabled, ')
          ..write('activities: $activities, ')
          ..write('scheduledTime: $scheduledTime, ')
          ..write('notificationEnabled: $notificationEnabled, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, intentionId, type, name, enabled,
      activities, scheduledTime, notificationEnabled, lastUpdated);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CheckInRoutine &&
          other.id == this.id &&
          other.intentionId == this.intentionId &&
          other.type == this.type &&
          other.name == this.name &&
          other.enabled == this.enabled &&
          other.activities == this.activities &&
          other.scheduledTime == this.scheduledTime &&
          other.notificationEnabled == this.notificationEnabled &&
          other.lastUpdated == this.lastUpdated);
}

class CheckInRoutinesCompanion extends UpdateCompanion<CheckInRoutine> {
  final Value<int> id;
  final Value<int> intentionId;
  final Value<String> type;
  final Value<String> name;
  final Value<bool> enabled;
  final Value<String> activities;
  final Value<DateTime> scheduledTime;
  final Value<bool> notificationEnabled;
  final Value<DateTime> lastUpdated;
  const CheckInRoutinesCompanion({
    this.id = const Value.absent(),
    this.intentionId = const Value.absent(),
    this.type = const Value.absent(),
    this.name = const Value.absent(),
    this.enabled = const Value.absent(),
    this.activities = const Value.absent(),
    this.scheduledTime = const Value.absent(),
    this.notificationEnabled = const Value.absent(),
    this.lastUpdated = const Value.absent(),
  });
  CheckInRoutinesCompanion.insert({
    this.id = const Value.absent(),
    required int intentionId,
    required String type,
    required String name,
    this.enabled = const Value.absent(),
    required String activities,
    required DateTime scheduledTime,
    this.notificationEnabled = const Value.absent(),
    required DateTime lastUpdated,
  })  : intentionId = Value(intentionId),
        type = Value(type),
        name = Value(name),
        activities = Value(activities),
        scheduledTime = Value(scheduledTime),
        lastUpdated = Value(lastUpdated);
  static Insertable<CheckInRoutine> custom({
    Expression<int>? id,
    Expression<int>? intentionId,
    Expression<String>? type,
    Expression<String>? name,
    Expression<bool>? enabled,
    Expression<String>? activities,
    Expression<DateTime>? scheduledTime,
    Expression<bool>? notificationEnabled,
    Expression<DateTime>? lastUpdated,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (intentionId != null) 'intention_id': intentionId,
      if (type != null) 'type': type,
      if (name != null) 'name': name,
      if (enabled != null) 'enabled': enabled,
      if (activities != null) 'activities': activities,
      if (scheduledTime != null) 'scheduled_time': scheduledTime,
      if (notificationEnabled != null)
        'notification_enabled': notificationEnabled,
      if (lastUpdated != null) 'last_updated': lastUpdated,
    });
  }

  CheckInRoutinesCompanion copyWith(
      {Value<int>? id,
      Value<int>? intentionId,
      Value<String>? type,
      Value<String>? name,
      Value<bool>? enabled,
      Value<String>? activities,
      Value<DateTime>? scheduledTime,
      Value<bool>? notificationEnabled,
      Value<DateTime>? lastUpdated}) {
    return CheckInRoutinesCompanion(
      id: id ?? this.id,
      intentionId: intentionId ?? this.intentionId,
      type: type ?? this.type,
      name: name ?? this.name,
      enabled: enabled ?? this.enabled,
      activities: activities ?? this.activities,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      notificationEnabled: notificationEnabled ?? this.notificationEnabled,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (intentionId.present) {
      map['intention_id'] = Variable<int>(intentionId.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (enabled.present) {
      map['enabled'] = Variable<bool>(enabled.value);
    }
    if (activities.present) {
      map['activities'] = Variable<String>(activities.value);
    }
    if (scheduledTime.present) {
      map['scheduled_time'] = Variable<DateTime>(scheduledTime.value);
    }
    if (notificationEnabled.present) {
      map['notification_enabled'] = Variable<bool>(notificationEnabled.value);
    }
    if (lastUpdated.present) {
      map['last_updated'] = Variable<DateTime>(lastUpdated.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CheckInRoutinesCompanion(')
          ..write('id: $id, ')
          ..write('intentionId: $intentionId, ')
          ..write('type: $type, ')
          ..write('name: $name, ')
          ..write('enabled: $enabled, ')
          ..write('activities: $activities, ')
          ..write('scheduledTime: $scheduledTime, ')
          ..write('notificationEnabled: $notificationEnabled, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }
}

class $JournalEntriesTable extends JournalEntries
    with TableInfo<$JournalEntriesTable, JournalEntry> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $JournalEntriesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _routineIdMeta =
      const VerificationMeta('routineId');
  @override
  late final GeneratedColumn<int> routineId = GeneratedColumn<int>(
      'routine_id', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES check_in_routines (id)'));
  static const VerificationMeta _dateMeta = const VerificationMeta('date');
  @override
  late final GeneratedColumn<DateTime> date = GeneratedColumn<DateTime>(
      'date', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _responsesMeta =
      const VerificationMeta('responses');
  @override
  late final GeneratedColumn<String> responses = GeneratedColumn<String>(
      'responses', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _moodMeta = const VerificationMeta('mood');
  @override
  late final GeneratedColumn<String> mood = GeneratedColumn<String>(
      'mood', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _meditationMinutesMeta =
      const VerificationMeta('meditationMinutes');
  @override
  late final GeneratedColumn<int> meditationMinutes = GeneratedColumn<int>(
      'meditation_minutes', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _completedAtMeta =
      const VerificationMeta('completedAt');
  @override
  late final GeneratedColumn<DateTime> completedAt = GeneratedColumn<DateTime>(
      'completed_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        routineId,
        date,
        responses,
        mood,
        meditationMinutes,
        status,
        completedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'journal_entries';
  @override
  VerificationContext validateIntegrity(Insertable<JournalEntry> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('routine_id')) {
      context.handle(_routineIdMeta,
          routineId.isAcceptableOrUnknown(data['routine_id']!, _routineIdMeta));
    } else if (isInserting) {
      context.missing(_routineIdMeta);
    }
    if (data.containsKey('date')) {
      context.handle(
          _dateMeta, date.isAcceptableOrUnknown(data['date']!, _dateMeta));
    } else if (isInserting) {
      context.missing(_dateMeta);
    }
    if (data.containsKey('responses')) {
      context.handle(_responsesMeta,
          responses.isAcceptableOrUnknown(data['responses']!, _responsesMeta));
    } else if (isInserting) {
      context.missing(_responsesMeta);
    }
    if (data.containsKey('mood')) {
      context.handle(
          _moodMeta, mood.isAcceptableOrUnknown(data['mood']!, _moodMeta));
    }
    if (data.containsKey('meditation_minutes')) {
      context.handle(
          _meditationMinutesMeta,
          meditationMinutes.isAcceptableOrUnknown(
              data['meditation_minutes']!, _meditationMinutesMeta));
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    } else if (isInserting) {
      context.missing(_statusMeta);
    }
    if (data.containsKey('completed_at')) {
      context.handle(
          _completedAtMeta,
          completedAt.isAcceptableOrUnknown(
              data['completed_at']!, _completedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  JournalEntry map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return JournalEntry(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      routineId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}routine_id'])!,
      date: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}date'])!,
      responses: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}responses'])!,
      mood: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}mood']),
      meditationMinutes: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}meditation_minutes']),
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!,
      completedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}completed_at']),
    );
  }

  @override
  $JournalEntriesTable createAlias(String alias) {
    return $JournalEntriesTable(attachedDatabase, alias);
  }
}

class JournalEntry extends DataClass implements Insertable<JournalEntry> {
  final int id;
  final int routineId;
  final DateTime date;
  final String responses;
  final String? mood;
  final int? meditationMinutes;
  final String status;
  final DateTime? completedAt;
  const JournalEntry(
      {required this.id,
      required this.routineId,
      required this.date,
      required this.responses,
      this.mood,
      this.meditationMinutes,
      required this.status,
      this.completedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['routine_id'] = Variable<int>(routineId);
    map['date'] = Variable<DateTime>(date);
    map['responses'] = Variable<String>(responses);
    if (!nullToAbsent || mood != null) {
      map['mood'] = Variable<String>(mood);
    }
    if (!nullToAbsent || meditationMinutes != null) {
      map['meditation_minutes'] = Variable<int>(meditationMinutes);
    }
    map['status'] = Variable<String>(status);
    if (!nullToAbsent || completedAt != null) {
      map['completed_at'] = Variable<DateTime>(completedAt);
    }
    return map;
  }

  JournalEntriesCompanion toCompanion(bool nullToAbsent) {
    return JournalEntriesCompanion(
      id: Value(id),
      routineId: Value(routineId),
      date: Value(date),
      responses: Value(responses),
      mood: mood == null && nullToAbsent ? const Value.absent() : Value(mood),
      meditationMinutes: meditationMinutes == null && nullToAbsent
          ? const Value.absent()
          : Value(meditationMinutes),
      status: Value(status),
      completedAt: completedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(completedAt),
    );
  }

  factory JournalEntry.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return JournalEntry(
      id: serializer.fromJson<int>(json['id']),
      routineId: serializer.fromJson<int>(json['routineId']),
      date: serializer.fromJson<DateTime>(json['date']),
      responses: serializer.fromJson<String>(json['responses']),
      mood: serializer.fromJson<String?>(json['mood']),
      meditationMinutes: serializer.fromJson<int?>(json['meditationMinutes']),
      status: serializer.fromJson<String>(json['status']),
      completedAt: serializer.fromJson<DateTime?>(json['completedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'routineId': serializer.toJson<int>(routineId),
      'date': serializer.toJson<DateTime>(date),
      'responses': serializer.toJson<String>(responses),
      'mood': serializer.toJson<String?>(mood),
      'meditationMinutes': serializer.toJson<int?>(meditationMinutes),
      'status': serializer.toJson<String>(status),
      'completedAt': serializer.toJson<DateTime?>(completedAt),
    };
  }

  JournalEntry copyWith(
          {int? id,
          int? routineId,
          DateTime? date,
          String? responses,
          Value<String?> mood = const Value.absent(),
          Value<int?> meditationMinutes = const Value.absent(),
          String? status,
          Value<DateTime?> completedAt = const Value.absent()}) =>
      JournalEntry(
        id: id ?? this.id,
        routineId: routineId ?? this.routineId,
        date: date ?? this.date,
        responses: responses ?? this.responses,
        mood: mood.present ? mood.value : this.mood,
        meditationMinutes: meditationMinutes.present
            ? meditationMinutes.value
            : this.meditationMinutes,
        status: status ?? this.status,
        completedAt: completedAt.present ? completedAt.value : this.completedAt,
      );
  JournalEntry copyWithCompanion(JournalEntriesCompanion data) {
    return JournalEntry(
      id: data.id.present ? data.id.value : this.id,
      routineId: data.routineId.present ? data.routineId.value : this.routineId,
      date: data.date.present ? data.date.value : this.date,
      responses: data.responses.present ? data.responses.value : this.responses,
      mood: data.mood.present ? data.mood.value : this.mood,
      meditationMinutes: data.meditationMinutes.present
          ? data.meditationMinutes.value
          : this.meditationMinutes,
      status: data.status.present ? data.status.value : this.status,
      completedAt:
          data.completedAt.present ? data.completedAt.value : this.completedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('JournalEntry(')
          ..write('id: $id, ')
          ..write('routineId: $routineId, ')
          ..write('date: $date, ')
          ..write('responses: $responses, ')
          ..write('mood: $mood, ')
          ..write('meditationMinutes: $meditationMinutes, ')
          ..write('status: $status, ')
          ..write('completedAt: $completedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, routineId, date, responses, mood,
      meditationMinutes, status, completedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is JournalEntry &&
          other.id == this.id &&
          other.routineId == this.routineId &&
          other.date == this.date &&
          other.responses == this.responses &&
          other.mood == this.mood &&
          other.meditationMinutes == this.meditationMinutes &&
          other.status == this.status &&
          other.completedAt == this.completedAt);
}

class JournalEntriesCompanion extends UpdateCompanion<JournalEntry> {
  final Value<int> id;
  final Value<int> routineId;
  final Value<DateTime> date;
  final Value<String> responses;
  final Value<String?> mood;
  final Value<int?> meditationMinutes;
  final Value<String> status;
  final Value<DateTime?> completedAt;
  const JournalEntriesCompanion({
    this.id = const Value.absent(),
    this.routineId = const Value.absent(),
    this.date = const Value.absent(),
    this.responses = const Value.absent(),
    this.mood = const Value.absent(),
    this.meditationMinutes = const Value.absent(),
    this.status = const Value.absent(),
    this.completedAt = const Value.absent(),
  });
  JournalEntriesCompanion.insert({
    this.id = const Value.absent(),
    required int routineId,
    required DateTime date,
    required String responses,
    this.mood = const Value.absent(),
    this.meditationMinutes = const Value.absent(),
    required String status,
    this.completedAt = const Value.absent(),
  })  : routineId = Value(routineId),
        date = Value(date),
        responses = Value(responses),
        status = Value(status);
  static Insertable<JournalEntry> custom({
    Expression<int>? id,
    Expression<int>? routineId,
    Expression<DateTime>? date,
    Expression<String>? responses,
    Expression<String>? mood,
    Expression<int>? meditationMinutes,
    Expression<String>? status,
    Expression<DateTime>? completedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (routineId != null) 'routine_id': routineId,
      if (date != null) 'date': date,
      if (responses != null) 'responses': responses,
      if (mood != null) 'mood': mood,
      if (meditationMinutes != null) 'meditation_minutes': meditationMinutes,
      if (status != null) 'status': status,
      if (completedAt != null) 'completed_at': completedAt,
    });
  }

  JournalEntriesCompanion copyWith(
      {Value<int>? id,
      Value<int>? routineId,
      Value<DateTime>? date,
      Value<String>? responses,
      Value<String?>? mood,
      Value<int?>? meditationMinutes,
      Value<String>? status,
      Value<DateTime?>? completedAt}) {
    return JournalEntriesCompanion(
      id: id ?? this.id,
      routineId: routineId ?? this.routineId,
      date: date ?? this.date,
      responses: responses ?? this.responses,
      mood: mood ?? this.mood,
      meditationMinutes: meditationMinutes ?? this.meditationMinutes,
      status: status ?? this.status,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (routineId.present) {
      map['routine_id'] = Variable<int>(routineId.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(date.value);
    }
    if (responses.present) {
      map['responses'] = Variable<String>(responses.value);
    }
    if (mood.present) {
      map['mood'] = Variable<String>(mood.value);
    }
    if (meditationMinutes.present) {
      map['meditation_minutes'] = Variable<int>(meditationMinutes.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (completedAt.present) {
      map['completed_at'] = Variable<DateTime>(completedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('JournalEntriesCompanion(')
          ..write('id: $id, ')
          ..write('routineId: $routineId, ')
          ..write('date: $date, ')
          ..write('responses: $responses, ')
          ..write('mood: $mood, ')
          ..write('meditationMinutes: $meditationMinutes, ')
          ..write('status: $status, ')
          ..write('completedAt: $completedAt')
          ..write(')'))
        .toString();
  }
}

class $CheckInActivitiesTable extends CheckInActivities
    with TableInfo<$CheckInActivitiesTable, CheckInActivity> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CheckInActivitiesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _configMeta = const VerificationMeta('config');
  @override
  late final GeneratedColumn<String> config = GeneratedColumn<String>(
      'config', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _enabledMeta =
      const VerificationMeta('enabled');
  @override
  late final GeneratedColumn<bool> enabled = GeneratedColumn<bool>(
      'enabled', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("enabled" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _lastUpdatedMeta =
      const VerificationMeta('lastUpdated');
  @override
  late final GeneratedColumn<DateTime> lastUpdated = GeneratedColumn<DateTime>(
      'last_updated', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns =>
      [id, type, name, config, enabled, lastUpdated];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'check_in_activities';
  @override
  VerificationContext validateIntegrity(Insertable<CheckInActivity> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('config')) {
      context.handle(_configMeta,
          config.isAcceptableOrUnknown(data['config']!, _configMeta));
    } else if (isInserting) {
      context.missing(_configMeta);
    }
    if (data.containsKey('enabled')) {
      context.handle(_enabledMeta,
          enabled.isAcceptableOrUnknown(data['enabled']!, _enabledMeta));
    }
    if (data.containsKey('last_updated')) {
      context.handle(
          _lastUpdatedMeta,
          lastUpdated.isAcceptableOrUnknown(
              data['last_updated']!, _lastUpdatedMeta));
    } else if (isInserting) {
      context.missing(_lastUpdatedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CheckInActivity map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CheckInActivity(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      config: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}config'])!,
      enabled: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}enabled'])!,
      lastUpdated: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_updated'])!,
    );
  }

  @override
  $CheckInActivitiesTable createAlias(String alias) {
    return $CheckInActivitiesTable(attachedDatabase, alias);
  }
}

class CheckInActivity extends DataClass implements Insertable<CheckInActivity> {
  final int id;
  final String type;
  final String name;
  final String config;
  final bool enabled;
  final DateTime lastUpdated;
  const CheckInActivity(
      {required this.id,
      required this.type,
      required this.name,
      required this.config,
      required this.enabled,
      required this.lastUpdated});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['type'] = Variable<String>(type);
    map['name'] = Variable<String>(name);
    map['config'] = Variable<String>(config);
    map['enabled'] = Variable<bool>(enabled);
    map['last_updated'] = Variable<DateTime>(lastUpdated);
    return map;
  }

  CheckInActivitiesCompanion toCompanion(bool nullToAbsent) {
    return CheckInActivitiesCompanion(
      id: Value(id),
      type: Value(type),
      name: Value(name),
      config: Value(config),
      enabled: Value(enabled),
      lastUpdated: Value(lastUpdated),
    );
  }

  factory CheckInActivity.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CheckInActivity(
      id: serializer.fromJson<int>(json['id']),
      type: serializer.fromJson<String>(json['type']),
      name: serializer.fromJson<String>(json['name']),
      config: serializer.fromJson<String>(json['config']),
      enabled: serializer.fromJson<bool>(json['enabled']),
      lastUpdated: serializer.fromJson<DateTime>(json['lastUpdated']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'type': serializer.toJson<String>(type),
      'name': serializer.toJson<String>(name),
      'config': serializer.toJson<String>(config),
      'enabled': serializer.toJson<bool>(enabled),
      'lastUpdated': serializer.toJson<DateTime>(lastUpdated),
    };
  }

  CheckInActivity copyWith(
          {int? id,
          String? type,
          String? name,
          String? config,
          bool? enabled,
          DateTime? lastUpdated}) =>
      CheckInActivity(
        id: id ?? this.id,
        type: type ?? this.type,
        name: name ?? this.name,
        config: config ?? this.config,
        enabled: enabled ?? this.enabled,
        lastUpdated: lastUpdated ?? this.lastUpdated,
      );
  CheckInActivity copyWithCompanion(CheckInActivitiesCompanion data) {
    return CheckInActivity(
      id: data.id.present ? data.id.value : this.id,
      type: data.type.present ? data.type.value : this.type,
      name: data.name.present ? data.name.value : this.name,
      config: data.config.present ? data.config.value : this.config,
      enabled: data.enabled.present ? data.enabled.value : this.enabled,
      lastUpdated:
          data.lastUpdated.present ? data.lastUpdated.value : this.lastUpdated,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CheckInActivity(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('name: $name, ')
          ..write('config: $config, ')
          ..write('enabled: $enabled, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, type, name, config, enabled, lastUpdated);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CheckInActivity &&
          other.id == this.id &&
          other.type == this.type &&
          other.name == this.name &&
          other.config == this.config &&
          other.enabled == this.enabled &&
          other.lastUpdated == this.lastUpdated);
}

class CheckInActivitiesCompanion extends UpdateCompanion<CheckInActivity> {
  final Value<int> id;
  final Value<String> type;
  final Value<String> name;
  final Value<String> config;
  final Value<bool> enabled;
  final Value<DateTime> lastUpdated;
  const CheckInActivitiesCompanion({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.name = const Value.absent(),
    this.config = const Value.absent(),
    this.enabled = const Value.absent(),
    this.lastUpdated = const Value.absent(),
  });
  CheckInActivitiesCompanion.insert({
    this.id = const Value.absent(),
    required String type,
    required String name,
    required String config,
    this.enabled = const Value.absent(),
    required DateTime lastUpdated,
  })  : type = Value(type),
        name = Value(name),
        config = Value(config),
        lastUpdated = Value(lastUpdated);
  static Insertable<CheckInActivity> custom({
    Expression<int>? id,
    Expression<String>? type,
    Expression<String>? name,
    Expression<String>? config,
    Expression<bool>? enabled,
    Expression<DateTime>? lastUpdated,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (type != null) 'type': type,
      if (name != null) 'name': name,
      if (config != null) 'config': config,
      if (enabled != null) 'enabled': enabled,
      if (lastUpdated != null) 'last_updated': lastUpdated,
    });
  }

  CheckInActivitiesCompanion copyWith(
      {Value<int>? id,
      Value<String>? type,
      Value<String>? name,
      Value<String>? config,
      Value<bool>? enabled,
      Value<DateTime>? lastUpdated}) {
    return CheckInActivitiesCompanion(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      config: config ?? this.config,
      enabled: enabled ?? this.enabled,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (config.present) {
      map['config'] = Variable<String>(config.value);
    }
    if (enabled.present) {
      map['enabled'] = Variable<bool>(enabled.value);
    }
    if (lastUpdated.present) {
      map['last_updated'] = Variable<DateTime>(lastUpdated.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CheckInActivitiesCompanion(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('name: $name, ')
          ..write('config: $config, ')
          ..write('enabled: $enabled, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }
}

class $ActivityRecordsTable extends ActivityRecords
    with TableInfo<$ActivityRecordsTable, ActivityRecord> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ActivityRecordsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _activityIdMeta =
      const VerificationMeta('activityId');
  @override
  late final GeneratedColumn<int> activityId = GeneratedColumn<int>(
      'activity_id', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES check_in_activities (id)'));
  static const VerificationMeta _dateMeta = const VerificationMeta('date');
  @override
  late final GeneratedColumn<DateTime> date = GeneratedColumn<DateTime>(
      'date', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _dataMeta = const VerificationMeta('data');
  @override
  late final GeneratedColumn<String> data = GeneratedColumn<String>(
      'data', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _completedAtMeta =
      const VerificationMeta('completedAt');
  @override
  late final GeneratedColumn<DateTime> completedAt = GeneratedColumn<DateTime>(
      'completed_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns =>
      [id, activityId, date, data, status, completedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'activity_records';
  @override
  VerificationContext validateIntegrity(Insertable<ActivityRecord> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('activity_id')) {
      context.handle(
          _activityIdMeta,
          activityId.isAcceptableOrUnknown(
              data['activity_id']!, _activityIdMeta));
    } else if (isInserting) {
      context.missing(_activityIdMeta);
    }
    if (data.containsKey('date')) {
      context.handle(
          _dateMeta, date.isAcceptableOrUnknown(data['date']!, _dateMeta));
    } else if (isInserting) {
      context.missing(_dateMeta);
    }
    if (data.containsKey('data')) {
      context.handle(
          _dataMeta, this.data.isAcceptableOrUnknown(data['data']!, _dataMeta));
    } else if (isInserting) {
      context.missing(_dataMeta);
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    } else if (isInserting) {
      context.missing(_statusMeta);
    }
    if (data.containsKey('completed_at')) {
      context.handle(
          _completedAtMeta,
          completedAt.isAcceptableOrUnknown(
              data['completed_at']!, _completedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ActivityRecord map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ActivityRecord(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      activityId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}activity_id'])!,
      date: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}date'])!,
      data: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}data'])!,
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!,
      completedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}completed_at']),
    );
  }

  @override
  $ActivityRecordsTable createAlias(String alias) {
    return $ActivityRecordsTable(attachedDatabase, alias);
  }
}

class ActivityRecord extends DataClass implements Insertable<ActivityRecord> {
  final int id;
  final int activityId;
  final DateTime date;
  final String data;
  final String status;
  final DateTime? completedAt;
  const ActivityRecord(
      {required this.id,
      required this.activityId,
      required this.date,
      required this.data,
      required this.status,
      this.completedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['activity_id'] = Variable<int>(activityId);
    map['date'] = Variable<DateTime>(date);
    map['data'] = Variable<String>(data);
    map['status'] = Variable<String>(status);
    if (!nullToAbsent || completedAt != null) {
      map['completed_at'] = Variable<DateTime>(completedAt);
    }
    return map;
  }

  ActivityRecordsCompanion toCompanion(bool nullToAbsent) {
    return ActivityRecordsCompanion(
      id: Value(id),
      activityId: Value(activityId),
      date: Value(date),
      data: Value(data),
      status: Value(status),
      completedAt: completedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(completedAt),
    );
  }

  factory ActivityRecord.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ActivityRecord(
      id: serializer.fromJson<int>(json['id']),
      activityId: serializer.fromJson<int>(json['activityId']),
      date: serializer.fromJson<DateTime>(json['date']),
      data: serializer.fromJson<String>(json['data']),
      status: serializer.fromJson<String>(json['status']),
      completedAt: serializer.fromJson<DateTime?>(json['completedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'activityId': serializer.toJson<int>(activityId),
      'date': serializer.toJson<DateTime>(date),
      'data': serializer.toJson<String>(data),
      'status': serializer.toJson<String>(status),
      'completedAt': serializer.toJson<DateTime?>(completedAt),
    };
  }

  ActivityRecord copyWith(
          {int? id,
          int? activityId,
          DateTime? date,
          String? data,
          String? status,
          Value<DateTime?> completedAt = const Value.absent()}) =>
      ActivityRecord(
        id: id ?? this.id,
        activityId: activityId ?? this.activityId,
        date: date ?? this.date,
        data: data ?? this.data,
        status: status ?? this.status,
        completedAt: completedAt.present ? completedAt.value : this.completedAt,
      );
  ActivityRecord copyWithCompanion(ActivityRecordsCompanion data) {
    return ActivityRecord(
      id: data.id.present ? data.id.value : this.id,
      activityId:
          data.activityId.present ? data.activityId.value : this.activityId,
      date: data.date.present ? data.date.value : this.date,
      data: data.data.present ? data.data.value : this.data,
      status: data.status.present ? data.status.value : this.status,
      completedAt:
          data.completedAt.present ? data.completedAt.value : this.completedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ActivityRecord(')
          ..write('id: $id, ')
          ..write('activityId: $activityId, ')
          ..write('date: $date, ')
          ..write('data: $data, ')
          ..write('status: $status, ')
          ..write('completedAt: $completedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, activityId, date, data, status, completedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ActivityRecord &&
          other.id == this.id &&
          other.activityId == this.activityId &&
          other.date == this.date &&
          other.data == this.data &&
          other.status == this.status &&
          other.completedAt == this.completedAt);
}

class ActivityRecordsCompanion extends UpdateCompanion<ActivityRecord> {
  final Value<int> id;
  final Value<int> activityId;
  final Value<DateTime> date;
  final Value<String> data;
  final Value<String> status;
  final Value<DateTime?> completedAt;
  const ActivityRecordsCompanion({
    this.id = const Value.absent(),
    this.activityId = const Value.absent(),
    this.date = const Value.absent(),
    this.data = const Value.absent(),
    this.status = const Value.absent(),
    this.completedAt = const Value.absent(),
  });
  ActivityRecordsCompanion.insert({
    this.id = const Value.absent(),
    required int activityId,
    required DateTime date,
    required String data,
    required String status,
    this.completedAt = const Value.absent(),
  })  : activityId = Value(activityId),
        date = Value(date),
        data = Value(data),
        status = Value(status);
  static Insertable<ActivityRecord> custom({
    Expression<int>? id,
    Expression<int>? activityId,
    Expression<DateTime>? date,
    Expression<String>? data,
    Expression<String>? status,
    Expression<DateTime>? completedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (activityId != null) 'activity_id': activityId,
      if (date != null) 'date': date,
      if (data != null) 'data': data,
      if (status != null) 'status': status,
      if (completedAt != null) 'completed_at': completedAt,
    });
  }

  ActivityRecordsCompanion copyWith(
      {Value<int>? id,
      Value<int>? activityId,
      Value<DateTime>? date,
      Value<String>? data,
      Value<String>? status,
      Value<DateTime?>? completedAt}) {
    return ActivityRecordsCompanion(
      id: id ?? this.id,
      activityId: activityId ?? this.activityId,
      date: date ?? this.date,
      data: data ?? this.data,
      status: status ?? this.status,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (activityId.present) {
      map['activity_id'] = Variable<int>(activityId.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(date.value);
    }
    if (data.present) {
      map['data'] = Variable<String>(data.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (completedAt.present) {
      map['completed_at'] = Variable<DateTime>(completedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ActivityRecordsCompanion(')
          ..write('id: $id, ')
          ..write('activityId: $activityId, ')
          ..write('date: $date, ')
          ..write('data: $data, ')
          ..write('status: $status, ')
          ..write('completedAt: $completedAt')
          ..write(')'))
        .toString();
  }
}

class $RoutineActivityConfigsTable extends RoutineActivityConfigs
    with TableInfo<$RoutineActivityConfigsTable, RoutineActivityConfig> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $RoutineActivityConfigsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _routineIdMeta =
      const VerificationMeta('routineId');
  @override
  late final GeneratedColumn<int> routineId = GeneratedColumn<int>(
      'routine_id', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES check_in_routines (id)'));
  static const VerificationMeta _activityIdMeta =
      const VerificationMeta('activityId');
  @override
  late final GeneratedColumn<int> activityId = GeneratedColumn<int>(
      'activity_id', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES check_in_activities (id)'));
  static const VerificationMeta _configMeta = const VerificationMeta('config');
  @override
  late final GeneratedColumn<String> config = GeneratedColumn<String>(
      'config', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _lastUpdatedMeta =
      const VerificationMeta('lastUpdated');
  @override
  late final GeneratedColumn<DateTime> lastUpdated = GeneratedColumn<DateTime>(
      'last_updated', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns =>
      [id, routineId, activityId, config, lastUpdated];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'routine_activity_configs';
  @override
  VerificationContext validateIntegrity(
      Insertable<RoutineActivityConfig> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('routine_id')) {
      context.handle(_routineIdMeta,
          routineId.isAcceptableOrUnknown(data['routine_id']!, _routineIdMeta));
    } else if (isInserting) {
      context.missing(_routineIdMeta);
    }
    if (data.containsKey('activity_id')) {
      context.handle(
          _activityIdMeta,
          activityId.isAcceptableOrUnknown(
              data['activity_id']!, _activityIdMeta));
    } else if (isInserting) {
      context.missing(_activityIdMeta);
    }
    if (data.containsKey('config')) {
      context.handle(_configMeta,
          config.isAcceptableOrUnknown(data['config']!, _configMeta));
    } else if (isInserting) {
      context.missing(_configMeta);
    }
    if (data.containsKey('last_updated')) {
      context.handle(
          _lastUpdatedMeta,
          lastUpdated.isAcceptableOrUnknown(
              data['last_updated']!, _lastUpdatedMeta));
    } else if (isInserting) {
      context.missing(_lastUpdatedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {routineId, activityId},
      ];
  @override
  RoutineActivityConfig map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return RoutineActivityConfig(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      routineId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}routine_id'])!,
      activityId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}activity_id'])!,
      config: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}config'])!,
      lastUpdated: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_updated'])!,
    );
  }

  @override
  $RoutineActivityConfigsTable createAlias(String alias) {
    return $RoutineActivityConfigsTable(attachedDatabase, alias);
  }
}

class RoutineActivityConfig extends DataClass
    implements Insertable<RoutineActivityConfig> {
  final int id;
  final int routineId;
  final int activityId;
  final String config;
  final DateTime lastUpdated;
  const RoutineActivityConfig(
      {required this.id,
      required this.routineId,
      required this.activityId,
      required this.config,
      required this.lastUpdated});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['routine_id'] = Variable<int>(routineId);
    map['activity_id'] = Variable<int>(activityId);
    map['config'] = Variable<String>(config);
    map['last_updated'] = Variable<DateTime>(lastUpdated);
    return map;
  }

  RoutineActivityConfigsCompanion toCompanion(bool nullToAbsent) {
    return RoutineActivityConfigsCompanion(
      id: Value(id),
      routineId: Value(routineId),
      activityId: Value(activityId),
      config: Value(config),
      lastUpdated: Value(lastUpdated),
    );
  }

  factory RoutineActivityConfig.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return RoutineActivityConfig(
      id: serializer.fromJson<int>(json['id']),
      routineId: serializer.fromJson<int>(json['routineId']),
      activityId: serializer.fromJson<int>(json['activityId']),
      config: serializer.fromJson<String>(json['config']),
      lastUpdated: serializer.fromJson<DateTime>(json['lastUpdated']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'routineId': serializer.toJson<int>(routineId),
      'activityId': serializer.toJson<int>(activityId),
      'config': serializer.toJson<String>(config),
      'lastUpdated': serializer.toJson<DateTime>(lastUpdated),
    };
  }

  RoutineActivityConfig copyWith(
          {int? id,
          int? routineId,
          int? activityId,
          String? config,
          DateTime? lastUpdated}) =>
      RoutineActivityConfig(
        id: id ?? this.id,
        routineId: routineId ?? this.routineId,
        activityId: activityId ?? this.activityId,
        config: config ?? this.config,
        lastUpdated: lastUpdated ?? this.lastUpdated,
      );
  RoutineActivityConfig copyWithCompanion(
      RoutineActivityConfigsCompanion data) {
    return RoutineActivityConfig(
      id: data.id.present ? data.id.value : this.id,
      routineId: data.routineId.present ? data.routineId.value : this.routineId,
      activityId:
          data.activityId.present ? data.activityId.value : this.activityId,
      config: data.config.present ? data.config.value : this.config,
      lastUpdated:
          data.lastUpdated.present ? data.lastUpdated.value : this.lastUpdated,
    );
  }

  @override
  String toString() {
    return (StringBuffer('RoutineActivityConfig(')
          ..write('id: $id, ')
          ..write('routineId: $routineId, ')
          ..write('activityId: $activityId, ')
          ..write('config: $config, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, routineId, activityId, config, lastUpdated);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is RoutineActivityConfig &&
          other.id == this.id &&
          other.routineId == this.routineId &&
          other.activityId == this.activityId &&
          other.config == this.config &&
          other.lastUpdated == this.lastUpdated);
}

class RoutineActivityConfigsCompanion
    extends UpdateCompanion<RoutineActivityConfig> {
  final Value<int> id;
  final Value<int> routineId;
  final Value<int> activityId;
  final Value<String> config;
  final Value<DateTime> lastUpdated;
  const RoutineActivityConfigsCompanion({
    this.id = const Value.absent(),
    this.routineId = const Value.absent(),
    this.activityId = const Value.absent(),
    this.config = const Value.absent(),
    this.lastUpdated = const Value.absent(),
  });
  RoutineActivityConfigsCompanion.insert({
    this.id = const Value.absent(),
    required int routineId,
    required int activityId,
    required String config,
    required DateTime lastUpdated,
  })  : routineId = Value(routineId),
        activityId = Value(activityId),
        config = Value(config),
        lastUpdated = Value(lastUpdated);
  static Insertable<RoutineActivityConfig> custom({
    Expression<int>? id,
    Expression<int>? routineId,
    Expression<int>? activityId,
    Expression<String>? config,
    Expression<DateTime>? lastUpdated,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (routineId != null) 'routine_id': routineId,
      if (activityId != null) 'activity_id': activityId,
      if (config != null) 'config': config,
      if (lastUpdated != null) 'last_updated': lastUpdated,
    });
  }

  RoutineActivityConfigsCompanion copyWith(
      {Value<int>? id,
      Value<int>? routineId,
      Value<int>? activityId,
      Value<String>? config,
      Value<DateTime>? lastUpdated}) {
    return RoutineActivityConfigsCompanion(
      id: id ?? this.id,
      routineId: routineId ?? this.routineId,
      activityId: activityId ?? this.activityId,
      config: config ?? this.config,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (routineId.present) {
      map['routine_id'] = Variable<int>(routineId.value);
    }
    if (activityId.present) {
      map['activity_id'] = Variable<int>(activityId.value);
    }
    if (config.present) {
      map['config'] = Variable<String>(config.value);
    }
    if (lastUpdated.present) {
      map['last_updated'] = Variable<DateTime>(lastUpdated.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('RoutineActivityConfigsCompanion(')
          ..write('id: $id, ')
          ..write('routineId: $routineId, ')
          ..write('activityId: $activityId, ')
          ..write('config: $config, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }
}

abstract class _$JournalDatabase extends GeneratedDatabase {
  _$JournalDatabase(QueryExecutor e) : super(e);
  $JournalDatabaseManager get managers => $JournalDatabaseManager(this);
  late final $IntentionsTable intentions = $IntentionsTable(this);
  late final $CheckInRoutinesTable checkInRoutines =
      $CheckInRoutinesTable(this);
  late final $JournalEntriesTable journalEntries = $JournalEntriesTable(this);
  late final $CheckInActivitiesTable checkInActivities =
      $CheckInActivitiesTable(this);
  late final $ActivityRecordsTable activityRecords =
      $ActivityRecordsTable(this);
  late final $RoutineActivityConfigsTable routineActivityConfigs =
      $RoutineActivityConfigsTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        intentions,
        checkInRoutines,
        journalEntries,
        checkInActivities,
        activityRecords,
        routineActivityConfigs
      ];
}

typedef $$IntentionsTableCreateCompanionBuilder = IntentionsCompanion Function({
  Value<int> id,
  required String name,
});
typedef $$IntentionsTableUpdateCompanionBuilder = IntentionsCompanion Function({
  Value<int> id,
  Value<String> name,
});

final class $$IntentionsTableReferences
    extends BaseReferences<_$JournalDatabase, $IntentionsTable, Intention> {
  $$IntentionsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$CheckInRoutinesTable, List<CheckInRoutine>>
      _checkInRoutinesRefsTable(_$JournalDatabase db) =>
          MultiTypedResultKey.fromTable(db.checkInRoutines,
              aliasName: $_aliasNameGenerator(
                  db.intentions.id, db.checkInRoutines.intentionId));

  $$CheckInRoutinesTableProcessedTableManager get checkInRoutinesRefs {
    final manager =
        $$CheckInRoutinesTableTableManager($_db, $_db.checkInRoutines)
            .filter((f) => f.intentionId.id($_item.id));

    final cache =
        $_typedResult.readTableOrNull(_checkInRoutinesRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$IntentionsTableFilterComposer
    extends Composer<_$JournalDatabase, $IntentionsTable> {
  $$IntentionsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  Expression<bool> checkInRoutinesRefs(
      Expression<bool> Function($$CheckInRoutinesTableFilterComposer f) f) {
    final $$CheckInRoutinesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.intentionId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableFilterComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$IntentionsTableOrderingComposer
    extends Composer<_$JournalDatabase, $IntentionsTable> {
  $$IntentionsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));
}

class $$IntentionsTableAnnotationComposer
    extends Composer<_$JournalDatabase, $IntentionsTable> {
  $$IntentionsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  Expression<T> checkInRoutinesRefs<T extends Object>(
      Expression<T> Function($$CheckInRoutinesTableAnnotationComposer a) f) {
    final $$CheckInRoutinesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.intentionId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableAnnotationComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$IntentionsTableTableManager extends RootTableManager<
    _$JournalDatabase,
    $IntentionsTable,
    Intention,
    $$IntentionsTableFilterComposer,
    $$IntentionsTableOrderingComposer,
    $$IntentionsTableAnnotationComposer,
    $$IntentionsTableCreateCompanionBuilder,
    $$IntentionsTableUpdateCompanionBuilder,
    (Intention, $$IntentionsTableReferences),
    Intention,
    PrefetchHooks Function({bool checkInRoutinesRefs})> {
  $$IntentionsTableTableManager(_$JournalDatabase db, $IntentionsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$IntentionsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$IntentionsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$IntentionsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> name = const Value.absent(),
          }) =>
              IntentionsCompanion(
            id: id,
            name: name,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String name,
          }) =>
              IntentionsCompanion.insert(
            id: id,
            name: name,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$IntentionsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({checkInRoutinesRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (checkInRoutinesRefs) db.checkInRoutines
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (checkInRoutinesRefs)
                    await $_getPrefetchedData(
                        currentTable: table,
                        referencedTable: $$IntentionsTableReferences
                            ._checkInRoutinesRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$IntentionsTableReferences(db, table, p0)
                                .checkInRoutinesRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.intentionId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$IntentionsTableProcessedTableManager = ProcessedTableManager<
    _$JournalDatabase,
    $IntentionsTable,
    Intention,
    $$IntentionsTableFilterComposer,
    $$IntentionsTableOrderingComposer,
    $$IntentionsTableAnnotationComposer,
    $$IntentionsTableCreateCompanionBuilder,
    $$IntentionsTableUpdateCompanionBuilder,
    (Intention, $$IntentionsTableReferences),
    Intention,
    PrefetchHooks Function({bool checkInRoutinesRefs})>;
typedef $$CheckInRoutinesTableCreateCompanionBuilder = CheckInRoutinesCompanion
    Function({
  Value<int> id,
  required int intentionId,
  required String type,
  required String name,
  Value<bool> enabled,
  required String activities,
  required DateTime scheduledTime,
  Value<bool> notificationEnabled,
  required DateTime lastUpdated,
});
typedef $$CheckInRoutinesTableUpdateCompanionBuilder = CheckInRoutinesCompanion
    Function({
  Value<int> id,
  Value<int> intentionId,
  Value<String> type,
  Value<String> name,
  Value<bool> enabled,
  Value<String> activities,
  Value<DateTime> scheduledTime,
  Value<bool> notificationEnabled,
  Value<DateTime> lastUpdated,
});

final class $$CheckInRoutinesTableReferences extends BaseReferences<
    _$JournalDatabase, $CheckInRoutinesTable, CheckInRoutine> {
  $$CheckInRoutinesTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static $IntentionsTable _intentionIdTable(_$JournalDatabase db) =>
      db.intentions.createAlias($_aliasNameGenerator(
          db.checkInRoutines.intentionId, db.intentions.id));

  $$IntentionsTableProcessedTableManager get intentionId {
    final manager = $$IntentionsTableTableManager($_db, $_db.intentions)
        .filter((f) => f.id($_item.intentionId));
    final item = $_typedResult.readTableOrNull(_intentionIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static MultiTypedResultKey<$JournalEntriesTable, List<JournalEntry>>
      _journalEntriesRefsTable(_$JournalDatabase db) =>
          MultiTypedResultKey.fromTable(db.journalEntries,
              aliasName: $_aliasNameGenerator(
                  db.checkInRoutines.id, db.journalEntries.routineId));

  $$JournalEntriesTableProcessedTableManager get journalEntriesRefs {
    final manager = $$JournalEntriesTableTableManager($_db, $_db.journalEntries)
        .filter((f) => f.routineId.id($_item.id));

    final cache = $_typedResult.readTableOrNull(_journalEntriesRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$RoutineActivityConfigsTable,
      List<RoutineActivityConfig>> _routineActivityConfigsRefsTable(
          _$JournalDatabase db) =>
      MultiTypedResultKey.fromTable(db.routineActivityConfigs,
          aliasName: $_aliasNameGenerator(
              db.checkInRoutines.id, db.routineActivityConfigs.routineId));

  $$RoutineActivityConfigsTableProcessedTableManager
      get routineActivityConfigsRefs {
    final manager = $$RoutineActivityConfigsTableTableManager(
            $_db, $_db.routineActivityConfigs)
        .filter((f) => f.routineId.id($_item.id));

    final cache =
        $_typedResult.readTableOrNull(_routineActivityConfigsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$CheckInRoutinesTableFilterComposer
    extends Composer<_$JournalDatabase, $CheckInRoutinesTable> {
  $$CheckInRoutinesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get enabled => $composableBuilder(
      column: $table.enabled, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get activities => $composableBuilder(
      column: $table.activities, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get scheduledTime => $composableBuilder(
      column: $table.scheduledTime, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get notificationEnabled => $composableBuilder(
      column: $table.notificationEnabled,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => ColumnFilters(column));

  $$IntentionsTableFilterComposer get intentionId {
    final $$IntentionsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.intentionId,
        referencedTable: $db.intentions,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$IntentionsTableFilterComposer(
              $db: $db,
              $table: $db.intentions,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  Expression<bool> journalEntriesRefs(
      Expression<bool> Function($$JournalEntriesTableFilterComposer f) f) {
    final $$JournalEntriesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.journalEntries,
        getReferencedColumn: (t) => t.routineId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$JournalEntriesTableFilterComposer(
              $db: $db,
              $table: $db.journalEntries,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<bool> routineActivityConfigsRefs(
      Expression<bool> Function($$RoutineActivityConfigsTableFilterComposer f)
          f) {
    final $$RoutineActivityConfigsTableFilterComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.id,
            referencedTable: $db.routineActivityConfigs,
            getReferencedColumn: (t) => t.routineId,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$RoutineActivityConfigsTableFilterComposer(
                  $db: $db,
                  $table: $db.routineActivityConfigs,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$CheckInRoutinesTableOrderingComposer
    extends Composer<_$JournalDatabase, $CheckInRoutinesTable> {
  $$CheckInRoutinesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get enabled => $composableBuilder(
      column: $table.enabled, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get activities => $composableBuilder(
      column: $table.activities, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get scheduledTime => $composableBuilder(
      column: $table.scheduledTime,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get notificationEnabled => $composableBuilder(
      column: $table.notificationEnabled,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => ColumnOrderings(column));

  $$IntentionsTableOrderingComposer get intentionId {
    final $$IntentionsTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.intentionId,
        referencedTable: $db.intentions,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$IntentionsTableOrderingComposer(
              $db: $db,
              $table: $db.intentions,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$CheckInRoutinesTableAnnotationComposer
    extends Composer<_$JournalDatabase, $CheckInRoutinesTable> {
  $$CheckInRoutinesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<bool> get enabled =>
      $composableBuilder(column: $table.enabled, builder: (column) => column);

  GeneratedColumn<String> get activities => $composableBuilder(
      column: $table.activities, builder: (column) => column);

  GeneratedColumn<DateTime> get scheduledTime => $composableBuilder(
      column: $table.scheduledTime, builder: (column) => column);

  GeneratedColumn<bool> get notificationEnabled => $composableBuilder(
      column: $table.notificationEnabled, builder: (column) => column);

  GeneratedColumn<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => column);

  $$IntentionsTableAnnotationComposer get intentionId {
    final $$IntentionsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.intentionId,
        referencedTable: $db.intentions,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$IntentionsTableAnnotationComposer(
              $db: $db,
              $table: $db.intentions,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  Expression<T> journalEntriesRefs<T extends Object>(
      Expression<T> Function($$JournalEntriesTableAnnotationComposer a) f) {
    final $$JournalEntriesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.journalEntries,
        getReferencedColumn: (t) => t.routineId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$JournalEntriesTableAnnotationComposer(
              $db: $db,
              $table: $db.journalEntries,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<T> routineActivityConfigsRefs<T extends Object>(
      Expression<T> Function($$RoutineActivityConfigsTableAnnotationComposer a)
          f) {
    final $$RoutineActivityConfigsTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.id,
            referencedTable: $db.routineActivityConfigs,
            getReferencedColumn: (t) => t.routineId,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$RoutineActivityConfigsTableAnnotationComposer(
                  $db: $db,
                  $table: $db.routineActivityConfigs,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$CheckInRoutinesTableTableManager extends RootTableManager<
    _$JournalDatabase,
    $CheckInRoutinesTable,
    CheckInRoutine,
    $$CheckInRoutinesTableFilterComposer,
    $$CheckInRoutinesTableOrderingComposer,
    $$CheckInRoutinesTableAnnotationComposer,
    $$CheckInRoutinesTableCreateCompanionBuilder,
    $$CheckInRoutinesTableUpdateCompanionBuilder,
    (CheckInRoutine, $$CheckInRoutinesTableReferences),
    CheckInRoutine,
    PrefetchHooks Function(
        {bool intentionId,
        bool journalEntriesRefs,
        bool routineActivityConfigsRefs})> {
  $$CheckInRoutinesTableTableManager(
      _$JournalDatabase db, $CheckInRoutinesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CheckInRoutinesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CheckInRoutinesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CheckInRoutinesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> intentionId = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<bool> enabled = const Value.absent(),
            Value<String> activities = const Value.absent(),
            Value<DateTime> scheduledTime = const Value.absent(),
            Value<bool> notificationEnabled = const Value.absent(),
            Value<DateTime> lastUpdated = const Value.absent(),
          }) =>
              CheckInRoutinesCompanion(
            id: id,
            intentionId: intentionId,
            type: type,
            name: name,
            enabled: enabled,
            activities: activities,
            scheduledTime: scheduledTime,
            notificationEnabled: notificationEnabled,
            lastUpdated: lastUpdated,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int intentionId,
            required String type,
            required String name,
            Value<bool> enabled = const Value.absent(),
            required String activities,
            required DateTime scheduledTime,
            Value<bool> notificationEnabled = const Value.absent(),
            required DateTime lastUpdated,
          }) =>
              CheckInRoutinesCompanion.insert(
            id: id,
            intentionId: intentionId,
            type: type,
            name: name,
            enabled: enabled,
            activities: activities,
            scheduledTime: scheduledTime,
            notificationEnabled: notificationEnabled,
            lastUpdated: lastUpdated,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$CheckInRoutinesTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: (
              {intentionId = false,
              journalEntriesRefs = false,
              routineActivityConfigsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (journalEntriesRefs) db.journalEntries,
                if (routineActivityConfigsRefs) db.routineActivityConfigs
              ],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (intentionId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.intentionId,
                    referencedTable:
                        $$CheckInRoutinesTableReferences._intentionIdTable(db),
                    referencedColumn: $$CheckInRoutinesTableReferences
                        ._intentionIdTable(db)
                        .id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [
                  if (journalEntriesRefs)
                    await $_getPrefetchedData(
                        currentTable: table,
                        referencedTable: $$CheckInRoutinesTableReferences
                            ._journalEntriesRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$CheckInRoutinesTableReferences(db, table, p0)
                                .journalEntriesRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.routineId == item.id),
                        typedResults: items),
                  if (routineActivityConfigsRefs)
                    await $_getPrefetchedData(
                        currentTable: table,
                        referencedTable: $$CheckInRoutinesTableReferences
                            ._routineActivityConfigsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$CheckInRoutinesTableReferences(db, table, p0)
                                .routineActivityConfigsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.routineId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$CheckInRoutinesTableProcessedTableManager = ProcessedTableManager<
    _$JournalDatabase,
    $CheckInRoutinesTable,
    CheckInRoutine,
    $$CheckInRoutinesTableFilterComposer,
    $$CheckInRoutinesTableOrderingComposer,
    $$CheckInRoutinesTableAnnotationComposer,
    $$CheckInRoutinesTableCreateCompanionBuilder,
    $$CheckInRoutinesTableUpdateCompanionBuilder,
    (CheckInRoutine, $$CheckInRoutinesTableReferences),
    CheckInRoutine,
    PrefetchHooks Function(
        {bool intentionId,
        bool journalEntriesRefs,
        bool routineActivityConfigsRefs})>;
typedef $$JournalEntriesTableCreateCompanionBuilder = JournalEntriesCompanion
    Function({
  Value<int> id,
  required int routineId,
  required DateTime date,
  required String responses,
  Value<String?> mood,
  Value<int?> meditationMinutes,
  required String status,
  Value<DateTime?> completedAt,
});
typedef $$JournalEntriesTableUpdateCompanionBuilder = JournalEntriesCompanion
    Function({
  Value<int> id,
  Value<int> routineId,
  Value<DateTime> date,
  Value<String> responses,
  Value<String?> mood,
  Value<int?> meditationMinutes,
  Value<String> status,
  Value<DateTime?> completedAt,
});

final class $$JournalEntriesTableReferences extends BaseReferences<
    _$JournalDatabase, $JournalEntriesTable, JournalEntry> {
  $$JournalEntriesTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static $CheckInRoutinesTable _routineIdTable(_$JournalDatabase db) =>
      db.checkInRoutines.createAlias($_aliasNameGenerator(
          db.journalEntries.routineId, db.checkInRoutines.id));

  $$CheckInRoutinesTableProcessedTableManager get routineId {
    final manager =
        $$CheckInRoutinesTableTableManager($_db, $_db.checkInRoutines)
            .filter((f) => f.id($_item.routineId));
    final item = $_typedResult.readTableOrNull(_routineIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$JournalEntriesTableFilterComposer
    extends Composer<_$JournalDatabase, $JournalEntriesTable> {
  $$JournalEntriesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get responses => $composableBuilder(
      column: $table.responses, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get mood => $composableBuilder(
      column: $table.mood, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get meditationMinutes => $composableBuilder(
      column: $table.meditationMinutes,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get completedAt => $composableBuilder(
      column: $table.completedAt, builder: (column) => ColumnFilters(column));

  $$CheckInRoutinesTableFilterComposer get routineId {
    final $$CheckInRoutinesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.routineId,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableFilterComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$JournalEntriesTableOrderingComposer
    extends Composer<_$JournalDatabase, $JournalEntriesTable> {
  $$JournalEntriesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get responses => $composableBuilder(
      column: $table.responses, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get mood => $composableBuilder(
      column: $table.mood, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get meditationMinutes => $composableBuilder(
      column: $table.meditationMinutes,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get completedAt => $composableBuilder(
      column: $table.completedAt, builder: (column) => ColumnOrderings(column));

  $$CheckInRoutinesTableOrderingComposer get routineId {
    final $$CheckInRoutinesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.routineId,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableOrderingComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$JournalEntriesTableAnnotationComposer
    extends Composer<_$JournalDatabase, $JournalEntriesTable> {
  $$JournalEntriesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<String> get responses =>
      $composableBuilder(column: $table.responses, builder: (column) => column);

  GeneratedColumn<String> get mood =>
      $composableBuilder(column: $table.mood, builder: (column) => column);

  GeneratedColumn<int> get meditationMinutes => $composableBuilder(
      column: $table.meditationMinutes, builder: (column) => column);

  GeneratedColumn<String> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<DateTime> get completedAt => $composableBuilder(
      column: $table.completedAt, builder: (column) => column);

  $$CheckInRoutinesTableAnnotationComposer get routineId {
    final $$CheckInRoutinesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.routineId,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableAnnotationComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$JournalEntriesTableTableManager extends RootTableManager<
    _$JournalDatabase,
    $JournalEntriesTable,
    JournalEntry,
    $$JournalEntriesTableFilterComposer,
    $$JournalEntriesTableOrderingComposer,
    $$JournalEntriesTableAnnotationComposer,
    $$JournalEntriesTableCreateCompanionBuilder,
    $$JournalEntriesTableUpdateCompanionBuilder,
    (JournalEntry, $$JournalEntriesTableReferences),
    JournalEntry,
    PrefetchHooks Function({bool routineId})> {
  $$JournalEntriesTableTableManager(
      _$JournalDatabase db, $JournalEntriesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$JournalEntriesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$JournalEntriesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$JournalEntriesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> routineId = const Value.absent(),
            Value<DateTime> date = const Value.absent(),
            Value<String> responses = const Value.absent(),
            Value<String?> mood = const Value.absent(),
            Value<int?> meditationMinutes = const Value.absent(),
            Value<String> status = const Value.absent(),
            Value<DateTime?> completedAt = const Value.absent(),
          }) =>
              JournalEntriesCompanion(
            id: id,
            routineId: routineId,
            date: date,
            responses: responses,
            mood: mood,
            meditationMinutes: meditationMinutes,
            status: status,
            completedAt: completedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int routineId,
            required DateTime date,
            required String responses,
            Value<String?> mood = const Value.absent(),
            Value<int?> meditationMinutes = const Value.absent(),
            required String status,
            Value<DateTime?> completedAt = const Value.absent(),
          }) =>
              JournalEntriesCompanion.insert(
            id: id,
            routineId: routineId,
            date: date,
            responses: responses,
            mood: mood,
            meditationMinutes: meditationMinutes,
            status: status,
            completedAt: completedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$JournalEntriesTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({routineId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (routineId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.routineId,
                    referencedTable:
                        $$JournalEntriesTableReferences._routineIdTable(db),
                    referencedColumn:
                        $$JournalEntriesTableReferences._routineIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$JournalEntriesTableProcessedTableManager = ProcessedTableManager<
    _$JournalDatabase,
    $JournalEntriesTable,
    JournalEntry,
    $$JournalEntriesTableFilterComposer,
    $$JournalEntriesTableOrderingComposer,
    $$JournalEntriesTableAnnotationComposer,
    $$JournalEntriesTableCreateCompanionBuilder,
    $$JournalEntriesTableUpdateCompanionBuilder,
    (JournalEntry, $$JournalEntriesTableReferences),
    JournalEntry,
    PrefetchHooks Function({bool routineId})>;
typedef $$CheckInActivitiesTableCreateCompanionBuilder
    = CheckInActivitiesCompanion Function({
  Value<int> id,
  required String type,
  required String name,
  required String config,
  Value<bool> enabled,
  required DateTime lastUpdated,
});
typedef $$CheckInActivitiesTableUpdateCompanionBuilder
    = CheckInActivitiesCompanion Function({
  Value<int> id,
  Value<String> type,
  Value<String> name,
  Value<String> config,
  Value<bool> enabled,
  Value<DateTime> lastUpdated,
});

final class $$CheckInActivitiesTableReferences extends BaseReferences<
    _$JournalDatabase, $CheckInActivitiesTable, CheckInActivity> {
  $$CheckInActivitiesTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$ActivityRecordsTable, List<ActivityRecord>>
      _activityRecordsRefsTable(_$JournalDatabase db) =>
          MultiTypedResultKey.fromTable(db.activityRecords,
              aliasName: $_aliasNameGenerator(
                  db.checkInActivities.id, db.activityRecords.activityId));

  $$ActivityRecordsTableProcessedTableManager get activityRecordsRefs {
    final manager =
        $$ActivityRecordsTableTableManager($_db, $_db.activityRecords)
            .filter((f) => f.activityId.id($_item.id));

    final cache =
        $_typedResult.readTableOrNull(_activityRecordsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$RoutineActivityConfigsTable,
      List<RoutineActivityConfig>> _routineActivityConfigsRefsTable(
          _$JournalDatabase db) =>
      MultiTypedResultKey.fromTable(db.routineActivityConfigs,
          aliasName: $_aliasNameGenerator(
              db.checkInActivities.id, db.routineActivityConfigs.activityId));

  $$RoutineActivityConfigsTableProcessedTableManager
      get routineActivityConfigsRefs {
    final manager = $$RoutineActivityConfigsTableTableManager(
            $_db, $_db.routineActivityConfigs)
        .filter((f) => f.activityId.id($_item.id));

    final cache =
        $_typedResult.readTableOrNull(_routineActivityConfigsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$CheckInActivitiesTableFilterComposer
    extends Composer<_$JournalDatabase, $CheckInActivitiesTable> {
  $$CheckInActivitiesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get config => $composableBuilder(
      column: $table.config, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get enabled => $composableBuilder(
      column: $table.enabled, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => ColumnFilters(column));

  Expression<bool> activityRecordsRefs(
      Expression<bool> Function($$ActivityRecordsTableFilterComposer f) f) {
    final $$ActivityRecordsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.activityRecords,
        getReferencedColumn: (t) => t.activityId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ActivityRecordsTableFilterComposer(
              $db: $db,
              $table: $db.activityRecords,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<bool> routineActivityConfigsRefs(
      Expression<bool> Function($$RoutineActivityConfigsTableFilterComposer f)
          f) {
    final $$RoutineActivityConfigsTableFilterComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.id,
            referencedTable: $db.routineActivityConfigs,
            getReferencedColumn: (t) => t.activityId,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$RoutineActivityConfigsTableFilterComposer(
                  $db: $db,
                  $table: $db.routineActivityConfigs,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$CheckInActivitiesTableOrderingComposer
    extends Composer<_$JournalDatabase, $CheckInActivitiesTable> {
  $$CheckInActivitiesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get config => $composableBuilder(
      column: $table.config, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get enabled => $composableBuilder(
      column: $table.enabled, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => ColumnOrderings(column));
}

class $$CheckInActivitiesTableAnnotationComposer
    extends Composer<_$JournalDatabase, $CheckInActivitiesTable> {
  $$CheckInActivitiesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get config =>
      $composableBuilder(column: $table.config, builder: (column) => column);

  GeneratedColumn<bool> get enabled =>
      $composableBuilder(column: $table.enabled, builder: (column) => column);

  GeneratedColumn<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => column);

  Expression<T> activityRecordsRefs<T extends Object>(
      Expression<T> Function($$ActivityRecordsTableAnnotationComposer a) f) {
    final $$ActivityRecordsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.activityRecords,
        getReferencedColumn: (t) => t.activityId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ActivityRecordsTableAnnotationComposer(
              $db: $db,
              $table: $db.activityRecords,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<T> routineActivityConfigsRefs<T extends Object>(
      Expression<T> Function($$RoutineActivityConfigsTableAnnotationComposer a)
          f) {
    final $$RoutineActivityConfigsTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.id,
            referencedTable: $db.routineActivityConfigs,
            getReferencedColumn: (t) => t.activityId,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$RoutineActivityConfigsTableAnnotationComposer(
                  $db: $db,
                  $table: $db.routineActivityConfigs,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$CheckInActivitiesTableTableManager extends RootTableManager<
    _$JournalDatabase,
    $CheckInActivitiesTable,
    CheckInActivity,
    $$CheckInActivitiesTableFilterComposer,
    $$CheckInActivitiesTableOrderingComposer,
    $$CheckInActivitiesTableAnnotationComposer,
    $$CheckInActivitiesTableCreateCompanionBuilder,
    $$CheckInActivitiesTableUpdateCompanionBuilder,
    (CheckInActivity, $$CheckInActivitiesTableReferences),
    CheckInActivity,
    PrefetchHooks Function(
        {bool activityRecordsRefs, bool routineActivityConfigsRefs})> {
  $$CheckInActivitiesTableTableManager(
      _$JournalDatabase db, $CheckInActivitiesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CheckInActivitiesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CheckInActivitiesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CheckInActivitiesTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> config = const Value.absent(),
            Value<bool> enabled = const Value.absent(),
            Value<DateTime> lastUpdated = const Value.absent(),
          }) =>
              CheckInActivitiesCompanion(
            id: id,
            type: type,
            name: name,
            config: config,
            enabled: enabled,
            lastUpdated: lastUpdated,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String type,
            required String name,
            required String config,
            Value<bool> enabled = const Value.absent(),
            required DateTime lastUpdated,
          }) =>
              CheckInActivitiesCompanion.insert(
            id: id,
            type: type,
            name: name,
            config: config,
            enabled: enabled,
            lastUpdated: lastUpdated,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$CheckInActivitiesTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: (
              {activityRecordsRefs = false,
              routineActivityConfigsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (activityRecordsRefs) db.activityRecords,
                if (routineActivityConfigsRefs) db.routineActivityConfigs
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (activityRecordsRefs)
                    await $_getPrefetchedData(
                        currentTable: table,
                        referencedTable: $$CheckInActivitiesTableReferences
                            ._activityRecordsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$CheckInActivitiesTableReferences(db, table, p0)
                                .activityRecordsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.activityId == item.id),
                        typedResults: items),
                  if (routineActivityConfigsRefs)
                    await $_getPrefetchedData(
                        currentTable: table,
                        referencedTable: $$CheckInActivitiesTableReferences
                            ._routineActivityConfigsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$CheckInActivitiesTableReferences(db, table, p0)
                                .routineActivityConfigsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.activityId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$CheckInActivitiesTableProcessedTableManager = ProcessedTableManager<
    _$JournalDatabase,
    $CheckInActivitiesTable,
    CheckInActivity,
    $$CheckInActivitiesTableFilterComposer,
    $$CheckInActivitiesTableOrderingComposer,
    $$CheckInActivitiesTableAnnotationComposer,
    $$CheckInActivitiesTableCreateCompanionBuilder,
    $$CheckInActivitiesTableUpdateCompanionBuilder,
    (CheckInActivity, $$CheckInActivitiesTableReferences),
    CheckInActivity,
    PrefetchHooks Function(
        {bool activityRecordsRefs, bool routineActivityConfigsRefs})>;
typedef $$ActivityRecordsTableCreateCompanionBuilder = ActivityRecordsCompanion
    Function({
  Value<int> id,
  required int activityId,
  required DateTime date,
  required String data,
  required String status,
  Value<DateTime?> completedAt,
});
typedef $$ActivityRecordsTableUpdateCompanionBuilder = ActivityRecordsCompanion
    Function({
  Value<int> id,
  Value<int> activityId,
  Value<DateTime> date,
  Value<String> data,
  Value<String> status,
  Value<DateTime?> completedAt,
});

final class $$ActivityRecordsTableReferences extends BaseReferences<
    _$JournalDatabase, $ActivityRecordsTable, ActivityRecord> {
  $$ActivityRecordsTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static $CheckInActivitiesTable _activityIdTable(_$JournalDatabase db) =>
      db.checkInActivities.createAlias($_aliasNameGenerator(
          db.activityRecords.activityId, db.checkInActivities.id));

  $$CheckInActivitiesTableProcessedTableManager get activityId {
    final manager =
        $$CheckInActivitiesTableTableManager($_db, $_db.checkInActivities)
            .filter((f) => f.id($_item.activityId));
    final item = $_typedResult.readTableOrNull(_activityIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$ActivityRecordsTableFilterComposer
    extends Composer<_$JournalDatabase, $ActivityRecordsTable> {
  $$ActivityRecordsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get data => $composableBuilder(
      column: $table.data, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get completedAt => $composableBuilder(
      column: $table.completedAt, builder: (column) => ColumnFilters(column));

  $$CheckInActivitiesTableFilterComposer get activityId {
    final $$CheckInActivitiesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.activityId,
        referencedTable: $db.checkInActivities,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInActivitiesTableFilterComposer(
              $db: $db,
              $table: $db.checkInActivities,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$ActivityRecordsTableOrderingComposer
    extends Composer<_$JournalDatabase, $ActivityRecordsTable> {
  $$ActivityRecordsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get data => $composableBuilder(
      column: $table.data, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get completedAt => $composableBuilder(
      column: $table.completedAt, builder: (column) => ColumnOrderings(column));

  $$CheckInActivitiesTableOrderingComposer get activityId {
    final $$CheckInActivitiesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.activityId,
        referencedTable: $db.checkInActivities,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInActivitiesTableOrderingComposer(
              $db: $db,
              $table: $db.checkInActivities,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$ActivityRecordsTableAnnotationComposer
    extends Composer<_$JournalDatabase, $ActivityRecordsTable> {
  $$ActivityRecordsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<String> get data =>
      $composableBuilder(column: $table.data, builder: (column) => column);

  GeneratedColumn<String> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<DateTime> get completedAt => $composableBuilder(
      column: $table.completedAt, builder: (column) => column);

  $$CheckInActivitiesTableAnnotationComposer get activityId {
    final $$CheckInActivitiesTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.activityId,
            referencedTable: $db.checkInActivities,
            getReferencedColumn: (t) => t.id,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$CheckInActivitiesTableAnnotationComposer(
                  $db: $db,
                  $table: $db.checkInActivities,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return composer;
  }
}

class $$ActivityRecordsTableTableManager extends RootTableManager<
    _$JournalDatabase,
    $ActivityRecordsTable,
    ActivityRecord,
    $$ActivityRecordsTableFilterComposer,
    $$ActivityRecordsTableOrderingComposer,
    $$ActivityRecordsTableAnnotationComposer,
    $$ActivityRecordsTableCreateCompanionBuilder,
    $$ActivityRecordsTableUpdateCompanionBuilder,
    (ActivityRecord, $$ActivityRecordsTableReferences),
    ActivityRecord,
    PrefetchHooks Function({bool activityId})> {
  $$ActivityRecordsTableTableManager(
      _$JournalDatabase db, $ActivityRecordsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ActivityRecordsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ActivityRecordsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ActivityRecordsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> activityId = const Value.absent(),
            Value<DateTime> date = const Value.absent(),
            Value<String> data = const Value.absent(),
            Value<String> status = const Value.absent(),
            Value<DateTime?> completedAt = const Value.absent(),
          }) =>
              ActivityRecordsCompanion(
            id: id,
            activityId: activityId,
            date: date,
            data: data,
            status: status,
            completedAt: completedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int activityId,
            required DateTime date,
            required String data,
            required String status,
            Value<DateTime?> completedAt = const Value.absent(),
          }) =>
              ActivityRecordsCompanion.insert(
            id: id,
            activityId: activityId,
            date: date,
            data: data,
            status: status,
            completedAt: completedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$ActivityRecordsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({activityId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (activityId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.activityId,
                    referencedTable:
                        $$ActivityRecordsTableReferences._activityIdTable(db),
                    referencedColumn: $$ActivityRecordsTableReferences
                        ._activityIdTable(db)
                        .id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$ActivityRecordsTableProcessedTableManager = ProcessedTableManager<
    _$JournalDatabase,
    $ActivityRecordsTable,
    ActivityRecord,
    $$ActivityRecordsTableFilterComposer,
    $$ActivityRecordsTableOrderingComposer,
    $$ActivityRecordsTableAnnotationComposer,
    $$ActivityRecordsTableCreateCompanionBuilder,
    $$ActivityRecordsTableUpdateCompanionBuilder,
    (ActivityRecord, $$ActivityRecordsTableReferences),
    ActivityRecord,
    PrefetchHooks Function({bool activityId})>;
typedef $$RoutineActivityConfigsTableCreateCompanionBuilder
    = RoutineActivityConfigsCompanion Function({
  Value<int> id,
  required int routineId,
  required int activityId,
  required String config,
  required DateTime lastUpdated,
});
typedef $$RoutineActivityConfigsTableUpdateCompanionBuilder
    = RoutineActivityConfigsCompanion Function({
  Value<int> id,
  Value<int> routineId,
  Value<int> activityId,
  Value<String> config,
  Value<DateTime> lastUpdated,
});

final class $$RoutineActivityConfigsTableReferences extends BaseReferences<
    _$JournalDatabase, $RoutineActivityConfigsTable, RoutineActivityConfig> {
  $$RoutineActivityConfigsTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static $CheckInRoutinesTable _routineIdTable(_$JournalDatabase db) =>
      db.checkInRoutines.createAlias($_aliasNameGenerator(
          db.routineActivityConfigs.routineId, db.checkInRoutines.id));

  $$CheckInRoutinesTableProcessedTableManager get routineId {
    final manager =
        $$CheckInRoutinesTableTableManager($_db, $_db.checkInRoutines)
            .filter((f) => f.id($_item.routineId));
    final item = $_typedResult.readTableOrNull(_routineIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static $CheckInActivitiesTable _activityIdTable(_$JournalDatabase db) =>
      db.checkInActivities.createAlias($_aliasNameGenerator(
          db.routineActivityConfigs.activityId, db.checkInActivities.id));

  $$CheckInActivitiesTableProcessedTableManager get activityId {
    final manager =
        $$CheckInActivitiesTableTableManager($_db, $_db.checkInActivities)
            .filter((f) => f.id($_item.activityId));
    final item = $_typedResult.readTableOrNull(_activityIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$RoutineActivityConfigsTableFilterComposer
    extends Composer<_$JournalDatabase, $RoutineActivityConfigsTable> {
  $$RoutineActivityConfigsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get config => $composableBuilder(
      column: $table.config, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => ColumnFilters(column));

  $$CheckInRoutinesTableFilterComposer get routineId {
    final $$CheckInRoutinesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.routineId,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableFilterComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$CheckInActivitiesTableFilterComposer get activityId {
    final $$CheckInActivitiesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.activityId,
        referencedTable: $db.checkInActivities,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInActivitiesTableFilterComposer(
              $db: $db,
              $table: $db.checkInActivities,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$RoutineActivityConfigsTableOrderingComposer
    extends Composer<_$JournalDatabase, $RoutineActivityConfigsTable> {
  $$RoutineActivityConfigsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get config => $composableBuilder(
      column: $table.config, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => ColumnOrderings(column));

  $$CheckInRoutinesTableOrderingComposer get routineId {
    final $$CheckInRoutinesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.routineId,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableOrderingComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$CheckInActivitiesTableOrderingComposer get activityId {
    final $$CheckInActivitiesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.activityId,
        referencedTable: $db.checkInActivities,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInActivitiesTableOrderingComposer(
              $db: $db,
              $table: $db.checkInActivities,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$RoutineActivityConfigsTableAnnotationComposer
    extends Composer<_$JournalDatabase, $RoutineActivityConfigsTable> {
  $$RoutineActivityConfigsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get config =>
      $composableBuilder(column: $table.config, builder: (column) => column);

  GeneratedColumn<DateTime> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => column);

  $$CheckInRoutinesTableAnnotationComposer get routineId {
    final $$CheckInRoutinesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.routineId,
        referencedTable: $db.checkInRoutines,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CheckInRoutinesTableAnnotationComposer(
              $db: $db,
              $table: $db.checkInRoutines,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$CheckInActivitiesTableAnnotationComposer get activityId {
    final $$CheckInActivitiesTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.activityId,
            referencedTable: $db.checkInActivities,
            getReferencedColumn: (t) => t.id,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$CheckInActivitiesTableAnnotationComposer(
                  $db: $db,
                  $table: $db.checkInActivities,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return composer;
  }
}

class $$RoutineActivityConfigsTableTableManager extends RootTableManager<
    _$JournalDatabase,
    $RoutineActivityConfigsTable,
    RoutineActivityConfig,
    $$RoutineActivityConfigsTableFilterComposer,
    $$RoutineActivityConfigsTableOrderingComposer,
    $$RoutineActivityConfigsTableAnnotationComposer,
    $$RoutineActivityConfigsTableCreateCompanionBuilder,
    $$RoutineActivityConfigsTableUpdateCompanionBuilder,
    (RoutineActivityConfig, $$RoutineActivityConfigsTableReferences),
    RoutineActivityConfig,
    PrefetchHooks Function({bool routineId, bool activityId})> {
  $$RoutineActivityConfigsTableTableManager(
      _$JournalDatabase db, $RoutineActivityConfigsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$RoutineActivityConfigsTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$RoutineActivityConfigsTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$RoutineActivityConfigsTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> routineId = const Value.absent(),
            Value<int> activityId = const Value.absent(),
            Value<String> config = const Value.absent(),
            Value<DateTime> lastUpdated = const Value.absent(),
          }) =>
              RoutineActivityConfigsCompanion(
            id: id,
            routineId: routineId,
            activityId: activityId,
            config: config,
            lastUpdated: lastUpdated,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int routineId,
            required int activityId,
            required String config,
            required DateTime lastUpdated,
          }) =>
              RoutineActivityConfigsCompanion.insert(
            id: id,
            routineId: routineId,
            activityId: activityId,
            config: config,
            lastUpdated: lastUpdated,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$RoutineActivityConfigsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({routineId = false, activityId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (routineId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.routineId,
                    referencedTable: $$RoutineActivityConfigsTableReferences
                        ._routineIdTable(db),
                    referencedColumn: $$RoutineActivityConfigsTableReferences
                        ._routineIdTable(db)
                        .id,
                  ) as T;
                }
                if (activityId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.activityId,
                    referencedTable: $$RoutineActivityConfigsTableReferences
                        ._activityIdTable(db),
                    referencedColumn: $$RoutineActivityConfigsTableReferences
                        ._activityIdTable(db)
                        .id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$RoutineActivityConfigsTableProcessedTableManager
    = ProcessedTableManager<
        _$JournalDatabase,
        $RoutineActivityConfigsTable,
        RoutineActivityConfig,
        $$RoutineActivityConfigsTableFilterComposer,
        $$RoutineActivityConfigsTableOrderingComposer,
        $$RoutineActivityConfigsTableAnnotationComposer,
        $$RoutineActivityConfigsTableCreateCompanionBuilder,
        $$RoutineActivityConfigsTableUpdateCompanionBuilder,
        (RoutineActivityConfig, $$RoutineActivityConfigsTableReferences),
        RoutineActivityConfig,
        PrefetchHooks Function({bool routineId, bool activityId})>;

class $JournalDatabaseManager {
  final _$JournalDatabase _db;
  $JournalDatabaseManager(this._db);
  $$IntentionsTableTableManager get intentions =>
      $$IntentionsTableTableManager(_db, _db.intentions);
  $$CheckInRoutinesTableTableManager get checkInRoutines =>
      $$CheckInRoutinesTableTableManager(_db, _db.checkInRoutines);
  $$JournalEntriesTableTableManager get journalEntries =>
      $$JournalEntriesTableTableManager(_db, _db.journalEntries);
  $$CheckInActivitiesTableTableManager get checkInActivities =>
      $$CheckInActivitiesTableTableManager(_db, _db.checkInActivities);
  $$ActivityRecordsTableTableManager get activityRecords =>
      $$ActivityRecordsTableTableManager(_db, _db.activityRecords);
  $$RoutineActivityConfigsTableTableManager get routineActivityConfigs =>
      $$RoutineActivityConfigsTableTableManager(
          _db, _db.routineActivityConfigs);
}

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$journalDatabaseHash() => r'8d5b92d9b7f8c1ca671d777a02c9a1c44f30010b';

/// See also [journalDatabase].
@ProviderFor(journalDatabase)
final journalDatabaseProvider = Provider<JournalDatabase>.internal(
  journalDatabase,
  name: r'journalDatabaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalDatabaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalDatabaseRef = ProviderRef<JournalDatabase>;
String _$allIntentionsHash() => r'96f7f1d2efe387f41054198f6dd0c75c37e62c46';

/// See also [allIntentions].
@ProviderFor(allIntentions)
final allIntentionsProvider =
    AutoDisposeStreamProvider<List<Intention>>.internal(
  allIntentions,
  name: r'allIntentionsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$allIntentionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllIntentionsRef = AutoDisposeStreamProviderRef<List<Intention>>;
String _$routinesForIntentionHash() =>
    r'43eeb5a56a6e9a98ce63305504d2ecbfa088cb7a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [routinesForIntention].
@ProviderFor(routinesForIntention)
const routinesForIntentionProvider = RoutinesForIntentionFamily();

/// See also [routinesForIntention].
class RoutinesForIntentionFamily
    extends Family<AsyncValue<List<CheckInRoutine>>> {
  /// See also [routinesForIntention].
  const RoutinesForIntentionFamily();

  /// See also [routinesForIntention].
  RoutinesForIntentionProvider call(
    int intentionId,
  ) {
    return RoutinesForIntentionProvider(
      intentionId,
    );
  }

  @override
  RoutinesForIntentionProvider getProviderOverride(
    covariant RoutinesForIntentionProvider provider,
  ) {
    return call(
      provider.intentionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'routinesForIntentionProvider';
}

/// See also [routinesForIntention].
class RoutinesForIntentionProvider
    extends AutoDisposeStreamProvider<List<CheckInRoutine>> {
  /// See also [routinesForIntention].
  RoutinesForIntentionProvider(
    int intentionId,
  ) : this._internal(
          (ref) => routinesForIntention(
            ref as RoutinesForIntentionRef,
            intentionId,
          ),
          from: routinesForIntentionProvider,
          name: r'routinesForIntentionProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$routinesForIntentionHash,
          dependencies: RoutinesForIntentionFamily._dependencies,
          allTransitiveDependencies:
              RoutinesForIntentionFamily._allTransitiveDependencies,
          intentionId: intentionId,
        );

  RoutinesForIntentionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.intentionId,
  }) : super.internal();

  final int intentionId;

  @override
  Override overrideWith(
    Stream<List<CheckInRoutine>> Function(RoutinesForIntentionRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RoutinesForIntentionProvider._internal(
        (ref) => create(ref as RoutinesForIntentionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        intentionId: intentionId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<CheckInRoutine>> createElement() {
    return _RoutinesForIntentionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RoutinesForIntentionProvider &&
        other.intentionId == intentionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, intentionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RoutinesForIntentionRef
    on AutoDisposeStreamProviderRef<List<CheckInRoutine>> {
  /// The parameter `intentionId` of this provider.
  int get intentionId;
}

class _RoutinesForIntentionProviderElement
    extends AutoDisposeStreamProviderElement<List<CheckInRoutine>>
    with RoutinesForIntentionRef {
  _RoutinesForIntentionProviderElement(super.provider);

  @override
  int get intentionId => (origin as RoutinesForIntentionProvider).intentionId;
}

String _$intentionCrudNotifierHash() =>
    r'2b4d494ca1c0abfe66d2725a0f5df83bf247254f';

/// See also [IntentionCrudNotifier].
@ProviderFor(IntentionCrudNotifier)
final intentionCrudNotifierProvider =
    AutoDisposeAsyncNotifierProvider<IntentionCrudNotifier, void>.internal(
  IntentionCrudNotifier.new,
  name: r'intentionCrudNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$intentionCrudNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IntentionCrudNotifier = AutoDisposeAsyncNotifier<void>;
String _$routineCrudNotifierHash() =>
    r'4b306ecf1049576c1e478b404f0d98244e5b7720';

/// See also [RoutineCrudNotifier].
@ProviderFor(RoutineCrudNotifier)
final routineCrudNotifierProvider =
    AutoDisposeAsyncNotifierProvider<RoutineCrudNotifier, void>.internal(
  RoutineCrudNotifier.new,
  name: r'routineCrudNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$routineCrudNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RoutineCrudNotifier = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
