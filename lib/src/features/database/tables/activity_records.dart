import 'package:drift/drift.dart';
import 'package:mimi_app/src/features/database/tables/checkin_activities.dart';

@DataClassName('ActivityRecord')
class ActivityRecords extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get activityId => integer().references(CheckInActivities, #id)();
  DateTimeColumn get date => dateTime()();
  TextColumn get data => text()();
  TextColumn get status => text()();
  DateTimeColumn get completedAt => dateTime().nullable()();
}

/**------------------------------------------------------------------------


class CheckInActivities extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get type => text()();
  TextColumn get name => text()();
  TextColumn get config => text()();
  BoolColumn get enabled => boolean().withDefault(const Constant(true))();
  DateTimeColumn get lastUpdated => dateTime()();
}



class ActivityRecords extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get activityId => integer().references(CheckInActivities, #id)();
  DateTimeColumn get date => dateTime()();
  TextColumn get data =>
      text()(); // JSON data for the activity (responses, duration, etc.)
  TextColumn get status => text()(); // completed, skipped, etc.
  DateTimeColumn get completedAt => dateTime().nullable()();
}

class CheckInRoutines extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get type => text()(); // morning, noon, night (fixed)
  TextColumn get name => text()(); // Custom name if user wants to change
  BoolColumn get enabled => boolean().withDefault(const Constant(true))();
  TextColumn get activities => text()(); // JSON array of ordered activity IDs
  DateTimeColumn get scheduledTime => dateTime()();
  BoolColumn get notificationEnabled =>
      boolean().withDefault(const Constant(true))();
  DateTimeColumn get lastUpdated => dateTime()();
}


class JournalEntries extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get routineId => integer().references(CheckInRoutines, #id)();
  DateTimeColumn get date => dateTime()();
  TextColumn get responses => text()(); // JSON string of prompt responses
  TextColumn get mood => text().nullable()();
  IntColumn get meditationMinutes => integer().nullable()();
  TextColumn get status => text()(); // completed, missed, pending
  DateTimeColumn get completedAt => dateTime().nullable()();
}


@DataClassName('RoutineActivityConfig')
class RoutineActivityConfigs extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get routineId => integer().references(CheckInRoutines, #id)();
  IntColumn get activityId => integer().references(CheckInActivities, #id)();
  TextColumn get config => text()(); // JSON serialized ActivityConfig
  DateTimeColumn get lastUpdated => dateTime()();

  @override
  List<Set<Column>> get uniqueKeys => [
        {routineId, activityId}
      ];
}


// // lib/src/features/journal/models/activity.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'activity.freezed.dart';
part 'activity.g.dart';

@freezed
class Activity with _$Activity {
  const factory Activity({
    required int id,
    required String type,
    required String name,
    required ActivityConfig config, // Change from Map<String, dynamic>
    @Default(true) bool enabled,
    DateTime? lastUpdated,
  }) = _Activity;

  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'] as int,
      type: json['type'] as String,
      name: json['name'] as String,
      config: ActivityConfig.fromJson(json['config'] as Map<String, dynamic>),
      enabled: json['enabled'] as bool? ?? true,
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

@freezed
class ActivityConfig with _$ActivityConfig {
  // Journaling Config
  const factory ActivityConfig.journaling({
    @Default([]) List<String> prompts,
    @Default(true) bool includeDate,
    @Default(true) bool includeMood,
  }) = JournalingConfig;

  // Meditation Config
  const factory ActivityConfig.meditation({
    @Default([]) List<String> audioAssets,
    @Default(5) int defaultDuration,
    String? selectedAudioAsset,
  }) = MeditationConfig;

  // Breathwork Config
  const factory ActivityConfig.breathwork({
    @Default(4) int inhaleSeconds,
    @Default(4) int holdSeconds,
    @Default(4) int exhaleSeconds,
    @Default(5) int cycles,
  }) = BreathworkConfig;

  // Affirmations Config
  const factory ActivityConfig.affirmations({
    @Default([]) List<String> audioAssets,
    String? selectedAudioAsset,
    @Default(true) bool autoPlay,
  }) = AffirmationsConfig;

  // Mood Tracking Config
  const factory ActivityConfig.moodTracking({
    @Default(['😊 Happy', '😐 Neutral', '😔 Sad', '😤 Angry', '😴 Tired'])
    List<String> moods,
    @Default(true) bool includeNote,
  }) = MoodTrackingConfig;

  factory ActivityConfig.fromJson(Map<String, dynamic> json) =>
      _$ActivityConfigFromJson(json);
}

@freezed
class ActivityData with _$ActivityData {
  const factory ActivityData({
    required int id,
    required int activityId,
    required DateTime date,
    required Map<String, dynamic> data,
    required String status,
    DateTime? completedAt,
  }) = _ActivityData;

  factory ActivityData.fromJson(Map<String, dynamic> json) =>
      _$ActivityDataFromJson(json);
}


 *------------------------------------------------------------------------**/
