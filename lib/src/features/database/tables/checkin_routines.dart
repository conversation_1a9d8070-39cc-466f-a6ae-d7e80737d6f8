import 'package:drift/drift.dart';
import 'package:mimi_app/src/features/database/tables/intentions.dart';

@DataClassName('CheckInRoutine')
class CheckInRoutines extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get intentionId => integer().references(Intentions, #id)();
  TextColumn get type => text()();
  TextColumn get name => text()();
  BoolColumn get enabled => boolean().withDefault(const Constant(true))();
  TextColumn get activities => text()();
  DateTimeColumn get scheduledTime => dateTime()();
  BoolColumn get notificationEnabled =>
      boolean().withDefault(const Constant(true))();
  DateTimeColumn get lastUpdated => dateTime()();
}
