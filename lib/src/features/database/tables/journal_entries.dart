// lib/src/features/journal/data/tables/journal_entries.dart
import 'package:drift/drift.dart';
import 'checkin_routines.dart';

@DataClassName('JournalEntry')
class JournalEntries extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get routineId => integer().references(CheckInRoutines, #id)();
  DateTimeColumn get date => dateTime()();
  TextColumn get responses => text()();
  TextColumn get mood => text().nullable()();
  IntColumn get meditationMinutes => integer().nullable()();
  TextColumn get status => text()();
  DateTimeColumn get completedAt => dateTime().nullable()();
}
