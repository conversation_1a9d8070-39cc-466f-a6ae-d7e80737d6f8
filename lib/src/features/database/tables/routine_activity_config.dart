import 'package:drift/drift.dart';
import 'package:mimi_app/src/features/database/tables/checkin_activities.dart';
import 'package:mimi_app/src/features/database/tables/checkin_routines.dart';

@DataClassName('RoutineActivityConfig')
class RoutineActivityConfigs extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get routineId => integer().references(CheckInRoutines, #id)();
  IntColumn get activityId => integer().references(CheckInActivities, #id)();
  TextColumn get config => text()(); // JSON serialized ActivityConfig
  DateTimeColumn get lastUpdated => dateTime()();

  @override
  List<Set<Column>> get uniqueKeys => [
        {routineId, activityId}
      ];
}
