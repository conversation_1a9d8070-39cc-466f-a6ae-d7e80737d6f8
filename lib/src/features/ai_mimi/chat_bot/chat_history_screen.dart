import 'package:flutter/material.dart';

import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';

// Screen to show a specific chat history

class ChatHistoryScreen extends StatelessWidget {
  final String sessionId;
  final List<Map<String, dynamic>> chats;
  final VoidCallback onDelete;

  const ChatHistoryScreen({
    super.key,
    required this.sessionId,
    required this.chats,
    required this.onDelete,
  });

  List<Map<String, dynamic>> getChatsForSession() {
    return chats.where((chat) => chat['sessionId'] == sessionId).toList();
  }

  Widget _buildUserBubble(BuildContext context, String message) {
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: const EdgeInsets.only(left: 48, right: 16, top: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(12),
        child: Text(
          message,
          style: TextStyle(color: Theme.of(context).colorScheme.onPrimary),
        ),
      ),
    );
  }

  Widget _buildAIBubble(BuildContext context, String message) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(right: 48, left: 16, top: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.light
              ? AppColors.surfaceLight
              : AppColors.surfaceDark,
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(12),
        child: MarkdownBody(data: message),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final sessionChats = getChatsForSession();

    return Scaffold(
      appBar: AppBar(
        title: Text(DateFormat('MMM d, y')
            .format(DateTime.parse(sessionChats.first['timestamp']))),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Delete Chat'),
                  content:
                      const Text('Are you sure you want to delete this chat?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context); // Close dialog
                        onDelete(); // Call delete callback
                        context
                            .goNamed(RouteNames.chat); // Go back to chat screen
                      },
                      child: Text(
                        'Delete',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.error),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: sessionChats.length,
        itemBuilder: (context, index) {
          final chat = sessionChats[index];
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (chat['question'] != null) ...[
                _buildUserBubble(context, chat['question']),
              ],
              if (chat['answer'] != null) ...[
                _buildAIBubble(context, chat['answer']),
              ],
              if (index < sessionChats.length - 1) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    DateFormat('HH:mm').format(
                      DateTime.parse(chat['timestamp']),
                    ),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.outline,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }
}
