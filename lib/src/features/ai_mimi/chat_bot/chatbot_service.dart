import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AIService {
  static final AIService _instance = AIService._internal();
  late SharedPreferences _prefs;
  bool _isInitialized = false;
  GenerativeModel? _generativeModel;

  AIService._internal();

  factory AIService() => _instance;

  static Future<AIService> create() async {
    final service = AIService();
    await service.initialize();
    return service;
  }

  Future<void> initialize() async {
    if (!_isInitialized) {
      _prefs = await SharedPreferences.getInstance();

      // Initialize the Vertex AI model
      _generativeModel = FirebaseVertexAI.instance.generativeModel(
        model: 'gemini-2.0-flash',
        generationConfig: GenerationConfig(
          temperature: 0.7,
        ),
      );

      _isInitialized = true;
    }
  }

  Future<String> getChatResponse(String userMessage,
      {String? sessionId}) async {
    // Make sure initialization is complete
    if (!_isInitialized || _generativeModel == null) {
      await initialize();
    }

    try {
      // Build conversation history with context (now async)
      final conversationHistory =
          await _buildConversationHistory(userMessage, sessionId);

      // Generate content using Vertex AI with conversation context
      final response =
          await _generativeModel!.generateContent(conversationHistory);
      final answer = response.text ?? 'Sorry, I couldn\'t generate a response.';

      // Only save non-greeting messages
      if (userMessage.isNotEmpty && !answer.startsWith('Hello, I am MimiAI')) {
        await _saveChat(userMessage, answer, sessionId: sessionId);
      }

      return answer;
    } catch (e) {
      if (kDebugMode) {
        print('Error generating response: $e');
      }
      return 'Error generating response: $e';
    }
  }

  /// Estimates token count for a given text (rough approximation)
  int _estimateTokens(String text) {
    // More accurate estimation: ~3.5 characters per token for English text
    // Add extra tokens for formatting and special characters
    return ((text.length / 3.5) * 1.2).ceil();
  }

  /// Summarizes old conversation messages to preserve context while reducing tokens
  Future<String> _summarizeOldMessages(
      List<Map<String, dynamic>> oldMessages) async {
    if (oldMessages.isEmpty) return '';

    try {
      final conversationText = oldMessages
          .map((msg) => "User: ${msg['question']}\nMimi: ${msg['answer']}")
          .join('\n\n');

      final summaryPrompt =
          '''Please create a concise summary of this conversation that preserves:
- Key emotional states and concerns discussed
- Important advice or insights provided
- Ongoing themes or patterns
- Any specific goals or commitments mentioned
- Context that would be important for continuing the conversation

Conversation to summarize:
$conversationText

Summary:''';

      final response = await _generativeModel!
          .generateContent([Content.text(summaryPrompt)]);
      final summary = response.text ?? '';

      if (kDebugMode) {
        print(
            'Summarized ${oldMessages.length} messages into ${_estimateTokens(summary)} tokens');
      }

      return summary;
    } catch (e) {
      if (kDebugMode) {
        print('Error summarizing messages: $e');
      }
      return '';
    }
  }

  /// Builds conversation history with smart token management and summarization
  Future<List<Content>> _buildConversationHistory(
      String userMessage, String? sessionId) async {
    final conversationHistory = <Content>[];

    // System instruction
    final systemInstruction =
        'You are a Mimi Bland. You are a self-mastery and emotional intelligence coach. You are not a therapist. Whenever you can\'t solve people\'s problems and you need to suggest a therapist, ask them to book a call with Mimi from https://mimibland.london/contact/ . Do not use the word therapist.';
    conversationHistory.add(Content.text(systemInstruction));

    // Token limits - generous to utilize Gemini's large context window
    const int maxTotalTokens = 200000; // 200k tokens for conversation history
    const int recentMessagesTokenLimit =
        50000; // Keep recent 50k tokens in full detail
    const int summaryChunkSize = 30; // Summarize in chunks of 30 messages

    int currentTokens = _estimateTokens(systemInstruction);
    final userMessageTokens = _estimateTokens(userMessage);

    if (sessionId != null) {
      final sessionMessages = _getSessionMessages(sessionId);

      if (sessionMessages.isNotEmpty) {
        // Separate recent and old messages
        final recentMessages = <Map<String, dynamic>>[];
        final oldMessages = <Map<String, dynamic>>[];

        int recentTokens = 0;

        // Collect recent messages within token limit
        for (final message in sessionMessages) {
          final messageTokens =
              _estimateTokens(message['question'].toString()) +
                  _estimateTokens(message['answer'].toString());

          if (recentTokens + messageTokens <= recentMessagesTokenLimit) {
            recentMessages.add(message);
            recentTokens += messageTokens;
          } else {
            oldMessages.add(message);
          }
        }

        // Summarize old messages if they exist
        if (oldMessages.isNotEmpty) {
          // Process old messages in chunks for better summarization
          final summaries = <String>[];

          for (int i = 0; i < oldMessages.length; i += summaryChunkSize) {
            final chunk = oldMessages.skip(i).take(summaryChunkSize).toList();
            final summary = await _summarizeOldMessages(chunk);
            if (summary.isNotEmpty) {
              summaries.add(summary);
            }
          }

          // Add summarized context
          if (summaries.isNotEmpty) {
            final combinedSummary = summaries.length == 1
                ? 'Previous conversation context: ${summaries.first}'
                : 'Previous conversation context:\n${summaries.asMap().entries.map((e) => '${e.key + 1}. ${e.value}').join('\n')}';

            final summaryTokens = _estimateTokens(combinedSummary);
            if (currentTokens +
                    summaryTokens +
                    recentTokens +
                    userMessageTokens <=
                maxTotalTokens) {
              conversationHistory.add(Content.text(combinedSummary));
              currentTokens += summaryTokens;
            }
          }
        }

        // Add recent messages in full detail (reversed to maintain chronological order)
        for (final message in recentMessages.reversed) {
          final questionTokens =
              _estimateTokens(message['question'].toString());
          final answerTokens = _estimateTokens(message['answer'].toString());

          // Check if we have room for both question and answer
          if (currentTokens +
                  questionTokens +
                  answerTokens +
                  userMessageTokens <=
              maxTotalTokens) {
            if (message['question'] != null &&
                message['question'].toString().isNotEmpty) {
              conversationHistory.add(Content.text(message['question']));
              currentTokens += questionTokens;
            }

            if (message['answer'] != null &&
                message['answer'].toString().isNotEmpty) {
              conversationHistory.add(Content.text(message['answer']));
              currentTokens += answerTokens;
            }
          } else {
            break; // Stop adding messages if we're approaching token limit
          }
        }
      }
    }

    // Add current user message
    conversationHistory.add(Content.text(userMessage));

    if (kDebugMode) {
      print('Conversation history built with ~$currentTokens tokens');
    }

    return conversationHistory;
  }

  /// Gets messages for a specific session, sorted by timestamp (oldest first)
  List<Map<String, dynamic>> _getSessionMessages(String sessionId) {
    if (!_isInitialized) return [];

    final List<String> chats = _prefs.getStringList('chats') ?? [];
    final sessionChats = chats
        .map((c) => Map<String, dynamic>.from(jsonDecode(c)))
        .where((chat) => chat['sessionId'] == sessionId)
        .toList();

    // Sort by timestamp (oldest first for conversation flow)
    sessionChats.sort((a, b) => DateTime.parse(a['timestamp'])
        .compareTo(DateTime.parse(b['timestamp'])));

    return sessionChats;
  }

  Future<void> _saveChat(String question, String answer,
      {String? sessionId}) async {
    if (!_isInitialized) await initialize();

    // Don't save if it's just a greeting
    if (question.isEmpty && answer.startsWith('Hello, I am MimiAI')) {
      return;
    }

    final List<String> chats = _prefs.getStringList('chats') ?? [];

    final newChat = jsonEncode({
      'timestamp': DateTime.now().toIso8601String(),
      'sessionId': sessionId ?? DateTime.now().toIso8601String(),
      'type': 'chat',
      'question': question,
      'answer': answer,
    });

    chats.add(newChat);
    await _prefs.setStringList('chats', chats);
  }

  List<Map<String, dynamic>> getAllChats() {
    if (!_isInitialized) return [];

    final List<String> chats = _prefs.getStringList('chats') ?? [];
    final parsed = chats
        .map((c) => Map<String, dynamic>.from(jsonDecode(c)))
        .where((chat) =>
            // Filter out any greeting messages that might have been saved previously
            !(chat['question'] == '' &&
                (chat['answer'] as String).startsWith('Hello, I am MimiAI')))
        .toList();

    // Sort by timestamp, most recent first
    parsed.sort((a, b) => DateTime.parse(b['timestamp'])
        .compareTo(DateTime.parse(a['timestamp'])));

    return parsed;
  }

  Future<void> clearChats() async {
    if (!_isInitialized) await initialize();
    await _prefs.remove('chats');
  }

  /// Gets conversation statistics for debugging and monitoring
  Map<String, dynamic> getConversationStats(String? sessionId) {
    if (!_isInitialized || sessionId == null) {
      return {
        'totalMessages': 0,
        'estimatedTokens': 0,
        'oldestMessage': null,
        'newestMessage': null,
      };
    }

    final sessionMessages = _getSessionMessages(sessionId);
    if (sessionMessages.isEmpty) {
      return {
        'totalMessages': 0,
        'estimatedTokens': 0,
        'oldestMessage': null,
        'newestMessage': null,
      };
    }

    int totalTokens = 0;
    for (final message in sessionMessages) {
      totalTokens += _estimateTokens(message['question'].toString());
      totalTokens += _estimateTokens(message['answer'].toString());
    }

    return {
      'totalMessages': sessionMessages.length,
      'estimatedTokens': totalTokens,
      'oldestMessage': sessionMessages.last['timestamp'],
      'newestMessage': sessionMessages.first['timestamp'],
    };
  }

  /// Gets detailed context management info for debugging
  Future<Map<String, dynamic>> getContextManagementInfo(
      String? sessionId) async {
    if (!_isInitialized || sessionId == null) {
      return {'error': 'Service not initialized or no session ID provided'};
    }

    final sessionMessages = _getSessionMessages(sessionId);
    if (sessionMessages.isEmpty) {
      return {
        'totalMessages': 0,
        'recentMessages': 0,
        'oldMessages': 0,
        'summariesGenerated': 0,
        'estimatedContextTokens': 0,
      };
    }

    // Simulate the same logic as _buildConversationHistory for analysis
    const int recentMessagesTokenLimit = 50000;
    const int summaryChunkSize = 30;

    final recentMessages = <Map<String, dynamic>>[];
    final oldMessages = <Map<String, dynamic>>[];

    int recentTokens = 0;

    for (final message in sessionMessages) {
      final messageTokens = _estimateTokens(message['question'].toString()) +
          _estimateTokens(message['answer'].toString());

      if (recentTokens + messageTokens <= recentMessagesTokenLimit) {
        recentMessages.add(message);
        recentTokens += messageTokens;
      } else {
        oldMessages.add(message);
      }
    }

    final summaryChunks = (oldMessages.length / summaryChunkSize).ceil();

    return {
      'totalMessages': sessionMessages.length,
      'recentMessages': recentMessages.length,
      'recentMessagesTokens': recentTokens,
      'oldMessages': oldMessages.length,
      'summaryChunks': summaryChunks,
      'estimatedSummaryTokens': summaryChunks * 500, // Rough estimate
      'totalEstimatedContextTokens': recentTokens + (summaryChunks * 500),
      'contextStrategy':
          oldMessages.isEmpty ? 'full_history' : 'summarized_with_recent',
    };
  }

  Future<void> deleteChat(String sessionId) async {
    if (!_isInitialized) await initialize();
    final List<String> chats = _prefs.getStringList('chats') ?? [];

    // Filter out all messages from this session
    final updatedChats = chats.where((chatString) {
      final chat = jsonDecode(chatString);
      return chat['sessionId'] != sessionId;
    }).toList();

    await _prefs.setStringList('chats', updatedChats);
  }
}
