// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_app_check/firebase_app_check.dart';
// import 'package:flutter/material.dart';
// import 'package:mimi_app/firebase_options.dart';
// import 'package:mimi_app/src/features/ai_mimi/chat_bot/chatbot_service.dart';

// // This is a test file to verify that the Vertex AI integration is working properly.
// // You can run this as a standalone Flutter app to test the AI service.

// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//   await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
//   await FirebaseAppCheck.instance.activate(
//     appleProvider: AppleProvider.deviceCheck,
//   );

//   runApp(const VertexAITestApp());
// }

// class VertexAITestApp extends StatefulWidget {
//   const VertexAITestApp({Key? key}) : super(key: key);

//   @override
//   State<VertexAITestApp> createState() => _VertexAITestAppState();
// }

// class _VertexAITestAppState extends State<VertexAITestApp> {
//   final TextEditingController _controller = TextEditingController();
//   String _response = '';
//   bool _isLoading = false;
//   late AIService _aiService;

//   @override
//   void initState() {
//     super.initState();
//     _initAIService();
//   }

//   Future<void> _initAIService() async {
//     _aiService = await AIService.create();
//   }

//   Future<void> _getAIResponse() async {
//     if (_controller.text.isEmpty) return;

//     setState(() {
//       _isLoading = true;
//       _response = '';
//     });

//     try {
//       final response = await _aiService.getChatResponse(_controller.text);
//       setState(() {
//         _response = response;
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         _response = 'Error: $e';
//         _isLoading = false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       home: Scaffold(
//         appBar: AppBar(
//           title: const Text('Vertex AI Test'),
//         ),
//         body: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             children: [
//               TextField(
//                 controller: _controller,
//                 decoration: const InputDecoration(
//                   labelText: 'Ask something',
//                   border: OutlineInputBorder(),
//                 ),
//               ),
//               const SizedBox(height: 16),
//               ElevatedButton(
//                 onPressed: _isLoading ? null : _getAIResponse,
//                 child: _isLoading
//                     ? const CircularProgressIndicator()
//                     : const Text('Send'),
//               ),
//               const SizedBox(height: 16),
//               Expanded(
//                 child: Container(
//                   padding: const EdgeInsets.all(16),
//                   decoration: BoxDecoration(
//                     border: Border.all(color: Colors.grey),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: SingleChildScrollView(
//                     child: Text(_response),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }
// }
