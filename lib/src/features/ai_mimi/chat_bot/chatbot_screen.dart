import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/features/ai_mimi/chat_bot/chat_history_screen.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/ai_mimi/chat_bot/chatbot_service.dart';
import 'package:mimi_app/src/core/widgets/custom_text_field.dart';

class ChatScreen extends ConsumerStatefulWidget {
  const ChatScreen({super.key});

  @override
  ChatScreenState createState() => ChatScreenState();
}

class ChatScreenState extends ConsumerState<ChatScreen> {
  final aiService = AIService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  List<Map<String, dynamic>> _currentMessages = [];
  bool _isLoading = false;
  String _currentTypingText = '';
  bool _isTyping = false;
  late String _currentSessionId;

  @override
  void initState() {
    super.initState();
    _currentSessionId = DateTime.now().toIso8601String();
    _showGreeting();
  }

  void _showGreeting() {
    setState(() {
      _currentMessages = [
        {
          'answer': 'Hello, I am MimiAI, How can I help you today?',
          'timestamp': DateTime.now().toIso8601String(),
          'type': 'greeting',
          'sessionId': _currentSessionId,
          'isGreeting': true,
        }
      ];
    });
  }

  Future<void> _simulateTyping(String text) async {
    setState(() {
      _isTyping = true;
      _currentTypingText = '';
    });

    for (int i = 0; i < text.length; i++) {
      if (!_isTyping) break;
      await Future.delayed(const Duration(milliseconds: 30));
      setState(() {
        _currentTypingText = text.substring(0, i + 1);
      });
    }

    setState(() {
      _isTyping = false;
      _currentTypingText = '';
    });
  }

  Widget _buildTypingIndicator() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(3, (index) {
          return TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: Duration(milliseconds: 600),
            curve: Curves.easeInOut,
            builder: (context, value, child) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                height: 8 * (0.5 + (value * 0.5)), // Pulsing effect
                width: 8,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.light
                      ? AppColors.greyLight300
                      : AppColors.greyDark300,
                  shape: BoxShape.circle,
                ),
              );
            },
          );
        }),
      ),
    );
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final message = _messageController.text.trim();
    _messageController.clear();

    final questionMessage = {
      'question': message,
      'timestamp': DateTime.now().toIso8601String(),
      'type': 'chat',
      'sessionId': _currentSessionId,
    };

    setState(() {
      _currentMessages.insert(0, questionMessage);
      _isLoading = true;
    });

    try {
      final response = await aiService.getChatResponse(message,
          sessionId: _currentSessionId);

      // Stop loading and create a message with empty answer to start typing
      setState(() {
        _isLoading = false;
        _currentMessages[0] = {
          'question': message,
          'answer': '', // Empty answer to trigger typing animation
          'timestamp': DateTime.now().toIso8601String(),
          'type': 'chat',
          'sessionId': _currentSessionId,
        };
      });

      // Start typing animation
      await _simulateTyping(response);

      // Update with final complete message
      final newMessage = {
        'question': message,
        'answer': response,
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'chat',
        'sessionId': _currentSessionId,
      };

      setState(() {
        _currentMessages[0] = newMessage;
      });

      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  Widget _buildChatHistory() {
    final allChats = aiService.getAllChats();
    // Group chats by sessionId
    final groupedChats = <String, List<Map<String, dynamic>>>{};

    for (var chat in allChats) {
      final sessionId = chat['sessionId'] as String;
      if (!groupedChats.containsKey(sessionId)) {
        groupedChats[sessionId] = [];
      }
      groupedChats[sessionId]!.add(chat);
    }

    // Group sessions by date
    final groupedByDate =
        <String, List<MapEntry<String, List<Map<String, dynamic>>>>>{};

    for (var entry in groupedChats.entries) {
      final firstChat = entry.value.first;
      final timestamp = DateTime.parse(firstChat['timestamp']);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      final chatDate = DateTime(timestamp.year, timestamp.month, timestamp.day);

      String dateKey;
      if (chatDate == today) {
        dateKey = 'Today';
      } else if (chatDate == yesterday) {
        dateKey = 'Yesterday';
      } else {
        final daysDiff = today.difference(chatDate).inDays;
        if (daysDiff <= 7) {
          dateKey = '$daysDiff days ago';
        } else {
          dateKey = DateFormat('MMM d, y').format(timestamp);
        }
      }

      if (!groupedByDate.containsKey(dateKey)) {
        groupedByDate[dateKey] = [];
      }
      groupedByDate[dateKey]!.add(entry);
    }

    return Drawer(
      backgroundColor: AppColors.background,
      child: Column(
        children: [
          Container(
            height: 120,
            padding: const EdgeInsets.only(top: 50, left: 20, right: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Chat History',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        backgroundColor: AppColors.surface,
                        title: Text(
                          'Clear History',
                          style: TextStyle(color: AppColors.textPrimary),
                        ),
                        content: Text(
                          'Are you sure you want to clear all chat history?',
                          style: TextStyle(color: AppColors.textSecondary),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(
                              'Cancel',
                              style: TextStyle(color: AppColors.textSecondary),
                            ),
                          ),
                          TextButton(
                            onPressed: () async {
                              await aiService.clearChats();
                              Navigator.pop(context);
                              setState(() {});
                            },
                            child: Text(
                              'Clear',
                              style: TextStyle(color: AppColors.primary),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.delete_outline,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: groupedByDate.length,
              itemBuilder: (context, dateIndex) {
                final dateKey = groupedByDate.keys.elementAt(dateIndex);
                final dateSessions = groupedByDate[dateKey]!;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date header
                    Padding(
                      padding: const EdgeInsets.only(top: 20, bottom: 12),
                      child: Text(
                        dateKey,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                    // Chat sessions for this date
                    ...dateSessions.map((sessionEntry) {
                      final sessionId = sessionEntry.key;
                      final sessionChats = sessionEntry.value;
                      final firstChat = sessionChats.first;
                      final question = firstChat['question'] ?? 'New Chat';

                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: () {
                              Navigator.pop(context); // Close drawer
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ChatHistoryScreen(
                                    onDelete: () async {
                                      await aiService.deleteChat(sessionId);
                                      if (mounted) {
                                        Navigator.pop(context);
                                        setState(() {});
                                      }
                                    },
                                    sessionId: sessionId,
                                    chats: allChats,
                                  ),
                                ),
                              );
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.surface.withValues(alpha: 0.5),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                question,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: AppColors.textPrimary,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message,
      {bool isLatest = false}) {
    final userQuestion = message['question'] as String?;
    final aiAnswer = message['answer'] as String?;
    final timestamp =
        message['timestamp'] as String? ?? DateTime.now().toIso8601String();
    final parsedTime = DateTime.tryParse(timestamp) ?? DateTime.now();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: Column(
        children: [
          if (userQuestion != null) ...[
            Align(
              alignment: Alignment.centerRight,
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                margin: const EdgeInsets.only(left: 48),
                decoration: BoxDecoration(
                  color: AppColors.primaryLight,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                    bottomLeft: Radius.circular(20),
                  ),
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Text(
                    userQuestion,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
          if (aiAnswer != null ||
              (isLatest && _isLoading) ||
              (isLatest && _isTyping)) ...[
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                margin: const EdgeInsets.only(right: 48),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.light
                      ? AppColors.surfaceLight
                      : AppColors.surfaceDark,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                ),
                child: Builder(
                  builder: (context) {
                    // Show typing indicator only for latest message while loading
                    if (isLatest && _isLoading) {
                      return _buildTypingIndicator();
                    }

                    // Show typing animation only for latest message
                    if (isLatest &&
                        _isTyping &&
                        _currentTypingText.isNotEmpty) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        child: MarkdownBody(
                          data: _currentTypingText,
                          styleSheet: MarkdownStyleSheet(
                            p: TextStyle(
                              color: Theme.of(context).brightness ==
                                      Brightness.light
                                  ? AppColors.greyLight100
                                  : AppColors.greyDark100,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      );
                    }

                    // Show complete message for all other cases
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      child: MarkdownBody(
                        data: aiAnswer ?? '',
                        styleSheet: MarkdownStyleSheet(
                          p: TextStyle(
                            color:
                                Theme.of(context).brightness == Brightness.light
                                    ? AppColors.greyLight100
                                    : AppColors.greyDark100,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Text(
              DateFormat('HH:mm').format(parsedTime),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).brightness == Brightness.light
                    ? AppColors.greyLight300
                    : AppColors.greyDark300,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      endDrawer: _buildChatHistory(),
      appBar: AppBar(
        title: const Text(
          'Chat with MimiAI',
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.goNamed(RouteNames.home),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: _currentMessages.isEmpty && !_isLoading
                ? const Center(
                    child: Text(
                      'Start a conversation',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    itemCount: _currentMessages.length,
                    itemBuilder: (context, index) {
                      // Reverse the index to show messages from top to bottom
                      final reversedIndex = _currentMessages.length - 1 - index;
                      return _buildMessageBubble(
                        _currentMessages[reversedIndex],
                        isLatest: reversedIndex == 0,
                      );
                    },
                  ),
          ),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: _messageController,
              hintText: 'Type your message...',
              style: CustomTextFieldStyle.withGradient,
              textCapitalization: TextCapitalization.sentences,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
              onChanged: (value) {
                // Optional: Handle text changes if needed
              },
            ),
          ),
          const SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(24),
            ),
            child: IconButton(
              icon: const Icon(Icons.send, color: Colors.white, size: 20),
              onPressed: _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
