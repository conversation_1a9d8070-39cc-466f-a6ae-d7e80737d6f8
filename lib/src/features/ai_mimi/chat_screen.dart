// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:mimi_app/src/core/widgets/gradient_scaffold.dart';
// import 'package:mimi_app/src/features/ai_mimi/chat_bot/chatbot_screen.dart';
// import 'package:mimi_app/src/features/ai_mimi/chat_bot/chatbot_service.dart';
// import 'package:mimi_app/src/features/ai_mimi/conversational/conversation_screen.dart';

// class AIChatContainer extends StatefulWidget {
//   const AIChatContainer({
//     super.key,
//   });

//   @override
//   State<AIChatContainer> createState() => _AIChatContainerState();
// }

// class _AIChatContainerState extends State<AIChatContainer> {
//   bool _isTextMode = true;
//   final aiService = AIService();

//   void _toggleMode() {
//     HapticFeedback.mediumImpact();
//     setState(() {
//       _isTextMode = !_isTextMode;
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GradientScaffold(
//       showMiniPlayer: false,
//       appBar: AppBar(
//         title: const Text(
//           'MimiAI',
//           style: TextStyle(
//             fontSize: 20,
//             fontWeight: FontWeight.w600,
//             color: Colors.black87,
//           ),
//         ),
//         backgroundColor: Colors.transparent,
//         elevation: 0,
//       ),
//       body: Column(
//         children: [
//           // Mode Toggle below AppBar
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//             child: Container(
//               decoration: BoxDecoration(
//                 color: Colors.grey[100],
//                 borderRadius: BorderRadius.circular(12),
//               ),
//               child: Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   _buildModeButton(
//                     icon: Icons.chat_bubble_outline,
//                     label: 'Chat',
//                     isSelected: _isTextMode,
//                     onTap: () {
//                       if (!_isTextMode) _toggleMode();
//                     },
//                   ),
//                   _buildModeButton(
//                     icon: Icons.mic,
//                     label: 'Voice',
//                     isSelected: !_isTextMode,
//                     onTap: () {
//                       if (_isTextMode) _toggleMode();
//                     },
//                   ),
//                 ],
//               ),
//             ),
//           ),

//           // Main Content Area
//           Expanded(
//             child: AnimatedSwitcher(
//               duration: const Duration(milliseconds: 300),
//               child: _isTextMode
//                   ? ChatScreen(
//                       key: const ValueKey('chat'),
//                     )
//                   : const ConversationScreen(
//                       key: ValueKey('voice'),
//                     ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildModeButton({
//     required IconData icon,
//     required String label,
//     required bool isSelected,
//     required VoidCallback onTap,
//   }) {
//     return GestureDetector(
//       onTap: onTap,
//       child: AnimatedContainer(
//         duration: const Duration(milliseconds: 200),
//         padding: const EdgeInsets.symmetric(
//           horizontal: 16,
//           vertical: 8,
//         ),
//         decoration: BoxDecoration(
//           color: isSelected ? Colors.white : Colors.transparent,
//           borderRadius: BorderRadius.circular(10),
//           boxShadow: isSelected
//               ? [
//                   BoxShadow(
//                     color: Colors.black.withOpacity(0.05),
//                     blurRadius: 4,
//                     offset: const Offset(0, 2),
//                   ),
//                 ]
//               : null,
//         ),
//         child: Row(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Icon(
//               icon,
//               size: 20,
//               color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
//             ),
//             const SizedBox(width: 8),
//             Text(
//               label,
//               style: TextStyle(
//                 color:
//                     isSelected ? Theme.of(context).primaryColor : Colors.grey,
//                 fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
