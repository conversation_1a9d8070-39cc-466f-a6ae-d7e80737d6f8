// lib/src/core/utils/testing/conversation_testing_utils.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class ConversationTestingUtils {
  // Value for initial free minutes for new users
  static const double initialFreeMinutes = 10.0;

  @visibleForTesting
  static Future<void> resetAllUsersMinutes() async {
    assert(kDebugMode, 'This method should only be called in debug mode');

    final batch = FirebaseFirestore.instance.batch();
    final users = await FirebaseFirestore.instance.collection('users').get();

    for (final user in users.docs) {
      batch.update(user.reference, {
        'purchased_minutes': initialFreeMinutes,
        'current_month_usage': 0.0,
        'last_usage_reset': FieldValue.serverTimestamp(),
        'last_usage_update': FieldValue.serverTimestamp(),
      });
    }

    await batch.commit();
  }

  static Future<void> resetCurrentUserMinutes(String userId) async {
    assert(kDebugMode, 'This method should only be called in debug mode');

    await FirebaseFirestore.instance.collection('users').doc(userId).update({
      'purchased_minutes': initialFreeMinutes,
      'current_month_usage': 0.0,
      'total_conversation_minutes': 0.0,
      'last_usage_reset': FieldValue.serverTimestamp(),
      'last_usage_update': FieldValue.serverTimestamp(),
    });
  }

  /// Initialize a new user with free minutes
  static Future<void> initializeNewUserMinutes(String userId) async {
    await FirebaseFirestore.instance.collection('users').doc(userId).set({
      'purchased_minutes': initialFreeMinutes,
      'current_month_usage': 0.0,
      'total_conversation_minutes': 0.0,
      'last_usage_reset': FieldValue.serverTimestamp(),
      'last_usage_update': FieldValue.serverTimestamp(),
    }, SetOptions(merge: true));
  }
}
