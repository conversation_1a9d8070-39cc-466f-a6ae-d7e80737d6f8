// lib/src/features/conversation/providers/conversation_providers.dart
import 'dart:async';
import 'package:mimi_app/src/features/user/providers/user_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/conversation_limits_state.dart';
import '../models/conversation_timer_state.dart';

part 'conversation_providers.g.dart';

@riverpod
class ConversationTimer extends _$ConversationTimer {
  Timer? _timer;

  @override
  ConversationTimerState build() {
    ref.onDispose(() {
      stopTimer();
    });
    return const ConversationTimerState(
      currentSessionDuration: Duration.zero,
    );
  }

  void startTimer() {
    if (state.isRunning) return;

    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      state = state.copyWith(
        currentSessionDuration:
            state.currentSessionDuration + const Duration(seconds: 1),
        isRunning: true,
      );
    });
  }

  void stopTimer() {
    _timer?.cancel();
    state = state.copyWith(isRunning: false);
  }

  void resetTimer() {
    stopTimer();
    state = state.copyWith(currentSessionDuration: Duration.zero);
  }
}

@riverpod
class UserConversationLimits extends _$UserConversationLimits {
  static const double _premiumMonthlyMinutes = 60.0;

  @override
  Stream<ConversationLimitsState> build() {
    // Get user from UserNotifier
    final user = ref.watch(userNotifierProvider).valueOrNull;
    if (user == null) return Stream.value(const ConversationLimitsState());

    return FirebaseFirestore.instance
        .collection('users')
        .doc(user.auth.uid)
        .snapshots()
        .map((doc) {
      if (!doc.exists) return const ConversationLimitsState();

      final data = doc.data()!;
      final lastReset = data['last_usage_reset']?.toDate();
      final isPremium = data['is_premium'] ?? false;
      final totalMinutesUsed =
          (data['total_conversation_minutes'] ?? 0.0) as double;
      final currentMonthUsage = (data['current_month_usage'] ?? 0.0) as double;
      final purchasedMinutes = (data['purchased_minutes'] ?? 0.0) as double;

      // Calculate remaining minutes
      double remainingMinutes = 0.0;
      if (isPremium) {
        remainingMinutes = _premiumMonthlyMinutes - currentMonthUsage;
        print(
            '🔥 CONVERSATION_DEBUG: Premium user - remainingMinutes = $_premiumMonthlyMinutes - $currentMonthUsage = $remainingMinutes');
      } else {
        // For non-premium users, subtract usage from purchased minutes
        remainingMinutes = purchasedMinutes - currentMonthUsage;
        print(
            '🔥 CONVERSATION_DEBUG: Non-premium user - remainingMinutes = $purchasedMinutes - $currentMonthUsage = $remainingMinutes');
      }

      return ConversationLimitsState(
        totalMinutesUsed: totalMinutesUsed,
        remainingMinutes: remainingMinutes,
        isPremium: isPremium,
        purchasedMinutes: purchasedMinutes,
        lastResetDate: lastReset,
      );
    });
  }

  Future<void> updateUsage(double minutes) async {
    final user = ref.read(userNotifierProvider).valueOrNull;
    if (user == null) {
      print('🔥 CONVERSATION_DEBUG: No user found, cannot update usage');
      return;
    }

    print(
        '🔥 CONVERSATION_DEBUG: Updating usage for user ${user.auth.uid} with $minutes minutes');

    final userDoc =
        FirebaseFirestore.instance.collection('users').doc(user.auth.uid);

    try {
      await FirebaseFirestore.instance.runTransaction((transaction) async {
        final snapshot = await transaction.get(userDoc);
        if (!snapshot.exists) {
          print('🔥 CONVERSATION_DEBUG: User document does not exist');
          return;
        }

        final currentMonthUsage =
            (snapshot.data()?['current_month_usage'] ?? 0.0) as double;
        final totalMinutes =
            (snapshot.data()?['total_conversation_minutes'] ?? 0.0) as double;
        final purchasedMinutes =
            (snapshot.data()?['purchased_minutes'] ?? 0.0) as double;

        print(
            '🔥 CONVERSATION_DEBUG: Before update - currentMonthUsage: $currentMonthUsage, totalMinutes: $totalMinutes, purchasedMinutes: $purchasedMinutes');

        final newCurrentMonthUsage = currentMonthUsage + minutes;
        final newTotalMinutes = totalMinutes + minutes;

        transaction.update(userDoc, {
          'current_month_usage': newCurrentMonthUsage,
          'total_conversation_minutes': newTotalMinutes,
          'last_usage_update': FieldValue.serverTimestamp(),
        });

        print(
            '🔥 CONVERSATION_DEBUG: After update - newCurrentMonthUsage: $newCurrentMonthUsage, newTotalMinutes: $newTotalMinutes');
      });
      print(
          '🔥 CONVERSATION_DEBUG: Usage update transaction completed successfully');
    } catch (e) {
      print('🔥 CONVERSATION_DEBUG: Error updating usage: $e');
      rethrow;
    }
  }

  Future<void> resetMonthlyUsage() async {
    final user = ref.read(userNotifierProvider).valueOrNull;
    if (user == null) return;

    await FirebaseFirestore.instance
        .collection('users')
        .doc(user.auth.uid)
        .update({
      'current_month_usage': 0.0,
      'last_usage_reset': FieldValue.serverTimestamp(),
    });
  }
}

@riverpod
class ConversationController extends _$ConversationController {
  @override
  Future<void> build() async {}

  Future<bool> canStartConversation() async {
    final limitsState = await ref.read(userConversationLimitsProvider.future);
    return limitsState.remainingMinutes > 0;
  }

  Future<void> startConversation() async {
    if (!await canStartConversation()) {
      throw Exception('No remaining conversation minutes');
    }

    ref.read(conversationTimerProvider.notifier).startTimer();
  }

  Future<void> endConversation() async {
    final timer = ref.read(conversationTimerProvider);
    final minutes = timer.currentSessionDuration.inSeconds / 60.0;

    print('🔥 CONVERSATION_DEBUG: endConversation called');
    print(
        '🔥 CONVERSATION_DEBUG: Session duration: ${timer.currentSessionDuration}');
    print('🔥 CONVERSATION_DEBUG: Calculated minutes: $minutes');

    // Update usage first
    await ref
        .read(userConversationLimitsProvider.notifier)
        .updateUsage(minutes);

    print('🔥 CONVERSATION_DEBUG: Usage update completed');

    // Then reset timer
    ref.read(conversationTimerProvider.notifier).resetTimer();

    print('🔥 CONVERSATION_DEBUG: Timer reset completed');
  }
}
