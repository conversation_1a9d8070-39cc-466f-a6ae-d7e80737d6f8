// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$conversationTimerHash() => r'939ce5afcb19e6075c1d656665ecfcb14c009bbf';

/// See also [ConversationTimer].
@ProviderFor(ConversationTimer)
final conversationTimerProvider = AutoDisposeNotifierProvider<ConversationTimer,
    ConversationTimerState>.internal(
  ConversationTimer.new,
  name: r'conversationTimerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$conversationTimerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConversationTimer = AutoDisposeNotifier<ConversationTimerState>;
String _$userConversationLimitsHash() =>
    r'66d332f6fff525810c40f5426b684b7cc4287588';

/// See also [UserConversationLimits].
@ProviderFor(UserConversationLimits)
final userConversationLimitsProvider = AutoDisposeStreamNotifierProvider<
    UserConversationLimits, ConversationLimitsState>.internal(
  UserConversationLimits.new,
  name: r'userConversationLimitsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userConversationLimitsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserConversationLimits
    = AutoDisposeStreamNotifier<ConversationLimitsState>;
String _$conversationControllerHash() =>
    r'df574c2e4b16b4036d5e88d9f2f9ba98f28cab16';

/// See also [ConversationController].
@ProviderFor(ConversationController)
final conversationControllerProvider =
    AutoDisposeAsyncNotifierProvider<ConversationController, void>.internal(
  ConversationController.new,
  name: r'conversationControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$conversationControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConversationController = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
