import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/ai_mimi/conversational/elevenlabs_wss_service.dart'
    as ws;
import 'dart:math' as math;
import 'dart:async';

// lib/src/features/conversation/screens/conversation_screen.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/features/ai_mimi/conversational/providers/conversation_providers.dart';
import 'package:mimi_app/src/features/purchases/utils/purchase_utils.dart';

class ConversationScreen extends ConsumerStatefulWidget {
  const ConversationScreen({super.key});

  @override
  ConsumerState<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends ConsumerState<ConversationScreen> {
  late ws.ElevenLabsWebSocketService webSocketService;
  Timer? _connectionStateTimer;

  @override
  void initState() {
    super.initState();
    webSocketService = ws.ElevenLabsWebSocketService(
      apiKey: '***************************************************',
      agentId: 'vPCymF8OG1x0Z05jjm8b',
      //agentId: 'agent_01jx11qrhcey5r2q5sjt1vs05v',
      isPublicAgent: true,
    );

    // Monitor connection state
    _connectionStateTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          // This will trigger a rebuild to show current connection state
        });
      }
    });
  }

  Future<void> startConversation() async {
    final controller = ref.read(conversationControllerProvider.notifier);

    try {
      // Check if user can start conversation
      if (!await controller.canStartConversation()) {
        _showPurchaseBottomSheet();
        return;
      }

      // Start conversation tracking
      await controller.startConversation();

      // Start WebSocket connection
      await webSocketService.openConnection();

      // Start recording
      await webSocketService.startUserRecording();

      _toggleConversation();
    } catch (e) {
      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting conversation: $e')),
        );
      }
    }
  }

  Future<void> endConversation() async {
    // End WebSocket connection
    webSocketService.closeConnection();

    // Update usage tracking
    await ref.read(conversationControllerProvider.notifier).endConversation();

    _toggleConversation();
  }

  void _showPurchaseBottomSheet() {
    PurchaseUtils.showMinutesPurchaseBottomSheet(context);
  }

  void _showMinutesSettings() {
    PurchaseUtils.showMinutesSettingsBottomSheet(context);
  }

  bool _isConversationStarted = false;

  void _toggleConversation() {
    setState(() {
      _isConversationStarted = !_isConversationStarted;
    });
  }

  String _getConnectionStatusText() {
    if (!_isConversationStarted) {
      return 'DISCONNECTED';
    }
    return 'CONNECTED';
  }

  Color _getConnectionStatusColor() {
    if (!_isConversationStarted) {
      return Colors.grey;
    }
    return Colors.green;
  }

  @override
  void dispose() {
    _connectionStateTimer?.cancel();
    // Ensure conversation is properly ended and resources cleaned up
    if (_isConversationStarted) {
      // Properly end the conversation to record time usage
      _endConversationSync();
    }
    webSocketService.dispose();
    super.dispose();
  }

  /// Synchronous version of endConversation for use in dispose
  void _endConversationSync() {
    // End WebSocket connection
    webSocketService.closeConnection();

    // Update usage tracking synchronously
    // Note: We can't await in dispose, so we fire and forget
    ref
        .read(conversationControllerProvider.notifier)
        .endConversation()
        .catchError((error) {
      // Log error but don't throw since we're in dispose
      debugPrint('Error ending conversation in dispose: $error');
    });

    _toggleConversation();
  }

  @override
  Widget build(BuildContext context) {
    final timerState = ref.watch(conversationTimerProvider);

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        // Ensure conversation is ended when navigating back
        if (didPop && _isConversationStarted) {
          _endConversationSync();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Talk to MimiAI'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () async {
              // End conversation before navigating back if one is active
              if (_isConversationStarted) {
                await endConversation();
              }
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
          actions: [],
        ),
        body: Column(
          children: [
            // Minutes Bar below app bar
            Row(
              children: [
                // Shop Icon (Leading)
                IconButton(
                  icon: const Icon(Icons.shopping_cart),
                  onPressed: _showPurchaseBottomSheet,
                  tooltip: 'Purchase Minutes',
                  style: IconButton.styleFrom(
                    backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                    foregroundColor: AppColors.primary,
                  ),
                ),

                // Remaining Minutes (Center)
                Expanded(
                  child: Consumer(
                    builder: (context, ref, child) {
                      final limitsState =
                          ref.watch(userConversationLimitsProvider);
                      return limitsState.when(
                        data: (data) => Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _formatRemainingMinutes(data.remainingMinutes),
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: data.remainingMinutes < 5
                                          ? Colors.red
                                          : AppColors.primary,
                                    ),
                              ),
                              Text(
                                'minutes left',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        error: (_, __) => const Center(
                          child: Text(
                            'Error loading minutes',
                            style: TextStyle(color: Colors.red),
                          ),
                        ),
                        loading: () => const Center(
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Analytics Icon (Trailing)
                IconButton(
                  icon: const Icon(Icons.analytics),
                  onPressed: _showMinutesSettings,
                  tooltip: 'Minutes Details',
                  style: IconButton.styleFrom(
                    backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                    foregroundColor: AppColors.primary,
                  ),
                ),
              ],
            ),

            // Main Content
            Expanded(
              child: Center(
                child: Card(
                  elevation: 8,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                    ),
                    padding: const EdgeInsets.only(top: 24, bottom: 24),
                    width: 300,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _getConnectionStatusText(),
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: _getConnectionStatusColor(),
                                  ),
                        ),
                        const SizedBox(height: 20),
                        // Show current session duration
                        Text(
                          'Current Session: ${_formatDuration(timerState.currentSessionDuration)}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        // const SizedBox(height: 20),
                        // Show total usage from Firestore
                        // limitsState.when(
                        //   data: (data) => Text(
                        //     'Total Usage: ${data.totalMinutesUsed.toStringAsFixed(1)} mins',
                        //     style: const TextStyle(fontSize: 16),
                        //   ),
                        //   error: (_, __) => const Text('Error loading usage'),
                        //   loading: () => const CircularProgressIndicator(),
                        // ),
                        const SizedBox(height: 24),
                        CircleWave(
                          speed: _isConversationStarted ? 1.5 : 0.3,
                        ),
                        const SizedBox(height: 24),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(24.0),
                                child: ElevatedButton(
                                  onPressed: () {
                                    _isConversationStarted
                                        ? endConversation()
                                        : startConversation();
                                  },
                                  // style: ElevatedButton.styleFrom(
                                  //   backgroundColor: AppColors.primaryDark,
                                  //   foregroundColor: AppColors.textColorDark,
                                  //   shape: RoundedRectangleBorder(
                                  //     borderRadius: BorderRadius.circular(2),
                                  //   ),
                                  // ),
                                  child: Text(
                                    _isConversationStarted
                                        ? 'End Conversation'
                                        : 'Start Conversation',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                          color: Colors.white,
                                        ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  /// Formats remaining minutes to show minutes and seconds
  /// Examples: 8.5 minutes -> "8:30", 10.0 minutes -> "10:00", 0.5 minutes -> "0:30"
  String _formatRemainingMinutes(double totalMinutes) {
    final wholeMinutes = totalMinutes.floor();
    final fractionalMinutes = totalMinutes - wholeMinutes;
    final seconds = (fractionalMinutes * 60).round();

    return '$wholeMinutes:${seconds.toString().padLeft(2, '0')}';
  }
}

class CircleWave extends StatefulWidget {
  final double size;
  final double speed;
  final int points;

  const CircleWave({
    super.key,
    this.size = 200,
    required this.speed,
    this.points = 100,
  });

  @override
  State<CircleWave> createState() => _CircleWaveState();
}

class _CircleWaveState extends State<CircleWave> with TickerProviderStateMixin {
  // Brighter, more vibrant colors
  static const colors = [
    [
      Color(0xFF7400B8), // Deep Pink
      Color(0xFF4169E1), // Royal Blue
    ],
    [
      Color(0xFF5E60CE), // Lime
      Color(0xFF9400D3), // Dark Violet
    ],
    [
      Color(0xFF4EA8DE), // Orange Red
      Color(0xFF1E90FF), // Dodger Blue
    ],
  ];

  late final AnimationController controller;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(
      vsync: this,
      upperBound: 2,
      duration: Duration(milliseconds: (10000 / widget.speed).round()),
    )..repeat();
  }

  @override
  void didUpdateWidget(CircleWave oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.speed != widget.speed) {
      controller.duration =
          Duration(milliseconds: (10000 / widget.speed).round());
      controller.repeat();
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        children: [
          for (int i = 0; i < colors.length; i++)
            Positioned.fill(
              child: CustomPaint(
                painter: CircleWavePainter(
                  animation: controller,
                  index: i,
                  gradientColors: colors[i],
                  points: widget.points,
                  size: widget.size,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class CircleWavePainter extends CustomPainter {
  CircleWavePainter({
    required this.animation,
    required this.index,
    required this.gradientColors,
    required this.points,
    required this.size,
  }) : super(repaint: animation);

  final Animation<double> animation;
  final int index;
  final List<Color> gradientColors;
  final int points;
  final double size;

  static const halfPi = math.pi / 2;
  static const twoPi = math.pi * 2;
  final int n = 7;

  double map(
      double value, double start1, double stop1, double start2, double stop2) {
    return ((value - start1) / (stop1 - start1)) * (stop2 - start2) + start2;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final t = animation.value;
    final halfWidth = size.width / 2;
    final halfHeight = size.height / 2;
    final q = index * halfPi;

    final scale = this.size / 400;
    final baseRadius = 165 * scale;

    final offsets = <Offset>[];
    for (var i = 0; i < points; i++) {
      final th = i * twoPi / points;
      double os = map(math.cos(th - twoPi * t), -1, 1, 0, 1);
      os = 0.125 * math.pow(os, 2.75);
      final r = baseRadius * (1 + os * math.cos(n * th + 1.5 * twoPi * t + q));
      offsets.add(Offset(
        r * math.sin(th) + halfWidth,
        -r * math.cos(th) + halfHeight,
      ));
    }

    if (offsets.isNotEmpty) {
      final path = Path()..addPolygon(offsets, true);

      // Main stroke with solid color
      final paint = Paint()
        ..color = Color.lerp(
          gradientColors[0],
          gradientColors[1],
          (math.sin(t * math.pi) + 1) / 2,
        )!
        ..strokeWidth = 4 * scale
        ..style = PaintingStyle.stroke;

      // Draw main path
      canvas.drawPath(path, paint);

      // Glow effect with the same color
      final glowPaint = Paint()
        ..color = paint.color.withOpacity(0.5)
        ..strokeWidth = 6 * scale
        ..style = PaintingStyle.stroke
        ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 10);

      canvas.drawPath(path, glowPaint);

      // Inner glow
      final innerGlowPaint = Paint()
        ..color = paint.color.withOpacity(0.3)
        ..strokeWidth = 20 * scale
        ..style = PaintingStyle.stroke
        ..maskFilter = const MaskFilter.blur(BlurStyle.inner, 5);

      canvas.drawPath(path, innerGlowPaint);
    }
  }

  @override
  bool shouldRepaint(CircleWavePainter oldDelegate) {
    return oldDelegate.animation != animation ||
        oldDelegate.index != index ||
        oldDelegate.points != points ||
        oldDelegate.size != size;
  }
}
