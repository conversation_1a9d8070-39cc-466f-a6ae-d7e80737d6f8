// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_timer_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConversationTimerStateImpl _$$ConversationTimerStateImplFromJson(
        Map<String, dynamic> json) =>
    _$ConversationTimerStateImpl(
      currentSessionDuration: Duration(
          microseconds: (json['currentSessionDuration'] as num).toInt()),
      isRunning: json['isRunning'] as bool? ?? false,
    );

Map<String, dynamic> _$$ConversationTimerStateImplToJson(
        _$ConversationTimerStateImpl instance) =>
    <String, dynamic>{
      'currentSessionDuration': instance.currentSessionDuration.inMicroseconds,
      'isRunning': instance.isRunning,
    };
