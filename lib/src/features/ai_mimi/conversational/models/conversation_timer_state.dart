import 'package:freezed_annotation/freezed_annotation.dart';

part 'conversation_timer_state.freezed.dart';
part 'conversation_timer_state.g.dart';

@freezed
class ConversationTimerState with _$ConversationTimerState {
  const factory ConversationTimerState({
    required Duration currentSessionDuration,
    @Default(false) bool isRunning,
  }) = _ConversationTimerState;

  factory ConversationTimerState.fromJson(Map<String, dynamic> json) =>
      _$ConversationTimerStateFromJson(json);
}
