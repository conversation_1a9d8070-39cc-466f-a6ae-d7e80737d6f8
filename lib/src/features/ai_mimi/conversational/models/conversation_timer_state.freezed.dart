// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversation_timer_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ConversationTimerState _$ConversationTimerStateFromJson(
    Map<String, dynamic> json) {
  return _ConversationTimerState.fromJson(json);
}

/// @nodoc
mixin _$ConversationTimerState {
  Duration get currentSessionDuration => throw _privateConstructorUsedError;
  bool get isRunning => throw _privateConstructorUsedError;

  /// Serializes this ConversationTimerState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConversationTimerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConversationTimerStateCopyWith<ConversationTimerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversationTimerStateCopyWith<$Res> {
  factory $ConversationTimerStateCopyWith(ConversationTimerState value,
          $Res Function(ConversationTimerState) then) =
      _$ConversationTimerStateCopyWithImpl<$Res, ConversationTimerState>;
  @useResult
  $Res call({Duration currentSessionDuration, bool isRunning});
}

/// @nodoc
class _$ConversationTimerStateCopyWithImpl<$Res,
        $Val extends ConversationTimerState>
    implements $ConversationTimerStateCopyWith<$Res> {
  _$ConversationTimerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConversationTimerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentSessionDuration = null,
    Object? isRunning = null,
  }) {
    return _then(_value.copyWith(
      currentSessionDuration: null == currentSessionDuration
          ? _value.currentSessionDuration
          : currentSessionDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      isRunning: null == isRunning
          ? _value.isRunning
          : isRunning // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConversationTimerStateImplCopyWith<$Res>
    implements $ConversationTimerStateCopyWith<$Res> {
  factory _$$ConversationTimerStateImplCopyWith(
          _$ConversationTimerStateImpl value,
          $Res Function(_$ConversationTimerStateImpl) then) =
      __$$ConversationTimerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Duration currentSessionDuration, bool isRunning});
}

/// @nodoc
class __$$ConversationTimerStateImplCopyWithImpl<$Res>
    extends _$ConversationTimerStateCopyWithImpl<$Res,
        _$ConversationTimerStateImpl>
    implements _$$ConversationTimerStateImplCopyWith<$Res> {
  __$$ConversationTimerStateImplCopyWithImpl(
      _$ConversationTimerStateImpl _value,
      $Res Function(_$ConversationTimerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConversationTimerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentSessionDuration = null,
    Object? isRunning = null,
  }) {
    return _then(_$ConversationTimerStateImpl(
      currentSessionDuration: null == currentSessionDuration
          ? _value.currentSessionDuration
          : currentSessionDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      isRunning: null == isRunning
          ? _value.isRunning
          : isRunning // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversationTimerStateImpl implements _ConversationTimerState {
  const _$ConversationTimerStateImpl(
      {required this.currentSessionDuration, this.isRunning = false});

  factory _$ConversationTimerStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConversationTimerStateImplFromJson(json);

  @override
  final Duration currentSessionDuration;
  @override
  @JsonKey()
  final bool isRunning;

  @override
  String toString() {
    return 'ConversationTimerState(currentSessionDuration: $currentSessionDuration, isRunning: $isRunning)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversationTimerStateImpl &&
            (identical(other.currentSessionDuration, currentSessionDuration) ||
                other.currentSessionDuration == currentSessionDuration) &&
            (identical(other.isRunning, isRunning) ||
                other.isRunning == isRunning));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, currentSessionDuration, isRunning);

  /// Create a copy of ConversationTimerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversationTimerStateImplCopyWith<_$ConversationTimerStateImpl>
      get copyWith => __$$ConversationTimerStateImplCopyWithImpl<
          _$ConversationTimerStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversationTimerStateImplToJson(
      this,
    );
  }
}

abstract class _ConversationTimerState implements ConversationTimerState {
  const factory _ConversationTimerState(
      {required final Duration currentSessionDuration,
      final bool isRunning}) = _$ConversationTimerStateImpl;

  factory _ConversationTimerState.fromJson(Map<String, dynamic> json) =
      _$ConversationTimerStateImpl.fromJson;

  @override
  Duration get currentSessionDuration;
  @override
  bool get isRunning;

  /// Create a copy of ConversationTimerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConversationTimerStateImplCopyWith<_$ConversationTimerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
