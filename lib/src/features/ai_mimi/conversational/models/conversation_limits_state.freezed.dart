// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversation_limits_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ConversationLimitsState _$ConversationLimitsStateFromJson(
    Map<String, dynamic> json) {
  return _ConversationLimitsState.fromJson(json);
}

/// @nodoc
mixin _$ConversationLimitsState {
  double get totalMinutesUsed => throw _privateConstructorUsedError;
  double get remainingMinutes => throw _privateConstructorUsedError;
  double get currentSessionMinutes => throw _privateConstructorUsedError;
  bool get isPremium => throw _privateConstructorUsedError;
  double get purchasedMinutes => throw _privateConstructorUsedError;
  DateTime? get lastResetDate => throw _privateConstructorUsedError;

  /// Serializes this ConversationLimitsState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConversationLimitsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConversationLimitsStateCopyWith<ConversationLimitsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversationLimitsStateCopyWith<$Res> {
  factory $ConversationLimitsStateCopyWith(ConversationLimitsState value,
          $Res Function(ConversationLimitsState) then) =
      _$ConversationLimitsStateCopyWithImpl<$Res, ConversationLimitsState>;
  @useResult
  $Res call(
      {double totalMinutesUsed,
      double remainingMinutes,
      double currentSessionMinutes,
      bool isPremium,
      double purchasedMinutes,
      DateTime? lastResetDate});
}

/// @nodoc
class _$ConversationLimitsStateCopyWithImpl<$Res,
        $Val extends ConversationLimitsState>
    implements $ConversationLimitsStateCopyWith<$Res> {
  _$ConversationLimitsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConversationLimitsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalMinutesUsed = null,
    Object? remainingMinutes = null,
    Object? currentSessionMinutes = null,
    Object? isPremium = null,
    Object? purchasedMinutes = null,
    Object? lastResetDate = freezed,
  }) {
    return _then(_value.copyWith(
      totalMinutesUsed: null == totalMinutesUsed
          ? _value.totalMinutesUsed
          : totalMinutesUsed // ignore: cast_nullable_to_non_nullable
              as double,
      remainingMinutes: null == remainingMinutes
          ? _value.remainingMinutes
          : remainingMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      currentSessionMinutes: null == currentSessionMinutes
          ? _value.currentSessionMinutes
          : currentSessionMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      isPremium: null == isPremium
          ? _value.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      purchasedMinutes: null == purchasedMinutes
          ? _value.purchasedMinutes
          : purchasedMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      lastResetDate: freezed == lastResetDate
          ? _value.lastResetDate
          : lastResetDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConversationLimitsStateImplCopyWith<$Res>
    implements $ConversationLimitsStateCopyWith<$Res> {
  factory _$$ConversationLimitsStateImplCopyWith(
          _$ConversationLimitsStateImpl value,
          $Res Function(_$ConversationLimitsStateImpl) then) =
      __$$ConversationLimitsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double totalMinutesUsed,
      double remainingMinutes,
      double currentSessionMinutes,
      bool isPremium,
      double purchasedMinutes,
      DateTime? lastResetDate});
}

/// @nodoc
class __$$ConversationLimitsStateImplCopyWithImpl<$Res>
    extends _$ConversationLimitsStateCopyWithImpl<$Res,
        _$ConversationLimitsStateImpl>
    implements _$$ConversationLimitsStateImplCopyWith<$Res> {
  __$$ConversationLimitsStateImplCopyWithImpl(
      _$ConversationLimitsStateImpl _value,
      $Res Function(_$ConversationLimitsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConversationLimitsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalMinutesUsed = null,
    Object? remainingMinutes = null,
    Object? currentSessionMinutes = null,
    Object? isPremium = null,
    Object? purchasedMinutes = null,
    Object? lastResetDate = freezed,
  }) {
    return _then(_$ConversationLimitsStateImpl(
      totalMinutesUsed: null == totalMinutesUsed
          ? _value.totalMinutesUsed
          : totalMinutesUsed // ignore: cast_nullable_to_non_nullable
              as double,
      remainingMinutes: null == remainingMinutes
          ? _value.remainingMinutes
          : remainingMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      currentSessionMinutes: null == currentSessionMinutes
          ? _value.currentSessionMinutes
          : currentSessionMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      isPremium: null == isPremium
          ? _value.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      purchasedMinutes: null == purchasedMinutes
          ? _value.purchasedMinutes
          : purchasedMinutes // ignore: cast_nullable_to_non_nullable
              as double,
      lastResetDate: freezed == lastResetDate
          ? _value.lastResetDate
          : lastResetDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversationLimitsStateImpl implements _ConversationLimitsState {
  const _$ConversationLimitsStateImpl(
      {this.totalMinutesUsed = 0.0,
      this.remainingMinutes = 0.0,
      this.currentSessionMinutes = 0.0,
      this.isPremium = false,
      this.purchasedMinutes = 0.0,
      this.lastResetDate});

  factory _$ConversationLimitsStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConversationLimitsStateImplFromJson(json);

  @override
  @JsonKey()
  final double totalMinutesUsed;
  @override
  @JsonKey()
  final double remainingMinutes;
  @override
  @JsonKey()
  final double currentSessionMinutes;
  @override
  @JsonKey()
  final bool isPremium;
  @override
  @JsonKey()
  final double purchasedMinutes;
  @override
  final DateTime? lastResetDate;

  @override
  String toString() {
    return 'ConversationLimitsState(totalMinutesUsed: $totalMinutesUsed, remainingMinutes: $remainingMinutes, currentSessionMinutes: $currentSessionMinutes, isPremium: $isPremium, purchasedMinutes: $purchasedMinutes, lastResetDate: $lastResetDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversationLimitsStateImpl &&
            (identical(other.totalMinutesUsed, totalMinutesUsed) ||
                other.totalMinutesUsed == totalMinutesUsed) &&
            (identical(other.remainingMinutes, remainingMinutes) ||
                other.remainingMinutes == remainingMinutes) &&
            (identical(other.currentSessionMinutes, currentSessionMinutes) ||
                other.currentSessionMinutes == currentSessionMinutes) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.purchasedMinutes, purchasedMinutes) ||
                other.purchasedMinutes == purchasedMinutes) &&
            (identical(other.lastResetDate, lastResetDate) ||
                other.lastResetDate == lastResetDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalMinutesUsed,
      remainingMinutes,
      currentSessionMinutes,
      isPremium,
      purchasedMinutes,
      lastResetDate);

  /// Create a copy of ConversationLimitsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversationLimitsStateImplCopyWith<_$ConversationLimitsStateImpl>
      get copyWith => __$$ConversationLimitsStateImplCopyWithImpl<
          _$ConversationLimitsStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversationLimitsStateImplToJson(
      this,
    );
  }
}

abstract class _ConversationLimitsState implements ConversationLimitsState {
  const factory _ConversationLimitsState(
      {final double totalMinutesUsed,
      final double remainingMinutes,
      final double currentSessionMinutes,
      final bool isPremium,
      final double purchasedMinutes,
      final DateTime? lastResetDate}) = _$ConversationLimitsStateImpl;

  factory _ConversationLimitsState.fromJson(Map<String, dynamic> json) =
      _$ConversationLimitsStateImpl.fromJson;

  @override
  double get totalMinutesUsed;
  @override
  double get remainingMinutes;
  @override
  double get currentSessionMinutes;
  @override
  bool get isPremium;
  @override
  double get purchasedMinutes;
  @override
  DateTime? get lastResetDate;

  /// Create a copy of ConversationLimitsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConversationLimitsStateImplCopyWith<_$ConversationLimitsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
