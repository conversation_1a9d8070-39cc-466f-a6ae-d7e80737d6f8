// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_limits_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConversationLimitsStateImpl _$$ConversationLimitsStateImplFromJson(
        Map<String, dynamic> json) =>
    _$ConversationLimitsStateImpl(
      totalMinutesUsed: (json['totalMinutesUsed'] as num?)?.toDouble() ?? 0.0,
      remainingMinutes: (json['remainingMinutes'] as num?)?.toDouble() ?? 0.0,
      currentSessionMinutes:
          (json['currentSessionMinutes'] as num?)?.toDouble() ?? 0.0,
      isPremium: json['isPremium'] as bool? ?? false,
      purchasedMinutes: (json['purchasedMinutes'] as num?)?.toDouble() ?? 0.0,
      lastResetDate: json['lastResetDate'] == null
          ? null
          : DateTime.parse(json['lastResetDate'] as String),
    );

Map<String, dynamic> _$$ConversationLimitsStateImplToJson(
        _$ConversationLimitsStateImpl instance) =>
    <String, dynamic>{
      'totalMinutesUsed': instance.totalMinutesUsed,
      'remainingMinutes': instance.remainingMinutes,
      'currentSessionMinutes': instance.currentSessionMinutes,
      'isPremium': instance.isPremium,
      'purchasedMinutes': instance.purchasedMinutes,
      'lastResetDate': instance.lastResetDate?.toIso8601String(),
    };
