import 'package:freezed_annotation/freezed_annotation.dart';

part 'conversation_limits_state.freezed.dart';
part 'conversation_limits_state.g.dart';

@freezed
class ConversationLimitsState with _$ConversationLimitsState {
  const factory ConversationLimitsState({
    @Default(0.0) double totalMinutesUsed,
    @Default(0.0) double remainingMinutes,
    @Default(0.0) double currentSessionMinutes,
    @Default(false) bool isPremium,
    @Default(0.0) double purchasedMinutes,
    DateTime? lastResetDate,
  }) = _ConversationLimitsState;

  factory ConversationLimitsState.fromJson(Map<String, dynamic> json) =>
      _$ConversationLimitsStateFromJson(json);
}
