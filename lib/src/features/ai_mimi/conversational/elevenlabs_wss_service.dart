// import 'dart:async';
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:audio_session/audio_session.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:just_audio/just_audio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:path/path.dart' as p;

enum ConversationState {
  disconnected,
  connecting,
  connected,
  recording,
  processing,
  error
}

enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  error,
  reconnecting
}

// Class to represent a prepared audio chunk for buffering
class _PreparedAudioChunk {
  final String filePath;
  final int sizeBytes;
  final DateTime preparedAt;
  final String eventId;

  _PreparedAudioChunk({
    required this.filePath,
    required this.sizeBytes,
    required this.preparedAt,
    required this.eventId,
  });
}

class ElevenLabsWebSocketService {
  final String apiKey;
  final String agentId;
  final bool isPublicAgent;

  WebSocketChannel? _channel;
  StreamSubscription? _subscription;

  // State tracking
  ConversationState _conversationState = ConversationState.disconnected;
  WebSocketConnectionState _connectionState =
      WebSocketConnectionState.disconnected;
  String? _conversationId;
  DateTime? _conversationStartTime;

  // Audio statistics for debugging
  int _audioChunksSent = 0;
  int _audioChunksReceived = 0;
  int _totalAudioBytesSent = 0;
  int _totalAudioBytesReceived = 0;
  DateTime? _lastAudioChunkSent;
  DateTime? _lastAudioChunkReceived;

  // Audio queue for real-time playback
  final List<String> _audioQueue = [];
  bool _isPlayingAudio = false;

  // Enhanced buffering system
  final List<_PreparedAudioChunk> _preparedChunks = [];
  int _maxBufferSize = 3; // Number of chunks to keep prepared (adaptive)
  bool _isPreparingChunks = false;
  int _currentPlayingIndex = 0;

  // Adaptive buffering metrics
  int _consecutiveBufferingEvents = 0;
  DateTime? _lastBufferingEvent;

  // For user audio recording
  final _recorder = AudioRecorder();
  bool _isRecording = false;

  // Audio playback - simplified approach
  late final AudioPlayer _player;

  // Keep track of TTS .wav files (for naming)
  int _wavIndex = 0;

  // Logging utilities
  void _logWithTimestamp(String message, {String level = 'INFO'}) {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('[$timestamp] [$level] [ElevenLabs] $message');
  }

  void _logStateChange(String operation, String details) {
    _logWithTimestamp('$operation: $details', level: 'STATE');
  }

  void _logError(String operation, dynamic error, {String? context}) {
    final contextStr = context != null ? ' | Context: $context' : '';
    _logWithTimestamp('$operation FAILED: $error$contextStr', level: 'ERROR');
  }

  void _logConversationFlow(String event, [Map<String, dynamic>? data]) {
    final dataStr = data != null ? ' | Data: ${jsonEncode(data)}' : '';
    _logWithTimestamp('CONVERSATION: $event$dataStr', level: 'FLOW');
  }

  void _updateConversationState(ConversationState newState, {String? reason}) {
    final oldState = _conversationState;
    _conversationState = newState;
    final reasonStr = reason != null ? ' | Reason: $reason' : '';
    _logStateChange('Conversation State', '$oldState → $newState$reasonStr');
  }

  void _updateConnectionState(WebSocketConnectionState newState,
      {String? reason}) {
    final oldState = _connectionState;
    _connectionState = newState;
    final reasonStr = reason != null ? ' | Reason: $reason' : '';
    _logStateChange('Connection State', '$oldState → $newState$reasonStr');
  }

  void _logAudioStats() {
    final now = DateTime.now();
    final timeSinceLastSent = _lastAudioChunkSent != null
        ? now.difference(_lastAudioChunkSent!).inMilliseconds
        : null;
    final timeSinceLastReceived = _lastAudioChunkReceived != null
        ? now.difference(_lastAudioChunkReceived!).inMilliseconds
        : null;

    _logWithTimestamp(
        'Audio Stats: Sent: $_audioChunksSent chunks (${_totalAudioBytesSent}B) | '
        'Received: $_audioChunksReceived chunks (${_totalAudioBytesReceived}B) | '
        'Last sent: ${timeSinceLastSent}ms ago | Last received: ${timeSinceLastReceived}ms ago',
        level: 'STATS');
  }

  ElevenLabsWebSocketService({
    required this.apiKey,
    required this.agentId,
    this.isPublicAgent = false,
  }) {
    _logConversationFlow('ElevenLabsWebSocketService initialized', {
      'agent_id': agentId,
      'is_public_agent': isPublicAgent,
      'api_key_length': apiKey.length
    });
    _initJustAudio();
  }

  // Public getters for debugging
  ConversationState get conversationState => _conversationState;
  WebSocketConnectionState get connectionState => _connectionState;
  String? get conversationId => _conversationId;
  bool get isRecording => _isRecording;

  // -----------------------------------------------------------
  // 1) Initialize the JustAudio player & empty playlist
  // -----------------------------------------------------------
  Future<void> _initJustAudio() async {
    _logWithTimestamp('Initializing JustAudio player...', level: 'AUDIO');

    try {
      _player = AudioPlayer();

      // Set volume to maximum for conversational AI
      await _player.setVolume(1.0);
      _logWithTimestamp('Audio player volume set to maximum (1.0)',
          level: 'AUDIO');

      // Configure audio session for conversational AI with compatibility in mind
      final session = await AudioSession.instance;

      try {
        // Use a compatible configuration that works with other audio players
        await session.configure(const AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions:
              AVAudioSessionCategoryOptions.defaultToSpeaker,
          avAudioSessionMode: AVAudioSessionMode.voiceChat,
          avAudioSessionRouteSharingPolicy:
              AVAudioSessionRouteSharingPolicy.defaultPolicy,
          androidAudioAttributes: AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech,
            usage: AndroidAudioUsage.voiceCommunication,
          ),
          androidAudioFocusGainType:
              AndroidAudioFocusGainType.gainTransientMayDuck,
          androidWillPauseWhenDucked: false,
        ));

        await session.setActive(true);
        _logWithTimestamp('Audio session configured and activated successfully',
            level: 'AUDIO');
      } catch (e) {
        _logWithTimestamp('Audio session configuration failed: $e',
            level: 'AUDIO');
        // Try a simpler fallback configuration
        try {
          await session.configure(const AudioSessionConfiguration(
            avAudioSessionCategory: AVAudioSessionCategory.playback,
            avAudioSessionMode: AVAudioSessionMode.spokenAudio,
          ));
          await session.setActive(true);
          _logWithTimestamp('Fallback audio session configuration successful',
              level: 'AUDIO');
        } catch (fallbackError) {
          _logError('Audio Session Fallback', fallbackError,
              context: 'Both primary and fallback configurations failed');
          // Continue without audio session configuration
        }
      }

      _logWithTimestamp('JustAudio player initialized successfully',
          level: 'AUDIO');

      // Listen for playback events and errors
      _player.playbackEventStream.listen((event) {
        _logWithTimestamp(
            'Playback event: state=${event.processingState}, playing=${_player.playing}, position=${event.updatePosition}',
            level: 'AUDIO');

        // Handle buffering events for adaptive buffering
        if (event.processingState == ProcessingState.buffering) {
          _handleBufferingEvent();
        }

        // When playback completes, update conversation state
        if (event.processingState == ProcessingState.completed) {
          _logWithTimestamp('Audio playback completed', level: 'AUDIO');
          _updateConversationState(ConversationState.connected,
              reason: 'Audio playback completed');
        }
      }, onError: (Object e, StackTrace st) {
        _logError('JustAudio Playback', e,
            context: 'Playback event stream error');
      });
    } catch (e) {
      _logError('JustAudio Initialization', e,
          context: 'Failed to initialize audio player');
    }
  }

  // -----------------------------------------------------------
  // 2) Build or Get WebSocket URL
  // -----------------------------------------------------------
  Future<String> _getSignedUrl() async {
    final url = Uri.parse(
        'https://api.elevenlabs.io/v1/convai/conversation/get_signed_url?agent_id=$agentId');
    final response = await http.get(url, headers: {
      'xi-api-key': apiKey,
    });

    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);
      final signedUrl = responseBody['url'];
      if (signedUrl == null || signedUrl.isEmpty) {
        throw Exception('Signed URL is missing in the response.');
      }
      return signedUrl;
    } else {
      throw Exception(
          'Failed to generate signed URL: ${response.statusCode}, ${response.body}');
    }
  }

  Future<String> _getWebSocketUrl() async {
    if (isPublicAgent) {
      // Public agent
      return 'wss://api.elevenlabs.io/v1/convai/conversation?agent_id=$agentId';
    } else {
      // Private agent
      return await _getSignedUrl();
    }
  }

  // -----------------------------------------------------------
  // 3) Open the WebSocket Connection
  // -----------------------------------------------------------
  Future<void> openConnection() async {
    _logConversationFlow('Starting WebSocket connection');
    _updateConnectionState(WebSocketConnectionState.connecting,
        reason: 'openConnection called');

    // Stop any existing audio playback for fresh conversation
    try {
      await _player.stop();
      // Ensure volume is set to maximum for new conversation
      await _player.setVolume(1.0);
      _logWithTimestamp(
          'Stopped existing audio playback and reset volume for new conversation',
          level: 'AUDIO');
    } catch (e) {
      _logWithTimestamp(
          'Note: Could not stop audio playback (may not be playing): $e',
          level: 'AUDIO');
    }

    try {
      final webSocketUrl = await _getWebSocketUrl();
      _logWithTimestamp(
          'Connecting to WebSocket URL: ${webSocketUrl.substring(0, 50)}...',
          level: 'CONN');

      _channel = WebSocketChannel.connect(Uri.parse(webSocketUrl));
      _updateConnectionState(WebSocketConnectionState.connected,
          reason: 'WebSocket channel created');

      _subscription = _channel!.stream.listen(
        (message) {
          if (message is String) {
            final parsed = jsonDecode(message);
            final messageType = parsed['type'] ?? 'unknown';

            _logWithTimestamp('Received message type: $messageType',
                level: 'MSG');

            if (parsed['type'] == 'audio') {
              // TTS chunk from ElevenLabs - play immediately as per official docs
              final audioBase64 = parsed['audio_event']['audio_base_64'];
              final audioId = parsed['audio_event']['event_id'];
              final audioSize = (audioBase64?.length ?? 0) as int;

              _audioChunksReceived++;
              _totalAudioBytesReceived += audioSize;
              _lastAudioChunkReceived = DateTime.now();

              _logWithTimestamp(
                  'Received TTS chunk: event_id=$audioId, size=${audioSize}B, total_chunks=$_audioChunksReceived',
                  level: 'AUDIO');

              // Play audio chunk immediately (ElevenLabs official pattern)
              _playAudioChunk(audioBase64, audioId);
            } else if (parsed['type'] == 'conversation_initiation_metadata') {
              _conversationId = parsed['conversation_initiation_metadata_event']
                  ['conversation_id'];
              _conversationStartTime = DateTime.now();
              _updateConversationState(ConversationState.connected,
                  reason: 'Conversation initiated');
              _logConversationFlow('Conversation initiated', {
                'conversation_id': _conversationId,
                'timestamp': _conversationStartTime!.toIso8601String()
              });
            } else if (parsed['type'] == 'user_transcript') {
              final transcript =
                  parsed['user_transcription_event']['user_transcript'];
              _logConversationFlow('User transcript received', {
                'transcript': transcript,
                'length': transcript?.length ?? 0
              });
            } else if (parsed['type'] == 'agent_response') {
              final response = parsed['agent_response_event']['agent_response'];
              _updateConversationState(ConversationState.processing,
                  reason: 'Agent response received');
              _logConversationFlow('Agent response received',
                  {'response': response, 'length': response?.length ?? 0});
            } else if (parsed['type'] == 'interruption') {
              final eventId = parsed['interruption_event']['event_id'];
              _logConversationFlow(
                  'Interruption detected - ElevenLabs will handle automatically',
                  {'event_id': eventId});
              // ElevenLabs automatically stops sending audio chunks on interruption
              // No manual intervention needed
            } else if (parsed['type'] == 'agent_response_correction') {
              final originalResponse = parsed['agent_response_correction_event']
                  ['original_agent_response'];
              final correctedResponse =
                  parsed['agent_response_correction_event']
                      ['corrected_agent_response'];
              _logConversationFlow(
                  'Agent response corrected after interruption', {
                'original': originalResponse,
                'corrected': correctedResponse
              });
              // ElevenLabs has already handled the interruption
            } else if (parsed['type'] == 'vad_score') {
              final vadScore = parsed['vad_score_event']['vad_score'];
              // Log VAD scores occasionally to monitor speech detection
              if (vadScore > 0.8) {
                _logWithTimestamp('High VAD score detected: $vadScore',
                    level: 'AUDIO');
              }
            } else if (parsed['type'] == 'ping') {
              final eventId = parsed['ping_event']['event_id'];
              _logWithTimestamp('Received ping event: $eventId', level: 'CONN');

              // Respond with pong as per ElevenLabs documentation
              if (_channel != null &&
                  _connectionState == WebSocketConnectionState.connected) {
                final pongMessage = jsonEncode({
                  'type': 'pong',
                  'event_id': eventId,
                });
                _channel!.sink.add(pongMessage);
                _logWithTimestamp('Sent pong response: $eventId',
                    level: 'CONN');
              }
            } else {
              _logWithTimestamp(
                  'Unhandled message type: $messageType | Data: ${jsonEncode(parsed)}',
                  level: 'WARN');
            }
          } else {
            _logError('Message Processing', 'Received non-string message',
                context: 'Type: ${message.runtimeType}');
          }
        },
        onError: (error) {
          _updateConnectionState(WebSocketConnectionState.error,
              reason: 'WebSocket error: $error');
          _logError('WebSocket Connection', error,
              context: 'Will attempt reconnection');
          reconnect();
        },
        onDone: () {
          _updateConnectionState(WebSocketConnectionState.disconnected,
              reason: 'WebSocket connection closed');
          _logConversationFlow(
              'WebSocket connection closed - attempting reconnection');
          reconnect();
        },
      );

      _logConversationFlow('WebSocket connection established successfully');
      // Log initial audio stats
      _logAudioStats();
    } catch (e) {
      _updateConnectionState(WebSocketConnectionState.error,
          reason: 'Connection failed: $e');
      _logError('WebSocket Connection', e,
          context: 'Failed to establish connection');
    }
  }

  void closeConnection() {
    _logConversationFlow('Closing WebSocket connection');
    _stopUserRecording();
    _subscription?.cancel();
    _channel?.sink.close();
    _updateConnectionState(WebSocketConnectionState.disconnected,
        reason: 'Manual close');
    _updateConversationState(ConversationState.disconnected,
        reason: 'Connection closed');

    // Stop any ongoing audio playback and cleanup buffers
    try {
      if (_player.playing) {
        _player.stop();
        _isPlayingAudio = false;
      }

      // Clear audio queues and prepared chunks
      _audioQueue.clear();
      _cleanupPreparedChunks();
      _isPreparingChunks = false;
      _currentPlayingIndex = 0;

      // Reset adaptive buffering metrics
      _consecutiveBufferingEvents = 0;
      _lastBufferingEvent = null;
      _maxBufferSize = 3; // Reset to default
    } catch (e) {
      _logWithTimestamp('Note: Could not stop audio playback: $e',
          level: 'AUDIO');
    }

    // Log final stats
    _logAudioStats();
    if (_conversationStartTime != null) {
      final duration = DateTime.now().difference(_conversationStartTime!);
      _logConversationFlow('Conversation ended', {
        'duration_seconds': duration.inSeconds,
        'conversation_id': _conversationId
      });
    }

    _logConversationFlow('Connection closed manually');
  }

  // Dispose method for proper resource cleanup
  void dispose() {
    _logConversationFlow('Disposing ElevenLabsWebSocketService');

    // Close connection if still open
    closeConnection();

    // Cleanup any remaining prepared chunks
    _cleanupPreparedChunks();

    // Dispose audio resources
    _player.dispose();
    _recorder.dispose();

    _logConversationFlow('ElevenLabsWebSocketService disposed');
  }

  void reconnect() {
    _logConversationFlow('Starting reconnection process');
    _updateConnectionState(WebSocketConnectionState.reconnecting,
        reason: 'Reconnection initiated');
    closeConnection();
    Future.delayed(const Duration(seconds: 2), () async {
      _logConversationFlow('Attempting reconnection after delay');
      await openConnection();
    });
  }

  // -----------------------------------------------------------
  // 4) Start/Stop Recording (User -> ElevenLabs)
  // -----------------------------------------------------------
  Future<void> startUserRecording() async {
    _logConversationFlow('Attempting to start user recording');

    // Start streaming the user's microphone -> WebSocket
    if (_isRecording) {
      _logError('Start Recording', 'Already recording',
          context: 'Recording state: $_isRecording');
      return;
    }

    // ElevenLabs automatically detects when user starts speaking and handles interruptions
    // No manual intervention needed - just start recording

    try {
      if (await _recorder.hasPermission()) {
        _isRecording = true;
        _updateConversationState(ConversationState.recording,
            reason: 'User recording started');

        final audioStream = await _recorder.startStream(
          const RecordConfig(
            encoder: AudioEncoder.pcm16bits,
            sampleRate: 16000,
            numChannels: 1,
          ),
        );

        _logConversationFlow('User recording started successfully',
            {'sample_rate': 16000, 'channels': 1, 'encoder': 'pcm16bits'});

        audioStream.listen((audioChunk) {
          if (audioChunk.isNotEmpty) {
            // Validate audio data quality
            final hasNonZeroData = audioChunk.any((byte) => byte != 0);
            final maxValue = audioChunk.isNotEmpty
                ? audioChunk.reduce((a, b) => a > b ? a : b)
                : 0;
            final minValue = audioChunk.isNotEmpty
                ? audioChunk.reduce((a, b) => a < b ? a : b)
                : 0;

            // Only log audio quality issues or every 50th chunk to reduce spam
            if (!hasNonZeroData) {
              _logError('Audio Quality', 'Received silent audio chunk',
                  context: 'Length: ${audioChunk.length}B, all zeros detected');
            } else if (_audioChunksSent % 50 == 0) {
              _logWithTimestamp(
                  'Audio quality check: length=${audioChunk.length}B, range=[$minValue, $maxValue], chunks_sent=$_audioChunksSent',
                  level: 'AUDIO');
            }

            _sendUserAudioChunk(audioChunk);
          } else {
            _logError('Audio Recording', 'Received empty audio chunk',
                context: 'This may indicate microphone issues');
          }
        }).onError((error) {
          _logError('Audio Stream', error,
              context: 'Audio recording stream failed');
          _updateConversationState(ConversationState.error,
              reason: 'Audio stream error');
        });
      } else {
        _logError('Start Recording', 'Microphone permission denied',
            context: 'User must grant microphone permission');
        _updateConversationState(ConversationState.error,
            reason: 'No microphone permission');
      }
    } catch (e) {
      _logError('Start Recording', e,
          context: 'Failed to initialize audio recording');
      _updateConversationState(ConversationState.error,
          reason: 'Recording initialization failed');
    }
  }

  Future<void> _stopUserRecording() async {
    if (!_isRecording) return;

    _logConversationFlow('Stopping user recording');
    try {
      _isRecording = false;
      if (await _recorder.isRecording()) {
        await _recorder.stop();
        _updateConversationState(ConversationState.connected,
            reason: 'Recording stopped');
        _logConversationFlow('User recording stopped successfully', {
          'total_chunks_sent': _audioChunksSent,
          'total_bytes_sent': _totalAudioBytesSent
        });
      }
    } catch (e) {
      _logError('Stop Recording', e, context: 'Failed to stop audio recording');
    }
  }

  // -----------------------------------------------------------
  // 5) Send user audio chunks to ElevenLabs
  // -----------------------------------------------------------
  void _sendUserAudioChunk(Uint8List audioData) {
    if (_channel == null) {
      _logError('Send Audio Chunk', 'WebSocket connection not established',
          context: 'Connection state: $_connectionState');
      return;
    }

    if (_connectionState != WebSocketConnectionState.connected) {
      _logError('Send Audio Chunk', 'WebSocket not in connected state',
          context: 'Current state: $_connectionState');
      return;
    }

    try {
      final audioBase64 = base64Encode(audioData);
      final message = jsonEncode({
        'user_audio_chunk': audioBase64,
      });
      _channel!.sink.add(message);

      // Update statistics
      _audioChunksSent++;
      _totalAudioBytesSent += audioData.length;
      _lastAudioChunkSent = DateTime.now();

      // Log every 100th chunk to avoid spam but provide progress updates
      if (_audioChunksSent % 100 == 0) {
        _logWithTimestamp(
            'Audio chunk milestone: sent $_audioChunksSent chunks (${_totalAudioBytesSent}B total)',
            level: 'AUDIO');
      }
    } catch (e) {
      _logError('Send Audio Chunk', e,
          context: 'Chunk size: ${audioData.length}B');
    }
  }

  // -----------------------------------------------------------
  // 6) Enhanced audio chunk processing with buffering
  // -----------------------------------------------------------
  Future<void> _playAudioChunk(String audioBase64, dynamic eventId) async {
    try {
      // Add to queue for sequential playback
      _audioQueue.add(audioBase64);

      _logWithTimestamp(
          'Added audio chunk to queue (event_id: $eventId, queue_length: ${_audioQueue.length})',
          level: 'AUDIO');

      // Start preparing chunks in background if not already doing so
      if (!_isPreparingChunks) {
        _prepareAudioChunks();
      }

      // If not currently playing, start playback
      if (!_isPlayingAudio) {
        await _processAudioQueue();
      }
    } catch (e) {
      _logError('Play Audio Chunk', e,
          context: 'Failed to queue audio chunk for playback');
    }
  }

  // Prepare audio chunks in background for smoother playback
  Future<void> _prepareAudioChunks() async {
    if (_isPreparingChunks) return;
    _isPreparingChunks = true;

    try {
      while (
          _audioQueue.isNotEmpty && _preparedChunks.length < _maxBufferSize) {
        final audioBase64 = _audioQueue.removeAt(0);

        // Convert to WAV and save to file
        final pcmData = base64Decode(audioBase64);
        final wavData = _addWavHeader(pcmData, 16000, 1);

        final tempDir = await getTemporaryDirectory();
        final filePath = p.join(
          tempDir.path,
          'tts_chunk_${_wavIndex++}.wav',
        );
        final tempFile = File(filePath);
        await tempFile.writeAsBytes(wavData);

        final preparedChunk = _PreparedAudioChunk(
          filePath: filePath,
          sizeBytes: tempFile.lengthSync(),
          preparedAt: DateTime.now(),
          eventId: 'chunk_${_wavIndex - 1}',
        );

        _preparedChunks.add(preparedChunk);

        _logWithTimestamp(
            'Prepared audio chunk: $filePath (${preparedChunk.sizeBytes}B) - Buffer: ${_preparedChunks.length}/$_maxBufferSize',
            level: 'AUDIO');
      }
    } catch (e) {
      _logError('Audio Chunk Preparation', e,
          context: 'Failed to prepare audio chunks');
    } finally {
      _isPreparingChunks = false;
    }
  }

  // Enhanced audio queue processing with buffering
  Future<void> _processAudioQueue() async {
    if (_isPlayingAudio) {
      return;
    }

    _isPlayingAudio = true;

    try {
      // Process prepared chunks first for smoother playback
      while (_preparedChunks.isNotEmpty || _audioQueue.isNotEmpty) {
        _PreparedAudioChunk? chunk;

        // Use prepared chunk if available, otherwise prepare one on-demand
        if (_preparedChunks.isNotEmpty) {
          chunk = _preparedChunks.removeAt(0);
          _currentPlayingIndex++;
        } else if (_audioQueue.isNotEmpty) {
          // Prepare chunk on-demand if buffer is empty
          final audioBase64 = _audioQueue.removeAt(0);
          final pcmData = base64Decode(audioBase64);
          final wavData = _addWavHeader(pcmData, 16000, 1);

          final tempDir = await getTemporaryDirectory();
          final filePath = p.join(
            tempDir.path,
            'tts_chunk_${_wavIndex++}.wav',
          );
          final tempFile = File(filePath);
          await tempFile.writeAsBytes(wavData);

          chunk = _PreparedAudioChunk(
            filePath: filePath,
            sizeBytes: tempFile.lengthSync(),
            preparedAt: DateTime.now(),
            eventId: 'chunk_${_wavIndex - 1}',
          );
        }

        if (chunk != null) {
          _logWithTimestamp(
              'Playing audio chunk: ${chunk.filePath} (${chunk.sizeBytes}B)',
              level: 'AUDIO');

          try {
            // Stop any current playback and reset player state
            await _player.stop();

            // Ensure volume is set to maximum for each chunk
            await _player.setVolume(1.0);

            // Set the audio source and play
            await _player
                .setAudioSource(AudioSource.uri(Uri.file(chunk.filePath)));
            await _player.play();

            // Wait for this chunk to complete before playing next
            await _player.processingStateStream
                .where((state) => state == ProcessingState.completed)
                .first;

            _logWithTimestamp('Audio chunk completed successfully',
                level: 'AUDIO');
          } catch (e) {
            _logError('Audio Chunk Playback', e,
                context: 'Failed to play chunk: ${chunk.filePath}');
          }

          // Continue preparing chunks in background
          if (!_isPreparingChunks && _audioQueue.isNotEmpty) {
            _prepareAudioChunks();
          }
        }
      }
    } catch (e) {
      _logError('Audio Queue Processing', e,
          context: 'Failed to process audio queue');
    } finally {
      _isPlayingAudio = false;
      _currentPlayingIndex = 0;
      _logWithTimestamp('Audio queue processing completed', level: 'AUDIO');
    }
  }

  // Handle buffering events for adaptive buffering
  void _handleBufferingEvent() {
    final now = DateTime.now();

    // Track consecutive buffering events
    if (_lastBufferingEvent != null &&
        now.difference(_lastBufferingEvent!).inSeconds < 5) {
      _consecutiveBufferingEvents++;
    } else {
      _consecutiveBufferingEvents = 1;
    }

    _lastBufferingEvent = now;

    // Increase buffer size if experiencing frequent buffering
    if (_consecutiveBufferingEvents >= 3 && _maxBufferSize < 6) {
      _maxBufferSize++;
      _logWithTimestamp(
          'Adaptive buffering: Increased buffer size to $_maxBufferSize due to frequent buffering',
          level: 'AUDIO');
    }
  }

  // Cleanup prepared audio chunks and their files
  void _cleanupPreparedChunks() {
    for (final chunk in _preparedChunks) {
      try {
        final file = File(chunk.filePath);
        if (file.existsSync()) {
          file.deleteSync();
          _logWithTimestamp('Cleaned up audio file: ${chunk.filePath}',
              level: 'AUDIO');
        }
      } catch (e) {
        _logWithTimestamp(
            'Failed to cleanup audio file: ${chunk.filePath} - $e',
            level: 'AUDIO');
      }
    }
    _preparedChunks.clear();
  }

  // -----------------------------------------------------------
  // 8) WAV Header Helper
  // -----------------------------------------------------------
  Uint8List _addWavHeader(Uint8List pcmData, int sampleRate, int channels) {
    final int byteRate = sampleRate * channels * 2; // 16-bit PCM
    final int totalDataLen = pcmData.length + 36;

    final header = BytesBuilder();
    header.add(utf8.encode('RIFF')); // Chunk ID
    header.add(_intToBytes(totalDataLen, 4)); // Chunk size
    header.add(utf8.encode('WAVE')); // Format
    header.add(utf8.encode('fmt ')); // Sub-chunk 1 ID
    header.add(_intToBytes(16, 4)); // Sub-chunk 1 size
    header.add(_intToBytes(1, 2)); // Audio format (1=PCM)
    header.add(_intToBytes(channels, 2));
    header.add(_intToBytes(sampleRate, 4));
    header.add(_intToBytes(byteRate, 4));
    header.add(_intToBytes(channels * 2, 2)); // Block align
    header.add(_intToBytes(16, 2)); // Bits per sample
    header.add(utf8.encode('data'));
    header.add(_intToBytes(pcmData.length, 4));

    return Uint8List.fromList(header.toBytes() + pcmData);
  }

  List<int> _intToBytes(int value, int length) {
    final result = <int>[];
    for (int i = 0; i < length; i++) {
      result.add((value >> (8 * i)) & 0xFF);
    }
    return result;
  }
}
