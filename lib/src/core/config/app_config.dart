class AppConfig {
  // Auth Features
  static const enableGoogleSignIn = false;
  static const enableAppleSignIn = false;
  static const enableAnonymousSignIn = false;
  static const enableEmailSignIn = true;
  static const enablePasswordReset = true;
  static const enableDeleteAccount = true;
  static const enableBiometrics = false;

  // Other App Features
  static const enablePushNotifications = true;
  static const enableAnalytics = true;
  static const enableCrashlytics = true;

  // Environment & API Config
  static const useEmulator = false;
  static const logNetworkCalls = false;

  // App Settings
  static const forceUpdateVersion = "1.0.0";
  static const minimumSupportedVersion = "1.0.0";

  // Firebase Features
  static const enableFirebaseAuth = true;
  static const enableFirebaseStorage = false;
  static const enableFirebaseMessaging = false;

  // Development Tools
  static const showDebugBanner = false;
  static const enableLogging = true;

  static const enableThemeSwitching = true;

  // RevenueCat Configuration
  static const String revenueCatApiKeyIOS = 'appl_gSHZZwjXrDPgTXkQfsBOzNtycoM';
  static const String revenueCatApiKeyAndroid =
      'goog_YOUR_ANDROID_API_KEY_HERE'; // TODO: Add Android key
  static const String revenueCatApiKeyMacOS =
      'appl_gSHZZwjXrDPgTXkQfsBOzNtycoM'; // Same as iOS for Apple platforms
}
