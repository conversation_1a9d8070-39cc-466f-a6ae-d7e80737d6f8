// lib/core/config/url_config.dart

abstract class UrlConfig {
  static const Map<String, String> socialUrls = {
    'facebook': 'https://facebook.com/yourapp',
    'twitter': 'https://twitter.com/yourapp',
    'instagram': 'https://instagram.com/mimibland.london',
  };

  static const Map<String, String> storeUrls = {
    'ios': 'https://apps.apple.com/app/id{YOUR_APP_ID}',
    'android':
        'https://play.google.com/store/apps/details?id={YOUR_PACKAGE_ID}',
  };

  static const String supportEmail = '<EMAIL>';
  static const String websiteUrl = 'https://yourapp.com';
  static const String privacyPolicyUrl =
      'https://newlifeacademy2020.co.uk/privacy/';
  static const String termsUrl = 'https://newlifeacademy2020.co.uk/terms/';
  static const String eulaUrl =
      'https://www.apple.com/legal/internet-services/itunes/dev/stdeula/';
}
