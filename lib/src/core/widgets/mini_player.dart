import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/features/audio_player/data/providers/audio_player_provider.dart';

class MiniPlayer extends ConsumerWidget {
  static const double height = 70.0;
  final String heroTag = 'mini-player-artwork';

  const MiniPlayer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerControllerProvider);

    return audioState.when(
      data: (track) {
        final controller = ref.watch(audioPlayerControllerProvider.notifier);
        final currentTrack = controller.currentTrack;

        if (currentTrack == null) return const SizedBox.shrink();

        return Container(
          height: height,
          margin: const EdgeInsets.symmetric(horizontal: 1.0, vertical: 4.0),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(4),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _navigateToPlayer(context),
              borderRadius: BorderRadius.circular(12),
              child: Row(
                children: [
                  // Artwork with Hero animation
                  SizedBox(
                    height: height,
                    width: height,
                    child: Hero(
                      tag: heroTag,
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(4),
                          bottomLeft: Radius.circular(4),
                        ),
                        child: currentTrack.artworkUrl != null
                            ? Image.asset(
                                currentTrack.artworkUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (_, __, ___) =>
                                    _buildPlaceholder(context),
                              )
                            : _buildPlaceholder(context),
                      ),
                    ),
                  ),
                  // Track Info
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            currentTrack.title,
                            style: Theme.of(context).textTheme.titleMedium,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            currentTrack.categoryId,
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Play/Pause Button
                  Consumer(
                    builder: (context, ref, _) {
                      final isPlaying = controller.isPlaying;
                      return IconButton(
                        icon: Icon(
                          isPlaying
                              ? Icons.pause_circle_filled
                              : Icons.play_circle_filled,
                          size: 38,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        onPressed: () {
                          isPlaying ? controller.pause() : controller.play();
                        },
                      );
                    },
                  ),
                  // Close Button
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 24,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    onPressed: () {
                      controller.stopAndClear();
                    },
                  ),
                  const SizedBox(width: 4),
                ],
              ),
            ),
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: Icon(
        Icons.music_note,
        size: 32,
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
      ),
    );
  }

  void _navigateToPlayer(BuildContext context) {
    context.push('/player');
  }
}
