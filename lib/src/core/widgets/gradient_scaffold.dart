// lib/core/presentation/widgets/gradient_scaffold.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/features/audio_player/presentation/widgets/mini_player.dart';

class GradientScaffold extends ConsumerWidget {
  final Widget body;
  final List<Color>? gradientColors;
  final PreferredSizeWidget? appBar;

  final bool? resizeToAvoidBottomInset;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? bottomNavigationBar;
  final bool extendBody;
  final bool extendBodyBehindAppBar;
  final bool showMiniPlayer; // New parameter

  const GradientScaffold({
    super.key,
    required this.body,
    this.gradientColors,
    this.appBar,
    this.resizeToAvoidBottomInset,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.bottomNavigationBar,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
    this.showMiniPlayer = true, // Default to true
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Prepare the body content
    Widget bodyContent = body;
    if (showMiniPlayer) {
      bodyContent = bodyContent.withMiniPlayer(extendBody: extendBody);
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
            colors: [AppColors.gradientStart, AppColors.gradientEnd],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter),
        // color: Theme.of(context).brightness == Brightness.dark
        //     ? AppColors.primary
        //     : AppColors.backgroundLight,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: appBar != null
            ? AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                title: appBar is AppBar ? (appBar as AppBar).title : null,
                actions: appBar is AppBar ? (appBar as AppBar).actions : null,
                leading: appBar is AppBar ? (appBar as AppBar).leading : null,
                bottom: appBar is AppBar ? (appBar as AppBar).bottom : null,
              )
            : null,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
        floatingActionButton: floatingActionButton,
        floatingActionButtonLocation: floatingActionButtonLocation,
        bottomNavigationBar: bottomNavigationBar != null
            ? Theme(
                data: Theme.of(context).copyWith(
                  bottomNavigationBarTheme: const BottomNavigationBarThemeData(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                  ),
                ),
                child: bottomNavigationBar!,
              )
            : null,
        extendBody: extendBody,
        extendBodyBehindAppBar: extendBodyBehindAppBar,
        body: bodyContent,
      ),
    );
  }
}
