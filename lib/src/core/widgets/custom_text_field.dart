// lib/src/core/widgets/custom_text_field.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/typography_constants.dart';

enum CustomTextFieldStyle {
  basic,
  withBottomBorder,
  withGradient,
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final CustomTextFieldStyle style;
  final FocusNode? focusNode;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onTap;
  final bool autofocus;
  final bool enableInteractiveSelection;
  final int? maxLines;
  final bool expands;
  final TextAlignVertical? textAlignVertical;
  final TextCapitalization textCapitalization;
  final String? Function(String?)? validator;
  final bool isFormField;
  final TextInputAction? textInputAction;

  const CustomTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.style = CustomTextFieldStyle.basic,
    this.focusNode,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.autofocus = false,
    this.enableInteractiveSelection = true,
    this.maxLines = 1,
    this.expands = false,
    this.textAlignVertical,
    this.textCapitalization = TextCapitalization.sentences,
    this.validator,
    this.isFormField = false,
    this.textInputAction,
  });

  @override
  Widget build(BuildContext context) {
    final textField = _buildTextField(context);

    switch (style) {
      case CustomTextFieldStyle.basic:
        return textField;
      case CustomTextFieldStyle.withBottomBorder:
        return Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.5),
                width: 1,
              ),
            ),
          ),
          child: textField,
        );
      case CustomTextFieldStyle.withGradient:
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withValues(alpha: 0.3),
                Colors.black.withValues(alpha: 0.2),
                Colors.transparent,
              ],
              stops: const [0.0, 0.4, 1.0],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: textField,
        );
    }
  }

  Widget _buildTextField(BuildContext context) {
    final inputDecoration = InputDecoration(
      hintText: hintText,
      hintStyle: _getHintStyle(context),
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      filled: false,
      contentPadding: _getContentPadding(),
    );

    final textStyle = _getTextStyle(context);

    if (isFormField) {
      return TextFormField(
        controller: controller,
        focusNode: focusNode,
        autofocus: autofocus,
        enableInteractiveSelection: enableInteractiveSelection,
        maxLines: expands ? null : maxLines,
        expands: expands,
        onChanged: onChanged,
        onFieldSubmitted: onSubmitted,
        onTap: onTap,
        style: textStyle,
        decoration: inputDecoration,
        textAlignVertical: textAlignVertical,
        textCapitalization: textCapitalization,
        textInputAction: textInputAction,
        validator: validator,
      );
    } else {
      return TextField(
        controller: controller,
        focusNode: focusNode,
        autofocus: autofocus,
        enableInteractiveSelection: enableInteractiveSelection,
        maxLines: expands ? null : maxLines,
        expands: expands,
        onChanged: onChanged,
        onSubmitted: onSubmitted,
        onTap: onTap,
        style: textStyle,
        decoration: inputDecoration,
        textAlignVertical: textAlignVertical,
        textCapitalization: textCapitalization,
        textInputAction: textInputAction,
      );
    }
  }

  EdgeInsets _getContentPadding() {
    switch (style) {
      case CustomTextFieldStyle.basic:
      case CustomTextFieldStyle.withBottomBorder:
        return const EdgeInsets.symmetric(vertical: 12);
      case CustomTextFieldStyle.withGradient:
        return const EdgeInsets.only(
          top: 20,
          bottom: 40,
          left: 16,
          right: 16,
        );
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    switch (style) {
      case CustomTextFieldStyle.basic:
      case CustomTextFieldStyle.withBottomBorder:
        return TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontSize: AppTypography.bodySmall,
        );
      case CustomTextFieldStyle.withGradient:
        return TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontSize: AppTypography.bodySmall,
        );
    }
  }

  TextStyle _getHintStyle(BuildContext context) {
    switch (style) {
      case CustomTextFieldStyle.basic:
      case CustomTextFieldStyle.withBottomBorder:
        return TextStyle(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          fontSize: AppTypography.bodyMedium,
        );
      case CustomTextFieldStyle.withGradient:
        return TextStyle(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          fontSize: AppTypography.bodySmall,
        );
    }
  }
}
