import 'package:flutter/material.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/features/ai_mimi/conversational/conversation_screen.dart';

class AiFab extends StatelessWidget {
  const AiFab({super.key});

  @override
  Widget build(BuildContext context) {
    final key = GlobalKey<ExpandableFabState>();

    return ExpandableFab(
      key: key,
      type: ExpandableFabType.fan,
      distance: 70,
      pos: ExpandableFabPos.center,
      fanAngle: 180,
      overlayStyle: ExpandableFabOverlayStyle(
        color: Colors.black.withValues(alpha: 0.5),
        blur: 5,
      ),
      openButtonBuilder: RotateFloatingActionButtonBuilder(
        child: const Icon(Icons.psychology_outlined),
        fabSize: ExpandableFabSize.regular,
        foregroundColor: Colors.white,
        backgroundColor: AppColors.primary,
        shape: const CircleBorder(),
      ),
      closeButtonBuilder: DefaultFloatingActionButtonBuilder(
        child: const Icon(Icons.close),
        fabSize: ExpandableFabSize.regular,
        foregroundColor: Colors.black87,
        backgroundColor: Colors.white,
        shape: const CircleBorder(),
      ),
      children: [
        FloatingActionButton(
          heroTag: 'chat',
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.chat_bubble_outline, color: Colors.white),
          onPressed: () => context.pushNamed(RouteNames.chat),
        ),
        FloatingActionButton(
          heroTag: 'voice',
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.mic, color: Colors.white),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ConversationScreen(),
              ),
            );
          },
        ),
        FloatingActionButton(
          heroTag: 'breathwork',
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.waves, color: Colors.white),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ConversationScreen(),
              ),
            );
          },
        ),
        FloatingActionButton(
          heroTag: 'focus',
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.mic, color: Colors.white),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ConversationScreen(),
              ),
            );
          },
        ),
        FloatingActionButton(
          heroTag: 'quotes',
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.mic, color: Colors.white),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ConversationScreen(),
              ),
            );
          },
        ),
      ],
    );
  }
}
