import 'package:flutter/material.dart';

/// A reusable header widget for intention and routine setting flows.
///
/// This widget provides consistent styling and layout for headers across
/// the app's flow screens, including support for titles, subtitles, and
/// flexible alignment options.
class FlowHeaderWidget extends StatelessWidget {
  /// The main title text to display
  final String title;

  /// Optional subtitle text to display below the title
  final String? subtitle;

  /// Text alignment for the header content
  final TextAlign textAlign;

  /// Text style for the title. If null, uses theme's headlineMedium
  final TextStyle? titleStyle;

  /// Text style for the subtitle. If null, uses theme's titleMedium
  final TextStyle? subtitleStyle;

  /// Padding around the header content
  final EdgeInsetsGeometry padding;

  /// Spacing between title and subtitle
  final double spacing;

  const FlowHeaderWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.textAlign = TextAlign.start,
    this.titleStyle,
    this.subtitleStyle,
    this.padding = const EdgeInsets.fromLTRB(24, 0, 24, 24),
    this.spacing = 8.0,
  });

  /// Factory constructor for centered headers
  const FlowHeaderWidget.centered({
    super.key,
    required this.title,
    this.subtitle,
    this.titleStyle,
    this.subtitleStyle,
    this.padding = const EdgeInsets.fromLTRB(24, 0, 24, 24),
    this.spacing = 8.0,
  }) : textAlign = TextAlign.center;

  /// Factory constructor for headers with top padding (typically for first screen in flow)
  const FlowHeaderWidget.withTopPadding({
    super.key,
    required this.title,
    this.subtitle,
    this.textAlign = TextAlign.start,
    this.titleStyle,
    this.subtitleStyle,
    this.spacing = 8.0,
  }) : padding = const EdgeInsets.fromLTRB(24, 4, 24, 24);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: textAlign == TextAlign.center
            ? CrossAxisAlignment.center
            : CrossAxisAlignment.stretch,
        children: [
          Text(
            title,
            style: titleStyle ?? theme.textTheme.headlineMedium,
            textAlign: textAlign,
          ),
          if (subtitle != null) ...[
            SizedBox(height: spacing),
            Text(
              subtitle!,
              style: subtitleStyle ?? theme.textTheme.titleSmall,
              textAlign: textAlign,
            ),
          ],
        ],
      ),
    );
  }
}
