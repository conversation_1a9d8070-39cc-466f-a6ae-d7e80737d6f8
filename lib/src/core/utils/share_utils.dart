// lib/core/utils/share_utils.dart
import 'dart:math';
import 'dart:io' show Platform;

import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';
import 'package:share_plus/share_plus.dart';
import '../config/url_config.dart';

class ShareUtils {
  static Future<void> shareApp(BuildContext context) async {
    try {
      final String url = Platform.isIOS
          ? UrlConfig.storeUrls['ios']!
          : UrlConfig.storeUrls['android']!;

      await Share.share(
        '${AppStrings.shareAppMessage}\n$url',
        subject: AppStrings.shareAppSubject,
      );
    } catch (e) {
      showFailureToast(
        context,
        title: 'Error',
        description: AppStrings.shareError,
      );
    }
  }

  static Future<void> shareContent({
    required BuildContext context,
    required String content,
    String? subject,
    Rectangle<double>? sharePositionOrigin,
  }) async {
    try {
      if (Platform.isIOS && sharePositionOrigin != null) {
        await Share.share(
          content,
          subject: subject,
          sharePositionOrigin: Rect.fromLTWH(
            sharePositionOrigin.left,
            sharePositionOrigin.top,
            sharePositionOrigin.width,
            sharePositionOrigin.height,
          ),
        );
      } else {
        await Share.share(
          content,
          subject: subject,
        );
      }
    } catch (e) {
      showFailureToast(
        context,
        title: 'Error',
        description: AppStrings.shareError,
      );
    }
  }
}
