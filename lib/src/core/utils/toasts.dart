import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';
import 'package:firebase_auth/firebase_auth.dart';

//Update Colors as Required in App :

// Note: This shadow definition needs context to access theme colors
// Consider moving this to a method that takes BuildContext
List<BoxShadow> getLowModeShadow(BuildContext context) => [
      BoxShadow(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
        blurRadius: 24.0,
        offset: const Offset(0, 8),
      ),
    ];

void showSuccessToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.success,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: getLowModeShadow(context),
  );
}

void showFailureToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.error,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: getLowModeShadow(context),
  );
}

void showWarningToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.warning,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: getLowModeShadow(context),
  );
}

void showInfoToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.info,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: getLowModeShadow(context),
  );
}

/// Maps Firebase Auth error codes to user-friendly messages
String getAuthErrorMessage(dynamic error) {
  // Handle the "bad state" error specifically
  if (error.toString().contains('Bad state: Future already completed')) {
    return 'Authentication request is already in progress. Please wait.';
  }

  if (error is FirebaseAuthException) {
    switch (error.code) {
      case 'user-not-found':
        return 'No account found with this email address.';
      case 'wrong-password':
        return 'Invalid password. Please try again.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'email-already-in-use':
        return 'An account with this email already exists.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled.';
      case 'invalid-credential':
        return 'Invalid email or password. Please check your credentials.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with this email using a different sign-in method.';
      case 'requires-recent-login':
        return 'Please sign in again to complete this action.';
      case 'network-request-failed':
        return 'Network error. Please check your connection and try again.';
      case 'channel-error':
        return 'Connection error. Please check your internet connection.';
      case 'timeout':
        return 'Request timed out. Please try again.';
      case 'unknown':
        return 'An unexpected error occurred. Please try again.';
      default:
        return error.message ?? 'An unexpected error occurred.';
    }
  }

  // Handle other common error patterns
  String errorString = error.toString();
  if (errorString.contains('network') || errorString.contains('connection')) {
    return 'Network error. Please check your connection and try again.';
  }
  if (errorString.contains('timeout')) {
    return 'Request timed out. Please try again.';
  }
  if (errorString.contains('permission') || errorString.contains('denied')) {
    return 'Permission denied. Please check your credentials.';
  }

  return 'An unexpected error occurred. Please try again.';
}

/// Shows authentication error toast with user-friendly message
void showAuthErrorToast(BuildContext context, dynamic error) {
  final message = getAuthErrorMessage(error);
  showFailureToast(
    context,
    title: 'Authentication Error',
    description: message,
  );
}

/// Shows authentication success toast
void showAuthSuccessToast(BuildContext context, String message) {
  showSuccessToast(
    context,
    title: 'Success',
    description: message,
  );
}
