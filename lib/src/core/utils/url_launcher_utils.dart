// lib/core/utils/url_launcher_utils.dart
import 'package:flutter/material.dart';
import 'package:mimi_app/src/core/theme/constants/app_strings.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:mimi_app/src/core/utils/toasts.dart';

class UrlLauncherUtils {
  static Future<void> launchURL(
    BuildContext context,
    String url, {
    LaunchMode mode = LaunchMode.externalApplication,
  }) async {
    await launchUrlHelper(context, url);
  }

  static Future<void> launchEmail(
    BuildContext context, {
    required String email,
    String? subject,
    String? body,
  }) async {
    try {
      final Uri emailUri = Uri(
        scheme: 'mailto',
        path: email,
        queryParameters: {
          if (subject != null) 'subject': subject,
          if (body != null) 'body': body,
        },
      );

      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        if (context.mounted) {
          showFailureToast(
            context,
            title: 'Error',
            description: AppStrings.emailError,
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: AppStrings.emailError,
        );
      }
    }
  }
}

Future<void> launchUrlHelper(BuildContext context, String url) async {
  final uri = Uri.parse(url);
  try {
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (context.mounted) {
        showFailureToast(context, title: 'Error', description: 'Could not launch $url');
      }
    }
  } catch (e) {
    if (context.mounted) {
      showFailureToast(context, title: 'Error', description: 'Error launching URL: $e');
    }
  }
}
