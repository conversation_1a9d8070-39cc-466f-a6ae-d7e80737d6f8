// lib/core/router/router_provider.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/src/core/router/route_names.dart';
import 'package:mimi_app/src/core/widgets/mini_player.dart';
import 'package:mimi_app/src/features/activities/breathwork/screens/breathwork_screen.dart';
import 'package:mimi_app/src/features/activities/breathwork/screens/standalone_breathwork_settings.dart';
import 'package:mimi_app/src/features/admin/admin.dart';
import 'package:mimi_app/src/features/ai_mimi/chat_bot/chatbot_screen.dart';
import 'package:mimi_app/src/features/ai_mimi/conversational/conversation_screen.dart';
import 'package:mimi_app/src/features/audio_player/presentation/screens/audio_player_screen.dart';
import 'package:mimi_app/src/features/audio_player/presentation/screens/category_tracks_screen.dart';
import 'package:mimi_app/src/features/auth/presentation/forgot_password_screen.dart';
import 'package:mimi_app/src/features/auth/presentation/login_screen.dart';
import 'package:mimi_app/src/features/auth/presentation/signup_screen.dart';
import 'package:mimi_app/src/features/auth/presentation/splash_screen.dart';
import 'package:mimi_app/src/features/daily_quotes/quotes_screen.dart';
import 'package:mimi_app/src/features/daily_quotes/quote_settings_screen.dart';
import 'package:mimi_app/src/features/discover/discover_screen.dart';
import 'package:mimi_app/src/features/home/<USER>';
import 'package:mimi_app/src/features/intentions/about_journal_page.dart';
import 'package:mimi_app/src/features/intentions/presentation/intentions_list_screen.dart';
import 'package:mimi_app/src/features/journal/presentation/checkin_editor_screen.dart';
import 'package:mimi_app/src/features/journal/presentation/checkin_flow.dart';
import 'package:mimi_app/src/features/journal/presentation/insights_screen.dart';

import 'package:mimi_app/src/features/journal/presentation/journal_history_screen.dart';
import 'package:mimi_app/src/features/navigation/add_button.dart';
import 'package:mimi_app/src/features/navigation/bottom_nav_bar.dart';

import 'package:mimi_app/src/features/onboarding/presentation/enhanced_onboarding_flow.dart';
import 'package:mimi_app/src/features/purchases/purchase_screen.dart';
import 'package:mimi_app/src/features/purchases/presentation/subscription_debug_screen.dart';
import 'package:mimi_app/src/features/timer/pomodoro_screen.dart';
import 'package:mimi_app/src/features/user/presentation/edit_profile_screen.dart';
import 'package:mimi_app/src/features/user/presentation/email_update_screen.dart';
import 'package:mimi_app/src/features/user/presentation/password_update_screen.dart';
import 'package:mimi_app/src/features/user/presentation/profile_screen.dart';
import 'package:mimi_app/src/features/video_player/screen/video_discover_screen.dart';
import 'package:mimi_app/src/features/video_player/screen/video_list_screen.dart';
import 'package:mimi_app/src/features/video_player/screen/video_player_screen.dart';
import 'package:mimi_app/src/features/video_player/model/video_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:mimi_app/src/features/intentions/presentation/name_routine_screen.dart';
import 'package:mimi_app/src/features/intentions/presentation/set_routine_reminder_screen.dart';
import 'package:mimi_app/src/features/intentions/presentation/edit_intention_screen.dart';
import 'package:mimi_app/src/features/intentions/presentation/add_activities_to_routine_screen.dart';

import 'package:mimi_app/src/features/activities/affirmations/screen/affirmations_settings_screen.dart';
import 'package:mimi_app/src/features/activities/breathwork/screens/breathwork_settings_screen.dart';
import 'package:mimi_app/src/features/activities/meditation/screens/meditation_settings_screen.dart';
import 'package:mimi_app/src/features/activities/journaling/screens/journaling_settings_screen.dart';
import 'package:mimi_app/src/features/activities/mood_tracking/screens/moodtracking_settings_screen.dart';
import 'package:mimi_app/src/features/activities/gratitude/screens/gratitude_settings_screen.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity.dart';
import 'package:mimi_app/src/features/activities/shared/models/activity_config.dart';
import 'package:mimi_app/src/features/activities/shared/screens/activity_wizard_screen.dart';
import 'package:mimi_app/src/features/intentions/presentation/add_activities_to_routine_screen.dart'
    show ActivitySettingsNavArgs;

part 'router_provider.g.dart';

@Riverpod(keepAlive: true)
GoRouter router(Ref ref) {
  //final shellNavigatorKey = GlobalKey<NavigatorState>();

  return GoRouter(
    initialLocation: '/',
    routes: [
      // Auth & Public Routes
      GoRoute(
        path: '/',
        name: RouteNames.splash,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        name: RouteNames.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        name: RouteNames.signup,
        builder: (context, state) => const SignupScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: RouteNames.forgotPassword,
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        name: RouteNames.onboarding,
        builder: (context, state) => const EnhancedOnboardingFlow(),
      ),
      GoRoute(
        path: '/category/:id', // Dynamic path segment for category ID
        builder: (context, state) {
          final categoryId = state.pathParameters['id'] ?? '';
          return CategoryTracksScreen(categoryId: categoryId);
        },
      ),
      GoRoute(
        path: '/video',
        name: RouteNames.video,
        builder: (context, state) => const DiscoverVideoScreen(),
        routes: [
          GoRoute(
            path: 'category/:id', // child route, no leading slash
            builder: (context, state) {
              final categoryId = state.pathParameters['id'] ?? '';
              return VideoListScreen(category: categoryId);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/video-player',
        name: RouteNames.videoPlayer,
        builder: (context, state) {
          final video = state.extra as Video?;
          if (video == null) {
            return const Scaffold(
              body: Center(child: Text('Error: No video data')),
            );
          }
          return UnifiedVideoPlayerScreen(video: video);
        },
      ),
      GoRoute(
        path: '/chat',
        name: RouteNames.chat,
        builder: (context, state) => const ChatScreen(),
      ),
      GoRoute(
        path: '/conversation',
        name: RouteNames.conversation,
        builder: (context, state) => const ConversationScreen(),
      ),
      GoRoute(
        path: '/checkinflow/:id',
        name: RouteNames.checkin,
        builder: (context, state) => CheckinFlowScreen(
          routineId: int.parse(state.pathParameters['id']!),
        ),
      ),
      GoRoute(
        path: '/player',
        name: RouteNames.player,
        builder: (context, state) => const AudioPlayerScreen(),
      ),
      GoRoute(
        path: '/purchase',
        name: RouteNames.purchase,
        builder: (context, state) => const PurchaseScreen(),
      ),
      GoRoute(
        path: '/subscription-debug',
        name: RouteNames.subscriptionDebug,
        builder: (context, state) => const SubscriptionDebugScreen(),
      ),
      GoRoute(
        path: '/timer',
        name: RouteNames.timer,
        builder: (context, state) => const PomodoroScreen(),
      ),

      GoRoute(
        path: '/breathwork',
        name: RouteNames.breathwork,
        builder: (context, state) => const BreathworkScreen(),
        routes: [
          GoRoute(
            path: 'settings',
            builder: (context, state) => const StandaloneBreathworkSettings(),
          ),
        ],
      ),
      GoRoute(
        path: '/quote',
        name: RouteNames.quote,
        builder: (context, state) => const QuoteScreen(),
      ),
      GoRoute(
        path: '/quotes/settings',
        name: RouteNames.quoteSettings,
        builder: (context, state) => const QuoteSettingsScreen(),
      ),

      GoRoute(
        path: '/intentions',
        name: RouteNames.intentionsList,
        builder: (context, state) => const IntentionsListScreen(),
        routes: [
          GoRoute(
            path: '/intention/:intentionId/checkin/:routineId',
            name: RouteNames.checkinEditor,
            builder: (context, state) {
              final routineIdString = state.pathParameters['routineId'];
              final intentionIdString = state.pathParameters['intentionId'];
              if (routineIdString == null || intentionIdString == null) {
                return const Scaffold(
                    body: Center(child: Text('Error: Missing parameters')));
              }
              return CheckinEditorScreen(
                routineId: int.parse(routineIdString),
                intentionId: int.parse(intentionIdString),
              );
            },
          ),
          GoRoute(
            path: '/intention/:intentionId/checkin/new',
            name: RouteNames.checkinCreator,
            builder: (context, state) {
              final intentionIdString = state.pathParameters['intentionId'];
              if (intentionIdString == null) {
                return const Scaffold(
                    body: Center(child: Text('Error: Missing intentionId')));
              }
              return CheckinEditorScreen(
                routineId: null,
                intentionId: int.parse(intentionIdString),
              );
            },
          ),
          GoRoute(
            path: '/intentionsInfo',
            name: RouteNames.intentionsInfo,
            builder: (context, state) => const AboutJournalPage(),
          ),
          GoRoute(
            path: '/history',
            name: RouteNames.journalHistory,
            builder: (context, state) => const JournalHistoryScreen(),
          ),
          GoRoute(
            path: '/new', // Path for creating a new intention
            name: RouteNames.newIntention,
            builder: (context, state) {
              return const EditIntentionScreen(intentionId: null);
            },
          ),
          GoRoute(
            path: 'edit/:intentionId', // Path for editing an existing intention
            name: RouteNames.editIntention,
            builder: (context, state) {
              final intentionIdString = state.pathParameters['intentionId'];
              final intentionId = intentionIdString != null
                  ? int.tryParse(intentionIdString)
                  : null;
              if (intentionId == null) {
                // Handle error: intentionId is required for editing but not found or invalid
                // You could navigate to an error screen or back to the list
                return const Scaffold(
                    body: Center(
                        child: Text(
                            'Error: Missing or invalid intention ID for edit')));
              }
              return EditIntentionScreen(intentionId: intentionId);
            },
          ),
          GoRoute(
            path: ':intentionId/routine/name', // For creating a new routine
            name: RouteNames.nameNewRoutine,
            builder: (context, state) {
              final intentionIdString = state.pathParameters['intentionId']!;
              final intentionId = int.parse(intentionIdString);
              return NameRoutineScreen(
                  intentionId: intentionId, routineId: null);
            },
          ),
          GoRoute(
            path:
                ':intentionId/routine/name/:routineId', // For editing an existing routine's name
            name: RouteNames.editRoutineName,
            builder: (context, state) {
              final intentionIdString = state.pathParameters['intentionId']!;
              final routineIdString = state.pathParameters['routineId']!;
              final intentionId = int.parse(intentionIdString);
              final routineId = int.parse(routineIdString);
              return NameRoutineScreen(
                  intentionId: intentionId, routineId: routineId);
            },
          ),
          GoRoute(
            path: ':intentionId/routine/:routineId/reminder',
            name: RouteNames.setRoutineReminder,
            builder: (context, state) {
              final intentionIdString = state.pathParameters['intentionId']!;
              final routineIdString = state.pathParameters['routineId']!;
              final intentionId = int.parse(intentionIdString);
              final routineId = int.parse(routineIdString);
              return SetRoutineReminderScreen(
                  intentionId: intentionId, routineId: routineId);
            },
          ),
          GoRoute(
            path: ':intentionId/routine/:routineId/activity-wizard',
            name: RouteNames.activityWizard,
            builder: (context, state) {
              final intentionId =
                  int.parse(state.pathParameters['intentionId']!);
              final routineId = int.parse(state.pathParameters['routineId']!);
              final extra = state.extra as Map<String, dynamic>?;

              if (extra == null || !extra.containsKey('selectedActivities')) {
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text('Invalid navigation parameters')),
                );
              }

              final selectedActivities =
                  List<String>.from(extra['selectedActivities'] as List);
              final currentIndex = extra['currentIndex'] as int? ?? 0;

              // Handle activities if they exist in the extra parameters
              List<Activity>? activities;
              if (extra['activities'] != null) {
                activities = (extra['activities'] as List)
                    .map<Activity>((json) =>
                        Activity.fromJson(json as Map<String, dynamic>))
                    .toList();
              }

              return ActivityWizardScreen(
                intentionId: intentionId,
                routineId: routineId,
                selectedActivities: selectedActivities,
                currentIndex: currentIndex,
                activities: activities,
              );
            },
          ),
          GoRoute(
            path: ':intentionId/routine/:routineId/activities',
            name: RouteNames.addActivitiesToRoutine,
            builder: (context, state) {
              final intentionId =
                  int.parse(state.pathParameters['intentionId']!);
              final routineId = int.parse(state.pathParameters['routineId']!);
              return AddActivitiesToRoutineScreen(
                intentionId: intentionId,
                routineId: routineId,
              );
            },
          ),
          GoRoute(
            path:
                ':intentionId/routine/:routineId/activity/breathwork/settings',
            name: RouteNames.breathworkSettings,
            builder: (context, state) {
              final routineId = int.parse(state.pathParameters['routineId']!);
              final navArgs = state.extra as ActivitySettingsNavArgs?;

              if (navArgs == null) {
                print(
                    'Error: BreathworkSettingsScreen missing required parameters. Redirecting.');
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text(
                          'Required parameters are missing. Please go back and try again.')),
                );
              }

              // Create a temporary Activity object with the required fields
              final activity = Activity(
                id: -1, // Will be set by the database
                name: 'Breathwork',
                type: 'breathwork',
                config: const ActivityConfig.breathwork(
                  selectedPatternId: 'box',
                  cycles: 3,
                  availablePatterns: [],
                ),
              );

              return BreathworkSettingsScreen(
                activity: activity,
                routineId: routineId,
              );
            },
          ),
          GoRoute(
            path:
                ':intentionId/routine/:routineId/activity/meditation/settings',
            name: RouteNames.meditationSettings,
            builder: (context, state) {
              final routineId = int.parse(state.pathParameters['routineId']!);
              final navArgs = state.extra as ActivitySettingsNavArgs?;

              if (navArgs == null) {
                print(
                    'Error: MeditationSettingsScreen missing required parameters. Redirecting.');
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text(
                          'Required parameters are missing. Please go back and try again.')),
                );
              }

              // Create a temporary Activity object with the required fields
              final activity = Activity(
                id: -1, // Will be set by the database
                name: 'Meditation',
                type: 'meditation',
                config: const ActivityConfig.meditation(),
              );

              return MeditationSettingsScreen(
                activity: activity,
                routineId: routineId,
              );
            },
          ),
          GoRoute(
            path:
                ':intentionId/routine/:routineId/activity/journaling/settings',
            name: RouteNames.journalingSettings,
            builder: (context, state) {
              final routineId = int.parse(state.pathParameters['routineId']!);
              final navArgs = state.extra as ActivitySettingsNavArgs?;

              if (navArgs == null) {
                print(
                    'Error: JournalingSettingsScreen missing required parameters. Redirecting.');
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text(
                          'Required parameters are missing. Please go back and try again.')),
                );
              }

              // Create a temporary Activity object with the required fields
              final activity = Activity(
                id: -1, // Will be set by the database
                name: 'Journaling',
                type: 'journaling',
                config: const ActivityConfig.journaling(),
              );

              return JournalingSettingsScreen(
                activity: activity,
                routineId: routineId,
              );
            },
          ),
          GoRoute(
            path:
                ':intentionId/routine/:routineId/activity/affirmations/settings',
            name: RouteNames.affirmationsSettings,
            builder: (context, state) {
              final routineId = int.parse(state.pathParameters['routineId']!);
              final navArgs = state.extra as ActivitySettingsNavArgs?;

              if (navArgs == null) {
                print(
                    'Error: AffirmationsSettingsScreen missing required parameters. Redirecting.');
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text(
                          'Required parameters are missing. Please go back and try again.')),
                );
              }

              // Create a temporary Activity object with the required fields
              final activity = Activity(
                id: -1, // Will be set by the database
                name: 'Affirmations',
                type: 'affirmations',
                config: const ActivityConfig.affirmations(),
              );

              return AffirmationsSettingsScreen(
                activity: activity,
                routineId: routineId,
              );
            },
          ),
          GoRoute(
            path:
                ':intentionId/routine/:routineId/activity/mood_tracking/settings',
            name: RouteNames.moodTrackingSettings,
            builder: (context, state) {
              final routineId = int.parse(state.pathParameters['routineId']!);
              final navArgs = state.extra as ActivitySettingsNavArgs?;

              if (navArgs == null) {
                print(
                    'Error: MoodTrackingSettingsScreen missing required parameters. Redirecting.');
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text(
                          'Required parameters are missing. Please go back and try again.')),
                );
              }

              // Create a temporary Activity object with the required fields
              final activity = Activity(
                id: -1, // Will be set by the database
                name: 'Mood Tracking',
                type: 'mood_tracking',
                config: const ActivityConfig.moodTracking(),
              );

              return MoodTrackingSettingsScreen(
                activity: activity,
                routineId: routineId,
              );
            },
          ),
          GoRoute(
            path: ':intentionId/routine/:routineId/activity/gratitude/settings',
            name: RouteNames.gratitudeSettings,
            builder: (context, state) {
              final routineId = int.parse(state.pathParameters['routineId']!);
              final navArgs = state.extra as ActivitySettingsNavArgs?;

              if (navArgs == null) {
                print(
                    'Error: GratitudeSettingsScreen missing required parameters. Redirecting.');
                return Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text(
                          'Required parameters are missing. Please go back and try again.')),
                );
              }

              // Create a temporary Activity object with the required fields
              final activity = Activity(
                id: -1, // Will be set by the database
                name: 'Gratitude',
                type: 'gratitude',
                config: const ActivityConfig.gratitude(),
              );

              return GratitudeSettingsScreen(
                activity: activity,
                routineId: routineId,
              );
            },
          ),
        ],
      ),

      // Shell Route for Bottom Navigation
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) {
          return Scaffold(
            resizeToAvoidBottomInset: false,
            extendBody: true,
            body: LayoutBuilder(
              builder: (context, constraints) {
                // Get the safe area bottom padding to account for device-specific bottom insets

                final totalBottomSpace = 88.0;

                return Stack(
                  children: [
                    // Main content with proper padding to avoid bottom navigation
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom:
                          totalBottomSpace, // Height of bottom navigation bar + safe area
                      child: navigationShell,
                    ),
                    // Mini player positioned above the bottom navigation with extra spacing
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: totalBottomSpace +
                          8.0, // Position above the bottom navigation with 8px extra spacing
                      child: const MiniPlayer(),
                    ),
                  ],
                );
              },
            ),
            bottomNavigationBar: const BottomNavBar(),
            floatingActionButton: const AddButton(),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
          );
        },
        branches: [
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/home',
                name: RouteNames.home,
                builder: (context, state) => const HomeScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/discover',
                name: RouteNames.discover,
                builder: (context, state) => const DiscoverScreen(),
              ),
            ],
          ),
          // StatefulShellBranch(
          //   routes: [
          //     // GoRoute(
          //     //   path: '/video',
          //     //   //name: RouteNames.timer,
          //     //   builder: (context, state) => const DiscoverVideoScreen(),
          //     //   routes: [
          //     //     GoRoute(
          //     //       path: 'category/:id', // child route, no leading slash
          //     //       builder: (context, state) {
          //     //         final categoryId = state.pathParameters['id'] ?? '';
          //     //         return VideoListScreen(category: categoryId);
          //     //       },
          //     //     ),
          //     //   ],
          //     // ),
          //   ],
          // ),
          // StatefulShellBranch(
          //   routes: [
          //     GoRoute(
          //       path: '/journal',
          //       name: RouteNames.journal,
          //       builder: (context, state) => const JournalDashboardScreen(),
          //       routes: [
          //         GoRoute(
          //           path: 'checkin/:id',
          //           builder: (context, state) => CheckinEditorScreen(
          //             routineId: int.parse(state.pathParameters['id']!),
          //           ),
          //         ),
          //         GoRoute(
          //           path: 'intentionsInfo',
          //           name: RouteNames.intentionsInfo,
          //           builder: (context, state) => const AboutJournalPage(),
          //         ),
          //         GoRoute(
          //           path: 'history',
          //           name: RouteNames.journalHistory,
          //           builder: (context, state) => const JournalHistoryScreen(),
          //         ),
          //       ],
          //     ),
          //   ],
          // ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/insights',
                name: RouteNames.insights,
                builder: (context, state) => const InsightsScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/profile',
                name: RouteNames.profile,
                builder: (context, state) => const ProfileScreen(),
                routes: [
                  GoRoute(
                    path: 'edit',
                    name: RouteNames.profileEdit,
                    builder: (context, state) => const EditProfileScreen(),
                  ),
                  GoRoute(
                    path: 'email',
                    name: RouteNames.profileEmail,
                    builder: (context, state) => const ChangeEmailScreen(),
                  ),
                  GoRoute(
                    path: 'password',
                    name: RouteNames.profilePassword,
                    builder: (context, state) => const ChangePasswordScreen(),
                  ),
                  GoRoute(
                    path: '/admin',
                    builder: (context, state) => const AdminScreen(),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ],
  );
}
