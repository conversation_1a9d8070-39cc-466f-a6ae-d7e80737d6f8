// lib/core/router/route_names.dart

abstract class RouteNames {
  // Auth Routes
  static const splash = '/';
  static const login = '/login';
  static const signup = '/signup';
  static const forgotPassword = '/forgot-password';
  static const onboarding = '/onboarding';

  // Main App Routes
  static const home = '/home';
  static const discover = '/discover';
  static const audio = '/audio';
  static const video = '/video';
  static const videoPlayer = '/video-player';
  static const timer = '/timer';
  static const player = '/player';
  static const profile = '/profile';
  static const breathwork = '/breathwork';
  static const activities = '/activities';
  static const explore = '/explore';
  static const journal = '/journal';
  static const journalHistory = '/journal/history';
  static const insights = '/insights';
  static const checkin = '/checkinflow/:id';
  static const intentionsInfo = '/intentionsInfo';
  static const intentionsList = '/intentions';
  static const checkinEditor =
      '/intentions/intention/:intentionId/checkin/:routineId';
  static const checkinCreator =
      '/intentions/intention/:intentionId/checkin/new';
  static const settings = '/settings';
  static const about = '/settings';
  static const language = '/language';
  static const chat = '/chat';
  static const conversation = '/conversation';
  static const purchase = '/purchase';
  static const subscriptionDebug = '/subscription-debug';
  static const quote = '/quote';
  static const quoteSettings = '/quotes/settings';

  // New Intention/Routine Flow
  static const newIntention = '/intentions/new'; // New route for creating
  static const editIntention =
      '/intentions/edit'; // Path will include /:intentionId
  static const nameNewRoutine =
      '/intentions/routine/name/new'; // For creating a new routine
  static const editRoutineName =
      '/intentions/routine/name/edit'; // For editing an existing routine's name, path will include /:intentionId and /:routineId
  static const setRoutineReminder = '/intentions/routine/reminder';
  static const addActivitiesToRoutine = '/intentions/routine/activities';

  // Activity Settings (assuming they are part of the routine flow and need intentionId/routineId)
  static const breathworkSettings =
      '/intentions/routine/activity/breathwork/settings';
  static const meditationSettings =
      '/intentions/routine/activity/meditation/settings';
  static const journalingSettings =
      '/intentions/routine/activity/journaling/settings';
  static const affirmationsSettings =
      '/intentions/routine/activity/affirmations/settings';
  static const moodTrackingSettings =
      '/intentions/routine/activity/mood_tracking/settings';
  static const gratitudeSettings =
      '/intentions/routine/activity/gratitude/settings';
  static const activityWizard = '/activityWizard';

  // Standalone Journaling (Example, adjust if needed)
  static const standaloneJournal = '/journal/standalone';

  // Nested Routes Example
  static const profileEdit = '/edit';
  static const profileEmail = '/email';
  static const profilePassword = '/password';
  static const profileSettings = '/profile/settings';
  static const profileSupport = '/profile/support';
  static const profileFaq = '/profile/faq';
  static const profilePrivacy = '/profile/privacy';
  static const profileTerms = '/profile/terms';
  static const profileAbout = '/profile/about';

  static const List<String> publicRoutes = [
    splash,
    login,
    signup,
    forgotPassword,
  ];
}
