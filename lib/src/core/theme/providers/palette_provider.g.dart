// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'palette_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$paletteNotifierHash() => r'96009a35cff727e3742070b67814a0c07b7438d0';

/// See also [PaletteNotifier].
@ProviderFor(PaletteNotifier)
final paletteNotifierProvider =
    AutoDisposeAsyncNotifierProvider<PaletteNotifier, String>.internal(
  PaletteNotifier.new,
  name: r'paletteNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$paletteNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PaletteNotifier = AutoDisposeAsyncNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
