// lib/core/theme/providers/palette_provider.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/color_constants.dart';
import '../../../features/auth/providers/auth_state_provider.dart';

part 'palette_provider.g.dart';

@riverpod
class PaletteNotifier extends _$PaletteNotifier {
  static const _paletteKey = 'selected_palette';
  static const _defaultPalette = 'midnight_dark'; // Default to Midnight palette
  static const _authRequiredPalette =
      'midnight_dark'; // Force Midnight for auth/onboarding

  @override
  Future<String> build() async {
    // Watch auth state to determine palette behavior
    final authState = ref.watch(authStateNotifierProvider);

    return authState.when(
      initial: () => _authRequiredPalette,
      loading: () => _authRequiredPalette,
      authenticated: (_) => _getStoredOrDefaultPalette(),
      unauthenticated: () => _authRequiredPalette,
      error: (_) => _authRequiredPalette,
    );
  }

  Future<String> _getStoredOrDefaultPalette() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_paletteKey) ?? _defaultPalette;
  }

  Future<void> setPalette(String paletteId) async {
    // Only allow palette changes for authenticated users
    final authState = ref.read(authStateNotifierProvider);
    final isAuthenticated = authState.maybeWhen(
      authenticated: (_) => true,
      orElse: () => false,
    );

    if (!isAuthenticated) {
      // Force Midnight palette for unauthenticated users
      AppColors.setPalette(_authRequiredPalette);
      state = AsyncValue.data(_authRequiredPalette);
      return;
    }

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_paletteKey, paletteId);

      // Update AppColors immediately
      AppColors.setPalette(paletteId);

      return paletteId;
    });
  }

  String getCurrentPaletteId() {
    return state.valueOrNull ?? _defaultPalette;
  }

  /// Reset palette preferences (called on logout)
  Future<void> resetPalettePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_paletteKey);

    // Force Midnight palette
    AppColors.setPalette(_authRequiredPalette);
    state = AsyncValue.data(_authRequiredPalette);
  }

  /// Force Midnight palette for auth/onboarding flows
  void forceAuthPalette() {
    AppColors.setPalette(_authRequiredPalette);
    state = AsyncValue.data(_authRequiredPalette);
  }
}
