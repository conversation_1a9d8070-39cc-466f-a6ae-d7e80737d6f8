// lib/core/theme/palettes/purple_dark_palette.dart

import 'package:flutter/material.dart';
import 'base_palette.dart';

/// Dark purple palette - original app colors with mystical dark theme
class PurpleDarkPalette extends BasePalette {
  @override
  String get id => 'purple_dark';
  
  @override
  String get name => 'Mystical Purple';
  
  @override
  String get description => 'Deep purple tones for a mystical, immersive experience';
  
  @override
  Color get primary => const Color.fromARGB(255, 250, 163, 57); // Original orange accent
  
  @override
  Color get secondary => const Color.fromARGB(255, 144, 99, 249); // Original purple
  
  @override
  Color get background => const Color(0xFF492496); // Original dark purple background
  
  @override
  Color get surface => const Color.fromARGB(255, 125, 86, 210); // Original surface purple
  
  @override
  Color get gradientStart => const Color(0xFF492496);
  
  @override
  Color get gradientEnd => const Color(0xFF653FBE);
  
  @override
  Color get textPrimary => const Color(0xFFE6E1E5); // Light text for dark background
  
  @override
  Color get textSecondary => const Color(0xFFB9B6BC); // Medium light text
  
  @override
  Color get textOnPrimary => const Color(0xFF1C1B1F); // Dark text on orange
  
  @override
  Color get border => const Color(0xFF938F96);
  
  @override
  Color get accent1 => const Color(0xFF60A5FA); // Blue
  
  @override
  Color get accent2 => const Color(0xFF4ADE80); // Green
  
  @override
  Color get accent3 => const Color(0xFFFB7185); // Pink
  
  @override
  Color get accent4 => const Color(0xFFFFB800); // Yellow
  
  @override
  Color get success => const Color(0xFF4ADE80);
  
  @override
  Color get error => const Color(0xFFF44336);
  
  @override
  Color get warning => const Color(0xFFFFB74D);
  
  @override
  Color get info => const Color(0xFF60A5FA);
}
