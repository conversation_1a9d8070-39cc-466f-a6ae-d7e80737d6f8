// lib/core/theme/palettes/base_palette.dart

import 'package:flutter/material.dart';

/// Base class for all color palettes
abstract class BasePalette {
  /// Unique identifier for the palette
  String get id;
  
  /// Display name for the palette
  String get name;
  
  /// Description of the palette
  String get description;
  
  // Primary Colors
  Color get primary;
  Color get secondary;
  
  // Background & Surface
  Color get background;
  Color get surface;
  
  // Gradients
  Color get gradientStart;
  Color get gradientEnd;
  
  // Text Colors
  Color get textPrimary;
  Color get textSecondary;
  Color get textOnPrimary;
  
  // Utility
  Color get border;
  
  // Accent Colors
  Color get accent1; // Blue
  Color get accent2; // Green
  Color get accent3; // Pink
  Color get accent4; // Yellow
  
  // Feedback Colors
  Color get success;
  Color get error;
  Color get warning;
  Color get info;
}
