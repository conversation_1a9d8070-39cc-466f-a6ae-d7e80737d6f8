// lib/core/theme/palettes/green_dark_palette.dart

import 'package:flutter/material.dart';
import 'base_palette.dart';

/// Dark green palette - deep forest vibes
class GreenDarkPalette extends BasePalette {
  @override
  String get id => 'green_dark';
  
  @override
  String get name => 'Forest Depths';
  
  @override
  String get description => 'Deep green tones for a rich, forest-like experience';
  
  @override
  Color get primary => const Color(0xFF4ADE80); // Bright green accent
  
  @override
  Color get secondary => const Color(0xFF22C55E); // Medium green
  
  @override
  Color get background => const Color(0xFF064E3B); // Dark forest green background
  
  @override
  Color get surface => const Color(0xFF059669); // Medium dark green surface
  
  @override
  Color get gradientStart => const Color(0xFF064E3B);
  
  @override
  Color get gradientEnd => const Color(0xFF047857);
  
  @override
  Color get textPrimary => const Color(0xFFD1FAE5); // Light green text
  
  @override
  Color get textSecondary => const Color(0xFFA7F3D0); // Medium light green text
  
  @override
  Color get textOnPrimary => const Color(0xFF14532D); // Dark green text on bright green
  
  @override
  Color get border => const Color(0xFF6EE7B7);
  
  @override
  Color get accent1 => const Color(0xFF3B82F6); // Blue
  
  @override
  Color get accent2 => const Color(0xFF10B981); // Green
  
  @override
  Color get accent3 => const Color(0xFFEC4899); // Pink
  
  @override
  Color get accent4 => const Color(0xFFF59E0B); // Yellow
  
  @override
  Color get success => const Color(0xFF10B981);
  
  @override
  Color get error => const Color(0xFFEF4444);
  
  @override
  Color get warning => const Color(0xFFF59E0B);
  
  @override
  Color get info => const Color(0xFF3B82F6);
}
