// lib/core/theme/palettes/green_light_palette.dart

import 'package:flutter/material.dart';
import 'base_palette.dart';

/// Light green palette - fresh and natural
class GreenLightPalette extends BasePalette {
  @override
  String get id => 'green_light';
  
  @override
  String get name => 'Nature\'s Embrace';
  
  @override
  String get description => 'Fresh green tones inspired by nature\'s tranquility';
  
  @override
  Color get primary => const Color(0xFF059669); // Green primary
  
  @override
  Color get secondary => const Color(0xFF10B981); // Lighter green
  
  @override
  Color get background => const Color(0xFFF0FDF4); // Very light green background
  
  @override
  Color get surface => const Color(0xFFFFFFFF); // White surface
  
  @override
  Color get gradientStart => const Color(0xFFF0FDF4);
  
  @override
  Color get gradientEnd => const Color(0xFFDCFCE7);
  
  @override
  Color get textPrimary => const Color(0xFF1F2937); // Dark gray
  
  @override
  Color get textSecondary => const Color(0xFF6B7280); // Medium gray
  
  @override
  Color get textOnPrimary => const Color(0xFFFFFFFF); // White text on green
  
  @override
  Color get border => const Color(0xFFE5E7EB);
  
  @override
  Color get accent1 => const Color(0xFF3B82F6); // Blue
  
  @override
  Color get accent2 => const Color(0xFF10B981); // Green
  
  @override
  Color get accent3 => const Color(0xFFEC4899); // Pink
  
  @override
  Color get accent4 => const Color(0xFFF59E0B); // Yellow
  
  @override
  Color get success => const Color(0xFF10B981);
  
  @override
  Color get error => const Color(0xFFEF4444);
  
  @override
  Color get warning => const Color(0xFFF59E0B);
  
  @override
  Color get info => const Color(0xFF3B82F6);
}
