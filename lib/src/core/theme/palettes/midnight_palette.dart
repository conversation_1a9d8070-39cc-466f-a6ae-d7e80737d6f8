// lib/core/theme/palettes/meditopia_dark_palette.dart
import 'package:flutter/material.dart';
import 'base_palette.dart';

/// Dark palette inspired by Meditopia's signature design
class MidnightPalette extends BasePalette {
  @override
  String get id => 'midnight_dark';

  @override
  String get name => 'Midnight Signature';

  @override
  String get description =>
      'Deep twilight gradients for ultimate calm and focus';

  @override
  Color get primary => const Color(0xFF8B5CF6); // Vibrant purple

  @override
  Color get secondary => const Color(0xFF6366F1); // Indigo

  @override
  Color get background => const Color(0xFF1A1625); // Deep dark purple

  @override
  Color get surface => const Color(0xFF2D2438); // Dark purple surface

  @override
  Color get gradientStart => const Color(0xFF1A1625);

  @override
  Color get gradientEnd => const Color(0xFF2C2444); // Deep blue

  @override
  Color get textPrimary => const Color(0xFFFFFFFF); // Pure white

  @override
  Color get textSecondary => const Color(0xFFB8B5C3); // Light purple-gray

  @override
  Color get textOnPrimary => const Color(0xFFFFFFFF); // White on purple

  @override
  Color get border => const Color(0xFF3F3651);

  @override
  Color get accent1 => const Color(0xFF06B6D4); // Cyan

  @override
  Color get accent2 => const Color(0xFF8B5CF6); // Purple

  @override
  Color get accent3 => const Color(0xFFEC4899); // Pink

  @override
  Color get accent4 => const Color(0xFFF59E0B); // Amber

  @override
  Color get success => const Color(0xFF10B981);

  @override
  Color get error => const Color(0xFFEF4444);

  @override
  Color get warning => const Color(0xFFF59E0B);

  @override
  Color get info => const Color(0xFF06B6D4);
}
