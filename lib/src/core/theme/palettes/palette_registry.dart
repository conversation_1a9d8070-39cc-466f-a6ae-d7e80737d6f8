// lib/core/theme/palettes/palette_registry.dart

import 'package:mimi_app/src/core/theme/palettes/midnight_palette.dart';
import 'package:mimi_app/src/core/theme/palettes/pastel_serenity.dart';
import 'package:mimi_app/src/core/theme/palettes/zen_harmony.dart';

import 'base_palette.dart';
import 'purple_light_palette.dart';
import 'purple_dark_palette.dart';
import 'green_light_palette.dart';
import 'green_dark_palette.dart';

/// Registry for managing all available color palettes
class PaletteRegistry {
  static final Map<String, BasePalette> _palettes = {
    'purple_light': PurpleLightPalette(),
    'purple_dark': PurpleDarkPalette(),
    'green_light': GreenLightPalette(),
    'green_dark': GreenDarkPalette(),
    'midnight_dark': MidnightPalette(),
    'zen_harmony': ZenHarmonyPalette(),
    'pastel_serenity': PastelSerenityPalette(),
  };

  /// Get all available palettes
  static List<BasePalette> get availablePalettes => _palettes.values.toList();

  /// Get a palette by ID, returns default if not found
  static BasePalette getPalette(String id) {
    return _palettes[id] ?? MidnightPalette(); // Default to Midnight palette
  }

  /// Register a new palette
  static void registerPalette(BasePalette palette) {
    _palettes[palette.id] = palette;
  }

  /// Check if a palette exists
  static bool hasPalette(String id) {
    return _palettes.containsKey(id);
  }

  /// Get palette IDs
  static List<String> get paletteIds => _palettes.keys.toList();

  /// Get default palette
  static BasePalette get defaultPalette => MidnightPalette();
}
