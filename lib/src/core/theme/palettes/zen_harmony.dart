// lib/core/theme/palettes/zen_harmony_palette.dart
import 'package:flutter/material.dart';
import 'base_palette.dart';

/// Zen-inspired palette with warm earth tones and calming blues
class ZenHarmonyPalette extends BasePalette {
  @override
  String get id => 'zen_harmony';

  @override
  String get name => 'Zen Harmony';

  @override
  String get description =>
      'Balanced earth and sky tones for mindful meditation';

  @override
  Color get primary => const Color(0xFF0891B2); // Calm teal

  @override
  Color get secondary => const Color(0xFF0EA5E9); // Sky blue

  @override
  Color get background => const Color(0xFFFEFEFE); // Pure white

  @override
  Color get surface => const Color(0xFFF8FAFC); // Slight blue-white

  @override
  Color get gradientStart => const Color(0xFFF0F9FF);

  @override
  Color get gradientEnd => const Color(0xFFE0F2FE); // Light sky blue

  @override
  Color get textPrimary => const Color(0xFF1E293B); // Deep slate

  @override
  Color get textSecondary => const Color(0xFF475569); // Medium slate

  @override
  Color get textOnPrimary => const Color(0xFFFFFFFF); // White on teal

  @override
  Color get border => const Color(0xFFE2E8F0);

  @override
  Color get accent1 => const Color(0xFF0891B2); // Teal

  @override
  Color get accent2 => const Color(0xFF65A30D); // Warm green

  @override
  Color get accent3 => const Color(0xFFDC2626); // Warm red

  @override
  Color get accent4 => const Color(0xFFD97706); // Warm amber

  @override
  Color get success => const Color(0xFF16A34A);

  @override
  Color get error => const Color(0xFFDC2626);

  @override
  Color get warning => const Color(0xFFCA8A04);

  @override
  Color get info => const Color(0xFF0284C7);
}
