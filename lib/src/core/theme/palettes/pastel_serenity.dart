import 'package:flutter/material.dart';
import 'base_palette.dart';

/// Soft pastel palette for gentle meditation experiences
class PastelSerenityPalette extends BasePalette {
  @override
  String get id => 'pastel_serenity';

  @override
  String get name => 'Pastel Serenity';

  @override
  String get description => 'Gentle pastel tones for peaceful meditation';

  @override
  Color get primary => const Color(0xFFB4A7D6); // Soft lavender

  @override
  Color get secondary => const Color(0xFFD4B4DE); // Pale purple

  @override
  Color get background =>
      const Color(0xFFFCFAFF); // Almost white with hint of purple

  @override
  Color get surface => const Color(0xFFF8F5FF); // Very light lavender

  @override
  Color get gradientStart => const Color(0xFFFCFAFF);

  @override
  Color get gradientEnd => const Color(0xFFF0E6FF); // Light pastel purple

  @override
  Color get textPrimary => const Color(0xFF4A4458); // Soft dark purple

  @override
  Color get textSecondary => const Color(0xFF7C7086); // Medium purple-gray

  @override
  Color get textOnPrimary => const Color(0xFFFFFFFF); // White on pastel

  @override
  Color get border => const Color(0xFFE8E1F5);

  @override
  Color get accent1 => const Color(0xFFB8E6FF); // Pastel blue

  @override
  Color get accent2 => const Color(0xFFB8F5D1); // Pastel green

  @override
  Color get accent3 => const Color(0xFFFFB8D6); // Pastel pink

  @override
  Color get accent4 => const Color(0xFFFFF2B8); // Pastel yellow

  @override
  Color get success => const Color(0xFF9AE6B4);

  @override
  Color get error => const Color(0xFFFC8181);

  @override
  Color get warning => const Color(0xFFFBD38D);

  @override
  Color get info => const Color(0xFF90CDF4);
}
