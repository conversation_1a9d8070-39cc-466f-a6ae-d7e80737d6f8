// lib/core/theme/palettes/purple_light_palette.dart

import 'package:flutter/material.dart';
import 'base_palette.dart';

/// Light purple palette - soft and dreamy
class PurpleLightPalette extends BasePalette {
  @override
  String get id => 'purple_light';
  
  @override
  String get name => 'Purple Dreams';
  
  @override
  String get description => 'Soft purple tones for a dreamy, light experience';
  
  @override
  Color get primary => const Color(0xFF6D28D9); // Purple primary
  
  @override
  Color get secondary => const Color(0xFF8B5CF6); // Lighter purple
  
  @override
  Color get background => const Color(0xFFFAF5FF); // Very light purple background
  
  @override
  Color get surface => const Color(0xFFFFFFFF); // White surface
  
  @override
  Color get gradientStart => const Color(0xFFFAF5FF);
  
  @override
  Color get gradientEnd => const Color(0xFFEDE9FE);
  
  @override
  Color get textPrimary => const Color(0xFF1F2937); // Dark gray
  
  @override
  Color get textSecondary => const Color(0xFF6B7280); // Medium gray
  
  @override
  Color get textOnPrimary => const Color(0xFFFFFFFF); // White text on purple
  
  @override
  Color get border => const Color(0xFFE5E7EB);
  
  @override
  Color get accent1 => const Color(0xFF60A5FA); // Blue
  
  @override
  Color get accent2 => const Color(0xFF4ADE80); // Green
  
  @override
  Color get accent3 => const Color(0xFFFB7185); // Pink
  
  @override
  Color get accent4 => const Color(0xFFFFB800); // Yellow
  
  @override
  Color get success => const Color(0xFF4ADE80);
  
  @override
  Color get error => const Color(0xFFF44336);
  
  @override
  Color get warning => const Color(0xFFFFB74D);
  
  @override
  Color get info => const Color(0xFF60A5FA);
}
