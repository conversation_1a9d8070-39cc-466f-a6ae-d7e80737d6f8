// lib/core/theme/constants/color_constants.dart

import 'package:flutter/material.dart';
import '../palettes/base_palette.dart';
import '../palettes/palette_registry.dart';

class AppColors {
  static String _currentPaletteId = 'midnight_dark';

  /// Set the current color palette by ID
  static void setPalette(String paletteId) {
    if (PaletteRegistry.hasPalette(paletteId)) {
      _currentPaletteId = paletteId;
    }
  }

  /// Get the current palette ID
  static String get currentPaletteId => _currentPaletteId;

  /// Get the current palette object
  static BasePalette get currentPalette =>
      PaletteRegistry.getPalette(_currentPaletteId);

  /// Get all available palettes
  static List<BasePalette> get availablePalettes =>
      PaletteRegistry.availablePalettes;

  // Primary Colors
  static Color get primary => currentPalette.primary;
  static Color get secondary => currentPalette.secondary;

  // Background & Surface
  static Color get background => currentPalette.background;
  static Color get surface => currentPalette.surface;

  // Gradients
  static Color get gradientStart => currentPalette.gradientStart;
  static Color get gradientEnd => currentPalette.gradientEnd;

  // Text Colors
  static Color get textPrimary => currentPalette.textPrimary;
  static Color get textSecondary => currentPalette.textSecondary;
  static Color get textOnPrimary => currentPalette.textOnPrimary;

  // Utility
  static Color get border => currentPalette.border;

  // Accent Colors
  static Color get accent1 => currentPalette.accent1;
  static Color get accent2 => currentPalette.accent2;
  static Color get accent3 => currentPalette.accent3;
  static Color get accent4 => currentPalette.accent4;

  // Feedback Colors
  static Color get success => currentPalette.success;
  static Color get error => currentPalette.error;
  static Color get warning => currentPalette.warning;
  static Color get info => currentPalette.info;

  // Backward compatibility getters (deprecated - use new names)
  @deprecated
  static Color get primaryLight => primary;

  @deprecated
  static Color get backgroundLight => background;

  @deprecated
  static Color get backgroundDark => background;

  @deprecated
  static Color get surfaceLight => surface;

  @deprecated
  static Color get surfaceDark => surface;

  @deprecated
  static Color get backgroundGradientDarkStart => gradientStart;

  @deprecated
  static Color get backgroundGradientDarkEnd => gradientEnd;

  @deprecated
  static Color get greyLight100 => textPrimary;

  @deprecated
  static Color get greyLight200 => textSecondary;

  @deprecated
  static Color get greyLight300 => border;

  @deprecated
  static Color get greyLight400 => border;

  @deprecated
  static Color get greyDark100 => textPrimary;

  @deprecated
  static Color get greyDark200 => textSecondary;

  @deprecated
  static Color get greyDark300 => border;

  @deprecated
  static Color get greyDark400 => border;
}

/// Extension to get color schemes
extension ColorSchemeExtension on ColorScheme {
  Color get customBackground => AppColors.background;

  Color get customSurface => AppColors.surface;

  Color get customPrimary => AppColors.primary;

  Color get customGrey100 => AppColors.textPrimary;

  Color get customGrey200 => AppColors.textSecondary;

  Color get customGrey300 => AppColors.border;

  Color get customGrey400 => AppColors.border;
}
