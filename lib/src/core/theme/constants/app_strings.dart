// lib/core/constants/app_strings.dart

/// Application-wide string constants
abstract class AppStrings {
// App Info
  static const String appName = 'Your App Name';
  static const String appVersion = '1.0.0';
  static const String hello = 'Hello';
  static const String greeting =
      'Let’s make today intentional and full of purpose.';

// Add to lib/core/constants/app_strings.dart
  static const String downloadComplete = 'Download Complete';
  static const String downloading = 'Downloading';
  static const String download = 'Download';

// Add to lib/core/constants/app_strings.dart
  static const String currentlyPlaying = 'Currently Playing';
  static const String playlist = 'Playlist';
  static const String noTrackSelected = 'No Track Selected';

// Add to lib/core/constants/app_strings.dart
  static const String categoryError = 'Unable to load category';
  static const String tracksLoadError = 'Unable to load tracks';
  static const String noTracksAvailable =
      'No tracks available in this category';
  static const String playSelectedTracks = 'Play selected tracks';

// Bottom Navigation
  static const String home = 'Home';
  static const String profile = 'Profile';
  static const String settings = 'Settings';
  static const String explore = 'Explore';
  static const String journal = 'Journal';
  static const String insights = 'Insights';
  static const String intentions = 'Intentions';

// Authentication
  static const String login = 'Login';
  static const String signup = 'Sign Up';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String forgotPassword = 'Forgot Password?';
  static const String resetPassword = 'Reset Password';
  static const String loginSuccess = 'Login Successful';
  static const String signupSuccess = 'Sign Up Successful';
  static const String logout = 'Logout';
  static const String continueWithGoogle = 'Continue with Google';
  static const String continueWithApple = 'Continue with Apple';
  static const String orContinueWith = 'Or continue with';
  static const String dontHaveAccount = "Don't have an account? ";
  static const String alreadyHaveAccount = 'Already have an account? ';
  static const String passwordRequirements = 'Password must Be ';

// Form Validations

  static const String firstName = 'First Name';
  static const String firstNameRequired = 'First Name is required';
  static const String lastName = 'Last Name';
  static const String lastNameRequired = 'Last Name is required';
  static const String emailRequired = 'Email is required';

  static const String invalidEmail = 'Please enter a valid email';
  static const String passwordRequired = 'Password is required';
  static const String passwordTooShort =
      'Password must be at least 6 characters';
  static const String passwordNotStrong = 'Passwords Not Strong';
  static const String passwordsDontMatch = 'Passwords do not match';
  static const String emailsDontMatch = 'Emails do not match';

// Profile
  static const String editProfile = 'Update Name';
  static const String updateEmail = 'Change Email';
  static const String updateProfile = 'Update Profile';
  static const String changePassword = 'Change Password';
  static const String profileSettings = 'Profile Settings';
  static const String personalInfo = 'Personal Information';
  static const String accountSettings = 'Account Settings';

// Settings
  static const String appearance = 'Appearance';
  static const String darkMode = 'Dark Mode';
  static const String lightMode = 'Light Mode';
  static const String systemDefault = 'System Default';
  static const String notifications = 'Notifications';
  static const String language = 'Language';
  static const String about = 'About';
  static const String privacyPolicy = 'Privacy Policy';
  static const String termsOfService = 'Terms of Service';
  static const String eula = 'EULA';
  static const String version = 'Version';
  static const String changeEmail = 'Change Email';
  static const String changeEmailDescription = 'Change Email Description';
  static const String newEmail = 'New Email';
  static const String confirmEmail = 'Confirm Email';
  static const String currentPassword = 'Current Password';
  static const String newPassword = 'New Password';

// General Actions
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String update = 'Update';
  static const String confirm = 'Confirm';
  static const String back = 'Back';
  static const String next = 'Next';
  static const String done = 'Done';
  static const String skip = 'Skip';

// Error Messages
  static const String errorOccurred = 'An error occurred';
  static const String noInternet = 'No internet connection';
  static const String tryAgain = 'Please try again';
  static const String sessionExpired = 'Session expired';
  static const String unauthorized = 'Unauthorized access';
  static const String somethingWentWrong = 'Something went wrong';

// Success Messages
  static const String changesSaved = 'Changes saved successfully';
  static const String profileUpdated = 'Profile updated successfully';
  static const String passwordChanged = 'Password changed successfully';
  static const String passwordChangeSuccess = 'Password changed successfully';
  static const String emailChangeSuccess = 'Email changed successfully';
// Empty States
  static const String noData = 'No data available';
  static const String noResults = 'No results found';
  static const String emptyList = 'List is empty';

// Loading States
  static const String loading = 'Loading...';
  static const String saving = 'Saving...';
  static const String pleaseWait = 'Please wait...';
  static const String processing = 'Processing...';

// Confirmation Dialogs
  static const String areYouSure = 'Are you sure?';

  static const String deleteAccount = 'Delete Account';
  static const String accountDeleted = 'Do you want to delete this item?';
  static const String deleteConfirmation = 'Do you want to delete this item?';
  static const String logoutConfirmation = 'Do you want to logout?';
  static const String yes = 'Yes';
  static const String no = 'No';

// Profile & Settings
  static const String socialMedia = 'Social Media';
  static const String followUs = 'Follow Us';
  static const String shareApp = 'Share App';
  // static const String rateApp = 'Rate App';
  static const String shareAppSubject = 'Check out this amazing app!';
  static const String shareAppMessage =
      'I\'m using this great app. Check it out!';
  static const String support = 'Support';
  static const String customerSupport = 'Customer Support';
  static const String contactUs = 'Contact Us';
  static const String supportEmailSubject = 'Support Request';
  static const String supportEmailBody = 'Please describe your issue:';

// Social Media
  static const String facebook = 'Facebook';
  static const String youtube = 'YouTube';
  static const String twitter = 'Twitter';
  static const String instagram = 'Instagram';

// Confirmation Dialogs
  static const String deleteAccountConfirmation =
      'Are you sure you want to delete your account? This action cannot be undone.';
  // static const String logoutConfirmation = 'Are you sure you want to logout?';
  // static const String confirm = 'Confirm';
  // static const String cancel = 'Cancel';

  // Error Messages
  static const String socialMediaError = 'Could not open social media link';
  static const String shareError = 'Could not share app';
  static const String emailError = 'Could not open email client';
  static const String urlError = 'Could not open URL';

  // Loading States
  static const String processingRequest = 'Processing...';
  static const String deletingAccount = 'Deleting account...';
  static const String signingOut = 'Signing out...';
  static const String loadingProfile = 'Loading profile...';

  static const String preferences = 'Preferences';

  static const String chat = 'Chat';

  static const String onboardingTitle1 =
      'Welcome to Self-Mastery by Mimi Bland';
  static const String onboardingDescription1 =
      'Begin your transformative journey with expert guidance, proven strategies, and personalised tools for lasting change.';

  static const String onboardingTitle2 = 'Set Intentions Create Checkins';
  static const String onboardingDescription2 =
      'Define your goals, track your progress, and stay accountable with structured check-ins that keep you moving forward.';

  static const String onboardingTitle3 =
      'Talk or Chat with MimiAI in a judgement free space.';
  static const String onboardingDescription3 =
      'Get instant support and personalised insights through AI-powered conversations designed to understand and guide you.';

  static const String onboardingTitle4 =
      'Find Daily Wisdom to boost your motivation with Quotes and Videos';
  static const String onboardingDescription4 =
      'Start each day inspired with curated motivational content, uplifting quotes, and transformative video messages.';

  static const String onboardingTitle5 =
      'Your App Your colours, customized completely to your preferences';
  static const String onboardingDescription5 =
      'Personalise your experience with custom themes, colours, and visual elements that reflect your unique style and energy.';
}
