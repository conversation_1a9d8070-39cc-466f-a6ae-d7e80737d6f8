// lib/core/theme/widgets/palette_selector.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/color_constants.dart';
import '../providers/palette_provider.dart';
import '../palettes/base_palette.dart';

class PaletteSelector extends ConsumerWidget {
  const PaletteSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPaletteIdAsync = ref.watch(paletteNotifierProvider);

    return currentPaletteIdAsync.when(
      data: (currentPaletteId) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Color Palette',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          ...AppColors.availablePalettes.map(
            (palette) => PaletteTile(
              palette: palette,
              isSelected: palette.id == currentPaletteId,
              onTap: () => ref
                  .read(paletteNotifierProvider.notifier)
                  .setPalette(palette.id),
            ),
          ),
        ],
      ),
      loading: () => const CircularProgressIndicator(),
      error: (_, __) => const Text('Error loading palette'),
    );
  }
}

class PaletteTile extends StatelessWidget {
  final BasePalette palette;
  final bool isSelected;
  final VoidCallback onTap;

  const PaletteTile({
    super.key,
    required this.palette,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Get preview colors directly from the palette
    final previewColors = [
      palette.primary,
      palette.secondary,
      palette.background,
      palette.surface,
    ];

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: previewColors
              .map(
                (color) => Container(
                  width: 16,
                  height: 16,
                  margin: const EdgeInsets.only(right: 4),
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
              )
              .toList(),
        ),
        title: Text(palette.name),
        subtitle: Text(palette.description),
        trailing: isSelected
            ? const Icon(Icons.check_circle, color: Colors.green)
            : const Icon(Icons.radio_button_unchecked),
        onTap: onTap,
      ),
    );
  }
}
