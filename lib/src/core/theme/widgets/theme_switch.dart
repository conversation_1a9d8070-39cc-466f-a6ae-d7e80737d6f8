// lib/core/theme/widgets/theme_toggle_button.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mimi_app/src/core/theme/providers/theme_provider.dart';

class ThemeToggleButton extends ConsumerWidget {
  const ThemeToggleButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeState = ref.watch(themeNotifierProvider);

    return themeState.when(
      data: (themeMode) => Switch.adaptive(
        value: themeMode == ThemeMode.dark,
        onChanged: (_) =>
            ref.read(themeNotifierProvider.notifier).toggleTheme(),
      ),
      loading: () => const SizedBox.square(
        dimension: 20,
        child: CircularProgressIndicator(),
      ),
      error: (_, __) => const Icon(Icons.error),
    );
  }
}
