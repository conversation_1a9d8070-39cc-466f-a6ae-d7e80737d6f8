// lib/core/theme/config/theme_config.dart

import 'package:flutter/material.dart';
import '../constants/color_constants.dart';
import '../constants/typography_constants.dart';
import '../constants/sizing_constants.dart';

class ThemeConfig {
  // Remove static getter to prevent caching
  static ThemeData createLightTheme() => _getTheme();

  static ThemeData _getTheme() {
    final colorScheme = _getColorScheme();

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: AppColors.background,
      textTheme: _getTextTheme(),
      cardTheme: _cardTheme(),
      appBarTheme: _appBarTheme(),
      elevatedButtonTheme: _elevatedButtonTheme(colorScheme),
      outlinedButtonTheme: _outlinedButtonTheme(colorScheme),
      inputDecorationTheme: _inputDecorationTheme(),
      dialogTheme: _dialogTheme(),
      bottomSheetTheme: _bottomSheetTheme(),
      snackBarTheme: _snackBarTheme(),
    );
  }

  static ColorScheme _getColorScheme() {
    return ColorScheme(
      brightness: Brightness.light,
      primary: AppColors.primary,
      onPrimary: AppColors.textOnPrimary,
      secondary: AppColors.accent1,
      onSecondary: AppColors.textOnPrimary,
      error: AppColors.error,
      onError: AppColors.textOnPrimary,
      surface: AppColors.surface,
      onSurface: AppColors.textPrimary,
    );
  }

  static TextTheme _getTextTheme() {
    final Color textColor = AppColors.textPrimary;

    return TextTheme(
      displayLarge: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.displayLarge,
        fontWeight: AppTypography.regular,
      ),
      displayMedium: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.displayMedium,
        fontWeight: AppTypography.regular,
      ),
      titleLarge: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.titleLarge,
        fontWeight: AppTypography.medium,
      ),
      titleMedium: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.titleMedium,
        fontWeight: AppTypography.medium,
      ),
      titleSmall: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.titleSmall,
        fontWeight: AppTypography.medium,
      ),
      headlineLarge: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.headlineLarge,
        fontWeight: AppTypography.medium,
      ),
      headlineMedium: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.headlineMedium,
        fontWeight: AppTypography.bold,
      ),
      headlineSmall: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.headlineSmall,
        fontWeight: AppTypography.medium,
      ),
      bodyLarge: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.bodyLarge,
        fontWeight: AppTypography.regular,
      ),
      bodyMedium: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.bodyMedium,
        fontWeight: AppTypography.regular,
      ),
      bodySmall: TextStyle(
        color: textColor,
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.bodySmall,
        fontWeight: AppTypography.regular,
      ),
    );
  }

  static CardTheme _cardTheme() {
    return CardTheme(
      color: AppColors.surface,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusL),
      ),
      margin: EdgeInsets.all(AppSizing.spaceS),
    );
  }

  static AppBarTheme _appBarTheme() {
    return AppBarTheme(
      elevation: 0,
      centerTitle: true,
      backgroundColor: AppColors.background,
      foregroundColor: AppColors.textPrimary,
      iconTheme: IconThemeData(
        color: AppColors.textPrimary,
        size: AppSizing.iconM,
      ),
    );
  }

  static ElevatedButtonThemeData _elevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        padding: EdgeInsets.symmetric(
          vertical: AppSizing.spaceM,
        ),
        minimumSize: Size(double.infinity, 50),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizing.radiusM),
        ),
      ),
    );
  }

  static OutlinedButtonThemeData _outlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        side: BorderSide(color: colorScheme.primary),
        padding: EdgeInsets.symmetric(
          vertical: AppSizing.spaceM,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizing.radiusM),
        ),
      ),
    );
  }

  static InputDecorationTheme _inputDecorationTheme() {
    final borderColor = AppColors.border;

    return InputDecorationTheme(
      filled: true,
      fillColor: AppColors.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        borderSide: BorderSide(color: borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        borderSide: BorderSide(color: borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2,
        ),
      ),
    );
  }

  static DialogTheme _dialogTheme() {
    return DialogTheme(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusL),
      ),
    );
  }

  static BottomSheetThemeData _bottomSheetTheme() {
    return BottomSheetThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
    );
  }

  static SnackBarThemeData _snackBarTheme() {
    return SnackBarThemeData(
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
      ),
    );
  }
}
