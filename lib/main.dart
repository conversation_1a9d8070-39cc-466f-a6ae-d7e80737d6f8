import 'package:audioplayers/audioplayers.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mimi_app/firebase_options.dart';
import 'package:mimi_app/src/core/router/router_provider.dart';
import 'package:mimi_app/src/core/theme/config/theme_config.dart';
import 'package:mimi_app/src/core/theme/constants/color_constants.dart';
import 'package:mimi_app/src/core/theme/providers/palette_provider.dart';
import 'package:mimi_app/src/features/journal/providers/providers.dart';
import 'package:mimi_app/src/features/purchases/providers/revenuecat_provider.dart';
import 'package:mimi_app/src/features/timer/sound_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Force portrait orientation for the main app
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await FirebaseAppCheck.instance.activate(
    // androidProvider: AndroidProvider.playIntegrity,
    appleProvider: AppleProvider.deviceCheck,
  );
  AudioCache.instance = AudioCache(prefix: '');
  final sharedPreferences = await SharedPreferences.getInstance();
  await SentryFlutter.init(
    (options) {
      options.dsn =
          'https://<EMAIL>/4509595204190288';
      // Adds request headers and IP for users, for more info visit:
      // https://docs.sentry.io/platforms/dart/guides/flutter/data-management/data-collected/
      options.sendDefaultPii = true;
      // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
      // We recommend adjusting this value in production.
      options.tracesSampleRate = 1.0;
      // The sampling rate for profiling is relative to tracesSampleRate
      // Setting to 1.0 will profile 100% of sampled transactions:
      options.profilesSampleRate = 1.0;
    },
    appRunner: () => runApp(SentryWidget(
      child: ProviderScope(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(sharedPreferences),
        ],
        child: const MainApp(),
      ),
    )),
  );
}

class MainApp extends ConsumerWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Initialize RevenueCat first
    final revenueCatAsync = ref.watch(revenueCatInitializationProvider);
    ref.watch(initializeJournalProvider);

    return revenueCatAsync.when(
      data: (_) {
        // Only after RevenueCat is initialized, build the main app
        return const _MainAppContent();
      },
      loading: () => MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Initializing app...'),
              ],
            ),
          ),
        ),
      ),
      error: (error, _) => MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 48, color: Colors.red),
                SizedBox(height: 16),
                Text('Error initializing app'),
                SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _MainAppContent extends ConsumerStatefulWidget {
  const _MainAppContent();

  @override
  ConsumerState<_MainAppContent> createState() => _MainAppContentState();
}

class _MainAppContentState extends ConsumerState<_MainAppContent> {
  late final GoRouter _router;

  @override
  void initState() {
    super.initState();
    // Create router once and keep it stable throughout the app lifecycle
    _router = ref.read(routerProvider);
  }

  @override
  Widget build(BuildContext context) {
    // Watch palette changes for theme updates only
    final paletteAsync = ref.watch(paletteNotifierProvider);

    // Initialize palette on first load
    paletteAsync.whenData((paletteId) {
      AppColors.setPalette(paletteId);
    });

    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      themeMode: ThemeMode.light, // Always use light mode
      theme: paletteAsync.when(
        data: (paletteId) {
          // Ensure palette is set before creating theme
          AppColors.setPalette(paletteId);
          return ThemeConfig.createLightTheme();
        },
        loading: () {
          // Use Midnight palette while loading
          AppColors.setPalette('midnight_dark');
          return ThemeConfig.createLightTheme();
        },
        error: (_, __) {
          // Use Midnight palette on error
          AppColors.setPalette('midnight_dark');
          return ThemeConfig.createLightTheme();
        },
      ),
      routerConfig: _router, // Use the stable router instance
    );
  }
}
